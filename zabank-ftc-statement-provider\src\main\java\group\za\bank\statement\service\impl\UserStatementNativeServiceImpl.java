package group.za.bank.statement.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import feign.Response;
import group.za.bank.fund.trade.constants.enums.YesOrNoEnum;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.IdWorker;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.pub.entity.req.FileDownloadReq;
import group.za.bank.invest.pub.feign.FileService;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.statement.agent.StkInfoAgent;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.common.parser.HtmlParser;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementHtmlParsedData;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.domain.repository.TdMonthlyStatementParsedDataRepository;
import group.za.bank.statement.entity.dto.*;
import group.za.bank.statement.entity.req.NativeDetailReq;
import group.za.bank.statement.entity.req.NativeSummaryReq;
import group.za.bank.statement.entity.resp.NativeDetailResp;
import group.za.bank.statement.entity.resp.NativeSubBusinessSummaryResp;
import group.za.bank.statement.entity.resp.NativeSummaryListResp;
import group.za.bank.statement.entity.resp.NativeSummaryResp;
import group.za.bank.statement.manager.CryptoTradeCoreManager;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.StatementBasicInfoService;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.UserStatementNativeService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.bank.statement.utils.LangUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum.*;
import static group.za.bank.statement.common.constants.StatementConstants.HTML_PARSE_CRYPTO_ORDER_CURRENCY_NAME;
import static group.za.bank.statement.constants.enums.StatementBusinessModuleTypeEnum.getBySubBusinessTypeAndModuleType;
import static group.za.bank.statement.constants.enums.StatementHtmlParsedDataTypeEnum.HOLDING_CHANGE_STOCK_IN;
import static group.za.bank.statement.constants.enums.StatementHtmlParsedDataTypeEnum.HOLDING_CHANGE_STOCK_OUT;
import static group.za.bank.statement.service.impl.MonthlyStatementServiceImpl.STATEMENT_SUMMARY_DATA_TYPE_KEY;
import static group.za.bank.statement.utils.LangUtils.selectLang;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.engStr2Date;
import static group.za.invest.common.utils.DateUtil.TimeFormatter.DD_MM_YYYY;

/**
 * <AUTHOR>
 * @createTime 09 15:30
 * @description
 */
@Service
@Slf4j
public class UserStatementNativeServiceImpl implements UserStatementNativeService {
    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private UserRemoteService userRemoteService;

    @Autowired
    private MonthlyStatementManager monthlyStatementManager;

    @Autowired
    private MonthlyStatementService monthlyStatementService;

    @Autowired
    private StatementBasicInfoService statementBasicInfoService;
    @Autowired
    private TdMonthlyStatementParsedDataRepository tdMonthlyStatementParsedDataRepository;


    @Autowired
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;


    @Autowired
    private TdMonthlyStatementParsedDataRepository statementParsedDataRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private StkInfoAgent stkInfoAgent;

    @Autowired
    private CryptoTradeCoreManager cryptoTradeCoreManager;

    /**
     * html解析配置
     */
    public final static String NXT_HTML_PARSE_CONFIG = "nxt_account_monthly_conf.json";
    public final static String HTML_PARSE_CONFIG = "hk-account_monthly_conf.json";
    public final static String UNIT_PREFIX_TAG = "@";
    public final static String EMPTY_STRING = "--";
    public final static String SPLITTER_BACKWARD_DIRECTION = "=>";
    public final static String SPLITTER_FORWARD_DIRECTION = "<=";
    public static Pattern ENGLISH_AND_CHINESE_SPLIT_PATTERN = Pattern.compile("((?<englishPart>(([a-zA-Z()\\.](\\s)?(/)?)+))((?<chinesePart>[^x00-xf]+(/)?))?)");

    public final static List<String> NORMAL_ACCOUNT_TYPE_LIST = Arrays.asList(AccountTypeEnum.STOCK.getValue(),
            AccountTypeEnum.FUND.getValue(), AccountTypeEnum.CRYPTO.getValue());
    /**
     * 用户月结单native页面总结数据
     *
     * @param langEnum
     * @param req
     * @return
     */
    @Override
    public NativeSummaryListResp queryUserMonthlyStatementNativeSummaryData(I18nSupportEnum langEnum, NativeSummaryReq req) {
        NativeSummaryListResp nativeSummaryListResp = new NativeSummaryListResp();
        String bankUserId = req.getBankUserId();
        String business = Strings.isEmpty(req.getBusiness()) ? systemConfig.getDefaultMonthlyStatementBusinessType() : req.getBusiness();
        String lang = selectLang(langEnum.getName());
        //账户信息
        UserInvestAccountInfo userInvestAccountInfo = queryUserActivatedInvestAccountInfo(bankUserId, req.getChannel());
        if (Strings.isEmpty(userInvestAccountInfo.getClientId())) {
            nativeSummaryListResp.setSummaryList(new ArrayList<>());
            return nativeSummaryListResp;
        }

        String clientId = userInvestAccountInfo.getClientId();
        nativeSummaryListResp.setClientId(clientId);

        //用户时间范围内的所有月结单(包含两个语言的数据)，不管是否已发送
        List<TdFundMonthlyStatement> monthlyStatementList
                = tdMonthlyStatementMapper.queryUserFundMonthlyStatementList(bankUserId, clientId, null, null, null);
        Map<String, List<TdFundMonthlyStatement>> periodAndStatementMap = new HashMap<>(monthlyStatementList.size());
        for (TdFundMonthlyStatement statement : monthlyStatementList) {
            periodAndStatementMap.putIfAbsent(statement.getPeriod(), new ArrayList<>());
            periodAndStatementMap.computeIfPresent(statement.getPeriod(), (key, valueList) -> {
                valueList.add(statement);
                return valueList;
            });
        }

        //构建需要查询的时间区间（默认三年）
        Date todayDate = new Date();
        Integer nativeSummaryMonthSize = systemConfig.getNativeSummaryMonthSize();
        Date startMonth = DateUtil.addMonth(todayDate, - nativeSummaryMonthSize);

        String endPeriod = DateUtil.format(todayDate, DateUtil.FORMAT_SHORT);
        String startPeriod = DateUtil.format(startMonth, DateUtil.FORMAT_SHORT);

        List<String> allMonthList = DateUtil.getMonthsList(startPeriod, endPeriod);
        log.info("查询用户月结单,userId:{}. startPeriod:{},endPeriod:{},allMonthList{}",
                bankUserId, startPeriod, endPeriod, allMonthList);

        List<NativeSummaryResp> nativeSummaryRespList = new ArrayList<>(allMonthList.size());

        for (String period : allMonthList) {
            List<TdFundMonthlyStatement> monthlyStatements = periodAndStatementMap.get(period);
            NativeSummaryResp nativeSummaryResp = changeStatementRecord2Resp(business, period, lang, monthlyStatements);
            nativeSummaryRespList.add(nativeSummaryResp);
        }

        nativeSummaryListResp.setSummaryList(nativeSummaryRespList);
        return nativeSummaryListResp;
    }



    /**
     * 根基用户结单记录转成app响应resp
     *
     * @param business
     * @param period
     * @param docLang
     * @param monthlyStatements
     * @return
     */
    NativeSummaryResp changeStatementRecord2Resp(String business, String period, String docLang, List<TdFundMonthlyStatement> monthlyStatements) {
        //构建基础数据
        NativeSummaryResp nativeSummaryResp = buildBaseNativeSummaryResp(period, business);

        Date todayDate = new Date();
        Date lastMonth = DateUtil.addMonth(todayDate, -1);
        //本月
        String currentMonthPeriod = DateUtil.format(todayDate, systemConfig.getMonthlyStatementPeriodFormat());
        //上月
        String lastMonthPeriod = DateUtil.format(lastMonth, systemConfig.getMonthlyStatementPeriodFormat());

        //查询info主表记录
        TdFundMonthlyStatementInfo monthlyStatementInfo = monthlyStatementManager.getStatement(period);

        //结单是否已生成
        //1.当月的结单一定为生成中状态
        if (currentMonthPeriod.equals(period)) {
            nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.GENERATING.getType());
            return nativeSummaryResp;
        }
        //2.上月的结单
        if (lastMonthPeriod.equals(period)) {
            //2.1  上月的结单，主表记录为空，则是生成中状态
            if (monthlyStatementInfo == null) {
                nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.GENERATING.getType());
                return nativeSummaryResp;
            }
            //如果明细数据为空，则需要判断主表状态
            if (null == monthlyStatements) {
                if (MonthlyStatementInfoStatusEnum.FINISHED.getDbValue().equals(monthlyStatementInfo.getStatus())) {
                    nativeSummaryResp.setMarketValue(StatementConstants.ZERO_DEFAULT);
                    nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.NONE.getType());
                } else {
                    //2.2.2 数据还没推送，是生成中状态
                    nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.GENERATING.getType());
                }
                return nativeSummaryResp;
            }

            //2.2 上月的结单，主表记录已经初始化，则看明细的推送状态，只要有一个语言推送成功，则显示已生成状态
            return this.buildExistData(docLang, period, nativeSummaryResp, monthlyStatements);
        }else {
            //3.1 无主表记录或者无结单明细数据的或者
            if (monthlyStatementInfo == null || CollectionUtils.isEmpty(monthlyStatements)) {
                nativeSummaryResp.setMarketValue(StatementConstants.ZERO_DEFAULT);
                nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.NONE.getType());
                return nativeSummaryResp;
            }else{
                //3.2 构建有数据的阿结单
                return this.buildExistData(docLang, period, nativeSummaryResp, monthlyStatements);
            }
        }
    }

    /**
     * 已存在数据的构建
     * @param docLang
     * @param period
     * @param nativeSummaryResp
     * @param monthlyStatements
     * @return
     */
    private NativeSummaryResp buildExistData(String docLang, String period, NativeSummaryResp nativeSummaryResp, List<TdFundMonthlyStatement> monthlyStatements){
        if (!hasAnyStatementFinished(monthlyStatements)) {
            //2.2.1 数据还没推送，是生成中状态
            nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.GENERATING.getType());
            return nativeSummaryResp;
        }
        //2.2.2 数据已推送，则是结单已生成状态
        nativeSummaryResp.setStatementStatus(StatementNativeStatusEnum.GENERATED.getType());

        this.generatedData(docLang, period, nativeSummaryResp, monthlyStatements);

        return nativeSummaryResp;
    }

    /**
     * 数据组装
     * @param docLang
     * @param period
     * @param nativeSummaryResp
     * @param monthlyStatements
     */
    private void generatedData(String docLang, String period, NativeSummaryResp nativeSummaryResp, List<TdFundMonthlyStatement> monthlyStatements){
        Optional<TdFundMonthlyStatement> monthlyStatementOpt = monthlyStatements.stream()
                .filter(t -> t.getDocLang().equals(docLang)).findFirst();
        TdFundMonthlyStatement monthlyStatement = monthlyStatementOpt.get();

        nativeSummaryResp.setObsKey(monthlyStatement.getDocUrl());
        nativeSummaryResp.setGenerateDate(monthlyStatement.getRecordTime());
        nativeSummaryResp.setStatementId(monthlyStatement.getBusinessId());
        //结单数据
        TdMonthlyStatementBusinessData monthlyStatementBusinessData = monthlyStatementManager
                .getUserStatementBusinessData(monthlyStatement.getBusinessId(), STATEMENT_SUMMARY_DATA_TYPE_KEY);
        BaseMonthlyStatementDataDto baseMonthlyStatementDataDto = null;
        if (monthlyStatementBusinessData == null || monthlyStatementBusinessData.getData() == null) {
            log.error("用户月结单总览数据不存在! statementId:" + monthlyStatement.getBusinessId()
                    + ",businessSubtype:" + STATEMENT_SUMMARY_DATA_TYPE_KEY);
            nativeSummaryResp.setMarketValue(StatementConstants.ZERO_DEFAULT);
            nativeSummaryResp.setCurrency(CurrencyEnum.HKD.getCurrency());
        } else {
            Map<String, Object> statementDataMap = monthlyStatementBusinessData.getData();
            baseMonthlyStatementDataDto = group.za.invest.web.json.JSONObject.parse(JsonUtils.toJsonString(statementDataMap)
                    , BaseMonthlyStatementDataDto.class);
            String totalAmountString = baseMonthlyStatementDataDto.getTotalAmount();
            if (totalAmountString.contains(CurrencyEnum.HKD.getCurrency())) {
                totalAmountString = totalAmountString.replace(CurrencyEnum.HKD.getCurrency(), "");
                totalAmountString = totalAmountString.replace(",", "");
                totalAmountString = totalAmountString.trim();
            }
            //去掉格式化返回给前端
            nativeSummaryResp.setMarketValue(totalAmountString);
            nativeSummaryResp.setCurrency(CurrencyEnum.HKD.getCurrency());
        }

        log.info("填充用户{}月结单业务数据!userId:{},statementId:{}"
                , monthlyStatement.getBankUserId(), period, monthlyStatement.getBusinessId());
        fillSubBusinessNativeData(period, nativeSummaryResp, monthlyStatement, baseMonthlyStatementDataDto);
    }
    /**
     * 有任意一条推送成功就代表成功
     * @param monthlyStatements
     * @return
     */
    private boolean hasAnyStatementFinished(List<TdFundMonthlyStatement> monthlyStatements) {
        return monthlyStatements.stream()
                .anyMatch(t -> MonthlyStatementPubStatusEnum.FINISHED.getDbValue().equals(t.getPubStatus())
                        && MonthlyStatementDocStatusEnum.FINISHED.getDbValue().equals(t.getDocStatus()));
    }

    /**
     * 构建NativeSummaryResp基础数据
     * @param period
     * @param business
     * @return
     */
    private NativeSummaryResp buildBaseNativeSummaryResp(String period, String business){
        NativeSummaryResp nativeSummaryResp = new NativeSummaryResp();
        nativeSummaryResp.setPeriod(period);
        nativeSummaryResp.setBusiness(business);
        String monthlyStatementPeriodFormat = systemConfig.getMonthlyStatementPeriodFormat();
        Date periodDate = DateUtil.parse(period, monthlyStatementPeriodFormat);
        Date monthStartDate = DateUtil.getFirstDayOfMonth(periodDate);
        Date monthEndDate = DateUtil.getLastDayOfMonth(periodDate);
        nativeSummaryResp.setPeriodStartDate(monthStartDate);
        nativeSummaryResp.setPeriodEndDate(monthEndDate);
        nativeSummaryResp.setSubBusinessSummaryList(new ArrayList<>());

        return nativeSummaryResp;
    }


    /**
     * 填充各业务数据
     *
     * @param nativeSummaryResp
     * @param monthlyStatement
     * @param baseMonthlyStatementDataDto
     */
    private void fillSubBusinessNativeData(String period, NativeSummaryResp nativeSummaryResp
            , TdFundMonthlyStatement monthlyStatement, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto) {

        String statementId = monthlyStatement.getBusinessId();
        if(baseMonthlyStatementDataDto.isStock()){
            //股票的
            NativeSubBusinessSummaryResp nativeSubBusinessSummaryResp = buildNativeSubBusinessSummaryResp(statementId, StatementSubBusinessTypeEnum.STOCK.getType());
            nativeSummaryResp.getSubBusinessSummaryList().add(nativeSubBusinessSummaryResp);
        }
        if(baseMonthlyStatementDataDto.isHkStock()){
            //股票的
            NativeSubBusinessSummaryResp nativeSubBusinessSummaryResp = buildNativeSubBusinessSummaryResp(statementId, StatementSubBusinessTypeEnum.HK_STOCK.getType());
            nativeSummaryResp.getSubBusinessSummaryList().add(nativeSubBusinessSummaryResp);
        }
        if(baseMonthlyStatementDataDto.isCrypto()){
            //虚拟货币的
            NativeSubBusinessSummaryResp nativeSubBusinessSummaryResp = buildNativeSubBusinessSummaryResp(statementId, StatementSubBusinessTypeEnum.CRYPTO.getType());
            nativeSummaryResp.getSubBusinessSummaryList().add(nativeSubBusinessSummaryResp);
        }
        if(baseMonthlyStatementDataDto.isFund()){
            //基金的
            NativeSubBusinessSummaryResp nativeSubBusinessSummaryResp = buildNativeSubBusinessSummaryResp(statementId, StatementSubBusinessTypeEnum.FUND.getType());
            nativeSummaryResp.getSubBusinessSummaryList().add(nativeSubBusinessSummaryResp);
        }
        if(baseMonthlyStatementDataDto.isSfund()){
            //南向通基金的
            NativeSubBusinessSummaryResp nativeSubBusinessSummaryResp = buildNativeSubBusinessSummaryResp(statementId, StatementSubBusinessTypeEnum.SFUND.getType());
            nativeSummaryResp.getSubBusinessSummaryList().add(nativeSubBusinessSummaryResp);
        }

        log.info("用户{},{}月结单业务数据填充结果为:{}", monthlyStatement.getBankUserId()
                , period, nativeSummaryResp);
    }

    /**
     * 构建NativeSubBusinessSummaryResp数据
     *
     * @param statementId
     * @param subBusinessType
     * @return
     */
    private NativeSubBusinessSummaryResp buildNativeSubBusinessSummaryResp(String statementId, String subBusinessType){
        NativeSubBusinessSummaryResp stockNativeSubBusinessSummaryResp = new NativeSubBusinessSummaryResp();
        stockNativeSubBusinessSummaryResp.setSubBusinessType(subBusinessType);
        stockNativeSubBusinessSummaryResp.setCurrency(CurrencyEnum.HKD.getCurrency());
        stockNativeSubBusinessSummaryResp.setMarketValue(BigDecimal.ZERO.toPlainString());

        StatementBusinessService statementBusinessService = monthlyStatementService
                .getStatementBusinessService(subBusinessType);
        //结单数据
        TdMonthlyStatementBusinessData monthlyStatementBusinessData = monthlyStatementManager
                .getUserStatementBusinessData(statementId, subBusinessType);

        if (monthlyStatementBusinessData != null && monthlyStatementBusinessData.getData() != null) {
            log.info("用户结单有{}数据statementId:{}", subBusinessType, statementId);
            BigDecimal totalMarketValue = null;
            Map<String, Object> statementDataMap = monthlyStatementBusinessData.getData();
            if(StatementSubBusinessTypeEnum.STOCK.getType().equals(subBusinessType)){
                StockMonthlyStatementDataDto statementDataDto =
                        (StockMonthlyStatementDataDto) statementBusinessService.parseDataMap2Dto(statementDataMap);
                if (statementDataDto != null && null != statementDataDto.getBasicInfoDto()) {
                    StockMonthlyStatementDataDto.BasicInfoDto basicInfo = statementDataDto.getBasicInfoDto();
                    totalMarketValue = basicInfo.getTotalMarketValue();
                }
            }
            if(StatementSubBusinessTypeEnum.HK_STOCK.getType().equals(subBusinessType)){
                StockMonthlyStatementDataDto statementDataDto =
                        (StockMonthlyStatementDataDto) statementBusinessService.parseDataMap2Dto(statementDataMap);
                if (statementDataDto != null && null != statementDataDto.getBasicInfoDto()) {
                    StockMonthlyStatementDataDto.BasicInfoDto basicInfo = statementDataDto.getBasicInfoDto();
                    totalMarketValue = basicInfo.getTotalMarketValue();
                }
            }
            if(StatementSubBusinessTypeEnum.FUND.getType().equals(subBusinessType)
                    || StatementSubBusinessTypeEnum.SFUND.getType().equals(subBusinessType)){
                FundMonthlyStatementDataDto statementDataDto =
                        (FundMonthlyStatementDataDto) statementBusinessService.parseDataMap2Dto(statementDataMap);
                if (statementDataDto != null && null != statementDataDto.getBasicInfo()) {
                    FundMonthlyStatementDataDto.BasicInfoDto basicInfo = statementDataDto.getBasicInfo();
                    //用基金格式化处理过的值
                    totalMarketValue = new BigDecimal(basicInfo.getTotalMarket().replace(",", ""));
                }
            }
            if(StatementSubBusinessTypeEnum.CRYPTO.getType().equals(subBusinessType)){
                CryptoMonthlyStatementDataDto statementDataDto =
                        (CryptoMonthlyStatementDataDto) statementBusinessService.parseDataMap2Dto(statementDataMap);
                if (statementDataDto != null && null != statementDataDto.getBasicInfoDto()) {
                    CryptoMonthlyStatementDataDto.BasicInfoDto basicInfo = statementDataDto.getBasicInfoDto();
                    totalMarketValue = basicInfo.getTotalMarket();
                }
            }

            if (totalMarketValue != null) {
                stockNativeSubBusinessSummaryResp.setMarketValue(totalMarketValue.setScale(2, BigDecimal.ROUND_DOWN).toPlainString());
                stockNativeSubBusinessSummaryResp.setCurrency(CurrencyEnum.HKD.getCurrency());
            } else {
                stockNativeSubBusinessSummaryResp.setMarketValue("");
            }

        }
        return stockNativeSubBusinessSummaryResp;
    }

    /**
     * 用户月结单native页面各模块数据
     * 1.通过statementId去mongodb找解析结果，找到的话直接走步骤6
     * 2.通过statementId去obs上下载html文件：英简和英繁两份（根据参数指定的语言去下载一份即可，加锁防并发[返回处理中给用户]）
     * 3.解析html文件，抽取各模块的table表格（英简和英繁的表格路径不一样）
     * 4.将表格转成json对象，并在这个时候做app显示适配（标题修正、属性值合并）
     * 5.将结果存到mongodb（异步即可）
     * 6.根据参数对数据结果进行分页处理
     *
     * @param langEnum
     * @param req
     * @return
     */
    @Override
    public NativeDetailResp queryUserMonthlyStatementNativeDetail(I18nSupportEnum langEnum, NativeDetailReq req) {
        NativeDetailResp nativeDetailResp = new NativeDetailResp();
        String bankUserId = req.getBankUserId();
        String period = req.getPeriod();
        String appLang = langEnum.getNameOfBank();
        String docLang = getDocLangByAppLang(langEnum);

        TdFundMonthlyStatementInfo statementInfo = monthlyStatementManager.getStatement(period);
        if (statementInfo == null) {
            return nativeDetailResp;
        }
        //账户信息
        UserInvestAccountInfo userInvestAccountInfo = queryUserActivatedInvestAccountInfo(bankUserId, req.getChannel());
        if (Strings.isEmpty(userInvestAccountInfo.getClientId())) {
            return nativeDetailResp;
        }
        String clientId = userInvestAccountInfo.getClientId();

        //查当前账户当期的结单记录
        List<TdFundMonthlyStatement> statementList = monthlyStatementManager.listUserStatementRecords(period, clientId);
        if(CollectionUtils.isEmpty(statementList)){
            return nativeDetailResp;
        }

        //查询mongo基础数据
        Optional<TdFundMonthlyStatement> tdFundMonthlyStatement = statementList.stream().filter(t -> t.getDocLang().equals(docLang)).findFirst();
        if(!tdFundMonthlyStatement.isPresent()){
            return nativeDetailResp;
        }
        TdMonthlyStatementBusinessData monthlyStatementBusinessData = monthlyStatementManager
                .getUserStatementBusinessData(tdFundMonthlyStatement.get().getBusinessId(), STATEMENT_SUMMARY_DATA_TYPE_KEY);
        Map<String, Object> statementDataMap = monthlyStatementBusinessData.getData();
        BaseMonthlyStatementDataDto baseMonthlyStatementDataDto = group.za.invest.web.json.JSONObject.parse(JsonUtils.toJsonString(statementDataMap)
                , BaseMonthlyStatementDataDto.class);
        if(null == baseMonthlyStatementDataDto){
            log.error("用户native查询结单基础数据不存在!period:{},businessId:{},userId:{},clientId:{},appLang:{}"
                    , period, statementInfo.getBusinessId(), bankUserId, clientId, appLang);
            return nativeDetailResp;
        }

        //查下对应的mongodb数据
        List<TdMonthlyStatementHtmlParsedData> statementParsedDataList = tdMonthlyStatementParsedDataRepository
                .getByStatementInfoIdAndUserIdAndClientIdAndAppLang(statementInfo.getBusinessId(), bankUserId, clientId, appLang);
        if (CollectionUtils.isEmpty(statementParsedDataList)) {
            log.error("用户native查询结单解析结果不存在!period:{},businessId:{},userId:{},clientId:{},appLang:{}"
                    , period, statementInfo.getBusinessId(), bankUserId, clientId, appLang);
            return nativeDetailResp;
        }
        //如果有多条，取最新的一条数据
        TdMonthlyStatementHtmlParsedData statementParsedData = statementParsedDataList.stream()
                .max(Comparator.comparing(TdMonthlyStatementHtmlParsedData::getRecordDate)).get();

        log.info("填充月结单html文件业务数据,period:{},userId:{},clientId:{},appLang:{},dataMap:{}"
                , period, bankUserId, clientId, appLang, statementParsedData);

        fillStatementHtmlBusinessData(appLang, statementParsedData, req, nativeDetailResp, baseMonthlyStatementDataDto);
        return nativeDetailResp;
    }

    /**
     * 根据APP的语言类型获取文档的语言类型
     * @param appLang
     * @return
     */
    private String getDocLangByAppLang(I18nSupportEnum appLang) {
        if (appLang.equals(CN_ZH)) {
            return CN_ZH.getName();
        } else {
            return CN_HK.getName();
        }
    }
    /**
     * 把html的解析值填充到resp
     *
     * @param statementParsedData
     */
    void fillStatementHtmlBusinessData(String appLang, TdMonthlyStatementHtmlParsedData statementParsedData
            , NativeDetailReq req, NativeDetailResp nativeDetailResp, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto) {

        //需要的数据模块列表
        List<StatementBusinessModuleTypeEnum> statementBusinessModuleTypeEnumList
                = getBySubBusinessTypeAndModuleType(req.getSubBusinessType(), req.getModuleType());
        //过滤掉没有开户的数据
        statementBusinessModuleTypeEnumList = filterModuleListByAccountStatus(req.getBankUserId(), req.getPeriod(), statementBusinessModuleTypeEnumList, baseMonthlyStatementDataDto);

        //转换
        String htmlBusinessDataJsonString = statementParsedData.getData();
        JSONObject htmlBusinessDataJsonObject = JSONUtil.parseObj(htmlBusinessDataJsonString);
        //分页
        filterAndPage(appLang, htmlBusinessDataJsonObject, statementBusinessModuleTypeEnumList, req, nativeDetailResp);

    }

    /**
     * 根据开户状态过滤掉不需要的模块，即未开户的完全不需要数据，连标题都不要
     *
     * @return
     */
    List<StatementBusinessModuleTypeEnum> filterModuleListByAccountStatus(String bankUserId, String period
            , List<StatementBusinessModuleTypeEnum> statementBusinessModuleTypeEnumList, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto) {
        if (CollectionUtils.isEmpty(statementBusinessModuleTypeEnumList)) {
            return statementBusinessModuleTypeEnumList;
        }
//        List<UserInvestAccountInfo> userInvestAccountInfoList = userRemoteService.queryUserAllInvestAccountInfo(bankUserId);
//        if (CollectionUtils.isEmpty(userInvestAccountInfoList)) {
//            return new ArrayList<>();
//        }

        List<StatementBusinessModuleTypeEnum> resultList = new ArrayList<>();
        for (StatementBusinessModuleTypeEnum statementBusinessModuleTypeEnum : statementBusinessModuleTypeEnumList) {
            StatementSubBusinessTypeEnum statementSubBusinessTypeEnum = statementBusinessModuleTypeEnum.getStatementSubBusinessTypeEnum();
            //基金需要根据状态判断是否需要展示
            if(StatementSubBusinessTypeEnum.FUND.equals(statementSubBusinessTypeEnum) &&
                    baseMonthlyStatementDataDto.isFund()){
                resultList.add(statementBusinessModuleTypeEnum);
            }
            //基金需要根据状态判断是否需要展示
            if(StatementSubBusinessTypeEnum.SFUND.equals(statementSubBusinessTypeEnum) &&
                    baseMonthlyStatementDataDto.isSfund()){
                resultList.add(statementBusinessModuleTypeEnum);
            }
            //股票需要根据状态判断是否需要展示
            if(StatementSubBusinessTypeEnum.STOCK.equals(statementSubBusinessTypeEnum) &&
                    baseMonthlyStatementDataDto.isStock()){
                resultList.add(statementBusinessModuleTypeEnum);
            }
            //股票需要根据状态判断是否需要展示
            if(StatementSubBusinessTypeEnum.HK_STOCK.equals(statementSubBusinessTypeEnum) &&
                    baseMonthlyStatementDataDto.isHkStock()){
                resultList.add(statementBusinessModuleTypeEnum);
            }
            //虚拟货币类型判断
            if (StatementSubBusinessTypeEnum.CRYPTO.equals(statementSubBusinessTypeEnum) &&
                    baseMonthlyStatementDataDto.isCrypto()){
                resultList.add(statementBusinessModuleTypeEnum);
            }
        }
        return resultList;
    }


    /**
     * 过滤数据并分页
     *
     * @param htmlBusinessDataJsonObject
     * @param statementBusinessModuleTypeEnumList
     * @param nativeDetailResp
     */
    private void filterAndPage(String appLang, JSONObject htmlBusinessDataJsonObject, List<StatementBusinessModuleTypeEnum> statementBusinessModuleTypeEnumList
            , NativeDetailReq req, NativeDetailResp nativeDetailResp) {

        if (CollectionUtils.isEmpty(statementBusinessModuleTypeEnumList)) {
            return;
        }
        nativeDetailResp.setDataTypeAndDataMap(new LinkedHashMap<>());

        //排序，保证顺序正确
        statementBusinessModuleTypeEnumList.sort(Comparator.comparingInt(StatementBusinessModuleTypeEnum::getSort));

        for (StatementBusinessModuleTypeEnum moduleTypeEnum : statementBusinessModuleTypeEnumList) {
            StatementModuleTypeEnum statementModuleTypeEnum = moduleTypeEnum.getStatementModuleTypeEnum();
            StatementSubBusinessTypeEnum statementSubBusinessTypeEnum = moduleTypeEnum.getStatementSubBusinessTypeEnum();
            NativeBusinessDto nativeBusinessDto = nativeDetailResp.getDataTypeAndDataMap().get(statementModuleTypeEnum.getModuleType());

            //无数据
            if (nativeBusinessDto == null) {
                nativeBusinessDto = new NativeBusinessDto();
                nativeBusinessDto.setDataType(statementModuleTypeEnum.getModuleType());
                nativeBusinessDto.setDataTypeTitle(selectModuleTitle(appLang, statementModuleTypeEnum));
                nativeBusinessDto.setNativeModuleList(new ArrayList<>());
                nativeDetailResp.getDataTypeAndDataMap().put(statementModuleTypeEnum.getModuleType(), nativeBusinessDto);
            }

            NativeBusinessDto originalNativeBusinessDto = htmlBusinessDataJsonObject.get(statementModuleTypeEnum.getModuleType(), NativeBusinessDto.class);
            NativeModuleDto nativeModuleDto = new NativeModuleDto();
            nativeModuleDto.setAssetType(statementSubBusinessTypeEnum.getType());
            nativeModuleDto.setAssetTypeTitle(selectSubBusinessTitle(appLang, statementSubBusinessTypeEnum));
            nativeModuleDto.setNativeBlockList(new ArrayList<>());
            List<NativeModuleDto> nativeModuleList = nativeBusinessDto.getNativeModuleList();
            nativeModuleList.add(nativeModuleDto);
            //抽数据
            if (originalNativeBusinessDto != null) {
                List<NativeModuleDto> nativeModuleDtoList = originalNativeBusinessDto.getNativeModuleList();
                if (!CollectionUtils.isEmpty(nativeModuleDtoList)) {
                    for (NativeModuleDto originalNativeModuleDto : nativeModuleDtoList) {
                        if (statementSubBusinessTypeEnum.getType().equals(originalNativeModuleDto.getAssetType())) {
                            nativeModuleDto.setNativeBlockList(originalNativeModuleDto.getNativeBlockList());
                        }
                    }
                }
            }
            //分页处理
            doPaging(appLang, nativeBusinessDto, statementModuleTypeEnum, statementSubBusinessTypeEnum, req);
            nativeDetailResp.getDataTypeAndDataMap().put(statementModuleTypeEnum.getModuleType(), nativeBusinessDto);
        }
    }


    /**
     * 分页处理
     *
     * @param nativeBusinessDto
     * @param req
     */
    private void doPaging(String appLang, NativeBusinessDto nativeBusinessDto, StatementModuleTypeEnum statementModuleTypeEnum
            , StatementSubBusinessTypeEnum statementSubBusinessTypeEnum, NativeDetailReq req) {

        List<NativeModuleDto> nativeModuleDtoList = nativeBusinessDto.getNativeModuleList();
        NativeModuleDto businessModuleDto = null;
        for (NativeModuleDto nativeModuleDto : nativeModuleDtoList) {
            if (statementSubBusinessTypeEnum.getType().equals(nativeModuleDto.getAssetType())) {
                businessModuleDto = nativeModuleDto;
                break;
            }
        }
        if (businessModuleDto == null) {
            return;
        }

        List<NativeBlockDto> nativeBlockList = businessModuleDto.getNativeBlockList();
        if (nativeBlockList == null) {
            nativeBlockList = new ArrayList<>();
        }

        //分页处理
        int pageNum = req.getPageNum();
        int pageSize = systemConfig.getNativePageSize();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = startIndex + pageSize;


        businessModuleDto.setPageNum(pageNum);
        businessModuleDto.setTotalNum(nativeBlockList.size());
        businessModuleDto.setPageSize(pageSize);

        if (endIndex > nativeBlockList.size()) {
            endIndex = nativeBlockList.size();
        }
        businessModuleDto.setNativeBlockList(nativeBlockList.subList(startIndex, endIndex));
    }


    /**
     * 获取用户当前激活的投资账户
     *
     * @param bankUserId
     * @return
     */
    private UserInvestAccountInfo queryUserActivatedInvestAccountInfo(String bankUserId, String channel) {
        //账户信息
        List<UserInvestAccountInfo> userInvestAccountInfoList = userRemoteService.queryUserAllInvestAccountInfo(bankUserId);
        //取所有账户里最早开户的账户数据
        UserInvestAccountInfo userInvestAccountInfo = userInvestAccountInfoList.stream()
                .filter(t ->{
                    if(ChannelEnum.NORMAL.getValue().equals(channel)){
                        return NORMAL_ACCOUNT_TYPE_LIST.contains(t.getAccountType());
                    }else{
                        return AccountTypeEnum.S_FUND.getValue().equals(t.getAccountType());
                    }
                })
                .min(Comparator.comparing(UserInvestAccountInfo::getOpenFinishedTime)).get();

        log.info("用户{}当前激活投资账户为:{}", bankUserId, userInvestAccountInfo);
        return userInvestAccountInfo;
    }


    /**
     * html文件下载、解析、存档
     * 1. 从obs上下载html文件
     * 2. 解析html文件（英简、英繁）
     * 3. 保存解析结果到mongodb（英简、英繁转成简、英、繁）
     *
     * @param statementInfo
     * @param userStatement
     */
    @Override
    public void htmlDocParseProcess(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement userStatement) {
        log.info("htmlDocParseProcess开始解析html文件");
        long start = System.currentTimeMillis();
        String docLang = userStatement.getDocLang();
        I18nSupportEnum langEnum = LangUtils.changeDocLang2Normal(docLang);
        List<TdMonthlyStatementHtmlParsedData> statementHtmlParsedDataList = new ArrayList<>(2);


        //1.下载文件
        InputStream htmlFileInputStream = downLoadHtmlFileFromObs(statementInfo, userStatement);


        //2.解析文件
        String htmlEntity = IoUtil.read(new InputStreamReader(htmlFileInputStream, StandardCharsets.UTF_8));
        TdMonthlyStatementHtmlParsedData statementHtmlParsedData = parseHtmlFileAndFillData(docLang, langEnum.getNameOfBank()
                , htmlEntity, statementInfo, userStatement);

        statementHtmlParsedDataList.add(statementHtmlParsedData);

        //从繁体中抽出英语版本
        if (CN_HK.getName().equals(docLang)) {
            TdMonthlyStatementHtmlParsedData enStatementHtmlParsedData = parseHtmlFileAndFillData(docLang, US_EN.getNameOfBank()
                    , htmlEntity, statementInfo, userStatement);
            statementHtmlParsedDataList.add(enStatementHtmlParsedData);
        }

        //3.将解析数据保存到mongodb
        saveHtmlParsedData2Mongodb(statementHtmlParsedDataList);
        log.info("htmlDocParseProcess保存用户月结单html文件解析结果到mongodb，耗时：{}ms", System.currentTimeMillis() - start);

    }



    /**
     * 下载html文件
     */
    InputStream downLoadHtmlFileFromObs(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement userStatement) {
        String htmlUrl = userStatement.getHtmlUrl();
        FileDownloadReq fileDownloadReq = new FileDownloadReq();
        fileDownloadReq.setObjectKey(htmlUrl);
        Response resp = fileService.fileDownload(fileDownloadReq);
        if (log.isDebugEnabled()) {
            log.debug("pub返回文件流header>>>{}", resp.headers().toString());
        }
        try (InputStream inputStream = resp.body().asInputStream()) {
            //转成html文件
            return inputStream;
        } catch (Exception e) {
            throw new RuntimeException("下载月结单html文件异常", e);
        }
    }


    /**
     * 解析html文件并将数据填充进去
     *
     * @param docLang
     * @param targetLang
     * @param htmlEntity
     * @param statementInfo
     * @param userStatement
     * @return
     */
    TdMonthlyStatementHtmlParsedData parseHtmlFileAndFillData(String docLang, String targetLang, String htmlEntity
            , TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement userStatement) {
        //初始化
        TdMonthlyStatementHtmlParsedData statementHtmlParsedData
                = generateStatementHtmlParsedDataDto(targetLang, statementInfo, userStatement);
        //区分是否是南向通模板
        String jsonConfig = userStatement.getChannel().equals(ChannelEnum.SOUTH_BOUND.getValue()) ? NXT_HTML_PARSE_CONFIG : HTML_PARSE_CONFIG;
        Map<String, Object> result = new HtmlParser().execute(jsonConfig, htmlEntity);
        String jsonResult = JSONUtil.toJsonStr(result);
        Map<String, JSONObject> dataTypeKeyAndDtoMap = JSONUtil.toBean(jsonResult, Map.class);
        Map<String, NativeBusinessDto> dtoMap = new HashMap<>();
        for (Map.Entry<String, JSONObject> entry : dataTypeKeyAndDtoMap.entrySet()) {
            NativeBusinessDto nativeBusinessDto = JSONUtil.toBean(entry.getValue(), NativeBusinessDto.class);
            dtoMap.put(entry.getKey(), nativeBusinessDto);
            List<NativeModuleDto> nativeModuleList = nativeBusinessDto.getNativeModuleList();
            if (CollectionUtils.isEmpty(nativeModuleList)) {
                continue;
            }
            for (NativeModuleDto moduleDto : nativeModuleList) {
                List<List<NativeRowDto>> rowDtos = moduleDto.getNativeRowList();
                if (CollectionUtils.isEmpty(rowDtos)) {
                    continue;
                }
                List<NativeBlockDto> nativeBlockList = new ArrayList<>();
                moduleDto.setNativeBlockList(nativeBlockList);

                for (List<NativeRowDto> nativeRowDtos : rowDtos) {
                    if (CollectionUtils.isEmpty(nativeRowDtos)) {
                        continue;
                    }
                    NativeBlockDto nativeBlockDto = new NativeBlockDto();
                    nativeBlockDto.setNativeRowList(nativeRowDtos);
                    nativeBlockList.add(nativeBlockDto);
                }
                moduleDto.setNativeRowList(null);
            }
            this.processParsedResult(docLang, targetLang, nativeBusinessDto);
        }
        statementHtmlParsedData.setData(JSONUtil.toJsonStr(dtoMap));
        return statementHtmlParsedData;
    }


    public static void main(String[] args) {
        //解析数据 todo:换成参数
        InputStream inputStream = FileUtil.getInputStream(new File("C:\\Users\\<USER>\\Desktop\\成交合同\\结单\\222.html"));
        String entity = IoUtil.read(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        Map<String, Object> result = new HtmlParser().execute("hk-account_monthly_conf.json", entity);
        String jsonResult = JSONUtil.toJsonStr(result);
        Map<String, JSONObject> dataTypeKeyAndDtoMap = JSONUtil.toBean(jsonResult, Map.class);
        List<NativeBusinessDto> nativeBusinessDtos = new ArrayList<>();
        for (Map.Entry<String, JSONObject> entry : dataTypeKeyAndDtoMap.entrySet()) {
            NativeBusinessDto nativeBusinessDto = JSONUtil.toBean(entry.getValue(), NativeBusinessDto.class);
            List<NativeModuleDto> nativeModuleList = nativeBusinessDto.getNativeModuleList();
            if (CollectionUtils.isEmpty(nativeModuleList)) {
                continue;
            }
            for (NativeModuleDto moduleDto : nativeModuleList) {
                List<List<NativeRowDto>> rowDtos = moduleDto.getNativeRowList();
                if (CollectionUtils.isEmpty(rowDtos)) {
                    continue;
                }
                List<NativeBlockDto> nativeBlockList = new ArrayList<>();
                moduleDto.setNativeBlockList(nativeBlockList);

                for (List<NativeRowDto> nativeRowDtos : rowDtos) {
                    if (CollectionUtils.isEmpty(nativeRowDtos)) {
                        continue;
                    }
                    NativeBlockDto nativeBlockDto = new NativeBlockDto();
                    nativeBlockDto.setNativeRowList(nativeRowDtos);
                    nativeBlockList.add(nativeBlockDto);
                }
                moduleDto.setNativeRowList(null);
            }
            UserStatementNativeServiceImpl userStatementNativeService = new UserStatementNativeServiceImpl();
            userStatementNativeService.processParsedResult(CN_HK.getName(), US_EN.getNameOfBank(), nativeBusinessDto);
            System.out.println(JSONUtil.toJsonStr(nativeBusinessDto));
            nativeBusinessDtos.add(nativeBusinessDto);
        }

        System.out.println(JSONUtil.toJsonStr(nativeBusinessDtos));
    }

    /**
     * 对解析结果进行加工处理
     *
     * @param docLang
     * @param targetLang
     * @param nativeBusinessDto
     * @return
     */
    private void processParsedResult(String docLang, String targetLang, NativeBusinessDto nativeBusinessDto) {
        List<NativeModuleDto> moduleDtoList = nativeBusinessDto.getNativeModuleList();
        if (CollectionUtils.isEmpty(moduleDtoList)) {
            return;
        }

        for (NativeModuleDto moduleDto : moduleDtoList) {
            List<NativeBlockDto> nativeBlockList = moduleDto.getNativeBlockList();
            if (CollectionUtils.isEmpty(nativeBlockList)) {
                continue;
            }
            for (NativeBlockDto blockDto : nativeBlockList) {
                //分组
                List<NativeRowGroupDto> parsedRowGroupList = groupBlockParsedRows(blockDto.getNativeRowList());

                //组装详情数据
                NativeRowGroupDto nativeRowGroupDto = parsedRowGroupList.get(0);
                List<List<NativeRowDto>> extractDetailRowList = extractDetailRowList(docLang, targetLang, nativeRowGroupDto.getNativeRowDetailList());
                blockDto.setNativeRowDetailList(extractDetailRowList);

                List<NativeRowDto> nativeRowDtoList = extractNormalRowList(docLang, targetLang, parsedRowGroupList);
                blockDto.setNativeRowList(nativeRowDtoList);

                NativeRowDto nativeHeaderRowDto = extractHeaderRow(docLang, targetLang, parsedRowGroupList, nativeRowDtoList);
                blockDto.setNativeTitle(nativeHeaderRowDto);

                NativeRowDto nativeDateRowDto = extractDateRow(docLang, targetLang, parsedRowGroupList);
                if (nativeDateRowDto != null) {
                    String dateString = nativeDateRowDto.getValue();
                    try {
                        blockDto.setTradeDate(engStr2Date(dateString));
                    } catch (Exception e) {
                        log.info("native日期解析异常，dateString：{}", dateString);
                        blockDto.setTradeDate(cn.hutool.core.date.DateUtil.parse(dateString, DD_MM_YYYY));
                    }
                }
            }
        }
    }

    /**
     * 抽取普通行
     *
     * @param parsedRowGroupList
     * @return
     */
    List<NativeRowDto> extractNormalRowList(String docLang, String appLang, List<NativeRowGroupDto> parsedRowGroupList) {
        if (CollectionUtils.isEmpty(parsedRowGroupList)) {
            return new ArrayList<>();
        }
        String currency = "";
        List<NativeRowDto> nativeRowDtoList = new ArrayList<>();
        for (NativeRowGroupDto nativeRowGroupDto : parsedRowGroupList) {
            StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(nativeRowGroupDto.getRowType());
            if (!rowTypeEnum.isDate() && !rowTypeEnum.isHeader()) {
                if (!CollectionUtils.isEmpty(nativeRowGroupDto.getNativeRowDetailList())) {

                    List<List<NativeRowDto>> nativeRowDetailList = nativeRowGroupDto.getNativeRowDetailList();
                    List<NativeRowDto> nativeRowDtos = nativeRowDetailList.get(0);
                    //从明细数据里面获取币种
                    Optional<NativeRowDto> cryptoPriceCurrencyOptional = nativeRowDtos.stream().filter(t -> t.getName().equals(HTML_PARSE_CRYPTO_ORDER_CURRENCY_NAME)).findFirst();
                    if (cryptoPriceCurrencyOptional.isPresent()) {
                        currency = cryptoPriceCurrencyOptional.get().getValue();
                    }
                } else {
                    NativeRowDto nativeRowDto = change2NativeRow(docLang, appLang, nativeRowGroupDto.getGroupId(), nativeRowGroupDto.getSeq(), nativeRowGroupDto.getRowList());
                    nativeRowDto.setValue(nativeRowDto.getValue());
                    //如果币种不为空，则拼接上币种
                    if (StringUtils.isNotEmpty(currency)) {
                        nativeRowDto.setValue(currency + " " + nativeRowDto.getValue());
                    }

                    if (Strings.isEmpty(nativeRowDto.getTitle())) {
                        continue;
                    }
                    nativeRowDtoList.add(nativeRowDto);
                }
            }
        }
        return nativeRowDtoList;
    }

    /**
     * 解析明细数据
     *
     * @param docLang
     * @param appLang
     * @param nativeRowDetailList
     * @return
     */
    List<List<NativeRowDto>> extractDetailRowList(String docLang, String appLang, List<List<NativeRowDto>> nativeRowDetailList) {
        if (CollectionUtils.isEmpty(nativeRowDetailList)) {
            return new ArrayList<>();
        }
        List<List<NativeRowDto>> nativeRowDtoList = new ArrayList<>();
        for (List<NativeRowDto> nativeRowDtos : nativeRowDetailList) {
            List<NativeRowDto> detail = new ArrayList<>();
            //详情数据从第三列开始解析
            List<NativeRowDto> subList = nativeRowDtos.subList(2, nativeRowDtos.size());
            List<NativeRowGroupDto> detailGroup = groupBlockParsedRows(subList);
            detailGroup.forEach(nativeRowDto -> {
                NativeRowDto resultDto = change2NativeRow(docLang, appLang, nativeRowDto.getGroupId(), nativeRowDto.getSeq(), nativeRowDto.getRowList());
                detail.add(resultDto);
            });
            nativeRowDtoList.add(detail);
        }
        //TODO 临时方案，把多解析的一行数据删除
        nativeRowDtoList.remove(nativeRowDtoList.size() -1);
        return nativeRowDtoList;
    }

    /**
     * 抽取日期行
     *
     * @param parsedRowGroupList
     * @return
     */
    NativeRowDto extractDateRow(String docLang, String appLang, List<NativeRowGroupDto> parsedRowGroupList) {
        if (CollectionUtils.isEmpty(parsedRowGroupList)) {
            return null;
        }
        NativeRowDto nativeRowDto = null;
        for (NativeRowGroupDto nativeRowGroupDto : parsedRowGroupList) {
            if (!CollectionUtils.isEmpty(nativeRowGroupDto.getNativeRowDetailList())) {
                List<NativeRowDto> nativeRowDtos = nativeRowGroupDto.getNativeRowDetailList().get(0);
                for (NativeRowDto rowDto : nativeRowDtos) {
                    StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(rowDto.getRowType());
                    if (rowTypeEnum.isDate()) {
                        nativeRowDto = change2NativeRow(docLang, appLang, nativeRowGroupDto.getGroupId(), nativeRowGroupDto.getSeq(), Arrays.asList(rowDto));
                        break;
                    }
                }
            } else {
                StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(nativeRowGroupDto.getRowType());
                if (rowTypeEnum.isDate()) {
                    nativeRowDto = change2NativeRow(docLang, appLang, nativeRowGroupDto.getGroupId(), nativeRowGroupDto.getSeq(), nativeRowGroupDto.getRowList());
                    break;
                }
            }
        }
        return nativeRowDto;
    }

    /**
     * 抽取标题行
     *
     * @param parsedRowGroupList
     * @return
     */
    NativeRowDto extractHeaderRow(String docLang, String appLang, List<NativeRowGroupDto> parsedRowGroupList, List<NativeRowDto> nativeRowDtoList) {
        if (CollectionUtils.isEmpty(parsedRowGroupList)) {
            return null;
        }
        NativeRowDto nativeRowDto = null;
        for (NativeRowGroupDto nativeRowGroupDto : parsedRowGroupList) {
            if (!CollectionUtils.isEmpty(nativeRowGroupDto.getNativeRowDetailList())) {
                //有明细数据，需要从明细数据里取title信息
                List<List<NativeRowDto>> nativeRowDetailList = nativeRowGroupDto.getNativeRowDetailList();
                List<NativeRowDto> nativeRowDtos = nativeRowDetailList.get(0);

                //分组
                List<NativeRowGroupDto> groupDtoList = groupBlockParsedRows(nativeRowDtos);
                for (NativeRowGroupDto rowDto : groupDtoList) {
                    StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(rowDto.getRowType());
                    if (rowTypeEnum.isHeader()) {
                        nativeRowDto = change2NativeRow(docLang, appLang, nativeRowGroupDto.getGroupId(), nativeRowGroupDto.getSeq(), rowDto.getRowList());
                        //title的value取交收金额，并移除外层的交收金额
                        NativeRowDto settleAmountRow = nativeRowDtoList.get(nativeRowDtoList.size() - 1);
                        nativeRowDto.setValue(settleAmountRow.getValue());
                        nativeRowDtoList.remove(nativeRowDtoList.size() - 1);
                        break;
                    }
                }
            } else {
                StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(nativeRowGroupDto.getRowType());
                if (rowTypeEnum.isHeader()) {
                    nativeRowDto = change2NativeRow(docLang, appLang, nativeRowGroupDto.getGroupId(), nativeRowGroupDto.getSeq(), nativeRowGroupDto.getRowList());
                    break;
                }
            }
        }
        return nativeRowDto;
    }

    /**
     * 对各行数据行进行分组
     *
     * @param parsedRowList
     */
    List<NativeRowGroupDto> groupBlockParsedRows(List<NativeRowDto> parsedRowList) {

        List<NativeRowGroupDto> parsedRowGroupList = new ArrayList<>();
        if (CollectionUtils.isEmpty(parsedRowList)) {
            return parsedRowGroupList;
        }

        Map<String, NativeRowGroupDto> groupIdAndNativeRowGroupDtoMap = new HashMap<>();

        for (NativeRowDto originalNativeRowDto : parsedRowList) {
            String groupKey = getGroupKey(originalNativeRowDto);
            NativeRowGroupDto nativeRowGroupDto = groupIdAndNativeRowGroupDtoMap.get(groupKey);
            if (nativeRowGroupDto == null) {
                nativeRowGroupDto = new NativeRowGroupDto();
                nativeRowGroupDto.setRowType(originalNativeRowDto.getRowType());
                nativeRowGroupDto.setRowList(new ArrayList<>());
                nativeRowGroupDto.setSeq(originalNativeRowDto.getSeq());
                groupIdAndNativeRowGroupDtoMap.put(groupKey, nativeRowGroupDto);
                parsedRowGroupList.add(nativeRowGroupDto);
            }
            nativeRowGroupDto.setGroupId(groupKey);
            if (!CollectionUtils.isEmpty(originalNativeRowDto.getNativeRowDetailList())) {
                nativeRowGroupDto.setNativeRowDetailList(originalNativeRowDto.getNativeRowDetailList());
            }
            nativeRowGroupDto.getRowList().add(originalNativeRowDto);
        }
        return parsedRowGroupList;
    }


    /**
     * @param nativeRowDto
     * @return
     */
    String getGroupKey(NativeRowDto nativeRowDto) {
        String groupId = nativeRowDto.getGroupId();
        if (Strings.isEmpty(groupId)) {
            groupId = nativeRowDto.getName();
        }
        return groupId;
    }


    /**
     * 根据app语言选择文本，用|分隔
     *
     * @param text
     * @return
     */
    String selectByAppLang(String appLang, String text, String value) {
        if (Strings.isEmpty(text) || !text.contains("|")) {
            return text;
        }
        String[] unitArray = text.split("\\|");
        int zhIndex = 0;
        int hkIndex = Math.min(unitArray.length, 1);
        int enIndex = Math.min(unitArray.length, 2);

        if (CN_ZH.getNameOfBank().equals(appLang)) {
            return unitArray[zhIndex];
        } else if (US_EN.getNameOfBank().equals(appLang)) {
            String enUnits = unitArray[enIndex];
            return selectEnUnit(enUnits, value);
        } else {
            return unitArray[hkIndex];
        }
    }

    /**
     * 根据en单位选择对应的单复数单位
     * @param enUnits
     * @param value
     * @return
     */
    String selectEnUnit(String enUnits, String value){
        if(!enUnits.contains("*")){
            return enUnits;
        }
        String[] enUnitArr = enUnits.split("\\*");
        String newValue = value.trim().replace(",", "")
                .replace("+", "")
                .replace("-", "")
                .trim();
        try {
            //只有±1才会展示单数
            BigDecimal num = new BigDecimal(newValue).abs();
            return num.compareTo(BigDecimal.ONE) == 0 ? enUnitArr[0] : enUnitArr[1];
        }catch (Exception e){
            log.info("selectEndUnit error value:{},newValue:{}, enUnits:{}, result:{}", value, newValue, enUnits, enUnitArr[2]);
            return enUnitArr[2];
        }
    }

    /**
     * 根据doc语言选择文本，用|分隔
     *
     * @param text
     * @return
     */
    String selectByDocLang(String docLang, String text) {
        if (Strings.isEmpty(text) || !text.contains("|")) {
            return text;
        }
        String[] unitArray = text.split("\\|");
        int zhIndex = 0;
        int hkIndex = Math.min(unitArray.length, 1);
        int enIndex = Math.min(unitArray.length, 2);

        if (CN_ZH.getName().equals(docLang)) {
            return unitArray[zhIndex];
        } else if (US_EN.getName().equals(docLang)) {
            return unitArray[enIndex];
        } else {
            return unitArray[hkIndex];
        }
    }

//    private NativeRowDto change2NativeRowDetail(String docLang, String appLang, String groupId, Integer seq, List<NativeRowDto> originalRowList) {
//        NativeRowDto nativeRowDto = new NativeRowDto();
//        nativeRowDto.setGroupId(groupId);
//        nativeRowDto.setSeq(seq);
//
//        //按同组groupId的seq排序后处理
//        List<NativeRowDto> rowList = originalRowList
//                .stream().sorted(Comparator.comparing(NativeRowDto::getSeq))
//                .collect(Collectors.toList());
//
//        for (NativeRowDto rowDto : rowList) {
//            StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(rowDto.getRowType());
//
//            //根据数据的位置进行拼接
//            if (StatementNativeRowContentTypeEnum.TITLE.getType().equals(rowTypeEnum.getContentType())) {
//                nativeRowDto.setTitle(contractRowContent(docLang, appLang, nativeRowDto.getTitle(), rowDto.getValue(), rowDto, nativeRowDto, rowTypeEnum, false).trim());
//            } else if (StatementNativeRowContentTypeEnum.VALUE.getType().equals(rowTypeEnum.getContentType())) {
//                String rowContent = contractRowContent(docLang, appLang, nativeRowDto.getValue(), rowDto.getValue(), rowDto, nativeRowDto, rowTypeEnum, true);
//                nativeRowDto.setValue(StringUtils.isNotBlank(rowContent) ? rowContent : rowContent.trim());
//            } else {
//                nativeRowDto.setTitle(contractRowContent(docLang, appLang, nativeRowDto.getTitle(), rowDto.getTitle(), rowDto, nativeRowDto, rowTypeEnum, false));
//                nativeRowDto.setValue(contractRowContent(docLang, appLang, nativeRowDto.getValue(), rowDto.getValue(), rowDto, nativeRowDto, rowTypeEnum, true));
//            }
//        }
//
//        nativeRowDto.setValue(nativeRowDto.getValue().trim());
//        //切割英语
//        selectTitleByAppLang(docLang, appLang, nativeRowDto);
//        //selectValueByAppLang(docLang, appLang, nativeRowDto);
//        return nativeRowDto;
//    }

    /**
     * 将一组行转成一行
     *
     * @param docLang
     * @param appLang
     * @param groupId
     * @param originalRowList
     * @param seq
     */
    private NativeRowDto change2NativeRow(String docLang, String appLang, String groupId, Integer seq, List<NativeRowDto> originalRowList) {
        NativeRowDto nativeRowDto = new NativeRowDto();
        nativeRowDto.setGroupId(groupId);
        nativeRowDto.setSeq(seq);

        //按同组groupId的seq排序后处理
        List<NativeRowDto> rowList = originalRowList
                .stream().sorted(Comparator.comparing(NativeRowDto::getSeq))
                .collect(Collectors.toList());

        for (NativeRowDto rowDto : rowList) {
            StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(rowDto.getRowType());
            nativeRowDto.setRowType(rowTypeEnum.getType());
            if (nativeRowDto.getExt() == null) {
                nativeRowDto.setExt(rowDto.getExt());
            }
            //根据数据的位置进行拼接
            if (StatementNativeRowContentTypeEnum.TITLE.getType().equals(rowTypeEnum.getContentType())) {
                nativeRowDto.setTitle(contractRowContent(docLang, appLang, nativeRowDto.getTitle(), rowDto.getValue(), rowDto, nativeRowDto, rowTypeEnum, false).trim());
            } else if (StatementNativeRowContentTypeEnum.VALUE.getType().equals(rowTypeEnum.getContentType())) {
                String rowContent = contractRowContent(docLang, appLang, nativeRowDto.getValue(), rowDto.getValue(), rowDto, nativeRowDto, rowTypeEnum, true);
                nativeRowDto.setValue(StringUtils.isNotBlank(rowContent) ? rowContent : rowContent.trim());
            } else {
                nativeRowDto.setTitle(contractRowContent(docLang, appLang, nativeRowDto.getTitle(), rowDto.getTitle(), rowDto, nativeRowDto, rowTypeEnum, false));
                nativeRowDto.setValue(contractRowContent(docLang, appLang, nativeRowDto.getValue(), rowDto.getValue(), rowDto, nativeRowDto, rowTypeEnum, true));
            }
        }
        //如果解析后的value等于USD,则需要把结果处理成--展示
        String value = nativeRowDto.getValue().trim();
        if(CurrencyEnum.USD.getCurrency().equals(value) &&
                groupId.startsWith(StatementConstants.HTML_PARSE_STOCK_HOLDING_GROUP_ID_PREFIX)){
            value = EMPTY_STRING;
        }
        //股票持仓等值港币市值特殊处理
        if(groupId.equals(StatementConstants.HTML_PARSE_STOCK_HOLDING_EQV_HKD_GROUP)
                && StringUtils.isBlank(value)){
            value = EMPTY_STRING;
        }

        nativeRowDto.setValue(value);

        //切割英语
        selectTitleByAppLang(docLang, appLang, nativeRowDto);
        //selectValueByAppLang(docLang, appLang, nativeRowDto);
        return nativeRowDto;
    }


    /**
     * 最后对标题进行中英文切割
     *
     * @param docLang
     * @param appLang
     * @param nativeRowDto
     */
    private void selectTitleByAppLang(String docLang, String appLang, NativeRowDto nativeRowDto) {
        String title = nativeRowDto.getTitle();
        if (Strings.isEmpty(title)) {
            return;
        }
        StatementHtmlParseConfigDto statementHtmlParseConfigDto = getStatementParseConfig(nativeRowDto);
        boolean isStockName = (statementHtmlParseConfigDto != null && YesOrNoEnum.YES.getValue().equals(statementHtmlParseConfigDto.getStockNameOrNot() + ""));
        boolean isFundName = (statementHtmlParseConfigDto != null && YesOrNoEnum.YES.getValue().equals(statementHtmlParseConfigDto.getFundNameOrNot() + ""));
        boolean isCryptoName = (statementHtmlParseConfigDto != null && YesOrNoEnum.YES.getValue().equals(statementHtmlParseConfigDto.getCryptoNameOrNot() + ""));

        if (isStockName) {
            title = extractStockNameByAppLang(title, appLang);
        } else if (isFundName) {
            title = extractFundNameByAppLang(title, appLang);
        } else if(isCryptoName){
            title = extractCryptoNameByAppLang(title, appLang);
        }else {
            String brTag = "<br>";
            title = judgeCryptoCurrency(title);
            if (title.contains(brTag)) {
                int blankIndex = title.lastIndexOf(brTag);
                if (US_EN.getNameOfBank().endsWith(appLang)) {
                    //抽英文
                    title = title.substring(0, blankIndex);
                } else {
                    //抽中文
                    title = title.substring(blankIndex + brTag.length());
                }

            }
            //如果抽取完对应的语言数据后，还存在换行符，则把换行符替换为空格
            if(title.contains(brTag)){
                title = title.replace(brTag, "");
            }

            Matcher matcher = ENGLISH_AND_CHINESE_SPLIT_PATTERN.matcher(title);
            if (matcher.find()) {
                if (US_EN.getNameOfBank().endsWith(appLang)) {
                    //抽英文
                    title = matcher.group("englishPart");
                } else {
                    //抽中文
                    title = matcher.group("chinesePart");
                }
            }
        }
        title = title.trim();
        nativeRowDto.setTitle(title);
    }

    /**
     * 判断是否是crypto拼接currency的特殊情况
     * @param title
     * @return
     */
    private String judgeCryptoCurrency(String title){
        if(title.trim().startsWith(StatementConstants.HTML_PARSE_CRYPTO_ORDER_CURRENCY_PREFIX)){
            title = title.trim();
            int currencyLength = StatementConstants.HTML_PARSE_CRYPTO_ORDER_CURRENCY_LENGTH;
            if(title.length() > currencyLength){
                title = title.substring(currencyLength);
                log.info("judgeCryptoCurrency result:{}", title);
                return title;
            }
        }
        return title;
    }
    /**
     * @param text
     * @param appLang
     * @return
     */
    private String extractStockNameByAppLang(String text, String appLang) {
        String brTag = "<br>";
        String stockInfo = text;
        String actionDesc = "";
        if (text.contains(brTag)) {
            int tagIndex = text.indexOf(brTag);
            stockInfo = text.substring(tagIndex + brTag.length());
            actionDesc = text.substring(0, tagIndex).trim();
        }

        if (actionDesc.contains(" ")) {
            int blankIndex = actionDesc.lastIndexOf(" ");
            if (US_EN.getNameOfBank().endsWith(appLang)) {
                //抽英文
                actionDesc = actionDesc.substring(0, blankIndex);
            } else {
                //抽中文
                actionDesc = actionDesc.substring(blankIndex + 1);
            }
        }

        log.info("extractStockNameByAppLang stockInfo:{}", stockInfo);

        //兼容股票名称取不到的情况（实际不会出现，英文简称或全称肯定是有的）
        String stockCode = "";
        if(stockInfo.indexOf(" ") > 0){
            stockCode = stockInfo.substring(0, stockInfo.indexOf(" ")).trim();
        }else {
            stockCode = stockInfo.trim();
        }

        stockCode = (stockCode + " " + stkInfoAgent.getStockNameAbbrDefaultEn(MarketCodeEnum.US.getValue(), stockCode, appLang));
//        stockCode = (stockCode + " ");
        actionDesc = StringUtils.isNotBlank(actionDesc) ? actionDesc + " - " : actionDesc + " ";

        return actionDesc + stockCode;
    }


    /**
     * @param text
     * @param appLang
     * @return
     */
    private String extractFundNameByAppLang(String text, String appLang) {
        String brTag = "<br>";
        if (text.contains(brTag)) {
            int blankIndex = text.lastIndexOf(brTag);
            if (US_EN.getNameOfBank().endsWith(appLang)) {
                //抽英文
                text = text.substring(blankIndex + brTag.length());
            } else {
                //抽中文
                text = text.substring(0, blankIndex);
            }
        }
        if(text.contains(StatementConstants.HTML_NBSP_FLAG)){
            text = text.replace(StatementConstants.HTML_NBSP_FLAG, " ");
        }
        return text.trim();
    }

    /**
     * 获取crypto名称
     * @param text
     * @param appLang
     * @return
     */
    private String extractCryptoNameByAppLang(String text, String appLang) {
        String brTag = "<br>";
        String cryptoInfo = text;
        String actionDesc = "";
        if (text.contains(brTag)) {
            int tagIndex = text.indexOf(brTag);
            cryptoInfo = text.substring(tagIndex + brTag.length());
            actionDesc = text.substring(0, tagIndex).trim();
        }

        if (actionDesc.contains(" ")) {
            int blankIndex = actionDesc.lastIndexOf(" ");
            if (US_EN.getNameOfBank().endsWith(appLang)) {
                //抽英文
                actionDesc = actionDesc.substring(0, blankIndex);
            } else {
                //抽中文
                actionDesc = actionDesc.substring(blankIndex + 1);
            }
        }

        String assetType = "";
        if(cryptoInfo.indexOf(" ") > 0){
            assetType = cryptoInfo.substring(0, cryptoInfo.indexOf(" ")).trim();
        }else {
            assetType = cryptoInfo.trim();
        }

        I18nSupportEnum i18nSupportEnum = judgeByNameOfBank(appLang);
        assetType = (assetType + " " + cryptoTradeCoreManager.getCryptoName(assetType, i18nSupportEnum));
//        assetType = (assetType + " ");
        actionDesc = StringUtils.isNotBlank(actionDesc) ? actionDesc + " - " : actionDesc + " ";

        return actionDesc + assetType;
    }

    /**
     * 最后对value进行中英文切割
     *
     * @param docLang
     * @param appLang
     * @param nativeRowDto
     */
    private void selectValueByAppLang(String docLang, String appLang, NativeRowDto nativeRowDto) {
        String value = nativeRowDto.getValue();
        if (value == null) {
            return;
        }
        if (US_EN.getNameOfBank().endsWith(appLang)) {
            //抽英文
        } else {

            //抽中文
        }
        value = value.trim();
        nativeRowDto.setValue(value);
    }


    /**
     * 把ext扩展转成实体
     *
     * @param nativeRowDto
     * @return
     */
    private StatementHtmlParseConfigDto getStatementParseConfig(NativeRowDto nativeRowDto) {
        String ext = nativeRowDto.getExt();
        if (!Strings.isEmpty(ext)) {
            return JSONUtil.toBean(ext, StatementHtmlParseConfigDto.class);
        }
        return null;
    }

    /**
     * 根据位置拼接内容
     *
     * @param originalContent
     * @param newContent
     */
    private String contractRowContent(String docLang, String appLang, String originalContent, String newContent, NativeRowDto rowDto
            , NativeRowDto resultRowDto, StatementNativeRowTypeEnum rowTypeEnum, boolean isValue) {

        if (originalContent == null) {
            originalContent = "";
        }
        String value = originalContent + " ";
        StatementHtmlParseConfigDto parseExtConfig = getStatementParseConfig(rowDto);
        //判断读取的文本是否是属于单位
        boolean isUnit = parseExtConfig != null && YesOrNoEnum.YES.getValue().equals(parseExtConfig.getUnitOrNot() + "");
        //如果读取的文本为单位&&旧值为空时，忽略拼接以及后续国际化逻辑
        if (StringUtils.isNotBlank(newContent) && StringUtils.isBlank(value) && isUnit) {
            return value;
        }

        //分隔符
        String splitter = rowDto.getSplitter();
        if (!Strings.isEmpty(splitter)) {
            newContent = processSplitter(docLang, appLang, splitter, newContent, rowDto, resultRowDto).trim();
        }

        //值类型的
        if (isValue) {
            StatementHtmlParsedDataTypeEnum statementHtmlParsedDataTypeEnum = StatementHtmlParsedDataTypeEnum.contentMatch(docLang, newContent);
            if (statementHtmlParsedDataTypeEnum != null) {
                String key = statementHtmlParsedDataTypeEnum.getKey(docLang);
                if (HOLDING_CHANGE_STOCK_IN.equals(statementHtmlParsedDataTypeEnum)) {
                    newContent = newContent.replace(key, "+");
                }
                if (HOLDING_CHANGE_STOCK_OUT.equals(statementHtmlParsedDataTypeEnum)) {
                    newContent = newContent.replace(key, "-");
                }
            }
        }

        //特殊处理收益及费用摘要的Bonus Others类型数据
        // 检查groupId不为空且rowDto的seq不为null
        if (StringUtils.isNotEmpty(resultRowDto.getGroupId()) && rowDto.getSeq() != null) {

            // 检查groupId是否以特定前缀开头且seq为2
            if (resultRowDto.getGroupId().startsWith(StatementConstants.HTML_PARSE_STOCK_DIVIDEND_ORDER_GROUP_ID_PREFIX)
                    && rowDto.getSeq() == 2) {

                // 检查newContent不等于--，并且value是否以Bonus Others业务类型描述开头
                if (!StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER.equals(newContent)
                        && value.startsWith(StatementBusinessTypeEnum.SCAS.getDescEn())) {

                    String brTag = "<br>";
                    int tagIndex = value.indexOf(brTag);

                    // 截取value到第一个br标签为止的内容，并去除首尾的空格(截取掉源股票代码信息，留下业务类型数据)
                    value = value.substring(0, tagIndex + brTag.length()).trim();

                    // 截取newContent到第一个空格字符为止的内容（截取目标股票代码）
                    newContent = newContent.substring(0, newContent.indexOf(" "));
                }
            }
        }

        if (StatementNativeRowContentLocationEnum.PREFIX.getType().equals(rowTypeEnum.getContentLocation())) {
            value = newContent + value;
        }

        if (StatementNativeRowContentLocationEnum.SUFFIX.getType().equals(rowTypeEnum.getContentLocation())) {
            value = value + newContent;
        }


        //处理单位
        String unit = rowDto.getValueUnit();
        if (StringUtils.isNotBlank(value) && isValue && !Strings.isEmpty(unit) && !value.contains(EMPTY_STRING)) {
            value = value.trim();
            if (unit.startsWith(UNIT_PREFIX_TAG)) {
                unit = unit.replace(UNIT_PREFIX_TAG, "");
                unit = selectByAppLang(appLang, unit, value);
                value = unit + " " + value;
            } else {
                unit = selectByAppLang(appLang, unit, value);
                value = value + " " + unit;
            }
        }

        //兼容native页面的--展示
        if (value.contains(EMPTY_STRING)) {
            value = value.replace(EMPTY_STRING, "");
            value = value.trim();
        }

        return value.replaceAll("\\s+", " ");
    }

    /**
     * 处理分隔符
     *
     * @param splitter
     * @param newContent
     * @param rowDto
     * @return
     */
    private String processSplitter(String docLang, String appLang, String splitter, String newContent, NativeRowDto rowDto, NativeRowDto resultRowDto) {
        String originalSplitter = splitter;
        if (Strings.isEmpty(splitter)) {
            return "";
        }
        StatementNativeRowTypeEnum rowTypeEnum = StatementNativeRowTypeEnum.judgeRowType(rowDto.getRowType());
        if (originalSplitter.startsWith(SPLITTER_FORWARD_DIRECTION)) {
            //取分隔符前边的部分
            splitter = originalSplitter.substring(SPLITTER_FORWARD_DIRECTION.length());
            splitter = selectByDocLang(docLang, splitter);
            if (newContent.contains(splitter)) {
                int index = newContent.indexOf(splitter);
                return newContent.substring(0, index);
            } else {
                return newContent;
            }
        } else if (originalSplitter.startsWith(SPLITTER_BACKWARD_DIRECTION)) {
            //取分隔符后面的部分
            splitter = originalSplitter.substring(SPLITTER_FORWARD_DIRECTION.length());
            splitter = selectByDocLang(docLang, splitter);
            if (newContent.contains(splitter)) {
                int index = newContent.indexOf(splitter);
                resultRowDto.setTitle(splitter);
                return newContent.substring(index + splitter.length());
            } else {
                return "";
            }
        } else {
            if (newContent.contains(splitter)) {
                String[] contentArray = newContent.split(splitter);
                if (contentArray.length == 2) {
                    if (US_EN.getNameOfBank().equals(appLang)) {
                        return contentArray[0];
                    } else {
                        return contentArray[1];
                    }
                }
            } else {
                return newContent;
            }
        }
        return newContent;
    }


    /**
     * 创建并初始化TdMonthlyStatementHtmlParsedData实例
     *
     * @param appLang
     * @param statementInfo
     * @param userStatement
     * @return
     */
    private TdMonthlyStatementHtmlParsedData generateStatementHtmlParsedDataDto(String appLang
            , TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement userStatement) {
        TdMonthlyStatementHtmlParsedData statementHtmlParsedData = new TdMonthlyStatementHtmlParsedData();
        statementHtmlParsedData.setId(IdWorker.idworker.nextIdStr());
        statementHtmlParsedData.setStatementInfoId(statementInfo.getBusinessId());
        statementHtmlParsedData.setStatementId(userStatement.getBusinessId());
        statementHtmlParsedData.setPeriod(statementInfo.getPeriod());
        statementHtmlParsedData.setBusiness(statementInfo.getBusinessType());
        statementHtmlParsedData.setUserId(userStatement.getBankUserId());
        statementHtmlParsedData.setClientId(userStatement.getClientId());
        statementHtmlParsedData.setAppLang(appLang);
        return statementHtmlParsedData;
    }

    /**
     * 根据语言选择模块标题
     *
     * @param appLang
     * @param statementModuleTypeEnum
     * @return
     */
    private String selectModuleTitle(String appLang, StatementModuleTypeEnum statementModuleTypeEnum) {
        if (US_EN.getNameOfBank().equals(appLang)) {
            return statementModuleTypeEnum.getModuleNameEn();
        }
        if (CN_ZH.getNameOfBank().equals(appLang)) {
            return statementModuleTypeEnum.getModuleNameZh();
        }

        if (CN_HK.getNameOfBank().equals(appLang)) {
            return statementModuleTypeEnum.getModuleNameHk();
        }
        return statementModuleTypeEnum.getModuleNameHk();
    }


    /**
     * 根据语言选择业务类型
     *
     * @param appLang
     * @param statementSubBusinessTypeEnum
     * @return
     */
    private String selectSubBusinessTitle(String appLang, StatementSubBusinessTypeEnum statementSubBusinessTypeEnum) {
        if (US_EN.getNameOfBank().equals(appLang)) {
            return statementSubBusinessTypeEnum.getTitleEn();
        }
        if (CN_ZH.getNameOfBank().equals(appLang)) {
            return statementSubBusinessTypeEnum.getTitleZh();
        }

        if (CN_HK.getNameOfBank().equals(appLang)) {
            return statementSubBusinessTypeEnum.getTitleHk();
        }
        return statementSubBusinessTypeEnum.getTitleHk();
    }


    /**
     * 将解析数据保存到mongodb
     * @param statementHtmlParsedDataList
     * @return
     */
    private int saveHtmlParsedData2Mongodb(List<TdMonthlyStatementHtmlParsedData> statementHtmlParsedDataList) {
        statementHtmlParsedDataList.forEach(tdMonthlyStatementHtmlParsedData -> {
            tdMonthlyStatementHtmlParsedData.setRecordDate(new Date());
            tdMonthlyStatementParsedDataRepository.save(tdMonthlyStatementHtmlParsedData);
        });
        return statementHtmlParsedDataList.size();
    }
}
