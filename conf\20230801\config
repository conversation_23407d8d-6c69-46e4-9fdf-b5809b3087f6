# 删除apollo配置
---core.sys-req-system
---core.sys-req-channel
---core.app-teller-id
---core.product-code
---core.currency-type
---bank.corefront.url

# 新增
# 历史基金月结单截止月份(YYYYMM)
history.fund.month.statement.cut.off.month = 202308

#house户列表
monthly.statement.house.account.list = HOUSE00001
#sbs tradedata gateway
sbs.tradedata.gateway.url = 待运维补充


# 修改配置
rabbitmq.consumer.userMonthlyStatementDataPrepare.concurrency = 5
rabbitmq.consumer.userMonthlyStatementDataPrepare.maxConcurrency = 5
rabbitmq.consumer.userMonthlyStatementDataPrepare.prefetch = 5

monthly.statement.doc.hkTempKey = zabank_invest_monthly_hk_20230801_v3.html
monthly.statement.doc.zhTempKey = zabank_invest_monthly_zh_20230801_v3.html