package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.counterfront.module.entity.order.resp.QueryOrderIdByContractIdListResp;
import group.za.bank.statement.service.remote.feign.CounterOrderFeign;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class CounterFrontRemoteServiceImplTest {
    @Mock
    CounterOrderFeign counterOrderFeign;
    @Mock
    Logger log;
    @InjectMocks
    CounterFrontRemoteServiceImpl counterFrontRemoteServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testQueryOrderIdByContractId() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        when(counterOrderFeign.queryOrderIdByContractId(any())).thenReturn(resp);
        QueryOrderIdByContractIdListResp result = counterFrontRemoteServiceImpl.queryOrderIdByContractId("contractId");
        Assert.assertEquals(null, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme