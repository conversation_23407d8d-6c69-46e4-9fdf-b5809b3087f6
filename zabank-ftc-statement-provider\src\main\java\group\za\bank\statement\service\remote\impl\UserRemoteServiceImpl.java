package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.constants.enums.AddressType;
import group.za.bank.invest.account.entity.dto.HabitancyInfoDto;
import group.za.bank.invest.account.entity.dto.QueryAccountInfoDto;
import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.account.entity.req.*;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.account.entity.resp.QueryBankUserInfoResp;
import group.za.bank.invest.account.feign.UserFeignService;
import group.za.bank.invest.basecommon.entity.resp.BaseListResp;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.ResponseValidator;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.USER_INVEST_ACCOUNT_NOT_FOUND;

/**
 * <AUTHOR>
 * @Date 2021/7/17 17:23
 * @Description 用户远程调用
 * @Version v1.0
 */
@Service
@Slf4j
public class UserRemoteServiceImpl implements UserRemoteService {

    @Resource
    private UserFeignService userFeignService;


    @Override
    public List<UserInvestAccountInfo> queryUserAllInvestAccountInfo(String bankUserId) {
        ListUserInvestAccountInfoReq listUserInvestAccountInfoReq = new ListUserInvestAccountInfoReq();
        List<String> bankUserIdList = new ArrayList<>(1);
        bankUserIdList.add(bankUserId);
        listUserInvestAccountInfoReq.setBankUserIdList(bankUserIdList);
        ResponseData<BaseListResp<UserInvestAccountInfo>> responseData = userFeignService.listUserInvestAccountInfo(listUserInvestAccountInfoReq);
        if (log.isDebugEnabled()) {
            log.debug("RPC queryUserAllInvestAccountInfo. req:{},resp:{}", listUserInvestAccountInfoReq, responseData);
        }
        if (!responseData.judgeSuccess() || responseData.getValue() == null) {
            log.warn("查询用户账户信息,bankUserId：{}，返回结果{}", bankUserId, JSON.toJSONString(responseData));
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        BaseListResp<UserInvestAccountInfo> userInvestAccountInfoBaseListResp = responseData.getValue();
        if (CollectionUtils.isEmpty(userInvestAccountInfoBaseListResp.getList())) {
            throw new BusinessException(USER_INVEST_ACCOUNT_NOT_FOUND);
        }

        return userInvestAccountInfoBaseListResp.getList();
    }


    @Override
    public UserInvestAccountInfo queryUserInvestAccountInfo(String bankUserId, AccountTypeEnum accountType) {
        ResponseData<UserInvestAccountInfo> responseData = null;
        try {
            QueryUserInvestAccountInfoReq req = new QueryUserInvestAccountInfoReq();
            req.setAccountType(accountType.getValue());
            req.setBankUserId(bankUserId);
            responseData = userFeignService.queryUserInvestAccountInfo(req);
            ResponseValidator.checkRemoteResponseValue(responseData);
            log.info("queryUserInvestAccountInfo, 入参：{}， 出参:{}", JSON.toJSONString(req), JSON.toJSONString(responseData));
        } catch (Exception e) {
            log.error("queryUserInvestAccountInfo error, bankUserId：{}, accountType:{} , 出参:{}", JSON.toJSONString(accountType), accountType, JSON.toJSONString(responseData), e);
            throw e;
        }
        return responseData.getValue();
    }

    @Override
    public UserInvestAccountInfo queryUserInvestAccountInfo(GetUserInvestAccountInfoReq req) {
        ResponseData<UserInvestAccountInfo> responseData = null;
        try {
            responseData = userFeignService.getUserInvestAccountInfo(req);
            ResponseValidator.checkRemoteResponseValue(responseData);
            log.info("queryUserInvestAccountInfo, 入参：{}， 出参:{}", JSON.toJSONString(req), JSON.toJSONString(responseData));
        } catch (Exception e) {
            log.error("queryUserInvestAccountInfo error, req：{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(responseData), e);
            throw e;
        }
        return responseData.getValue();
    }

    @Override
    public String queryUserLanguage(String bankUserId) {
        ResponseData<String> resp = userFeignService.queryUserLanguage(bankUserId);
        if (log.isDebugEnabled()) {
            log.debug("RPC queryUserLanguage. req:{},resp:{}", bankUserId, resp);
        }
        if (resp == null) {
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        if (!resp.judgeSuccess()) {
            log.warn("查询用户语言,bankUserId：{}，返回结果{}", bankUserId, JSON.toJSONString(resp));
            throw new BusinessException(resp.getCode(), resp.getMsg());
        }

        return resp.getValue();
    }


    @Override
    public HabitancyInfoDto queryHabitancyInfo(String bankUserId) {
        ResponseData<HabitancyInfoDto> resp = userFeignService.queryHabitancyInfo(bankUserId);
        if (log.isDebugEnabled()) {
            log.debug("RPC generatePdf. req:{},resp:{}", bankUserId, resp);
        }
        if (resp == null) {
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        if (!resp.judgeSuccess()) {
            log.warn("查询用户基本信息,bankUserId：{}，返回结果{}", bankUserId, JSON.toJSONString(resp));
            throw new BusinessException(resp.getCode(), resp.getMsg());
        }

        return resp.getValue();


    }


    /**
     * 查询用户基本信息
     *
     * @param bankUserId
     * @return
     */
    @Override
    public AddressInfoResp queryContactAddressInfo(String bankUserId) {
        ResponseData<AddressInfoResp> resp = null;
        try {
            QueryAddressInfoReq queryAddressInfoReq = new QueryAddressInfoReq();
            queryAddressInfoReq.setBankUserId(bankUserId);
            queryAddressInfoReq.setAddressType(AddressType.CONTACT_ADDR.getCode());
            resp = userFeignService.queryAddressInfo(queryAddressInfoReq);
            ResponseValidator.checkRemoteResponseValue(resp);
            log.info("queryUserInvestAccountInfo, 入参：{}， 出参:{}", JSON.toJSONString(queryAddressInfoReq), JSON.toJSONString(resp));
        } catch (Exception e) {
            log.error("queryUserInvestAccountInfo error, bankUserId：{}， 出参:{}", bankUserId, JSON.toJSONString(resp));
            throw e;
        }
        return resp.getValue();
    }

    @Override
    public Map<String, QueryAccountInfoDto> queryByBankUserIdList(List<String> userIdList) {
        QueryByBankUserIdListReq req = new QueryByBankUserIdListReq();
        req.setBankUserIdList(userIdList);
        log.info("RPC queryByBankUserIdList req={}", JSON.toJSONString(req));
        ResponseData<Map<String, QueryAccountInfoDto>> responseData = userFeignService.queryByBankUserIdList(req);
        if (responseData == null || !responseData.judgeSuccess() || responseData.getValue() == null) {
            log.warn("RPC queryByBankUserIdList FAIL");
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return responseData.getValue();
    }


    /**
     * 判断用户是否是mcv
     *
     * @param bankUserId
     * @return
     */
    @Override
    public boolean isUserMcv(String bankUserId) {
        ResponseData<Boolean> resp = userFeignService.verifyMcv(bankUserId);
        log.debug("RPC isUserMcv. req:{},resp:{}", bankUserId, resp);
        if (resp == null) {
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        if (!resp.judgeSuccess()) {
            log.warn("查询用户是否mcv错误,bankUserId：{}，返回结果{}"
                    , bankUserId, JSON.toJSONString(resp));
            throw new BusinessException(resp.getCode(), resp.getMsg());
        }
        return resp.getValue();

    }

    /**
     * 查询用户投资账号列表信息
     *
     * @param bankUserId
     * @return
     */
    @Override
    public List<UserInvestAccountInfo> queryUserInvestCloseAccountInfo(String bankUserId) {
        QueryUserInvestAccountInfoReq req = new QueryUserInvestAccountInfoReq();
        req.setBankUserId(bankUserId);
        req.setAccountType(AccountTypeEnum.FUND.getValue());
        log.info("RPC queryByBankUserIdList req={}", JSON.toJSONString(req));
        ResponseData<List<UserInvestAccountInfo>> responseData = userFeignService.queryUserInvestCloseAccountInfo(req);
        if (responseData == null || !responseData.judgeSuccess() || responseData.getValue() == null) {
            log.warn("RPC queryUserInvestCloseAccountInfo FAIL");
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return responseData.getValue();
    }


    @Override
    public Boolean judgeBankAccountClosed(String bankUserId) {
        ResponseData<Boolean> responseData = null;
        try{
            JudgeBankAccountClosedReq req = new JudgeBankAccountClosedReq();
            req.setBankUserId(bankUserId);
            responseData = userFeignService.judgeBankAccountClosed(req);
            log.info("judgeBankAccountClosed 入参:{}, 出参:{}", bankUserId, JSON.toJSONString(responseData));
            ResponseValidator.checkRemoteResponseValue(responseData);
            return responseData.getValue();
        }catch (Exception e){
            log.error("judgeBankAccountClosed error, 入参:{}, 出参:{}", bankUserId, JSON.toJSONString(responseData), e);
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
    }

    @Override
    public Map<String, QueryBankUserInfoResp> queryBankUserInfo(String bankUserId) {
        QueryBankUserInfoReq req = new QueryBankUserInfoReq();
        List<String> bankUserIdList = new ArrayList<>();
        bankUserIdList.add(bankUserId);
        req.setBankUserIdList(bankUserIdList);
        return this.queryBankUserInfoList(req);
    }

    @Override
    public Map<String, QueryBankUserInfoResp> queryBankUserInfoList(QueryBankUserInfoReq req) {
        ResponseData<Map<String, QueryBankUserInfoResp>> responseData = null;
        try {
            responseData = userFeignService.queryBankUserInfo(req);
            log.info("queryBankUserInfo 入参:{}, 出参:{}", JSON.toJSONString(req), JSON.toJSONString(responseData));
            ResponseValidator.checkRemoteResponseValue(responseData);
            return responseData.getValue();
        } catch (Exception e) {
            log.error("queryBankUserInfo error, 入参:{}, 出参:{}", JSON.toJSONString(req), JSON.toJSONString(responseData), e);
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
    }
}
