package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.entity.dto.MonthStatementRebuildDTO;
import group.za.bank.statement.service.MonthlyStatementService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.*;

public class MonthlyStatementRebuildJobTest {
    @Mock
    SystemConfig systemConfig;
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    MonthlyStatementRebuildJob monthlyStatementRebuildJob;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        mockedJsonUtils = mockStatic(JsonUtils.class);

        MonthStatementRebuildDTO monthStatementRebuildDTO = new MonthStatementRebuildDTO();
        monthStatementRebuildDTO.setPeriod("20190701");
        monthStatementRebuildDTO.setClientIdList("1,2");
        when(JsonUtils.toJavaObject(any(), eq(MonthStatementRebuildDTO.class))).thenReturn(monthStatementRebuildDTO);
    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
    }

    @Test
    public void testExecute() throws Exception {


        ReturnT<String> result = monthlyStatementRebuildJob.execute("param");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme