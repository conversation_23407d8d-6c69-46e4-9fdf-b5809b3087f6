
CREATE TABLE `za_bank_invest_statement`.`td_fund_monthly_order_tmp` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
 `order_no` varchar(20) NOT NULL COMMENT '订单id',
 `bank_user_id` varchar(20) NOT NULL COMMENT '银行用户id',
 `bank_account_id` varchar(20) NOT NULL COMMENT '银行基金账户',
 `broker_account_id` varchar(9) NOT NULL COMMENT '上手投资账户（AAIM投资账户）',
 `isin_no` varchar(16) NOT NULL COMMENT 'isin号',
 `currency` varchar(3) NOT NULL COMMENT '结算货币类型',
 `product_id` varchar(20) NOT NULL COMMENT '产品Id',
 `product_name` varchar(255) DEFAULT '' COMMENT '产品名称',
 `broker_order_no` varchar(64) DEFAULT NULL COMMENT '上手订单号',
 `business_type` varchar(8) NOT NULL COMMENT '业务类型(10-基金申购，11-基金赎回，12-基金分红，13-基金转换, 20-存 21-取',
 `apply_amt` decimal(19,6) NOT NULL DEFAULT '0.000000' COMMENT '申请金额',
 `apply_share` decimal(19,6) NOT NULL DEFAULT '0.000000' COMMENT '申请份额',
 `confirm_amt` decimal(19,6) DEFAULT '0.000000' COMMENT '确认金额',
 `confirm_share` decimal(19,6) DEFAULT '0.000000' COMMENT '确认份额',
 `confirm_net_value` decimal(19,6) DEFAULT '0.000000' COMMENT '成交净值',
 `confirm_net_value_date` date DEFAULT NULL COMMENT '确认净值日期',
 `fee_rate` decimal(19,6) NOT NULL DEFAULT '0.000000' COMMENT '下单时费率',
 `fee` decimal(19,6) NOT NULL DEFAULT '0.000000' COMMENT '下单时预估交易费用',
 `final_fee` decimal(19,6) DEFAULT '0.000000' COMMENT '最终交易费用',
 `trade_date` date NOT NULL COMMENT '订单所属交易日',
 `confirm_date` date DEFAULT NULL COMMENT '申购/赎回确认日期',
 `delivery_date` date DEFAULT NULL COMMENT '申购/赎回交收日期',
 `confirm_date_needed` int(20) DEFAULT NULL COMMENT '从发起交易到确认需要几个交易日',
 `delivery_date_needed` int(20) DEFAULT NULL COMMENT '从发起交易到交收需要几个交易日',
 `real_trade_date` date DEFAULT NULL COMMENT '实际交易日',
 `real_confirm_time` datetime DEFAULT NULL COMMENT '实际上手申购/赎回确认时间',
 `real_delivery_time` datetime DEFAULT NULL COMMENT '实际交收时间',
 `allfund_pre_delivery_time` datetime DEFAULT NULL COMMENT 'AllFund预计交收时间',
 `status` tinyint(4) NOT NULL COMMENT '订单状态：0-初始化，1-待支付，2-已支付，3-支付失败 10-下单处理中，11-已提交，20-订单修改中，30-订单撤销中，31-已撤销，40-下单失败，41-上手确认失败，50-已确认，60-待交收，61-已交收',
 `broker_clear_status` varchar(10) DEFAULT NULL COMMENT '自研柜台状态:NEW 下单成功 未到CUT_OFF日 可改单或取消,WA 下单成功 余额不足 等待处理 PRO 中间状态 已过CUT_OFF日 不能改单或取消 CAN 取消订单 ACK 已提交上手 REJ 已拒绝 FEX 已完成购买 SET 已交收',
 `activity_flag` varchar(32) DEFAULT NULL COMMENT '活动信息：第0位=1返现券， 第1位=1Coin抵扣',
 `apply_fail_reason` varchar(100) DEFAULT '' COMMENT '下单失败原因',
 `broker_order_status` varchar(8) DEFAULT '' COMMENT '上手订单状态',
 `channel` varchar(8) DEFAULT 'app' COMMENT '订单下单类型 app:APP下单 manual:手工下单',
 `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
 `version` int(10) NOT NULL DEFAULT '1' COMMENT '乐观锁版本号',
 `remark` varchar(100) DEFAULT '' COMMENT '备注',
 `creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
 `gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
 `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE KEY `uk_order_no` (`order_no`) USING BTREE,
 KEY `idx_bank_user_id` (`bank_user_id`) USING BTREE,
 KEY `idx_bank_account_id` (`bank_account_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='月结单生成订单临时表';


CREATE TABLE `za_bank_invest_statement`.`td_fund_monthly_statement` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`business_id` varchar(64) NOT NULL COMMENT '业务逻辑id',
`business_type` varchar(32) NOT NULL COMMENT '业务类型',
`period` char(6) NOT NULL COMMENT '月结单期数:''yyyyMM''',
`bank_user_id` varchar(20) NOT NULL COMMENT '银行用户id',
`client_id` varchar(20) DEFAULT NULL COMMENT '投资账户id',
`doc_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '月结单文档状态:0:-初始化,1-待生成,2-已生成,3-等待通知文件生成 执行顺序：0312',
`pub_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '月结单发送状态:1-待发送,2-已发送',
`doc_lang` varchar(6) DEFAULT NULL COMMENT '文档语言',
`html_url` varchar(512) DEFAULT NULL comment 'html文件地址',
`temp_key` varchar(128) DEFAULT NULL COMMENT '模板key',
`doc_url` varchar(512) DEFAULT NULL COMMENT '文档地址',
`pub_time` datetime DEFAULT NULL COMMENT '发送成功时间',
`doc_time` datetime DEFAULT NULL COMMENT '文档生成时间',
`record_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '结单记录生成时间',
`remark` varchar(100) DEFAULT '' COMMENT '备注',
`submit_time` datetime DEFAULT NULL comment '提交时间',
`creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
`gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_business_id` (`business_id`),
UNIQUE KEY `uk_client_business_period_doc` (`client_id`,`business_type`,`period`,`doc_lang`) USING BTREE
) ENGINE=InnoDB  COMMENT='基金月结单明细';

CREATE TABLE `za_bank_invest_statement`.`td_fund_monthly_statement_info` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`business_id` varchar(64) NOT NULL COMMENT '业务逻辑id',
`period` char(6) NOT NULL COMMENT '月结单期数:''yyyyMM''',
`business_type` varchar(32) NOT NULL COMMENT '业务类型',
`init_status` tinyint(4) DEFAULT '1' COMMENT '初始化状态:1-处理中,2-已完成',
`pub_confirm_status` tinyint(4) DEFAULT '0' COMMENT '月结单发送确认状态:0-未确认，1-已确认',
`status` tinyint(4) DEFAULT '1' COMMENT '处理状态:1-处理中,2-已完成',
`confirm_operator_id` varchar(64) DEFAULT '' COMMENT '确认发送人id',
`confirm_operator_name` varchar(128) DEFAULT '' COMMENT '确认发送人名字',
`hk_temp_key` varchar(128) DEFAULT NULL COMMENT '繁体模板key',
`zh_temp_key` varchar(128) DEFAULT NULL COMMENT '中文模板key',
`en_temp_key` varchar(128) DEFAULT NULL COMMENT '英文模板',
`record_time` datetime DEFAULT NULL COMMENT '结单记录生成时间',
`total_number` int(11) DEFAULT NULL COMMENT '结单数量',
`total_client_number` int(11) DEFAULT NULL COMMENT '当期账户数量',
`finished_number` int(11) DEFAULT NULL COMMENT '完成数量',
`usd_hkd_rate` decimal(14,8) DEFAULT NULL COMMENT 'usd换hkd汇率',
`cny_hkd_rate` decimal(14,8) DEFAULT NULL COMMENT 'cny换hkd汇率',
`rate_effect_time` datetime DEFAULT NULL COMMENT '汇率生效时间',
`remark` varchar(100) DEFAULT '' COMMENT '备注',
`creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
`gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_business_id` (`business_id`),
UNIQUE KEY `uk_period_business_type` (`period`,`business_type`) USING BTREE
) ENGINE=InnoDB  COMMENT='基金月结单总记录';


CREATE TABLE `za_bank_invest_statement`.`td_monthly_statement_data` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
 `business_id` varchar(64) NOT NULL COMMENT '记录id',
 `statement_id` varchar(64) NOT NULL COMMENT '结单id',
 `business_type` varchar(32) NOT NULL COMMENT '业务类型:fund-基金,stock-股票',
 `account_id` varchar(32) DEFAULT NULL COMMENT '账户id',
 `data_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '结单数据准备状态:0-未完成,1-无数据,2-处理完成',
 `record_time` datetime DEFAULT NULL COMMENT '记录时间',
 `remark` varchar(100) DEFAULT '' COMMENT '备注',
 `creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
 `gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
 `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_business_id` (`business_id`),
 UNIQUE KEY `uk_statement_business` (`statement_id`,`business_type`) USING BTREE
) ENGINE=InnoDB  COMMENT='月结单明细数据';

CREATE TABLE `za_bank_invest_statement`.`td_statement_file_record` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`statement_type` int(4) DEFAULT '1' COMMENT '1-日结单，2-月结单',
`statement_date` date DEFAULT NULL COMMENT '结单日期:月结单-yyyyMM，日结单-yyyyMMdd',
`data_status` int(4) DEFAULT '0' COMMENT '文件解析状态:0-未开始,1-处理中,2-成功，3-失败',
`creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
`gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_sfr_type_period` (`statement_type`,`statement_date`)
) ENGINE=InnoDB COMMENT='结单文件记录表';

CREATE TABLE `za_bank_invest_statement`.`td_stock_statement_data` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`business_id` varchar(64) DEFAULT NULL COMMENT '业务逻辑id',
`statement_date` varchar(8) NOT NULL COMMENT '结单日期',
`market_code` varchar(8) DEFAULT NULL COMMENT '市场代码',
`trade_date` date DEFAULT NULL COMMENT '交易日',
`statement_type` int(4) NOT NULL DEFAULT '1' COMMENT '结单类型:1-日结单，2-月结单',
`acc_no` varchar(20) DEFAULT NULL COMMENT 'ttl柜台账户',
`format_type` varchar(20) DEFAULT NULL COMMENT '1-港股成交单,2-美股成交单，3-交易变动明细，4证券资产摘要，5-现金摘要',
`param1` varchar(128) DEFAULT NULL comment 'param1',
`param2` varchar(128) DEFAULT NULL comment 'param2',
`param3` varchar(100) DEFAULT NULL comment 'param3',
`param4` varchar(128) DEFAULT NULL comment 'param4',
`param5` varchar(128) DEFAULT NULL comment 'param5',
`param6` varchar(128) DEFAULT NULL comment 'param6',
`param7` varchar(256) DEFAULT NULL comment 'param7',
`param8` varchar(256) DEFAULT NULL comment 'param8',
`param9` varchar(128) DEFAULT NULL comment 'param9',
`param10` varchar(256) DEFAULT NULL comment 'param10',
`param11` varchar(128) DEFAULT NULL comment 'param11',
`param12` varchar(256) DEFAULT NULL comment 'param12',
`param13` varchar(128) DEFAULT NULL comment 'param13',
`param14` varchar(256) DEFAULT NULL comment 'param14',
`param15` varchar(128) DEFAULT NULL comment 'param15',
`param16` varchar(256) DEFAULT NULL comment 'param16',
`param17` varchar(128) DEFAULT NULL comment 'param17',
`param18` varchar(256) DEFAULT NULL comment 'param18',
`param19` varchar(128) DEFAULT NULL comment 'param19',
`param20` varchar(256) DEFAULT NULL comment 'param20',
`param21` varchar(128) DEFAULT NULL comment 'param21',
`param22` varchar(128) DEFAULT NULL comment 'param22',
`param23` varchar(128) DEFAULT NULL comment 'param23',
`param24` varchar(128) DEFAULT NULL comment 'param24',
`param25` varchar(128) DEFAULT NULL comment 'param25',
`param26` varchar(128) DEFAULT NULL comment 'param26',
`param27` varchar(128) DEFAULT NULL comment 'param27',
`param28` varchar(128) DEFAULT NULL comment 'param28',
`param29` varchar(128) DEFAULT NULL comment 'param29',
`param30` varchar(128) DEFAULT NULL comment 'param30',
`param31` varchar(128) DEFAULT NULL comment 'param31',
`param32` varchar(128) DEFAULT NULL comment 'param32',
`param33` varchar(128) DEFAULT NULL comment 'param33',
`param34` varchar(128) DEFAULT NULL comment 'param34',
`param35` varchar(128) DEFAULT NULL comment 'param35',
`param36` varchar(128) DEFAULT NULL comment 'param36',
`param37` varchar(128) DEFAULT NULL comment 'param37',
`param38` varchar(128) DEFAULT NULL comment 'param38',
`param39` varchar(128) DEFAULT NULL comment 'param39',
`param40` varchar(128) DEFAULT NULL comment 'param40',
`record_time` datetime DEFAULT NULL COMMENT '结单记录生成时间',
`remark` varchar(100) DEFAULT '' COMMENT '备注',
`creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
`gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
PRIMARY KEY (`id`),
KEY `idx_stdata_bussinessid` (`business_id`),
KEY `idx_acc_format_type_trade_date` (`acc_no`,`format_type`,`trade_date`) USING BTREE
) ENGINE=InnoDB COMMENT='ttl股票结单数据';


CREATE TABLE `za_bank_invest_statement`.`t_message_snapshot0` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`message_id` varchar(32) NOT NULL COMMENT '消息Id',
`snapshot_status` varchar(10) NOT NULL COMMENT '发送快照状态 1-发送成功、2-发送失败、7-锁定状态、8-等待确认',
`message_content` varchar(5000) NOT NULL COMMENT 'json格式消息体',
`biz_type` varchar(20) NOT NULL COMMENT '消息业务类型',
`created_at` datetime NOT NULL COMMENT '创建时间戳',
`updated_at` datetime NOT NULL COMMENT '修改时间戳',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间戳',
`is_deleted` char(1) DEFAULT NULL COMMENT '是否删除',
`modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`),
KEY `idx_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB COMMENT='activity 消息快照表';


CREATE TABLE `za_bank_invest_statement`.`t_message_snapshot1` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`message_id` varchar(32) NOT NULL COMMENT '消息Id',
`snapshot_status` varchar(10) NOT NULL COMMENT '发送快照状态 1-发送成功、2-发送失败、7-锁定状态、8-等待确认',
`message_content` varchar(5000) NOT NULL COMMENT 'json格式消息体',
`biz_type` varchar(20) NOT NULL COMMENT '消息业务类型',
`created_at` datetime NOT NULL COMMENT '创建时间戳',
`updated_at` datetime NOT NULL COMMENT '修改时间戳',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间戳',
`is_deleted` char(1) DEFAULT NULL COMMENT '是否删除',
`modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`),
KEY `idx_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB COMMENT='activity 消息快照表';


CREATE TABLE `za_bank_invest_statement`.`t_message_snapshot2` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`message_id` varchar(32) NOT NULL COMMENT '消息Id',
`snapshot_status` varchar(10) NOT NULL COMMENT '发送快照状态 1-发送成功、2-发送失败、7-锁定状态、8-等待确认',
`message_content` varchar(5000) NOT NULL COMMENT 'json格式消息体',
`biz_type` varchar(20) NOT NULL COMMENT '消息业务类型',
`created_at` datetime NOT NULL COMMENT '创建时间戳',
`updated_at` datetime NOT NULL COMMENT '修改时间戳',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间戳',
`is_deleted` char(1) DEFAULT NULL COMMENT '是否删除',
`modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`),
KEY `idx_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB COMMENT='activity 消息快照表';


CREATE TABLE `za_bank_invest_statement`.`t_message_snapshot3` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`message_id` varchar(32) NOT NULL COMMENT '消息Id',
`snapshot_status` varchar(10) NOT NULL COMMENT '发送快照状态 1-发送成功、2-发送失败、7-锁定状态、8-等待确认',
`message_content` varchar(5000) NOT NULL COMMENT 'json格式消息体',
`biz_type` varchar(20) NOT NULL COMMENT '消息业务类型',
`created_at` datetime NOT NULL COMMENT '创建时间戳',
`updated_at` datetime NOT NULL COMMENT '修改时间戳',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间戳',
`is_deleted` char(1) DEFAULT NULL COMMENT '是否删除',
`modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`),
KEY `idx_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB COMMENT='activity 消息快照表';


CREATE TABLE `za_bank_invest_statement`.`t_message_snapshot4` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`message_id` varchar(32) NOT NULL COMMENT '消息Id',
`snapshot_status` varchar(10) NOT NULL COMMENT '发送快照状态 1-发送成功、2-发送失败、7-锁定状态、8-等待确认',
`message_content` varchar(5000) NOT NULL COMMENT 'json格式消息体',
`biz_type` varchar(20) NOT NULL COMMENT '消息业务类型',
`created_at` datetime NOT NULL COMMENT '创建时间戳',
`updated_at` datetime NOT NULL COMMENT '修改时间戳',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间戳',
`is_deleted` char(1) DEFAULT NULL COMMENT '是否删除',
`modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`),
KEY `idx_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB COMMENT='activity 消息快照表';


CREATE TABLE `za_bank_invest_statement`.`t_message_snapshot5` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`message_id` varchar(32) NOT NULL COMMENT '消息Id',
`snapshot_status` varchar(10) NOT NULL COMMENT '发送快照状态 1-发送成功、2-发送失败、7-锁定状态、8-等待确认',
`message_content` varchar(5000) NOT NULL COMMENT 'json格式消息体',
`biz_type` varchar(20) NOT NULL COMMENT '消息业务类型',
`created_at` datetime NOT NULL COMMENT '创建时间戳',
`updated_at` datetime NOT NULL COMMENT '修改时间戳',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间戳',
`is_deleted` char(1) DEFAULT NULL COMMENT '是否删除',
`modifier` varchar(32) DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`),
KEY `idx_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB COMMENT='activity 消息快照表';
