package group.za.bank.statement.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.Lists;
import feign.Response;
import group.za.bank.invest.account.constants.enums.UserInvestAccountStatusEnum;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.domain.account.entity.UserInvestAccount;
import group.za.bank.invest.domain.account.entity.ext.UserInvestAccountConditionHolder;
import group.za.bank.invest.domain.account.mapper.UserInvestAccountMapper;
import group.za.bank.invest.pub.entity.resp.SendMessageResp;
import group.za.bank.invest.pub.feign.FileService;
import group.za.bank.invest.pub.feign.MessageFeignService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.MonthlyStatementInfoStatusEnum;
import group.za.bank.statement.domain.entity.TdCloseAccountMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.mapper.TdCloseAccountMonthlyStatementMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.service.remote.FileRemoteService;
import group.za.bank.statement.utils.FileStringUtil;
import group.za.bank.statement.utils.JarDirConfig;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CloseMonthlyStatementServiceImplTest {

    @InjectMocks
    private CloseMonthlyStatementServiceImpl closeMonthlyStatementService;

    @Mock
    private TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;

    @Mock
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;

    @Mock
    private TdCloseAccountMonthlyStatementMapper tdCloseAccountMonthlyStatementMapper;

    @Mock
    private UserInvestAccountMapper userInvestAccountMapper;

    @Mock
    private SystemConfig systemConfig;

    @Mock
    private ThreadPoolTaskExecutor statementExecutor;

    @Mock
    private FileService fileService;

    @Mock
    private MessageFeignService messageFeignService;

    @Mock
    private FileRemoteService fileRemoteService;

    private final String TEST_PERIOD = "202311";

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> jsonUtils;
    private MockedStatic<JarDirConfig> mockedJarDirConfig;
    private MockedStatic<ZipUtil> mockedZipUtil;
    private MockedStatic<FileStringUtil> mockedFileStringUtil;

    @Before
    public void setUp() {
        // 配置SystemConfig返回邮件列表
        when(systemConfig.getStatementEmailList()).thenReturn(Lists.newArrayList("<EMAIL>"));
        
        // Mock ThreadPoolTaskExecutor的submit方法
        Future<?> future = mock(Future.class);
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run(); // 直接执行传入的任务
            return future;
        }).when(statementExecutor).submit(any(Runnable.class));
        
        // Mock JsonUtils的toJsonString方法
        jsonUtils = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");

        // Mock JarDirConfig
        mockedJarDirConfig = mockStatic(JarDirConfig.class);
        when(JarDirConfig.getAbsolutePath(anyString())).thenReturn("/tmp/test");

        // Mock ZipUtil
        mockedZipUtil = mockStatic(ZipUtil.class);
        when(ZipUtil.zip(anyString(), anyString())).thenReturn(new File("/tmp/test.zip"));

        // Mock FileStringUtil
        mockedFileStringUtil = mockStatic(FileStringUtil.class);
        when(FileStringUtil.fileByteString(anyString())).thenReturn("test content".getBytes());
    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        if (jsonUtils != null) {
            jsonUtils.close();
        }
        if (mockedJarDirConfig != null) {
            mockedJarDirConfig.close();
        }
        if (mockedZipUtil != null) {
            mockedZipUtil.close();
        }
        if (mockedFileStringUtil != null) {
            mockedFileStringUtil.close();
        }
    }

    @Test
    public void testGenerateCloseMonthlyStatement_WhenNoStatementInfo() {
        // 模拟无月结单信息的情况
        when(tdMonthlyStatementInfoMapper.selectOne(any())).thenReturn(null);
        
        closeMonthlyStatementService.generateCloseMonthlyStatement(TEST_PERIOD);
        
        // 验证是否没有继续执行后续操作
        verify(tdCloseAccountMonthlyStatementMapper, never()).selectCount(any());
    }

    @Test
    public void testGenerateCloseMonthlyStatement_WhenStatementInfoExists() throws IOException {
        // 准备测试数据
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());
        
        // Mock依赖方法
        when(tdMonthlyStatementInfoMapper.selectOne(any())).thenReturn(statementInfo);
        when(tdCloseAccountMonthlyStatementMapper.selectCount(any())).thenReturn(0);
        
        // 准备销户账户数据
        List<UserInvestAccount> userAccounts = new ArrayList<>();
        UserInvestAccount account = new UserInvestAccount();
        account.setClientId("TEST001");
        account.setAccountStatus(UserInvestAccountStatusEnum.CLOSED.getValue());
        userAccounts.add(account);
        
        when(userInvestAccountMapper.queryAllUserInvestAccountByCondition(any())).thenReturn(userAccounts);
        
        // 准备月结单数据
        List<TdFundMonthlyStatement> statements = new ArrayList<>();
        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setId(1L);
        statement.setClientId("TEST001");
        statement.setPeriod(TEST_PERIOD);
        statements.add(statement);
        
        when(tdMonthlyStatementMapper.queryStatementByPeriodAndClientId(anyString(), any(), anyInt(), anyLong()))
            .thenReturn(statements)
            .thenReturn(new ArrayList<>()); // 第二次调用返回空列表结束循环
            
        // Mock文件下载和上传
        Response.Body body = mock(Response.Body.class);
        Response response = mock(Response.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(new ByteArrayInputStream("test".getBytes()));
        when(fileService.fileDownload(any())).thenReturn(response);
        
        when(fileRemoteService.singleFile(any(), any())).thenReturn("testObsKey");
        
        // Mock消息发送
        ResponseData<SendMessageResp> messageResp = new ResponseData<>();
        messageResp.setCode("0000");
        when(messageFeignService.sendMessage(any())).thenReturn(messageResp);
        
        // 执行测试
        closeMonthlyStatementService.generateCloseMonthlyStatement(TEST_PERIOD);
        
        // 验证关键方法是否被调用
        //verify(tdCloseAccountMonthlyStatementMapper).insertBatch(any());
        //verify(fileService, atLeastOnce()).fileDownload(any());
        //verify(messageFeignService, atLeastOnce()).sendMessage(any());
    }

    @Test
    public void testPrepareData() {
        // 准备测试数据
        List<UserInvestAccount> userAccounts = new ArrayList<>();
        UserInvestAccount account = new UserInvestAccount();
        account.setClientId("TEST001");
        account.setAccountStatus(UserInvestAccountStatusEnum.CLOSED.getValue());
        userAccounts.add(account);

        // Mock依赖方法
        when(userInvestAccountMapper.queryAllUserInvestAccountByCondition(any(UserInvestAccountConditionHolder.class)))
            .thenReturn(userAccounts);

        List<TdFundMonthlyStatement> statements = new ArrayList<>();
        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setId(1L);
        statement.setClientId("TEST001");
        statements.add(statement);

        when(tdMonthlyStatementMapper.queryStatementByPeriodAndClientId(anyString(), any(), anyInt(), anyLong()))
            .thenReturn(statements)
            .thenReturn(new ArrayList<>()); // 第二次调用返回空列表结束循环

        // 执行测试
        closeMonthlyStatementService.prepareData(TEST_PERIOD);

        // 验证
        //verify(tdCloseAccountMonthlyStatementMapper).insertBatch(any());
    }

    @Test
    public void testGenerateCloseMonthlyStatement_WhenProcessingExists() {
        // 准备测试数据
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());
        
        // Mock依赖方法
        when(tdMonthlyStatementInfoMapper.selectOne(any())).thenReturn(statementInfo);
        // 第一次调用返回1，表示有数据
        // 第二次调用返回1，表示有处理中的数据
        when(tdCloseAccountMonthlyStatementMapper.selectCount(any()))
            .thenReturn(1)
            .thenReturn(1);
        
        closeMonthlyStatementService.generateCloseMonthlyStatement(TEST_PERIOD);
        
        // 验证是否调用了删除方法
        verify(tdCloseAccountMonthlyStatementMapper).deleteCloseAccountMonthlyStatement(TEST_PERIOD);
    }

    @Test
    public void testGenerateCloseMonthlyStatement_WhenAllFinished() {
        // 准备测试数据
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());
        
        // Mock依赖方法
        when(tdMonthlyStatementInfoMapper.selectOne(any())).thenReturn(statementInfo);
        // 第一次调用返回1，表示有数据
        // 第二次调用返回0，表示没有处理中的数据
        when(tdCloseAccountMonthlyStatementMapper.selectCount(any()))
            .thenReturn(1)
            .thenReturn(0);
        
        closeMonthlyStatementService.generateCloseMonthlyStatement(TEST_PERIOD);
        
        // 验证是否没有继续执行后续操作
        verify(tdCloseAccountMonthlyStatementMapper, never()).deleteCloseAccountMonthlyStatement(any());
    }

    @Test
    public void testSendCloseAccountMonthlyStatement_Success() throws IOException {
        // Mock查询数据
        List<TdCloseAccountMonthlyStatement> statements = new ArrayList<>();
        TdCloseAccountMonthlyStatement statement = new TdCloseAccountMonthlyStatement();
        statement.setId(1L);
        statement.setPeriod(TEST_PERIOD);
        statement.setClientId("TEST001");
        statement.setDocLang("CN");
        statements.add(statement);

        when(tdCloseAccountMonthlyStatementMapper.queryFundCloseAccountMonthlyStatement(anyString(), anyInt(), anyLong()))
            .thenReturn(statements)
            .thenReturn(new ArrayList<>()); // 第二次调用返回空列表结束循环

        // Mock文件下载
        Response.Body body = mock(Response.Body.class);
        Response response = mock(Response.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(new ByteArrayInputStream("test".getBytes()));
        when(fileService.fileDownload(any())).thenReturn(response);

        // Mock文件上传
        when(fileRemoteService.singleFile(any(), any())).thenReturn("testObsKey");

        // Mock消息发送
        ResponseData<SendMessageResp> messageResp = new ResponseData<>();
        messageResp.setCode("0000");
        when(messageFeignService.sendMessage(any())).thenReturn(messageResp);

        // 执行测试
        boolean result = closeMonthlyStatementService.sendCloseAccountMonthlyStatement(TEST_PERIOD);

        // 验证结果
        verify(tdCloseAccountMonthlyStatementMapper).updateByCondition(any(), any());
        verify(fileService).fileDownload(any());
        verify(fileRemoteService).singleFile(any(), any());
        verify(messageFeignService).sendMessage(any());
        assert result;
    }

    //@Test
    public void testSendCloseAccountMonthlyStatement_WhenNoEmailConfig() throws IOException {
        // Mock查询数据
        List<TdCloseAccountMonthlyStatement> statements = new ArrayList<>();
        TdCloseAccountMonthlyStatement statement = new TdCloseAccountMonthlyStatement();
        statement.setId(1L);
        statements.add(statement);

        when(tdCloseAccountMonthlyStatementMapper.queryFundCloseAccountMonthlyStatement(anyString(), anyInt(), anyLong()))
            .thenReturn(statements);

        // Mock空邮件列表
        when(systemConfig.getStatementEmailList()).thenReturn(new ArrayList<>());

        // Mock文件下载和上传
        Response.Body body = mock(Response.Body.class);
        Response response = mock(Response.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(new ByteArrayInputStream("test".getBytes()));
        when(fileService.fileDownload(any())).thenReturn(response);
        when(fileRemoteService.singleFile(any(), any())).thenReturn("testObsKey");

        // 执行测试
        boolean result = closeMonthlyStatementService.sendCloseAccountMonthlyStatement(TEST_PERIOD);

        // 验证结果
        assert !result;
    }
} 