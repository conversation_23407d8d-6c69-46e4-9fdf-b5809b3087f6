<?xml version="1.0" encoding="UTF-8"?>
<project
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>group.za.bank</groupId>
        <artifactId>zabank-ftc-statement-service</artifactId>
        <!-- 不需要更改此版本 -->
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zabank-ftc-statement-provider</artifactId>
    <version>${zabank.ftc.statement.share.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-invest-basecommon</artifactId>
            <version>${zabank.invest.basecommon.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-statement-domain</artifactId>
            <version>${zabank.ftc.statement.domain.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-statement-share</artifactId>
            <version>${zabank.ftc.statement.share.version}</version>
        </dependency>


        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-invest-common</artifactId>
            <version>${zabank.invest.common.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-trade-domain</artifactId>
            <version>${zabank.ftc.trade.domain.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-trade-share</artifactId>
            <version>${zabank.ftc.trade.share.version}</version>
        </dependency>


        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-product-share</artifactId>
            <version>${zabank.ftc.product.share.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-product-domain</artifactId>
            <version>${zabank.ftc.product.domain.version}</version>
        </dependency>


        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-account-domain</artifactId>
            <version>${zabank.ftc.account.domain.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-account-share</artifactId>
            <version>${zabank.ftc.account.share.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-quotasup-share</artifactId>
            <version>${zabank.sbs.quotasup.share.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-cache-spring-boot-starter</artifactId>
            <version>${zainvest.cache.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-rabbitmq-spring-boot-starter</artifactId>
            <version>${zainvest.rabbitmq.starter.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-public-share</artifactId>
            <version>${zabank.ftc.public.share.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.itextpdf</groupId>
                    <artifactId>itextpdf</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.itextpdf</groupId>
                    <artifactId>itext-asian</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ftc-public-domain</artifactId>
            <version>${zabank.ftc.public.domain.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl.job.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech</groupId>
            <artifactId>za-bank-sso-share</artifactId>
            <version>${za.bank.sso.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-mysql-druid-dependencies</artifactId>
            <version>${zainvest.druid.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-mybatis-spring-boot-starter</artifactId>
            <version>${zainvest.mybatis.version}</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <!--文件导出-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-file-spring-boot-starter</artifactId>
            <version>${zainvest.file.spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-opa-accfront-share</artifactId>
            <version>${zainvest.accfront.share.vesrion}</version>
        </dependency>

        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-file-parser</artifactId>
            <version>${zainvest.file.parserversion}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
        </dependency>
        <!--        <dependency>
                    <groupId>javax.persistence</groupId>
                    <artifactId>persistence-api</artifactId>
                    <version>1.0.2</version>
                </dependency>-->

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-counterfront-share</artifactId>
            <version>${zabank.sbs.countfront.share.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-bankfront-domain</artifactId>
            <version>${zabank-sbs-bankfront-domain.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>group.za.bank</groupId>
                    <artifactId>zabank-common-mybatisplus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-bankfront-share</artifactId>
            <version>${zabank-sbs-bankfront-share.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>group.za.bank</groupId>
                    <artifactId>zabank-common-mybatisplus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-trade-domain</artifactId>
            <version>${zabank-sbs-trade-domain.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>group.za.bank</groupId>
                    <artifactId>zabank-common-mybatisplus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-trade-share</artifactId>
            <version>${zabank-sbs-trade-share.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>group.za.bank</groupId>
                    <artifactId>zabank-common-mybatisplus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-tradedata-share</artifactId>
            <version>${zabank-sbs-tradedata-share.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>group.za.bank</groupId>
                    <artifactId>zabank-common-mybatisplus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-business-share</artifactId>
            <version>${zabank-sbs-business-share.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>group.za.bank</groupId>
                    <artifactId>zabank-common-mybatisplus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-business-domain</artifactId>
            <version>${zabank.sbs.business.domain.version}</version>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ccs-tradecore-domain</artifactId>
            <version>${zabank-ccs-tradecore-domain.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ccs-tradecore-share</artifactId>
            <version>${zabank-ccs-tradecore-share.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-ccs-coresup-share</artifactId>
            <version>${zabank-ccs-coresup-share.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-sbs-ipo-domain</artifactId>
            <version>${zabank-sbs-ipo-domain.version}</version>
        </dependency>
        <dependency>
            <groupId>group.za</groupId>
            <artifactId>zati-pcs-watchdog</artifactId>
            <version>${zati.pcs.watchdog.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun.oss.version}</version>
        </dependency>
<!--
        <dependency>
            <groupId>group.za.invest</groupId>
            <artifactId>zainvest-complex-datasource-spring-boot-starter</artifactId>
            <version>1.30.0-SNAPSHOT</version>
        </dependency>-->
      <!--  <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.0</version>
        </dependency>-->

        <!--Activity的组件-->
        <dependency>
            <groupId>com.zatech</groupId>
            <artifactId>zabank-act-messaging-component</artifactId>
            <version>${zabank.act.messaging.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>za-bank-message-share</artifactId>
                    <groupId>com.zatech</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>za-bank-framework-common</artifactId>
                    <groupId>com.zatech</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>za-bank-framework-common</artifactId>
                    <groupId>com.zatech</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>0.7.3</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--h2相关 支持单元测试-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.4.6</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.4.6</version>
            <scope>test</scope>
        </dependency>

        <!-- Jsoup 用于解析html -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>

        <!-- fel 表达引擎 -->
        <dependency>
            <groupId>org.eweb4j</groupId>
            <artifactId>fel</artifactId>
            <version>${fel.version}</version>
        </dependency>

        <!-- caffeine 缓存 -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.codehaus.jettison/jettison -->
        <dependency>
            <groupId>org.codehaus.jettison</groupId>
            <artifactId>jettison</artifactId>
            <version>${jettison.version}</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>${json-smart.version}</version>
        </dependency>
        <!--    mapstruct    -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct-processor.version}</version>
        </dependency>

    </dependencies>
    <properties>
        <!--不能被deploy-->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>


    <build>
        <finalName>zabank-ftc-statement-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <version>3.8.0</version>-->
<!--                <configuration>-->
<!--                    <source>${java.version}</source>-->
<!--                    <target>${java.version}</target>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <excludedGroups>group.za.bank.statement.category.IntegrationTest</excludedGroups>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>