package group.za.bank.statement.common.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

/**
 * ali oss配置类
 *
 * <AUTHOR>
 * @date 2023/08/07
 **/
@Configuration
public class AliOSSConfiguration {

    @Bean
    @Scope("prototype")
    public static OSS initOSSClient(AliOSSProperties properties) {
        return new OSSClientBuilder()
                .build(properties.getEndPoint(), properties.getAccessKeyId(), properties.getAccessKeySecret());
    }


}
