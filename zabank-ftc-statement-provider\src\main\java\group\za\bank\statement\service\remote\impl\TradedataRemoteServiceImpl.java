package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.sbs.tradedata.model.req.ActionCapitalInfoQueryReq;
import group.za.bank.sbs.tradedata.model.req.ActionFractionOrderQueryReq;
import group.za.bank.sbs.tradedata.model.req.ActionInfoQueryReq;
import group.za.bank.sbs.tradedata.model.resp.ActionCapitalInfoResp;
import group.za.bank.sbs.tradedata.model.resp.ActionFractionOrderResp;
import group.za.bank.sbs.tradedata.model.resp.ActionInfoResp;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.TradedataRemoteService;
import group.za.bank.statement.service.remote.feign.CompanyActionFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/06/09
 **/
@Service
@Slf4j
public class TradedataRemoteServiceImpl implements TradedataRemoteService {

    @Autowired
    private CompanyActionFeign companyActionFeign;

    @Override
    public ActionFractionOrderResp actionFractionOrderQuery(ActionFractionOrderQueryReq req) {
        log.info("actionFractionOrderQuery req:{}", JsonUtils.toJsonString(req));
        ResponseData<ActionFractionOrderResp> responseData = companyActionFeign.actionFractionOrderQuery(req);
        if(!responseData.judgeSuccess()){
            log.info("actionFractionOrderQuery失败:{}", responseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        log.info("actionFractionOrderQuery resp:{}", JsonUtils.toJsonString(responseData.getValue()));
        return responseData.getValue();
    }

    @Override
    public ActionCapitalInfoResp actionCapitalInfoQuery(ActionCapitalInfoQueryReq req) {
        log.info("actionCapitalInfoQuery req:{}", JsonUtils.toJsonString(req));
        ResponseData<ActionCapitalInfoResp> responseData = companyActionFeign.actionCapitalInfoQuery(req);
        if(!responseData.judgeSuccess()){
            log.info("actionCapitalInfoQuery失败:{}", responseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        log.info("actionCapitalInfoQuery resp:{}", JsonUtils.toJsonString(responseData.getValue()));
        return responseData.getValue();
    }

    @Override
    public ActionInfoResp actionInfoQuery(ActionInfoQueryReq req) {
        log.info("actionInfoQuery req:{}", JsonUtils.toJsonString(req));
        ResponseData<ActionInfoResp> responseData = companyActionFeign.actionInfoQuery(req);
        if(!responseData.judgeSuccess() || null == responseData.getValue()){
            log.info("actionInfoQuery查询失败:{}", responseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        log.info("actionInfoQuery resp:{}", JsonUtils.toJsonString(responseData.getValue()));
        return responseData.getValue();
    }
}
