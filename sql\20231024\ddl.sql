CREATE TABLE za_bank_invest_statement.`td_close_account_monthly_statement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务逻辑id',
  `period` char(6) DEFAULT NULL COMMENT '月结单期数:''yyyyMM''',
  `bank_user_id` varchar(20) NOT NULL COMMENT '银行用户id',
  `client_id` varchar(20) DEFAULT NULL COMMENT '投资账户id',
  `send_status` tinyint(4) DEFAULT '1' COMMENT '发送状态:1-待发送,2-已发送 ',
  `doc_lang` varchar(6) DEFAULT NULL COMMENT '文档语言',
  `temp_key` varchar(128) DEFAULT NULL COMMENT '模板key',
  `doc_url` varchar(512) DEFAULT NULL COMMENT '文档地址',
  `pub_time` datetime DEFAULT NULL COMMENT '发送成功时间',
  `remark` varchar(100) DEFAULT '' COMMENT '备注',
  `creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
  `gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_client_id_lang_key` (`period`,`client_id`,`doc_lang`) USING BTREE
) ENGINE=InnoDB COMMENT='销户月结单明细表';

-- 移除idx_client_business_period索引，新增idx_period_client和idx_client索引
ALTER TABLE `za_bank_invest_statement`.`td_fund_monthly_statement`
DROP INDEX `idx_client_business_period`,
ADD INDEX `idx_period_client` (`period`, `client_id`),
ADD INDEX `idx_client` (`client_id`);
