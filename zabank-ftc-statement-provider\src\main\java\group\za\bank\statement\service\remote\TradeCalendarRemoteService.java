package group.za.bank.statement.service.remote;

import group.za.bank.sbs.trade.model.resp.feign.MonthFirstTradeDateResp;
import group.za.bank.sbs.trade.model.resp.feign.MonthLastTradeDateResp;
import group.za.bank.sbs.trade.model.resp.feign.TradeCalendarResp;
import group.za.bank.sbs.trade.model.resp.feign.TradeDateDiffResp;

import java.util.Date;
import java.util.List;

/**
 * 交易日历远程接口
 *
 * <AUTHOR>
 * @date 2022/05/23
 **/
public interface TradeCalendarRemoteService {

    /**
     * 获取某月最后一个交易日
     * @param marketCode
     * @param yearMonth yyyyMM
     * @return
     */
    MonthLastTradeDateResp getMonthLastTradeDate(String marketCode, String yearMonth);

    /**
     * 获取某月的第一个交易日
     * @param marketCode
     * @param yearMonth
     * @return
     */
    MonthFirstTradeDateResp getMonthFirstTradeDate(String marketCode, String yearMonth);


    /**
     * 查询指定范围内的交易日历
     * @param marketCode
     * @param startDate
     * @param endDate
     * @return
     */
    List<TradeCalendarResp> queryTradeDateCalendarList(String marketCode,String startDate,String endDate);

    /**
     * 交易日偏移量查询
     * @param marketCode
     * @param baseDate
     * @param diff
     * @return
     */
    TradeDateDiffResp tradeDateDiff(String marketCode, String baseDate, Integer diff);

    /**
     * 判断是否是交易日
     * @param marketCode
     * @param baseDate
     * @return
     */

    boolean isTradeDay(String marketCode, Date baseDate);
}
