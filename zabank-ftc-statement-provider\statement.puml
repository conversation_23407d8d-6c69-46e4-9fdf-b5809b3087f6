@startuml
participant 结单服务
participant 结单mysql库 as 结单DB
participant 结单mongo库 as mongoDB
participant 基金交易mysql库 as 基金交易DB
participant 数字货币mysql库 as 数字货币DB
participant 股票交易mysql库 as 股票交易DB
participant 股票交易服务 as 股票交易服务
participant OBS文件服务器
participant 公共服务
participant 后管服务

group 第一步：月结单数据初始化阶段
    结单服务 ->> 结单DB: 确定股票，基金，crypto月结单数据已准备好

    group 确定需要生成月结单的用户
        loop 分页轮询投资用户检查是否需要生成月结单
            结单服务 ->> 基金交易DB: 查询基金交易/持仓记录判断是否需要生成基金月结单
            结单服务 ->> 数字货币DB: 查询数字货币交易/持仓记录判断是否需要生成crypto月结单
            结单服务 ->> 股票交易DB: 查询美股交易/持仓记录判断是否需要生成股票月结单
            结单服务 ->> 股票交易服务: 继续调用RPC判断是否需要生成股票月结单
            note right of 结单服务
              检查资产信息 - 检查用户在期初或期末是否有资产(DB操作)
              检查订单数据 - 检查订单交易数据(DB操作)
              检查交易变动 - 查找公司行动相关数据(DB操作)
              检查小数股订单 - 检查是否有公司行动小数股订单 (RPC接口，耗时高，占用70%耗时)
              检查金融税记录 - 查找ADR和金融税记录 (RPC接口)
              检查公司行动派息入账 - 检查是否有待处理的派息入账 (RPC接口)
              检查公司行动待处理数据 - 检查是否有待处理的公司行动 (RPC接口)
              检查转仓手续费记录 - 检查是否有转仓手续费记录(RPC接口)
              检查股息追缴扣款 - 检查是否有股息追缴扣款(DB操作)
              检查资金转账 - 检查是否有资金转账数据(DB操作)
              检查股票变动数据 - 检查是否有股票变动数据(DB操作)
            end note
        end
    end
end

group 第二步：月结单数据准备阶段
    结单服务 ->> 结单服务: 收到需要生成月结单用户MQ消息
    结单服务 ->> 结单DB: 捞取基金、股票和数字货币数据
    结单服务 ->> mongoDB: 落库结单json数据到mongo
    结单服务 ->> 结单DB: 更新基金结单数据为准备完成
end

group 第三步：月结单生成阶段
    loop 轮询需要生成结单的数据
        结单服务 ->> 结单DB: 查询本地待生成结单文件记录
        结单服务 ->> mongoDB: 获取mongo数据并组装结单用户基础数据
        结单服务 ->> 公共服务: 发起结单文件生成
    end

    loop 轮询结单文件生成中状态数据
        结单服务 ->> 结单DB: 查询结单文件生成中状态数据
        结单服务 ->> 公共服务: 发起文件生成状态查询
        结单服务 ->> OBS文件服务器:OBS下载结单html文件
        结单服务 ->> 结单服务: 解析结单html文件并落库结单数据
    end
end

group 第四步：月结单推送阶段
    后管服务 ->> 结单服务: 确认可以推送月结单
    结单服务 ->> 结单服务: 开始推送月结单给用户
end

@enduml