package group.za.bank.statement.job;


import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.agent.TradeCalendarAgent;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.entity.req.StatementDataParseReq;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.StockTradeDateService;
import group.za.bank.statement.service.TdStockStatementDataService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.Arrays;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StatementDataParseJobTest {
    @Mock
    TdStockStatementDataService stockStatementDataService;
    @Mock
    TradeCalendarAgent tradeCalendarAgent;

    @Mock
    StatementBusinessService statementBusinessService;

    @Mock
    StockTradeDateService stockTradeDateService;

    @Mock
    SystemConfig systemConfig;

    @Mock
    Logger log;
    @InjectMocks
    StatementDataDailyParseJob statementDataDailyParseJob;

    private MockedStatic<JsonUtils> mockedJsonUtils;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");

        StatementDataParseReq statementDataParseReq = new StatementDataParseReq();
        statementDataParseReq.setStatementDate("202206");
        statementDataParseReq.setPathDate("20220630");
        statementDataParseReq.setRefresh(true);

        // 修复：使用eq()匹配器来匹配Class参数
        when(JsonUtils.toJavaObject(any(String.class), eq(StatementDataParseReq.class)))
            .thenReturn(statementDataParseReq);
    }

    @After
    public void close(){
        mockedJsonUtils.close();
    }

    @Test
    public void testExecute() throws Exception {
        StatementDataParseReq statementDataParseReq = new StatementDataParseReq();
        statementDataParseReq.setStatementDate("202206");
        statementDataParseReq.setPathDate("20220630");
        statementDataParseReq.setRefresh(true);
        when(stockTradeDateService.queryCoreCalendarTradeDateList(any())).thenReturn(Arrays.asList("20220630"));
        //void 方法mock
        doNothing().when(stockStatementDataService).parseStatementMonthlyData(any(), anyString(), anyString(), anyBoolean());

        statementDataDailyParseJob.execute(JsonUtils.toJsonString(statementDataParseReq));
    }
}