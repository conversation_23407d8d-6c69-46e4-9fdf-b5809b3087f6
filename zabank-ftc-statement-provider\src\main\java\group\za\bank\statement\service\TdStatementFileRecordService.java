package group.za.bank.statement.service;

import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.invest.mybatis.repository.service.CrudService;

import java.util.Date;
import java.util.List;

/**
* This service interface access the database table td_statement_file_record
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 10:39:39
*/
public interface TdStatementFileRecordService extends CrudService<TdStatementFileRecord> {

    /**
     * 查询月结单文件解析状态
     * @param startDate yyyyMMdd
     * @param endDate yyyyMMdd
     * @return
     */
    List<TdStatementFileRecord> queryDailyFileRecords(String startDate,String endDate);

    /**
     * 查询月结单文件解析状态
     * @param statementDate yyyyMMdd
     * @return
     */
    List<TdStatementFileRecord> queryMonthlyFileRecords(String statementDate);

    /**
     * 校验文件记录
     *
     * @param statementType
     * @param statementDate        yyyyMMdd
     * @param refresh
     * @param fileLastModifiedTime
     * @return
     */
    TdStatementFileRecord checkFileRecord(Integer statementType, String statementDate, Boolean refresh, Date fileLastModifiedTime);

    /**
     * 查询日结单文件解析状态
     * @param statementDate
     * @return
     */
    TdStatementFileRecord queryDailyFileRecord(Date statementDate);
}
