#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 92176 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=45968, tid=43040
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.5******.28-jcef (21.0.5+8) (build 21.0.5+8-b631.28)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.5******.28-jcef (21.0.5+8-b631.28, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 

Host: Intel(R) Core(TM) i5-10505 CPU @ 3.20GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Thu Jun  5 08:54:52 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 1.032021 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000002257412dc60):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=43040, stack(0x000000a498000000,0x000000a498100000) (1024K)]


Current CompileTask:
C2:1032 1105       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSum (6 bytes)

Stack: [0x000000a498000000,0x000000a498100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e52b9]
V  [jvm.dll+0x8c3633]
V  [jvm.dll+0x8c5b8e]
V  [jvm.dll+0x8c6273]
V  [jvm.dll+0x288f46]
V  [jvm.dll+0xc66dd]
V  [jvm.dll+0xc6c13]
V  [jvm.dll+0x2feb50]
V  [jvm.dll+0x60c609]
V  [jvm.dll+0x2598a2]
V  [jvm.dll+0x259c5f]
V  [jvm.dll+0x252415]
V  [jvm.dll+0x24fc6e]
V  [jvm.dll+0x1cd6a4]
V  [jvm.dll+0x25f5dc]
V  [jvm.dll+0x25db26]
V  [jvm.dll+0x3ff5e6]
V  [jvm.dll+0x86b248]
V  [jvm.dll+0x6e3abd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000225740d38f0, length=16, elements={
0x000002254d435db0, 0x000002256f7d0080, 0x000002256f7d0ad0, 0x000002256f7d2690,
0x000002256f7d30e0, 0x000002256f7d3b30, 0x000002256f7d4580, 0x000002256f7d6310,
0x000002256f7d6da0, 0x000002256f92c3c0, 0x000002256f938630, 0x00000225740abbd0,
0x00000225740cb650, 0x00000225740f1510, 0x00000225741a9e60, 0x000002257412dc60
}

Java Threads: ( => current thread )
  0x000002254d435db0 JavaThread "main"                              [_thread_blocked, id=42724, stack(0x000000a496900000,0x000000a496a00000) (1024K)]
  0x000002256f7d0080 JavaThread "Reference Handler"          daemon [_thread_blocked, id=63180, stack(0x000000a497100000,0x000000a497200000) (1024K)]
  0x000002256f7d0ad0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=43860, stack(0x000000a497200000,0x000000a497300000) (1024K)]
  0x000002256f7d2690 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=43912, stack(0x000000a497300000,0x000000a497400000) (1024K)]
  0x000002256f7d30e0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=27268, stack(0x000000a497400000,0x000000a497500000) (1024K)]
  0x000002256f7d3b30 JavaThread "Service Thread"             daemon [_thread_blocked, id=19444, stack(0x000000a497500000,0x000000a497600000) (1024K)]
  0x000002256f7d4580 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=59296, stack(0x000000a497600000,0x000000a497700000) (1024K)]
  0x000002256f7d6310 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=67872, stack(0x000000a497700000,0x000000a497800000) (1024K)]
  0x000002256f7d6da0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=61884, stack(0x000000a497800000,0x000000a497900000) (1024K)]
  0x000002256f92c3c0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=44084, stack(0x000000a497900000,0x000000a497a00000) (1024K)]
  0x000002256f938630 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=36116, stack(0x000000a497a00000,0x000000a497b00000) (1024K)]
  0x00000225740abbd0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=19000, stack(0x000000a497b00000,0x000000a497c00000) (1024K)]
  0x00000225740cb650 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=41280, stack(0x000000a497d00000,0x000000a497e00000) (1024K)]
  0x00000225740f1510 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=31596, stack(0x000000a497e00000,0x000000a497f00000) (1024K)]
  0x00000225741a9e60 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=59412, stack(0x000000a497f00000,0x000000a498000000) (1024K)]
=>0x000002257412dc60 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=43040, stack(0x000000a498000000,0x000000a498100000) (1024K)]
Total: 16

Other Threads:
  0x000002256ed91080 VMThread "VM Thread"                           [id=41684, stack(0x000000a497000000,0x000000a497100000) (1024K)]
  0x000002256ed7ed00 WatcherThread "VM Periodic Task Thread"        [id=62668, stack(0x000000a496f00000,0x000000a497000000) (1024K)]
  0x000002254d6e4700 WorkerThread "GC Thread#0"                     [id=29336, stack(0x000000a496a00000,0x000000a496b00000) (1024K)]
  0x000002254d6f73d0 ConcurrentGCThread "G1 Main Marker"            [id=55072, stack(0x000000a496b00000,0x000000a496c00000) (1024K)]
  0x000002254d6f7de0 WorkerThread "G1 Conc#0"                       [id=56560, stack(0x000000a496c00000,0x000000a496d00000) (1024K)]
  0x000002256ec4e400 ConcurrentGCThread "G1 Refine#0"               [id=31860, stack(0x000000a496d00000,0x000000a496e00000) (1024K)]
  0x000002256ec4ee80 ConcurrentGCThread "G1 Service"                [id=50060, stack(0x000000a496e00000,0x000000a496f00000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  1108 1112       4       sun.security.util.math.intpoly.IntegerPolynomialP256::square (613 bytes)
C1 CompilerThread0  1109 1139       3       sun.security.provider.ByteArrayAccess::b2iBig64 (231 bytes)
C2 CompilerThread1  1109 1114       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
C2 CompilerThread2  1109 1105       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSum (6 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000604400000, size: 8124 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000022500000000-0x0000022500d00000-0x0000022500d00000), size 13631488, SharedBaseAddress: 0x0000022500000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000022501000000-0x0000022541000000, reserved size: **********
Narrow klass base: 0x0000022500000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32480M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8124M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 12288K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 0 survivors (0K)
 Metaspace       used 8296K, committed 8576K, reserved 1114112K
  class space    used 954K, committed 1088K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   1|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   2|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   3|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   4|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   5|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   6|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   7|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|   8|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|   9|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  10|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  11|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  12|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  13|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  14|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  15|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  16|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  17|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  18|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  19|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  20|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  21|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  22|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  23|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  24|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  25|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  26|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  27|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  28|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  29|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  30|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  31|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  32|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  33|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  34|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  35|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  36|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  37|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  38|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  39|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  40|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  41|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  42|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  43|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  44|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  45|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  46|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  47|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  48|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  49|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  50|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  51|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  52|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  53|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  54|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  55|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  56|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  57|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  58|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  59|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  60|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  61|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  62|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  63|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  64|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  65|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  66|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  67|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  68|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  69|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  70|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  71|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  72|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  73|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  74|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  75|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  76|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  77|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  78|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  79|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  80|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  81|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  82|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  83|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  84|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  85|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  86|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  87|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  88|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  89|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  90|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  91|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  92|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  93|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  94|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  95|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  96|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  97|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  98|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  99|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 100|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 101|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 102|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 103|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 104|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 105|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 106|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 107|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 108|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 109|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 110|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 111|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 112|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 113|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 114|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 115|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 116|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 117|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 118|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 119|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 120|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 121|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 122|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 123|0x0000000623000000, 0x00000006233d9438, 0x0000000623400000| 96%| E|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 124|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 125|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 
| 126|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000| PB 0x0000000623c00000| Complete 

Card table byte_map: [0x0000022563a30000,0x0000022564a10000] _byte_map_base: 0x0000022560a0e000

Marking Bits: (CMBitMap*) 0x000002254d6e4d10
 Bits: [0x0000022564a10000, 0x000002256c900000)

Polling page: 0x000002254d700000

Metaspace:

Usage:
  Non-class:      7.28 MB used.
      Class:    971.45 KB used.
       Both:      8.23 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.38 MB ( 12%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.06 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.44 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.39 MB
       Class:  14.98 MB
        Both:  23.37 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 226.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 135.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 373.
num_chunk_merges: 0.
num_chunk_splits: 221.
num_chunks_enlarged: 101.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=370Kb max_used=370Kb free=119629Kb
 bounds [0x000002255a8a0000, 0x000002255ab10000, 0x0000022561dd0000]
CodeHeap 'profiled nmethods': size=120000Kb used=1775Kb max_used=1775Kb free=118224Kb
 bounds [0x0000022552dd0000, 0x0000022553040000, 0x000002255a300000]
CodeHeap 'non-nmethods': size=5760Kb used=1430Kb max_used=1456Kb free=4329Kb
 bounds [0x000002255a300000, 0x000002255a570000, 0x000002255a8a0000]
 total_blobs=1650 nmethods=1133 adapters=422
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.017 Thread 0x000002257412dc60 1100       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::conditionalSet (53 bytes)
Event: 1.018 Thread 0x000002256f7d6310 nmethod 1094 0x000002255a8f8110 code [0x000002255a8f82a0, 0x000002255a8f8450]
Event: 1.018 Thread 0x000002256f7d6310 1106       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setDifference (6 bytes)
Event: 1.018 Thread 0x000002256f7d6da0 1108       3       sun.security.util.math.intpoly.IntegerPolynomial$Element::mutable (5 bytes)
Event: 1.018 Thread 0x000002256f7d6da0 nmethod 1108 0x0000022552f7e510 code [0x0000022552f7e6c0, 0x0000022552f7e880]
Event: 1.018 Thread 0x000002256f7d6da0 1109       3       sun.security.util.math.intpoly.IntegerPolynomial$Element::mutable (26 bytes)
Event: 1.018 Thread 0x000002256f7d6da0 nmethod 1109 0x0000022552f7e910 code [0x0000022552f7eb00, 0x0000022552f7f0c0]
Event: 1.019 Thread 0x000002256f7d6da0 1111       3       sun.security.ec.point.ProjectivePoint::<init> (20 bytes)
Event: 1.019 Thread 0x000002256f7d6da0 nmethod 1111 0x0000022552f7f290 code [0x0000022552f7f440, 0x0000022552f7f6d8]
Event: 1.021 Thread 0x000002256f7d6da0 1113       3       sun.security.ec.point.ProjectivePoint$Immutable::<init> (8 bytes)
Event: 1.022 Thread 0x000002256f7d6da0 nmethod 1113 0x0000022552f7f790 code [0x0000022552f7f940, 0x0000022552f7fc30]
Event: 1.022 Thread 0x000002257412dc60 nmethod 1100 0x000002255a8f8590 code [0x000002255a8f8720, 0x000002255a8f89f8]
Event: 1.022 Thread 0x000002257412dc60 1105       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSum (6 bytes)
Event: 1.023 Thread 0x000002256f7d6da0 1116       3       sun.security.ec.point.ProjectivePoint::fixed (35 bytes)
Event: 1.023 Thread 0x000002256f7d6da0 nmethod 1116 0x0000022552f7fd10 code [0x0000022552f7ff00, 0x0000022552f80578]
Event: 1.023 Thread 0x000002256f7d6310 nmethod 1106 0x000002255a8f8b90 code [0x000002255a8f8d40, 0x000002255a8f9058]
Event: 1.023 Thread 0x000002256f7d6310 1084       4       sun.security.util.math.intpoly.IntegerPolynomialP256::carryReduce (454 bytes)
Event: 1.024 Thread 0x000002256f7d6da0 1117       3       sun.security.ec.ECOperations::setSum (549 bytes)
Event: 1.025 Thread 0x000002256f7d6310 nmethod 1084 0x000002255a8f9210 code [0x000002255a8f93a0, 0x000002255a8f9688]
Event: 1.025 Thread 0x000002256f7d6310 1107       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setProduct (58 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.027 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\java.dll
Event: 0.108 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\zip.dll

Deoptimization events (14 events):
Event: 0.338 Thread 0x000002254d435db0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002255a8b2d9c relative=0x000000000000007c
Event: 0.338 Thread 0x000002254d435db0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002255a8b2d9c method=java.lang.CharacterDataLatin1.toLowerCase(I)I @ 16 c2
Event: 0.338 Thread 0x000002254d435db0 DEOPT PACKING pc=0x000002255a8b2d9c sp=0x000000a4969fac60
Event: 0.338 Thread 0x000002254d435db0 DEOPT UNPACKING pc=0x000002255a3546a2 sp=0x000000a4969fabf0 mode 2
Event: 0.471 Thread 0x000002254d435db0 DEOPT PACKING pc=0x0000022552e16507 sp=0x000000a4969fda20
Event: 0.471 Thread 0x000002254d435db0 DEOPT UNPACKING pc=0x000002255a354e42 sp=0x000000a4969fceb8 mode 0
Event: 0.586 Thread 0x000002254d435db0 DEOPT PACKING pc=0x0000022552dea6cc sp=0x000000a4969fd5e0
Event: 0.586 Thread 0x000002254d435db0 DEOPT UNPACKING pc=0x000002255a354e42 sp=0x000000a4969fca00 mode 0
Event: 0.663 Thread 0x000002254d435db0 DEOPT PACKING pc=0x0000022552de7fcc sp=0x000000a4969fb1d0
Event: 0.663 Thread 0x000002254d435db0 DEOPT UNPACKING pc=0x000002255a354e42 sp=0x000000a4969fa638 mode 0
Event: 0.938 Thread 0x00000225740cb650 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002255a8f0a4c relative=0x0000000000000f4c
Event: 0.938 Thread 0x00000225740cb650 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002255a8f0a4c method=java.util.concurrent.ConcurrentHashMap.putVal(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 159 c2
Event: 0.938 Thread 0x00000225740cb650 DEOPT PACKING pc=0x000002255a8f0a4c sp=0x000000a497dfdc00
Event: 0.938 Thread 0x00000225740cb650 DEOPT UNPACKING pc=0x000002255a3546a2 sp=0x000000a497dfdb80 mode 2

Classes loaded (20 events):
Event: 0.993 Loading class sun/security/ssl/XDHKeyExchange$1
Event: 0.993 Loading class sun/security/ssl/XDHKeyExchange$1 done
Event: 0.993 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry
Event: 0.994 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry done
Event: 0.994 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession
Event: 0.994 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession done
Event: 0.995 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384
Event: 0.995 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384 done
Event: 0.995 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521
Event: 0.995 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521 done
Event: 0.995 Loading class sun/security/util/math/intpoly/P384OrderField
Event: 0.996 Loading class sun/security/util/math/intpoly/P384OrderField done
Event: 0.996 Loading class sun/security/util/math/intpoly/P521OrderField
Event: 0.996 Loading class sun/security/util/math/intpoly/P521OrderField done
Event: 0.997 Loading class java/security/interfaces/ECPrivateKey
Event: 0.997 Loading class java/security/interfaces/ECPrivateKey done
Event: 0.998 Loading class sun/security/util/ArrayUtil
Event: 0.998 Loading class sun/security/util/ArrayUtil done
Event: 1.001 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
Event: 1.001 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.549 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b2cf78}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x0000000623b2cf78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.550 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b34588}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623b34588) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.551 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b3b020}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000623b3b020) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.552 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b41ab0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623b41ab0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.552 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b46138}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623b46138) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.563 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b6fcb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623b6fcb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.563 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b73640}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623b73640) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.567 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b82ba8}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623b82ba8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.576 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bb4850}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000623bb4850) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.579 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bbb120}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623bbb120) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.579 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bbe540}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623bbe540) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.727 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006235e5708}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006235e5708) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.772 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237e7af8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006237e7af8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.772 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237eb460}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006237eb460) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.781 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623019ce8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623019ce8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.805 Thread 0x00000225740abbd0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623621940}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623621940) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.813 Thread 0x00000225740abbd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062365b6a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062365b6a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.814 Thread 0x000002254d435db0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230a95c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006230a95c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.885 Thread 0x00000225740cb650 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623119e18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623119e18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.900 Thread 0x00000225740cb650 Exception <a 'java/lang/NoSuchMethodError'{0x000000062316bcd0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062316bcd0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.203 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.203 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.213 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.213 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.454 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.454 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.808 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.808 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.121 Thread 0x000002254d435db0 Thread added: 0x000002256f7d0ad0
Event: 0.121 Thread 0x000002254d435db0 Thread added: 0x000002256f7d2690
Event: 0.121 Thread 0x000002254d435db0 Thread added: 0x000002256f7d30e0
Event: 0.121 Thread 0x000002254d435db0 Thread added: 0x000002256f7d3b30
Event: 0.121 Thread 0x000002254d435db0 Thread added: 0x000002256f7d4580
Event: 0.121 Thread 0x000002254d435db0 Thread added: 0x000002256f7d6310
Event: 0.122 Thread 0x000002254d435db0 Thread added: 0x000002256f7d6da0
Event: 0.164 Thread 0x000002254d435db0 Thread added: 0x000002256f92c3c0
Event: 0.166 Thread 0x000002254d435db0 Thread added: 0x000002256f938630
Event: 0.176 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\net.dll
Event: 0.190 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\nio.dll
Event: 0.197 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\zip.dll
Event: 0.272 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\jimage.dll
Event: 0.634 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\sunmscapi.dll
Event: 0.709 Loaded shared library C:\software2\ideaIU-2*********.win\jbr\bin\extnet.dll
Event: 0.721 Thread 0x000002254d435db0 Thread added: 0x00000225740abbd0
Event: 0.813 Thread 0x00000225740abbd0 Thread added: 0x00000225740cb650
Event: 0.939 Thread 0x00000225740abbd0 Thread added: 0x00000225740f1510
Event: 1.008 Thread 0x000002256f7d6da0 Thread added: 0x00000225741a9e60
Event: 1.010 Thread 0x000002256f7d6da0 Thread added: 0x000002257412dc60


Dynamic libraries:
0x00007ff795e50000 - 0x00007ff795e5a000 	C:\software2\ideaIU-2*********.win\jbr\bin\java.exe
0x00007ff8689b0000 - 0x00007ff868bc7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff867970000 - 0x00007ff867a34000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff865a70000 - 0x00007ff865e43000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff865ff0000 - 0x00007ff866101000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff843ae0000 - 0x00007ff843af8000 	C:\software2\ideaIU-2*********.win\jbr\bin\jli.dll
0x00007ff860ac0000 - 0x00007ff860adb000 	C:\software2\ideaIU-2*********.win\jbr\bin\VCRUNTIME140.dll
0x00007ff867c50000 - 0x00007ff867e01000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8665e0000 - 0x00007ff866606000 	C:\WINDOWS\System32\win32u.dll
0x00007ff867b70000 - 0x00007ff867b99000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff866430000 - 0x00007ff866552000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff850580000 - 0x00007ff85081b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ff866110000 - 0x00007ff8661aa000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff867ba0000 - 0x00007ff867c47000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff868900000 - 0x00007ff868931000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff860ae0000 - 0x00007ff860aec000 	C:\software2\ideaIU-2*********.win\jbr\bin\vcruntime140_1.dll
0x00007ff852900000 - 0x00007ff85298d000 	C:\software2\ideaIU-2*********.win\jbr\bin\msvcp140.dll
0x00007fffa15f0000 - 0x00007fffa23b1000 	C:\software2\ideaIU-2*********.win\jbr\bin\server\jvm.dll
0x00007ff867e10000 - 0x00007ff867ec1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff867a40000 - 0x00007ff867ae7000 	C:\WINDOWS\System32\sechost.dll
0x00007ff865e50000 - 0x00007ff865e78000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff868120000 - 0x00007ff868234000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff867ed0000 - 0x00007ff867f41000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff865380000 - 0x00007ff8653cd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff85e4d0000 - 0x00007ff85e4da000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff850900000 - 0x00007ff850934000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff865360000 - 0x00007ff865373000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff864990000 - 0x00007ff8649a8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff843b10000 - 0x00007ff843b1a000 	C:\software2\ideaIU-2*********.win\jbr\bin\jimage.dll
0x00007ff853290000 - 0x00007ff8534c2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff866610000 - 0x00007ff8669a3000 	C:\WINDOWS\System32\combase.dll
0x00007ff867540000 - 0x00007ff867617000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff83c580000 - 0x00007ff83c5b2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff866560000 - 0x00007ff8665db000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8313b0000 - 0x00007ff8313d0000 	C:\software2\ideaIU-2*********.win\jbr\bin\java.dll
0x00007ff8669b0000 - 0x00007ff86724d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8662f0000 - 0x00007ff86642f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff863700000 - 0x00007ff86401d000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8687f0000 - 0x00007ff8688fb000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff867620000 - 0x00007ff867686000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8659a0000 - 0x00007ff8659cb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff831390000 - 0x00007ff8313a8000 	C:\software2\ideaIU-2*********.win\jbr\bin\zip.dll
0x00007ff843ab0000 - 0x00007ff843ac0000 	C:\software2\ideaIU-2*********.win\jbr\bin\net.dll
0x00007ff8649b0000 - 0x00007ff864adc000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff864f30000 - 0x00007ff864f9a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff831160000 - 0x00007ff831176000 	C:\software2\ideaIU-2*********.win\jbr\bin\nio.dll
0x00007ff8652a0000 - 0x00007ff8652bb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8648f0000 - 0x00007ff864927000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff864fd0000 - 0x00007ff864ff8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff865210000 - 0x00007ff86521c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff864220000 - 0x00007ff86424d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff867480000 - 0x00007ff867489000 	C:\WINDOWS\System32\NSI.dll
0x00007ff82faf0000 - 0x00007ff82fafe000 	C:\software2\ideaIU-2*********.win\jbr\bin\sunmscapi.dll
0x00007ff865e80000 - 0x00007ff865fe7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff865430000 - 0x00007ff86545d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff8653f0000 - 0x00007ff865427000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff800b00000 - 0x00007ff800b08000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff82fa80000 - 0x00007ff82fa89000 	C:\software2\ideaIU-2*********.win\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\software2\ideaIU-2*********.win\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;C:\software2\ideaIU-2*********.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 
java_class_path (initial): C:/software2/ideaIU-2*********.win/plugins/vcs-git/lib/git4idea-rt.jar;C:/software2/ideaIU-2*********.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8518631424                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8518631424                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/software/Git/mingw64/libexec/git-core;C:/software/Git/mingw64/libexec/git-core;C:\software\Git\mingw64\bin;C:\software\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\software\Git\cmd;C:\Program Files\dotnet\;C:\software2\xshell\;C:\Program Files\nodejs\;C:\software2\python3\Scripts\;C:\software2\python3\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\software2\apache-maven-3.5.4\bin;C:\software2\protoc-21.5-win64\bin;C:\software2\python\Scripts;C:\software2\go\bin;C:\software2\windsurf\Windsurf\bin;C:\software2\Microsoft VS Code\bin
USERNAME=yingfeng.fu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 3

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 76288K (0% of 33259564K total physical memory with 1209044K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 6794K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1626K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 1 days 22:38 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0x100, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201

Memory: 4k page, system-wide physical 32480M (1176M free)
TotalPageFile size 93174M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 74M, peak: 74M
current process commit charge ("private bytes"): 617M, peak: 618M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+8-b631.28) for windows-amd64 JRE (21.0.5+8-b631.28), built on 2024-11-23 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
