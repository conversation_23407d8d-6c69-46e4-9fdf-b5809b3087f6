package group.za.bank.statement.service.remote;

import group.za.bank.sbs.trade.model.dto.StkBusinessRecordDTO;
import group.za.bank.sbs.trade.model.dto.TaxQueryDTO;


import java.util.Date;
import java.util.List;

/**
 * 交易服务远程调用
 *
 * <AUTHOR>
 * @date 2023/04/03
 **/
public interface TradeRemoteService {

    /**
     * 根据boOrderNo查询成交记录
     */
    List<StkBusinessRecordDTO> queryStatementBusinessRecordList(String boOrderNo);

    /**
     * 税费查询(金融税收费，adr)
     * @param accountId
     * @param startDate
     * @param endDate
     * @return
     */
    List<TaxQueryDTO> queryTaxList(String accountId, String startDate, String endDate);


    /**
     * 获取结算日期
     * @param marketCode
     * @param tradeDate
     * @param dayDiff
     * @return
     */
    Date getSettlementDate(String marketCode, Date tradeDate, Integer dayDiff);


}
