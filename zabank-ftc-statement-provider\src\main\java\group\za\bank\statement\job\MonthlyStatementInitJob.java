package group.za.bank.statement.job;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.service.MonthlyStatementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @date 2021/8/2 17:07
 * @Version v1.0
 * 月结单初始化
 */
@Slf4j
@JobHandler(value = "monthlyStatementInitJob")
@Component
public class MonthlyStatementInitJob extends IJobHandler {


    @Autowired
    SystemConfig systemConfig;


    @Autowired
    MonthlyStatementService monthlyStatementService;

    /**
     * @Date 2021/8/3 18:04
     * @Returun com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @Version v1.0
     * 每月1号凌晨开始跑，持续跑个7天，等到股票的出来
     * {"period":"202203",businessType:"fund,stock"}
     */
    @Override
    public ReturnT<String> execute(String statementInfo) throws Exception {
        StopWatch stopWatch = new StopWatch();
        log.info("monthlyStatementInitJob start, 入参:{}", statementInfo);
        String period = null;
        try {
            if (Strings.isEmpty(statementInfo)) {
                Date currentDate = new Date();
                Date monthlyStatementDate = DateUtil.getLastDayOfLastMonth(currentDate);
                period = DateUtil.format(monthlyStatementDate, "yyyyMM");
            } else {
                TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = JSONObject.parseObject(statementInfo, TdFundMonthlyStatementInfo.class);
                period = tdFundMonthlyStatementInfo.getPeriod();
            }
            monthlyStatementService.initMonthlyStatements(period);
        } catch (Exception e) {
            log.error("monthlyStatementInitJob error 入参:{}, period:{}", statementInfo, period, e);
        }
        log.info("monthlyStatementInitJob end, 入参:{}, period:{}, cost:{}s", statementInfo, period, stopWatch.getTime(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }


}
