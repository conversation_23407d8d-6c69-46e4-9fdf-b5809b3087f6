package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;

import static group.za.bank.statement.constants.enums.StatementSubBusinessTypeEnum.STOCK;

/**
 * <AUTHOR>
 * @createTime 22 11:18
 * @description
 */
public enum StatementHtmlContentCompareTypeEnum {

    /**
     * 结单
     */
    EQUAL(1),
    START_WITH(2),
    END_WITH(3),
    CONTAINS(4),

    ;

    private Integer type;


    StatementHtmlContentCompareTypeEnum(Integer type) {
        this.type = type;
    }


    public Integer getType() {
        return type;
    }


}
