package group.za.bank.statement.entity.req;

import group.za.bank.invest.basecommon.entity.req.BasePageReq;
import group.za.bank.statement.constants.enums.ChannelEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description
 */
@Data
public class NativeDetailReq extends BasePageReq {


    /**
     * 通道类型，默认：normal，南向通：southBound
     */
    private String channel = ChannelEnum.NORMAL.getValue();

    /**
     * 结单期数 yyyyMM
     */
    private String period;
    /**
     * 结单业务类型
     */
    private String business;


    /**
     * 如果为null，则查询所有的业务
     * fund:基金
     * stock:股票
     */
    private String subBusinessType;

    /**
     * 模块类型
     *
     */
    private String moduleType;

}
