package group.za.bank.statement.service.remote.impl;

import group.za.bank.ccs.core.support.share.entity.dto.DayKlineSupDTO;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.service.remote.feign.CryptoQuotaKlineFeign;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class CryptoQuotaRemoteServiceImplTest {
    @Mock
    CryptoQuotaKlineFeign cryptoQuotaKlineFeign;
    @Mock
    Logger log;
    @InjectMocks
    CryptoQuotaRemoteServiceImpl cryptoQuotaRemoteServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testQueryDayKline() throws Exception {
        DayKlineSupDTO dayKlineSupDTO = new DayKlineSupDTO();
        ResponseData<List<DayKlineSupDTO>> responseData = new ResponseData<>();
        responseData.setValue(Arrays.asList(dayKlineSupDTO));
        responseData.setCode("0000");
        when(cryptoQuotaKlineFeign.queryDayKline(any())).thenReturn(responseData);
        cryptoQuotaRemoteServiceImpl.queryDayKline("assetId", "quotaDate");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme