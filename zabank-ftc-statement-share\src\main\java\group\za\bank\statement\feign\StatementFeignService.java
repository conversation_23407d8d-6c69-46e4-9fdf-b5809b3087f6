package group.za.bank.statement.feign;


import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.entity.dto.MonthlyStatementDto;
import group.za.bank.statement.entity.req.MonthlyStatementConfirmReq;
import group.za.bank.statement.entity.req.PubMonthlyStatementReq;
import group.za.bank.statement.entity.req.UserMonthlyStatementListReq;
import group.za.bank.statement.entity.resp.MonthlyStatementConfirmResp;
import group.za.bank.statement.entity.resp.UserInvestMonthlyStatementListResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date  2021/7/20 20:16
 * @Description 结单接口
 * @Version v1.0
 */
@FeignClient(value = "zabank-ftc-statement-service")
public interface StatementFeignService {

    /**
     * 后管确认推送
     * @param req
     * @return
     */
    @RequestMapping(value = "/inner/statement/invest/pubConfirm", method = RequestMethod.POST)
    ResponseData<MonthlyStatementConfirmResp> pubConfirm(@RequestBody @Valid MonthlyStatementConfirmReq req);



    /**
     * 后管订单列表
     * @param req
     * @return
     */
    @RequestMapping(value = "/inner/statement/invest/userStatementList", method = RequestMethod.POST)
    ResponseData<UserInvestMonthlyStatementListResp> userInvestMonthlyStatementList(@RequestBody @Valid UserMonthlyStatementListReq req);

    /**
     * 查询已发送的月结单记录
     */
    @RequestMapping(value = "/inner/statement/invest/queryPubMonthlyStatement", method = RequestMethod.POST)
    ResponseData<List<MonthlyStatementDto>> queryPubMonthlyStatement(@RequestBody @Valid PubMonthlyStatementReq req);


}
