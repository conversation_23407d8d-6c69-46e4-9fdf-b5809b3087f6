<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdFundMonthlyOrderTmpMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdFundMonthlyOrderTmp">
                        <id column="id" jdbcType="BIGINT"
                            property="id"/>
                        <result column="creator" jdbcType="VARCHAR"
                                property="creator"/>
                        <result column="gmt_created" jdbcType="TIMESTAMP"
                                property="gmtCreated"/>
                        <result column="modifier" jdbcType="VARCHAR"
                                property="modifier"/>
                        <result column="gmt_modified" jdbcType="TIMESTAMP"
                                property="gmtModified"/>
                        <result column="is_deleted" jdbcType="CHAR"
                                property="isDeleted"/>
                        <result column="order_no" jdbcType="VARCHAR"
                                property="orderNo"/>
                        <result column="bank_user_id" jdbcType="VARCHAR"
                                property="bankUserId"/>
                        <result column="bank_account_id" jdbcType="VARCHAR"
                                property="bankAccountId"/>
                        <result column="broker_account_id" jdbcType="VARCHAR"
                                property="brokerAccountId"/>
                        <result column="isin_no" jdbcType="VARCHAR"
                                property="isinNo"/>
                        <result column="currency" jdbcType="VARCHAR"
                                property="currency"/>
                        <result column="product_id" jdbcType="VARCHAR"
                                property="productId"/>
                        <result column="product_name" jdbcType="VARCHAR"
                                property="productName"/>
                        <result column="broker_order_no" jdbcType="VARCHAR"
                                property="brokerOrderNo"/>
                        <result column="business_type" jdbcType="VARCHAR"
                                property="businessType"/>
                        <result column="apply_amt" jdbcType="DECIMAL"
                                property="applyAmt"/>
                        <result column="apply_share" jdbcType="DECIMAL"
                                property="applyShare"/>
                        <result column="confirm_amt" jdbcType="DECIMAL"
                                property="confirmAmt"/>
                        <result column="confirm_share" jdbcType="DECIMAL"
                                property="confirmShare"/>
                        <result column="confirm_net_value" jdbcType="DECIMAL"
                                property="confirmNetValue"/>
                        <result column="confirm_net_value_date" jdbcType="DATE"
                                property="confirmNetValueDate"/>
                        <result column="fee_rate" jdbcType="DECIMAL"
                                property="feeRate"/>
                        <result column="fee" jdbcType="DECIMAL"
                                property="fee"/>
                        <result column="final_fee" jdbcType="DECIMAL"
                                property="finalFee"/>
                        <result column="trade_date" jdbcType="DATE"
                                property="tradeDate"/>
                        <result column="confirm_date" jdbcType="DATE"
                                property="confirmDate"/>
                        <result column="delivery_date" jdbcType="DATE"
                                property="deliveryDate"/>
                        <result column="confirm_date_needed" jdbcType="INTEGER"
                                property="confirmDateNeeded"/>
                        <result column="delivery_date_needed" jdbcType="INTEGER"
                                property="deliveryDateNeeded"/>
                        <result column="real_trade_date" jdbcType="DATE"
                                property="realTradeDate"/>
                        <result column="real_confirm_time" jdbcType="TIMESTAMP"
                                property="realConfirmTime"/>
                        <result column="real_delivery_time" jdbcType="TIMESTAMP"
                                property="realDeliveryTime"/>
                        <result column="allfund_pre_delivery_time" jdbcType="TIMESTAMP"
                                property="allfundPreDeliveryTime"/>
                        <result column="status" jdbcType="TINYINT"
                                property="status"/>
                        <result column="broker_clear_status" jdbcType="VARCHAR"
                                property="brokerClearStatus"/>
                        <result column="activity_flag" jdbcType="VARCHAR"
                                property="activityFlag"/>
                        <result column="apply_fail_reason" jdbcType="VARCHAR"
                                property="applyFailReason"/>
                        <result column="broker_order_status" jdbcType="VARCHAR"
                                property="brokerOrderStatus"/>
                        <result column="channel" jdbcType="VARCHAR"
                                property="channel"/>
                        <result column="apply_time" jdbcType="TIMESTAMP"
                                property="applyTime"/>
                        <result column="update_time" jdbcType="TIMESTAMP"
                                property="updateTime"/>
                        <result column="version" jdbcType="INTEGER"
                                property="version"/>
                        <result column="remark" jdbcType="VARCHAR"
                                property="remark"/>
            </resultMap>

            <!-- 通用查询结果列 -->
            <sql id="Base_Column_List">
                id, order_no, bank_user_id, bank_account_id, broker_account_id, isin_no, currency, product_id, product_name, broker_order_no, business_type, apply_amt, apply_share, confirm_amt, confirm_share, confirm_net_value, confirm_net_value_date, fee_rate, fee, final_fee, trade_date, confirm_date, delivery_date, confirm_date_needed, delivery_date_needed, real_trade_date, real_confirm_time, real_delivery_time, allfund_pre_delivery_time, `status`, broker_clear_status, activity_flag, apply_fail_reason, broker_order_status, channel, apply_time, update_time, version, remark, creator, gmt_created, modifier, gmt_modified, is_deleted
            </sql>

            <!-- 自定义通用SQL查询条件 -->
            <sql id="Where_Extra_Condition">
            </sql>

    <select id="countMonthlyStatementFundAccount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            `td_fund_monthly_order_tmp` t
        WHERE t.`is_deleted` = 'N'
          AND t.bank_user_id = #{bankUserId}
          AND t.bank_account_id = #{bankAccountId}
    </select>

    <select id="queryFundAccountMonthlyStatementConfirmedOrderList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_order_tmp` t
        WHERE t.`is_deleted` = 'N'
        AND t.`bank_account_id` = #{bankAccountId}
        AND t.bank_user_id = #{bankUserId}
        AND t.`business_type` in ('10','11')
        AND
        (
        t.`status` IN (41,50,60,61)
        <![CDATA[ AND t.`real_confirm_time`>= #{monthStartDate} ]]>
        <![CDATA[ AND t.`real_confirm_time`<= #{monthEndDate} ]]>
        )
        ORDER BY t.`real_confirm_time`,t.`apply_time`,t.id
    </select>


    <select id="queryFundAccountMonthlyStatementPendingOrderList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_order_tmp` t
        WHERE t.`status` IN (11,20,30)
        AND t.`business_type` in ('10','11')
        AND t.`is_deleted` = 'N'
        AND t.bank_user_id = #{bankUserId}
        AND t.`bank_account_id` = #{bankAccountId}
        <![CDATA[ AND t.`trade_date`>= #{monthStartDate} ]]>
        <![CDATA[ AND t.`trade_date`<= #{monthEndDate} ]]>
        ORDER BY t.apply_time,t.id
    </select>


    <select id="queryFundAccountMonthlyStatementDividendOrderList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_order_tmp` t
        WHERE t.`status` IN (61)
        AND t.`business_type` = '12'
        AND t.`is_deleted` = 'N'
        AND t.bank_user_id = #{bankUserId}
        AND t.`bank_account_id` = #{bankAccountId}
        <![CDATA[ AND t.`real_delivery_time`>= #{monthStartDate} ]]>
        <![CDATA[ AND t.`real_delivery_time`<= #{monthEndDate} ]]>
        ORDER BY t.`real_delivery_time`,t.id
    </select>


    <select id="queryFundAccountMonthlyStatementDepositAndWithdrawalList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_order_tmp` t
        WHERE t.`status` IN (61)
        AND t.`business_type` in ('20','21')
        AND t.`is_deleted` = 'N'
        AND t.bank_user_id = #{bankUserId}
        AND t.`bank_account_id` = #{bankAccountId}
        <![CDATA[ AND t.`real_confirm_time`>= #{monthStartDate} ]]>
        <![CDATA[ AND t.`real_confirm_time`<= #{monthEndDate} ]]>
        ORDER BY t.`real_confirm_time`,t.id
    </select>


    <delete id="deleteTdFundMonthlyOrderTmp" >
        delete from td_fund_monthly_order_tmp
    </delete>

    <select id="existsOrder" resultType="java.lang.Boolean">
        SELECT count(1)
        FROM `td_fund_monthly_order_tmp`
        limit 1
    </select>

    <!--activity查询用户月订单-->
    <select id="queryUserBuyOrder" resultMap="BaseResultMap">
        SELECT
            product_id,bank_user_id,bank_account_id,trade_date,order_no,apply_amt,fee_rate,currency
        FROM
            td_fund_monthly_order_tmp
        WHERE is_deleted = 'N'
          and bank_user_id = #{bankUserId}
          and bank_account_id = #{bankAccountId}
          and business_type = 10
          and status in(11,50,61)
    </select>

    </mapper>
