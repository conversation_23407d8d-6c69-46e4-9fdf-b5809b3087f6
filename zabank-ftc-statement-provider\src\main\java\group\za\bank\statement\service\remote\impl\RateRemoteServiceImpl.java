package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.bankfront.common.enums.RateSourceEnum;
import group.za.bank.sbs.bankfront.model.req.QueryRateListReq;
import group.za.bank.sbs.bankfront.model.req.QueryRateReq;
import group.za.bank.sbs.bankfront.model.resp.QueryRateListResp;
import group.za.bank.sbs.bankfront.model.resp.QueryRateResp;
import group.za.bank.sbs.trade.model.req.feign.StkRateInfoHisReq;
import group.za.bank.sbs.trade.model.resp.feign.StkRateInfoHisResp;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.RateRemoteService;
import group.za.bank.statement.service.remote.feign.HistoryRateFeign;
import group.za.bank.statement.service.remote.feign.RateFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 汇率相关
 * <AUTHOR>
 * @date 2022/05/12
 **/
@Slf4j
@Service
public class RateRemoteServiceImpl implements RateRemoteService {

    @Autowired
    private RateFeign rateFeign;

    @Autowired
    private HistoryRateFeign historyRateFeign;

    @Override
    public QueryRateResp queryRate(String sourceCcy, String targetCcy) {
        QueryRateReq queryRateReq = new QueryRateReq();
        //先默认银行核心
        queryRateReq.setRateSource(RateSourceEnum.ZA_BANK_CORE.getValue());
        queryRateReq.setSourceCcy(sourceCcy);
        queryRateReq.setTargetCcy(targetCcy);
        QueryRateListReq queryRateListReq = new QueryRateListReq();
        queryRateListReq.setQueryRateReqList(Arrays.asList(queryRateReq));
        ResponseData<QueryRateListResp> rateListResp = rateFeign.getRateList(queryRateListReq);
        if(!rateListResp.judgeSuccess()){
            log.error("查询汇率信息异常,source:{},target:{},{}", sourceCcy, targetCcy, rateListResp.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.RATE_QUERY_ERROR);
        }
        return rateListResp.getValue().getQueryRateRespList().get(0);
    }

    @Override
    public StkRateInfoHisResp queryRateHis(StkRateInfoHisReq req) {
        ResponseData<StkRateInfoHisResp> rateInfoHis = historyRateFeign.getRateInfoHis(req);
        if (!rateInfoHis.judgeSuccess()) {
            log.info("历史汇率查询失败：{}", rateInfoHis.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.RATE_QUERY_ERROR);
        }
        StkRateInfoHisResp stkRateInfoHisResp = rateInfoHis.getValue();
        if(null == stkRateInfoHisResp){
            log.info("历史汇率查询为空，source:{},target:{},date:{}", req.getSourceCcy(), req.getTargetCcy(), req.getTradeDate());
            throw new BusinessException(StatementErrorMsgEnum.RATE_QUERY_ERROR);
        }
        return stkRateInfoHisResp;
    }

    @Override
    public StkRateInfoHisResp getLatestHisRateInfo(StkRateInfoHisReq req) {
        ResponseData<StkRateInfoHisResp> rateInfoHis = historyRateFeign.getLatestHisRateInfo(req);
        if (!rateInfoHis.judgeSuccess()) {
            log.info("查询最新一条历史汇率失败：{}", rateInfoHis.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.RATE_QUERY_ERROR);
        }
        StkRateInfoHisResp stkRateInfoHisResp = rateInfoHis.getValue();
        if(null == stkRateInfoHisResp){
            log.info("查询最新一条历史汇率为空，source:{},target:{},date:{}", req.getSourceCcy(), req.getTargetCcy(), req.getTradeDate());
            throw new BusinessException(StatementErrorMsgEnum.RATE_QUERY_ERROR);
        }
        return stkRateInfoHisResp;
    }
}
