# 新增
# 销户月结单邮件接收人
close.monthly.statement.emailList=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

# 新增sbs数据源，配置对应从库即可
# sbs-trade
zainvest.sharding.group.sbsTrade-group.beanNamePrefix = sbsTrade
zainvest.sharding.group.sbsTrade-group.mybatis.mapper-locations[0] = classpath*:mapper/*.xml
zainvest.sharding.group.sbsTrade-group.mybatis.mapper-scanner.base-package = group.za.bank.sbs.trade.mapper
zainvest.sharding.group.sbsTrade-group.databases[0].ip = 待运维补充
zainvest.sharding.group.sbsTrade-group.databases[0].port = 待运维补充
zainvest.sharding.group.sbsTrade-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.sbsTrade-group.databases[0].userName = za_bank_stock_trade
zainvest.sharding.group.sbsTrade-group.databases[0].password = 待运维补充
zainvest.sharding.group.sbsTrade-group.databases[0].dbName = za_bank_stock_trade

# sbs-cash
zainvest.sharding.group.sbsCash-group.beanNamePrefix = sbsCash
zainvest.sharding.group.sbsCash-group.mybatis.mapper-locations[0] = classpath*:mapper/*.xml
zainvest.sharding.group.sbsCash-group.mybatis.mapper-scanner.base-package = group.za.bank.sbs.bankfront.mapper
zainvest.sharding.group.sbsCash-group.databases[0].ip = 待运维补充
zainvest.sharding.group.sbsCash-group.databases[0].port = 待运维补充
zainvest.sharding.group.sbsCash-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.sbsCash-group.databases[0].userName = za_bank_stock_cash
zainvest.sharding.group.sbsCash-group.databases[0].password = 待运维补充
zainvest.sharding.group.sbsCash-group.databases[0].dbName = za_bank_stock_cash