package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;

/**
 * 港股成交单据字段枚举
 * <AUTHOR>
 * @date 2022/04/27
 **/
@AllArgsConstructor
public enum FeeChargeType implements BaseEnum {
    /** 按金额收费 **/
    AMOUNT("A","按金额收费"),
    /** 按成交股数收费 **/
    SHARE("S","按成交股数收费")
    ;


    private String value;

    private String msg;


    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
