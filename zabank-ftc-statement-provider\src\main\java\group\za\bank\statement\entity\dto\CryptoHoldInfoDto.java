package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/08/28
 **/
@Data
public class CryptoHoldInfoDto {

    private String assetId;

    private String assetNameCn;

    private String assetNameHk;

    private String assetNameEn;

    private String exchangeCode;

    private String currency;

    private BigDecimal openVolume;

    private BigDecimal closeVolume;

    private BigDecimal closePrice;
}
