package group.za.bank.statement.utils;

import org.junit.Test;

import java.math.BigDecimal;

public class StockStatementNumberFormatUtilsTest {

    @Test
    public void testStatementQtyFormat() throws Exception {
        String result = StockStatementNumberFormatUtils.statementQtyFormat("1", "emptyValue");
    }

    @Test
    public void testStatementQtyFormat2() throws Exception {
        String result = StockStatementNumberFormatUtils.statementQtyFormat(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testStatementQtyFractionFormat() throws Exception {
        String result = StockStatementNumberFormatUtils.statementQtyFractionFormat(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testStatementShareFormat() throws Exception {
        String result = StockStatementNumberFormatUtils.statementShareFormat("1", "emptyValue");
    }

    @Test
    public void testStatementShareFormat2() throws Exception {
        String result = StockStatementNumberFormatUtils.statementShareFormat(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testStatementMoneyFormat() throws Exception {
        String result = StockStatementNumberFormatUtils.statementMoneyFormat("1", "emptyValue");
    }

    @Test
    public void testStatementMoneyFormatOfRoundDown() throws Exception {
        String result = StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown("1", "emptyValue");
    }

    @Test
    public void testStatementMoneyFormat2() throws Exception {
        String result = StockStatementNumberFormatUtils.statementMoneyFormat(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testStatementMoneyFormatOfRoundDown2() throws Exception {
        String result = StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testStatementPriceFormat() throws Exception {
        String result = StockStatementNumberFormatUtils.statementPriceFormat("1", "emptyValue");
    }

    @Test
    public void testStatementPriceFormat2() throws Exception {
        String result = StockStatementNumberFormatUtils.statementPriceFormat(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testStatement4RoundDownFormat() throws Exception {
        String result = StockStatementNumberFormatUtils.statement4RoundDownFormat(new BigDecimal(0), "emptyValue");
    }

    @Test
    public void testFormatCommaStyleStripTrailingZeros() throws Exception {
        String result = StockStatementNumberFormatUtils.formatCommaStyleStripTrailingZeros(new BigDecimal(0), "emptyValue");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme