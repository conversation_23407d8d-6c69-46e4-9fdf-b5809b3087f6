package group.za.bank.statement.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.Maps;
import feign.Response;
import group.za.bank.fund.trade.constants.enums.FundMonthlyStatementPubStatusEnum;
import group.za.bank.invest.account.constants.enums.UserInvestAccountStatusEnum;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.domain.account.entity.UserInvestAccount;
import group.za.bank.invest.domain.account.entity.ext.UserInvestAccountConditionHolder;
import group.za.bank.invest.domain.account.mapper.UserInvestAccountMapper;
import group.za.bank.invest.pub.constants.enums.RevIdType;
import group.za.bank.invest.pub.entity.dto.FileStoreType;
import group.za.bank.invest.pub.entity.req.FileDownloadReq;
import group.za.bank.invest.pub.entity.req.SendMessageReq;
import group.za.bank.invest.pub.entity.resp.SendMessageResp;
import group.za.bank.invest.pub.feign.FileService;
import group.za.bank.invest.pub.feign.MessageFeignService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.enums.MonthlyStatementInfoStatusEnum;
import group.za.bank.statement.constants.enums.StatementMsgTemplateCodeEnum;
import group.za.bank.statement.domain.entity.TdCloseAccountMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.mapper.TdCloseAccountMonthlyStatementMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.entity.dto.ByteArrayMultipartFile;
import group.za.bank.statement.service.CloseMonthlyStatementService;
import group.za.bank.statement.service.remote.FileRemoteService;
import group.za.bank.statement.utils.FileStringUtil;
import group.za.bank.statement.utils.JarDirConfig;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CloseMonthlyStatementServiceImpl implements CloseMonthlyStatementService {

    @Resource
    private TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;

    @Resource
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;

    @Resource
    private TdCloseAccountMonthlyStatementMapper tdCloseAccountMonthlyStatementMapper;

    @Resource
    private UserInvestAccountMapper userInvestAccountMapper;

    @Resource
    private SystemConfig systemConfig;

    @Resource(name = "statementExecutor")
    private ThreadPoolTaskExecutor statementExecutor;

    @Resource
    private FileService fileService;

    @Resource
    private MessageFeignService messageFeignService;

    @Resource
    private FileRemoteService fileRemoteService;

    /**
     * 月结单期数格式
     */
    private static final String MONTHLY_STATEMENT_PERIOD_FORMAT = "yyyyMM";

    /**
     * 月结单pdf文件url
     */
    private static final String MONTHLY_STATEMENT_FILE_URL = "statement/";

    /**
     * 月结单压缩文件
     */
    private static final String MONTHLY_STATEMENT_ZIP_FILE_URL = "zip_file/";

    @Override
    public void generateCloseMonthlyStatement(String period) {

        TdFundMonthlyStatementInfo condition = new TdFundMonthlyStatementInfo();
        condition.setPeriod(period);
        condition.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = tdMonthlyStatementInfoMapper.selectOne(condition);
        //1、查询期数period= '202211'  总表td_fund_monthly_statement_info表 status 状态 ：2（本期月结单已完成）有数据执行第二步，无数据结束流程
        if (tdFundMonthlyStatementInfo == null){
            log.info("period={} 此期月结单全流程任务还未完成，销户月结单任务不进行，请等待",period);
            return;
        }
        //2、查询 条件如 period= '202211' td_close_account_monthly_statement 表 。
        TdCloseAccountMonthlyStatement conditionCloseAccountMonthly = new TdCloseAccountMonthlyStatement();
        conditionCloseAccountMonthly.setPeriod(period);
        int count = tdCloseAccountMonthlyStatementMapper.selectCount(conditionCloseAccountMonthly);
        log.info("period={} 此期销户月结单pdf总条数={}",period,count);
        //条数大于0，查询是否存在未发送完的销户月结单数据
        if (count > 0){
            conditionCloseAccountMonthly.setSendStatus(FundMonthlyStatementPubStatusEnum.PROCESSING.getDbValue());
            int processingCount = tdCloseAccountMonthlyStatementMapper.selectCount(conditionCloseAccountMonthly);
            log.info("period={} 此期销户月结单pdf 待发送条数={}",period,processingCount);
            if (processingCount == 0){
                log.info("period={} 此期销户月结单任务已全部发送完，无效重复操作",period);
                return;
            }else {
                //物理删除，销户月结单全部重新开始下载压缩发送pdf
                tdCloseAccountMonthlyStatementMapper.deleteCloseAccountMonthlyStatement(period);
            }
        }

        //3、一次性查所有销户数据 ，无数据结束流程
        prepareData(period);

        //4、检查
        conditionCloseAccountMonthly.setSendStatus(FundMonthlyStatementPubStatusEnum.PROCESSING.getDbValue());
        int cnt = tdCloseAccountMonthlyStatementMapper.selectCount(conditionCloseAccountMonthly);
        if (cnt == 0){
            log.info("period={} 此期销户月结单任务校验查询无pdf，结束流程",period);
            return;
        }
        //5、下载 和压缩文件,发送
        boolean result = sendCloseAccountMonthlyStatement(period);
        log.info("period={}期数，结果result = {}",period,result);

    }

    boolean sendCloseAccountMonthlyStatement(String period){
        log.info("period={}销户月结单数据 下载 和压缩文件,发送开始",period);
        long id = 0L;
        int pageSize = 20;
        List<TdCloseAccountMonthlyStatement> tdCloseAccountMonthlyStatementList = null;
        List<Future> futureList = new ArrayList<>();
        while(true){
            tdCloseAccountMonthlyStatementList = tdCloseAccountMonthlyStatementMapper.queryFundCloseAccountMonthlyStatement(period,pageSize,id);
            if (CollectionUtils.isEmpty(tdCloseAccountMonthlyStatementList)){
                log.info("period={}无数据结束,pageSize={},id={}循环结束",period,pageSize,id);
                break;
            }
            id = tdCloseAccountMonthlyStatementList.get(tdCloseAccountMonthlyStatementList.size() - 1).getId();

            for (TdCloseAccountMonthlyStatement tdCloseAccountMonthlyStatement : tdCloseAccountMonthlyStatementList) {
                //202211_A00003921_CN_hk.pdf
                String pdfName = tdCloseAccountMonthlyStatement.getPeriod()+"_"
                        +tdCloseAccountMonthlyStatement.getClientId()+"_"
                        +tdCloseAccountMonthlyStatement.getDocLang()+".pdf";
                String pdfFile = JarDirConfig.getAbsolutePath("") + MONTHLY_STATEMENT_FILE_URL + tdCloseAccountMonthlyStatement.getPeriod() + "/" + pdfName;

                File fileTem = new File(pdfFile);
                if (!fileTem.getParentFile().exists()) {
                    //创建上级目录
                    fileTem.getParentFile().mkdirs();
                }
                futureList.add(statementExecutor.submit(() -> {
                    FileDownloadReq fileDownloadReq = new FileDownloadReq();
                    fileDownloadReq.setObjectKey(tdCloseAccountMonthlyStatement.getDocUrl());
                    fileDownloadReq.setBankUserId(tdCloseAccountMonthlyStatement.getBankUserId());
                    try(Response resp = fileService.fileDownload(fileDownloadReq);
                        InputStream inputStream  = resp.body().asInputStream()) {

                        FileUtils.copyInputStreamToFile(inputStream,fileTem);
                    } catch (Exception e) {
                        log.error("period={},bankUserId={}",period,tdCloseAccountMonthlyStatement.getBankUserId(),e);
                        throw new RuntimeException("销户月结单下载文件失败");
                    }
                }));
            }
        }

        /**
         * 确保所有子线程任务都完成才返回
         */
        confirmThreadFinished(futureList);

        //开始压缩文件
        String path = JarDirConfig.getAbsolutePath("") +MONTHLY_STATEMENT_FILE_URL+period;
        String zipPath = JarDirConfig.getAbsolutePath("") + MONTHLY_STATEMENT_ZIP_FILE_URL + period + ".zip";
        ZipUtil.zip(path, zipPath);


        byte[] byteExcel = FileStringUtil.fileByteString(zipPath);
        String zipFileName = period + "投资销户月结单.zip";
        // 上传obs 拿到obsKey
        ByteArrayMultipartFile firstFile =
                new ByteArrayMultipartFile("files", zipFileName, MediaType.MULTIPART_FORM_DATA_VALUE, byteExcel);
        String obsKey = fileRemoteService.singleFile(firstFile, FileStoreType.HW_BANK_OBS);
        log.info("期数={}，销户月结单obsKey={}",period,obsKey);
        if (StringUtils.isBlank(obsKey)) {
            log.error("期数={}，销户月结单obsKey为null",period);
            return false;
        }

        //默认简体中文
        String lang = I18nSupportEnum.CN_ZH.getName();
        String templateCode = StatementMsgTemplateCodeEnum.INVEST_CLOSE_MONTHLY_STATEMENT.getTemplateCode();

        List<String> emailList = systemConfig.getStatementEmailList();
        if (CollectionUtils.isEmpty(emailList)){
            log.error("销户月结单发送人员邮箱名单未配置，请关注！");
            return false;
        }
        log.info("销户月结单发送人员邮箱名单emailList={}", JSON.toJSONString(emailList));
        for (String email: emailList) {
            log.info("forTemplateCode={}:email={}",templateCode, email);
            sendFormsMessage(templateCode,lang,obsKey,email);
        }

        TdCloseAccountMonthlyStatement updateClose = new TdCloseAccountMonthlyStatement();
        updateClose.setPubTime(new Date());
        updateClose.setGmtModified(new Date());
        updateClose.setSendStatus(FundMonthlyStatementPubStatusEnum.FINISHED.getDbValue());

        TdCloseAccountMonthlyStatement conditionClose = new TdCloseAccountMonthlyStatement();
        conditionClose.setPeriod(period);
        conditionClose.setSendStatus(FundMonthlyStatementPubStatusEnum.PROCESSING.getDbValue());

        tdCloseAccountMonthlyStatementMapper.updateByCondition(updateClose,conditionClose);
        return true;
    }

    private boolean sendFormsMessage(String templateCode,String lang, String obsKey,String email) {
        Map<String, Object> paramMap = Maps.newHashMap();
        SendMessageReq req = new SendMessageReq();
        req.setRequestId(UUID.randomUUID().toString().substring(0, 30));
        req.setTemplateCode(templateCode);
        req.setRevId(email);
        req.setRevIdType(RevIdType.EMAIL.getCode());
        req.setContent(JsonUtils.toJsonString(paramMap));
        req.setLang(lang);
        req.setSysSrc(StatementConstants.SYSTEM_ID);
        List<String> obsKeyList = new ArrayList<>();
        obsKeyList.add(obsKey);
        req.setObsKeyList(obsKeyList);

        ResponseData<SendMessageResp> responseData = null;
        try{
            responseData = messageFeignService.sendMessage(req);
        }catch (Exception e){
            log.error("messageFeignService_sendMessage_error",e);
        }
        if (responseData == null){
            log.info("发送销户月结单远程调用feign返回为空 req={}", JsonUtils.toJsonString(req));
            return false;
        }
        if (!responseData.judgeSuccess()) {
            log.error("发送销户月结单部分失败 req={} resp={}", JsonUtils.toJsonString(req), JsonUtils.toJsonString(responseData));
            return false;
        }
        log.info("发送销户月结单通知成功。 templateCode={} req={}。", req.getTemplateCode(), JsonUtils.toJsonString(req));
        return true;
    }

    void  prepareData(String period){
        log.info("period={}销户月结单数据准备开始",period);
        UserInvestAccountConditionHolder conditionAcc = new UserInvestAccountConditionHolder();
        conditionAcc.setAccountStatus(UserInvestAccountStatusEnum.CLOSED.getValue());

        Date periodDate = DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT);
        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(periodDate));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(periodDate));

        conditionAcc.setCloseStartTime(monthStartDate);
        conditionAcc.setCloseEndTime(monthEndDate);

        List<UserInvestAccount> userInvestAccountList = userInvestAccountMapper.queryAllUserInvestAccountByCondition(conditionAcc);
        if (CollectionUtils.isEmpty(userInvestAccountList)) {
            log.info("period={}无数据结束",period);
            return;
        }
        List<String> clientIdList = userInvestAccountList.stream().distinct().map(UserInvestAccount::getClientId).collect(Collectors.toList());
        log.info("period={}此期，clientIdList={}",period, JsonUtils.toJsonString(clientIdList));

        long id = 0L;
        int pageSize = 20;
        List<TdFundMonthlyStatement> tdFundMonthlyStatementList = null;
        while(true){
            tdFundMonthlyStatementList = tdMonthlyStatementMapper.queryStatementByPeriodAndClientId(period,clientIdList,pageSize,id);
            if (CollectionUtils.isEmpty(tdFundMonthlyStatementList)){
                log.info("period={}此期，clientIdList账户={}，无月结单pdf",period, JsonUtils.toJsonString(clientIdList));
                break;
            }

            id = tdFundMonthlyStatementList.get(tdFundMonthlyStatementList.size() - 1).getId();

            List<TdCloseAccountMonthlyStatement> tdCloseAccountMonthlyStatementList = new ArrayList<>();
            for (TdFundMonthlyStatement tdFundMonthlyStatement : tdFundMonthlyStatementList) {
                TdCloseAccountMonthlyStatement insertCloseAccountMonthlyStatement = new TdCloseAccountMonthlyStatement();

                insertCloseAccountMonthlyStatement.setSendStatus(FundMonthlyStatementPubStatusEnum.PROCESSING.getDbValue());
                insertCloseAccountMonthlyStatement.setPeriod(period);
                insertCloseAccountMonthlyStatement.setBankUserId(tdFundMonthlyStatement.getBankUserId());
                insertCloseAccountMonthlyStatement.setClientId(tdFundMonthlyStatement.getClientId());
                insertCloseAccountMonthlyStatement.setBusinessId(tdFundMonthlyStatement.getBusinessId());
                insertCloseAccountMonthlyStatement.setDocLang(tdFundMonthlyStatement.getDocLang());
                insertCloseAccountMonthlyStatement.setTempKey(tdFundMonthlyStatement.getTempKey());
                insertCloseAccountMonthlyStatement.setDocUrl(tdFundMonthlyStatement.getDocUrl());
                tdCloseAccountMonthlyStatementList.add(insertCloseAccountMonthlyStatement);
            }
            if (!CollectionUtils.isEmpty(tdCloseAccountMonthlyStatementList)){
                tdCloseAccountMonthlyStatementMapper.insertBatch(tdCloseAccountMonthlyStatementList);
            }
        }

        log.info("period={}销户月结单数据准备结束",period);
    }

    private static void confirmThreadFinished(List<Future> futureList) {
        for (Future future : futureList) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("future线程任务异常人工排查！",e);
                throw new RuntimeException("future线程任务异常人工排查！");
            }
        }
    }
}