package group.za.bank.statement.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FileStringUtil {

    public static byte[] fileByteString(String path) {
        File file = new File(path);
        FileInputStream fis = null;
        byte[] bytes = null;
        try {
            fis = new FileInputStream(file);
            bytes = IOUtils.toByteArray(fis);
        } catch (FileNotFoundException e) {
            log.error("FileNotFound异常", e);
        } catch (IOException e) {
            log.error("IO流异常", e);
        } finally {
            try {
                if (fis != null){
                    fis.close();
                }
            } catch (IOException e) {
                log.error("流关闭异常", e);
            }
        }
        return bytes;
    }

    public static void deleteFile(String path) {
        File file = new File(path);
        if(file.exists()){
            file.delete();
        }
    }


}
