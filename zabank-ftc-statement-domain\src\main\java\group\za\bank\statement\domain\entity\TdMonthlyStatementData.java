package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * This class corresponds to the database table td_fund_monthly_statement
 * <p>
 * Database Table Remarks:
 * </p>
 *
 * <AUTHOR>
 * @since 2021年08月24日 11:36:52
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_monthly_statement_data")
public class TdMonthlyStatementData extends AuditIdEntity<Long> {
    private static final long serialVersionUID = 1L;
    /**
     * Database Column Name: td_fund_monthly_statement.business_id
     * <p>
     * Database Column Remarks: 业务逻辑id
     * </p>
     */
    @Column(name = "business_id")
    private String businessId;
    /**
     * Database Column Name: td_fund_monthly_statement.business_id
     * <p>
     * Database Column Remarks: 业务逻辑id
     * </p>
     */
    @Column(name = "statement_id")
    private String statementId;


    /**
     * Database Column Name: td_fund_monthly_statement.account_id
     * <p>
     * Database Column Remarks: 账户id
     * </p>
     */
    @Column(name = "account_id")
    private String accountId;


    /**
     * Database Column Name: td_fund_monthly_statement.business_type
     * <p>
     * Database Column Remarks: 业务类型:fund-基金,stock-股票
     * </p>
     */
    @Column(name = "business_type")
    private String businessType;




    /**
     * Database Column Name: td_fund_monthly_statement.doc_status
     * <p>
     * Database Column Remarks: 数据状态:0-待生成,1-已生成，2-无数据
     * </p>
     */
    @Column(name = "data_status")
    private Byte dataStatus;



    /**
     * Database Column Name: td_fund_monthly_statement.record_time
     * <p>
     * Database Column Remarks: 结单记录生成时间
     * </p>
     */
    @Column(name = "record_time")
    private Date recordTime;

    /**
     * Database Column Name: td_fund_monthly_statement.remark
     * <p>
     * Database Column Remarks: 备注
     * </p>
     */
    @Column(name = "remark")
    private String remark;


}