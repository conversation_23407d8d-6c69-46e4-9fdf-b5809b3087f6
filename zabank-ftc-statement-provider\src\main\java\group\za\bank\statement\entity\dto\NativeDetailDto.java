package group.za.bank.statement.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description
 */
@Data
public class NativeDetailDto {



    /**
     * Investment account position 投資帳戶持倉
     */
    private NativeBusinessDto holding;

    /**
     * Confirmed Transaction 已確認交易
     */
    private NativeBusinessDto confirmedOrder;

    /**
     * Fund Pending Transaction 基金處理中交易
     */
    private NativeBusinessDto pendingOrder;

    /**
     * Dividend 派息
     */
    private NativeBusinessDto dividendOrder;

    /**
     * Deposit & Withdrawal 基金存入及提取
     * Holdings Movement 股票持倉變動
     */
    private NativeBusinessDto holdingChange;

}
