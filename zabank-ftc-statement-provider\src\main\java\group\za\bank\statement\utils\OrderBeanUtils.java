package group.za.bank.statement.utils;

import group.za.bank.fund.domain.trade.entity.TdFundOrder;
import group.za.bank.statement.domain.entity.TdFundMonthlyOrderTmp;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/3 11:40
 * @Version 1.0
 **/
public class OrderBeanUtils {

    /**
     * <AUTHOR>
     * @Date 2022/11/3 10:47
     * @Description 复制不包含以下字段：creator、gmt_created、modifier、gmt_modified
     * @Version v1.0
     */
    public static TdFundMonthlyOrderTmp copyTdFundMonthlyOrderTmp(TdFundOrder tdFundOrder){
        TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp = new TdFundMonthlyOrderTmp();
        tdFundMonthlyOrderTmp.setOrderNo(tdFundOrder.getOrderNo());
        tdFundMonthlyOrderTmp.setBankUserId(tdFundOrder.getBankUserId());
        tdFundMonthlyOrderTmp.setBankAccountId(tdFundOrder.getBankAccountId());
        tdFundMonthlyOrderTmp.setBrokerAccountId(tdFundOrder.getBrokerAccountId());
        tdFundMonthlyOrderTmp.setIsinNo(tdFundOrder.getIsinNo());
        tdFundMonthlyOrderTmp.setCurrency(tdFundOrder.getCurrency());
        tdFundMonthlyOrderTmp.setProductId(tdFundOrder.getProductId());
        tdFundMonthlyOrderTmp.setProductName(tdFundOrder.getProductName());
        tdFundMonthlyOrderTmp.setBrokerOrderNo(tdFundOrder.getBrokerOrderNo());
        tdFundMonthlyOrderTmp.setBusinessType(tdFundOrder.getBusinessType());
        tdFundMonthlyOrderTmp.setApplyAmt(tdFundOrder.getApplyAmt());
        tdFundMonthlyOrderTmp.setApplyShare(tdFundOrder.getApplyShare());
        tdFundMonthlyOrderTmp.setConfirmAmt(tdFundOrder.getConfirmAmt());
        tdFundMonthlyOrderTmp.setConfirmShare(tdFundOrder.getConfirmShare());
        tdFundMonthlyOrderTmp.setConfirmNetValue(tdFundOrder.getConfirmNetValue());
        tdFundMonthlyOrderTmp.setConfirmNetValueDate(tdFundOrder.getConfirmNetValueDate());
        tdFundMonthlyOrderTmp.setFeeRate(tdFundOrder.getFeeRate());
        tdFundMonthlyOrderTmp.setFee(tdFundOrder.getFee());
        tdFundMonthlyOrderTmp.setFinalFee(tdFundOrder.getFinalFee());
        tdFundMonthlyOrderTmp.setTradeDate(tdFundOrder.getTradeDate());
        tdFundMonthlyOrderTmp.setConfirmDate(tdFundOrder.getConfirmDate());
        tdFundMonthlyOrderTmp.setDeliveryDate(tdFundOrder.getDeliveryDate());
        tdFundMonthlyOrderTmp.setConfirmDateNeeded(tdFundOrder.getConfirmDateNeeded());
        tdFundMonthlyOrderTmp.setDeliveryDateNeeded(tdFundOrder.getDeliveryDateNeeded());
        tdFundMonthlyOrderTmp.setRealTradeDate(tdFundOrder.getRealTradeDate());
        tdFundMonthlyOrderTmp.setRealConfirmTime(tdFundOrder.getRealConfirmTime());
        tdFundMonthlyOrderTmp.setRealDeliveryTime(tdFundOrder.getRealDeliveryTime());
        tdFundMonthlyOrderTmp.setAllfundPreDeliveryTime(tdFundOrder.getAllfundPreDeliveryTime());
        tdFundMonthlyOrderTmp.setStatus(tdFundOrder.getStatus());
        tdFundMonthlyOrderTmp.setBrokerClearStatus(tdFundOrder.getBrokerClearStatus());
        tdFundMonthlyOrderTmp.setApplyFailReason(tdFundOrder.getApplyFailReason());
        tdFundMonthlyOrderTmp.setBrokerOrderStatus(tdFundOrder.getBrokerOrderStatus());
        tdFundMonthlyOrderTmp.setChannel(tdFundOrder.getChannel());
        tdFundMonthlyOrderTmp.setApplyTime(tdFundOrder.getApplyTime());
        tdFundMonthlyOrderTmp.setUpdateTime(tdFundOrder.getUpdateTime());
        tdFundMonthlyOrderTmp.setVersion(tdFundOrder.getVersion());
        tdFundMonthlyOrderTmp.setRemark(tdFundOrder.getRemark());
        tdFundMonthlyOrderTmp.setActivityFlag(tdFundOrder.getActivityFlag());
        tdFundMonthlyOrderTmp.setId(tdFundOrder.getId());
        tdFundMonthlyOrderTmp.setIsDeleted(tdFundOrder.getIsDeleted());
        return tdFundMonthlyOrderTmp;
    }
}
