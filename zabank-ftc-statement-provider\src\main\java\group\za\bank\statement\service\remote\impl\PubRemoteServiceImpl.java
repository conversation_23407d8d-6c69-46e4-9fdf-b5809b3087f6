package group.za.bank.statement.service.remote.impl;

import group.za.bank.fund.trade.constants.enums.TradeErrorMsgEnum;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.ResponseValidator;
import group.za.bank.invest.pub.constants.enums.BusinessDataStorageTypeEnum;
import group.za.bank.invest.pub.entity.dto.GrayDetail;
import group.za.bank.invest.pub.entity.req.GeneratePdfReq;
import group.za.bank.invest.pub.entity.req.GrayQueryReq;
import group.za.bank.invest.pub.entity.req.QueryPdfReq;
import group.za.bank.invest.pub.entity.req.SendSingleMessageReq;
import group.za.bank.invest.pub.entity.resp.GeneratePdfResp;
import group.za.bank.invest.pub.entity.resp.QueryPdfResp;
import group.za.bank.invest.pub.entity.resp.SendMessageResp;
import group.za.bank.invest.pub.feign.NewMessageFeignService;
import group.za.bank.invest.pub.feign.PdfFeignService;
import group.za.bank.invest.pub.feign.PubFeignService;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.PubRemoteService;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 31 14:07
 * @description
 */
@Slf4j
@Service
public class PubRemoteServiceImpl implements PubRemoteService {

    @Autowired
    private PdfFeignService pdfFeignService;
    @Autowired
    private PubFeignService pubFeignService;
    @Resource
    private NewMessageFeignService newMessageFeignService;


    /**
     * pdf 自动生成填充模板
     * bo.li
     */
    @Override
    public GeneratePdfResp generatePdf(String businessTaskId, String templateCode, Map<String, Object> paramMap) {
        GeneratePdfReq req = new GeneratePdfReq();
        req.setBusinessTaskId(businessTaskId);
        req.setParamMap(paramMap);
        req.setTemplateCode(templateCode);
        req.setScene("1");
        req.setBusinessDataStorageType(BusinessDataStorageTypeEnum.BUSINESS_DATA_STORAGE_TYPE_OBS.getDbValue());
        ResponseData<GeneratePdfResp> resp = pdfFeignService.generatePdf(req);
        if (log.isDebugEnabled()) {
            log.debug("RPC generatePdf. req:{},resp:{}", req, resp);
        }
        if (resp == null) {
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        if (!resp.judgeSuccess()) {
            throw new BusinessException(resp.getCode(), resp.getMsg());
        }
        return resp.getValue();
    }

    /**
     * pdf 查询
     * bo.li
     */
    @Override
    public QueryPdfResp queryPdf(String businessTaskId) {
        QueryPdfReq req = new QueryPdfReq();
        req.setBusinessTaskId(businessTaskId);
        ResponseData<QueryPdfResp> resp = pdfFeignService.queryPdf(req);

        if (log.isDebugEnabled()) {
            log.debug("RPC queryPdf. req:{},resp:{}", req, resp);
        }
        if (resp == null) {
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        if (!resp.judgeSuccess()) {
            throw new BusinessException(resp.getCode(), resp.getMsg());
        }
        return resp.getValue();
    }

    @Override
    public String queryBusinessData(String businessTaskId) {
        ResponseData<String> response = null;
        try {
            response = pdfFeignService.queryBusinessData(businessTaskId);
            ResponseValidator.checkRemoteResponseValue(response);
            return response.getValue();
        } catch (Exception e) {
            log.error("queryBusinessData error, businessTaskId:{}, 出参:{}", businessTaskId, JSON.toJSONString(response), e);
            throw e;
        }
    }

    @Override
    public GrayDetail queryGray(GrayQueryReq req) {
        ResponseData<GrayDetail> responseData = null;
        try {
            responseData = pubFeignService.singleGrayQuery(req);
            log.info("queryGray 入参:{}, 出参：{}", JSON.toJSONString(req), JSON.toJSONString(responseData));
            // 校验返回值
            ResponseValidator.checkRemoteResponseValue(responseData);
            return responseData.getValue();
        } catch (Exception e) {
            log.error("queryGray error,req{},responseData:{}", JSON.toJSONString(req), JSON.toJSONString(responseData), e);
            throw new BusinessException(TradeErrorMsgEnum.INNER_SERVER_ERROR);
        }
    }

    @Override
    public boolean sendSingleMessage(SendSingleMessageReq sendSingleMessageReq, boolean isThrowException) {
        ResponseData<SendMessageResp> responseData = null;
        try{
            responseData = newMessageFeignService.sendSingleMessage(sendSingleMessageReq);
            log.info("sendSingleMessage, 入参:{}, 出参:{}", JSON.toJSONString(sendSingleMessageReq), JSON.toJSONString(responseData));
            ResponseValidator.checkRemoteResponse(responseData);
            return true;
        }catch (Exception e){
            log.error("sendSingleMessage error 入参:{}, 出参:{}", JSON.toJSONString(sendSingleMessageReq), JSON.toJSONString(responseData), e);
            if(isThrowException){
                throw new BusinessException(TradeErrorMsgEnum.INNER_SERVER_ERROR);
            }
        }
        return false;
    }
}
