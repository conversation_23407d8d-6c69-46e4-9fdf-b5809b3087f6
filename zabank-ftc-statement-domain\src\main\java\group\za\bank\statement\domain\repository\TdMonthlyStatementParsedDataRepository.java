package group.za.bank.statement.domain.repository;


import group.za.bank.statement.domain.entity.TdMonthlyStatementHtmlParsedData;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 29 14:14
 * @description
 */
@Repository
public interface TdMonthlyStatementParsedDataRepository extends CrudRepository<TdMonthlyStatementHtmlParsedData,String>{


    /**
     * 查询解析完的数据
     * @param statementInfoId
     * @param userId
     * @param clientId
     * @param appLang
     * @return
     */
    List<TdMonthlyStatementHtmlParsedData> getByStatementInfoIdAndUserIdAndClientIdAndAppLang(String statementInfoId, String userId, String clientId, String appLang);


    /**
     * 删除数据
     * @param statementInfoId
     * @param userId
     * @param clientId
     * @param appLang
     */
    void deleteByStatementInfoIdAndUserIdAndClientIdAndAppLang(String statementInfoId, String userId, String clientId, String appLang);

}
