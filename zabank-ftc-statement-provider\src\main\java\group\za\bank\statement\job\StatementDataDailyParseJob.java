package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.agent.TradeCalendarAgent;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.entity.req.StatementDataParseReq;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.StockTradeDateService;
import group.za.bank.statement.service.TdStockStatementDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.FILE_PARSE_REPEATED;

/**
 * ttl日结单解析任务
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Component
@Slf4j
@JobHandler(value = "statementDataDailyParseJob")
public class StatementDataDailyParseJob extends IJobHandler {

    private final static String DATE_FORMAT_YMD = "yyyyMMdd";

    @Autowired
    private TdStockStatementDataService stockStatementDataService;
    @Autowired
    private TradeCalendarAgent tradeCalendarAgent;
    @Resource(name = "stockStatementBusinessService")
    private StatementBusinessService statementBusinessService;

    @Autowired
    private StockTradeDateService stockTradeDateService;
    @Autowired
    private SystemConfig systemConfig;

    @Override
    public ReturnT<String> execute(String message) throws Exception {
        log.info("statementDataDailyParseJob param:{}", message);
        boolean isAutoTask = true;
        //默认是不重复解析的
        boolean refresh = false;
        //结单日期
        String statementDateString = null;
        //结单文件名字，正常的话跟结单日期是一致的
        String filePathDateString = null;

        if (StringUtils.isNotEmpty(message)) {
            try {
                StatementDataParseReq statementDataParseReq = JsonUtils.toJavaObject(message, StatementDataParseReq.class);
                refresh = statementDataParseReq.getRefresh();
                filePathDateString = statementDataParseReq.getPathDate();
                statementDateString = statementDataParseReq.getStatementDate();
                isAutoTask = false;
            } catch (Exception e) {
                log.info("statementDataDailyParseJob解析入参错误");
                throw new RuntimeException("statementDataDailyParseJob解析入参错误", e);
            }
        }

        if (Strings.isEmpty(statementDateString)) {
            statementDateString = DateUtil.format(new Date(), DATE_FORMAT_YMD);
        }
        //如果文件名称不指定，就用结单日期即可（一般的话结单日期跟文件名称是保持一致的）
        if (Strings.isEmpty(filePathDateString)) {
            filePathDateString = statementDateString;
        }

        Date statementDate = DateUtil.parse(statementDateString, DATE_FORMAT_YMD);
        String period = DateUtil.format(statementDate, systemConfig.getMonthlyStatementPeriodFormat());
        List<String> monthAllTradeDateList = stockTradeDateService.queryCoreCalendarTradeDateList(period);
        if (CollectionUtils.isEmpty(monthAllTradeDateList)) {
            log.error("解析ttl日结单任务异常,当月交易日列表为空,请检查! period:{}", period);
            return ReturnT.FAIL;
        }


        if (!monthAllTradeDateList.contains(statementDateString)) {
            log.info("非交易日，无法解析日结单，请留意! statementDate:{}", statementDateString);
            return ReturnT.SUCCESS;
        }

        try {
            // 拉取日结单数据
            stockStatementDataService.parseStatementMonthlyData(StatementTypeEnum.DAILY, statementDateString, filePathDateString, refresh);
        } catch (BusinessException e) {
            if (isAutoTask) {
                //不是通过参数指定日期的定时任务，每天都会重复跑，如果发生重复解析，是正常现象，无需处理
                if (FILE_PARSE_REPEATED.getCode().equals(e.getCode())) {
                    log.info("日结单已经解析完毕! statementDate:{}", statementDateString);
                    return ReturnT.SUCCESS;
                }
            }
            throw e;
        }

        return ReturnT.SUCCESS;
    }


}
