package group.za.bank.statement.manager;

import group.za.bank.ccs.core.support.share.entity.dto.DayKlineSupDTO;
import group.za.bank.ccs.tradecore.mapper.*;
import group.za.bank.ccs.tradecore.model.entity.*;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.entity.dto.CryptoHoldInfoDto;
import group.za.bank.statement.service.remote.CryptoQuotaRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 数字货币交易核心服务处理类
 *
 * <AUTHOR>
 * @date 2023/08/28
 **/
@Slf4j
@Component
public class CryptoTradeCoreManager {

    @Autowired
    private CcOrderExtendMapper orderExtendMapper;
    @Autowired
    private CcBusinessRecordExtendMapper businessRecordExtendMapper;
    @Resource
    private CustCcAssetMonthlyBackupMapper custCcAssetMonthlyBackupMapper;
    @Resource
    private CustCcAssetChangeDetailMapper custCcAssetChangeDetailMapper;
    @Resource
    private CustCcCryptoInfoMapper custCcCryptoInfoMapper;
    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private CryptoQuotaRemoteService cryptoQuotaRemoteService;

    @Autowired
    private CcsTaskResultExtendMapper taskResultExtendMapper;

    /**
     * 数字货币订单查询
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<CcOrder> cryptoOrderList(String userId, String startDate, String endDate){

        return orderExtendMapper.statementOrderQuery(userId, startDate, endDate);
    }

    /**
     * 数字货币订单成交明细查询
     * @param orderNo
     * @return
     */
    public List<CcBusinessRecord> cryptoBusinessRecordList(String orderNo){
        return businessRecordExtendMapper.statementBusinessRecordQuery(orderNo);
    }

    /**
     * 查询持仓数据
     * @param period
     * @param accountId
     * @return
     */
    public List<CryptoHoldInfoDto> cryptoHoldList(String period, String accountId){
        List<CryptoHoldInfoDto> resultList = new ArrayList<>();
        // 获取当月份的最后一天
        String monthLastDay = MonthlyUtils.getMonthLastDay(period);

        // 获取上个月的备份数据（期初）
        List<CcAssetMonthlyBackup> startPeriodList = custCcAssetMonthlyBackupMapper.selectList(calcLastPeriod(period), Collections.singletonList(accountId));
        // 获取本月的备份数据（期末）
        List<CcAssetMonthlyBackup> endPeriodList = custCcAssetMonthlyBackupMapper.selectList(calcCurrentPeriod(period), Collections.singletonList(accountId));

        // 获取货币列表信息
        List<CcCryptoInfo> allAssetType = this.getAllCryptoInfo();

        for (CcCryptoInfo ccCryptoInfo : allAssetType) {
            String assetType = ccCryptoInfo.getAssetType();

            CryptoHoldInfoDto cryptoHoldInfoDto = new CryptoHoldInfoDto();

            BigDecimal openVolume = BigDecimal.ZERO;
            BigDecimal closeVolume = BigDecimal.ZERO;

            CcAssetMonthlyBackup startPeriodData = startPeriodList.stream()
                    .filter(ccAssetMonthlyBackup -> ccAssetMonthlyBackup.getAssetType().equals(assetType)).findFirst()
                    .orElse(null);
            if (Objects.nonNull(startPeriodData)) {
                openVolume = startPeriodData.getSettleVolume();
            }

            CcAssetMonthlyBackup endPeriodData = endPeriodList.stream()
                    .filter(ccAssetMonthlyBackup -> ccAssetMonthlyBackup.getAssetType().equals(assetType)).findFirst()
                    .orElse(null);
            if (Objects.nonNull(endPeriodData)) {
                closeVolume = endPeriodData.getSettleVolume();
            }

            //期初期末都为0的数据过滤掉
            if(BigDecimal.ZERO.compareTo(openVolume) == 0 && BigDecimal.ZERO.compareTo(closeVolume) == 0){
                continue;
            }
            cryptoHoldInfoDto.setAssetId(assetType);
            cryptoHoldInfoDto.setAssetNameEn(ccCryptoInfo.getNameEng());
            cryptoHoldInfoDto.setAssetNameCn(ccCryptoInfo.getNameCn());
            cryptoHoldInfoDto.setAssetNameHk(ccCryptoInfo.getNameHk());
            cryptoHoldInfoDto.setOpenVolume(openVolume);
            cryptoHoldInfoDto.setCloseVolume(closeVolume);

            //查询收盘价，assetId="BTCHKD.HKY"
            List<String> usdAssets = Arrays.asList("AVAX","LINK");
            if(usdAssets.contains(assetType)){
                String assetId = String.format(StatementConstants.CRYPTO_ASSET_ID_USD_FORMAT, assetType);
                DayKlineSupDTO dayKlineSupDTO = cryptoQuotaRemoteService.queryDayKline(assetId, monthLastDay);

                cryptoHoldInfoDto.setClosePrice(dayKlineSupDTO.getClose());
                cryptoHoldInfoDto.setCurrency(CurrencyEnum.USD.getCurrency());

                log.info("accountId:{}, assetId:{} openVolume:{} closeVolume:{}，closePrice:{}",
                        accountId, assetId, openVolume, closeVolume, dayKlineSupDTO.getClose());
            }else{
                String assetId = String.format(StatementConstants.CRYPTO_ASSET_ID_FORMAT, assetType);
                DayKlineSupDTO dayKlineSupDTO = cryptoQuotaRemoteService.queryDayKline(assetId, monthLastDay);

                cryptoHoldInfoDto.setClosePrice(dayKlineSupDTO.getClose());
                cryptoHoldInfoDto.setCurrency(CurrencyEnum.HKD.getCurrency());

                log.info("accountId:{}, assetId:{} openVolume:{} closeVolume:{}，closePrice:{}",
                        accountId, assetId, openVolume, closeVolume, dayKlineSupDTO.getClose());
            }


            resultList.add(cryptoHoldInfoDto);
        }
        return resultList;
    }

    /**
     * 获取虚拟资产名称
     * @param assetType
     * @param i18nSupportEnum
     * @return
     */
    public String getCryptoName(String assetType, I18nSupportEnum i18nSupportEnum){
        CcCryptoInfo ccCryptoInfo = getCcCryptoInfo(assetType);
        switch (i18nSupportEnum){
            case CN_ZH:
                return ccCryptoInfo.getNameCn();
            case CN_HK:
                return ccCryptoInfo.getNameHk();
            default:
                return ccCryptoInfo.getNameEng();
        }
    }
    /**
     * 查询交易币对信息
     */
    public CcCryptoInfo getCcCryptoInfo(String assetType) {
        List<CcCryptoInfo> allAssetType = getAllCryptoInfo();
        return allAssetType.stream()
                .filter(t -> t.getAssetType().equals(assetType))
                .findFirst()
                .orElseThrow(() -> new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR));
    }

    /**
     * 查询交易币对信息
     */
    public List<CcCryptoInfo> getAllCryptoInfo() {
        String assetTypeKey = StatementRedisCacheKey.CRYPTO_INFO_CACHE_KEY.key();
        return AbstractCacheTemplate.queryFromCache(assetTypeKey, 1, TimeUnit.DAYS, () -> custCcCryptoInfoMapper.getAllAssetType(), false);
    }
    /**
     * 查询持仓变动数据
     * @param period
     * @param accountId
     * @return
     */
    public List<CcAssetChangeDetail> queryCryptoAssetChangeDetail(String period, String accountId){
        // 获取月份的开始结束日期
        Date startTime =  DateUtil.stringToDate(MonthlyUtils.getMonthFirstDay(period));
        Date endTime =  DateUtil.stringToDate(MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME) ;
        // 获取本月的变动数据
        return custCcAssetChangeDetailMapper.selectList(startTime, endTime, Collections.singletonList(accountId));
    }

    /**
     * 计算上个月的
     *
     * @param currentPeriod
     * @return
     */
    private String calcLastPeriod(String currentPeriod) {
        Date periodDate = DateUtil.parse(currentPeriod, systemConfig.getMonthlyStatementPeriodFormat());
        Date lastPeriodDate = DateUtil.addMonth(periodDate, -1);
        return DateUtil.format(lastPeriodDate, StatementConstants.CRYPTO_ASSET_BACKUP_MONTH_FORMAT);
    }

    /**
     * 计算当前月份
     * @param currentPeriod
     * @return
     */
    private String calcCurrentPeriod(String currentPeriod) {
        Date periodDate = DateUtil.parse(currentPeriod, systemConfig.getMonthlyStatementPeriodFormat());
        return DateUtil.format(periodDate, StatementConstants.CRYPTO_ASSET_BACKUP_MONTH_FORMAT);
    }

    /**
     * 查询数字货币资产备份任务结果
     * @param taskCode
     * @param taskDate
     * @return
     */
    public TaskResult queryByTaskCodeAndDate(String taskCode, Date taskDate) {
        return taskResultExtendMapper.queryByTaskCodeAndDate(taskCode, taskDate);
    }
}
