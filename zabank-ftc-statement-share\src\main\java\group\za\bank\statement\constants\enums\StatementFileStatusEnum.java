package group.za.bank.statement.constants.enums;

/**
 * 结单文件状态
 *
 * <AUTHOR>
 * @date 2022/05/05
 **/
public enum StatementFileStatusEnum {
    TODO(0, "未处理"),
    ING(1, "处理中"),
    SUCCESS(2, "成功"),
    FAIL(3, "失败"),
    ;

    private Integer fileStatus;

    private String desc;

    StatementFileStatusEnum(Integer fileStatus, String desc) {
        this.fileStatus = fileStatus;
        this.desc = desc;
    }

    public Integer getFileStatus() {
        return fileStatus;
    }

    public String getDesc() {
        return desc;
    }
}
