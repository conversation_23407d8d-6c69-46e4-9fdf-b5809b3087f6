package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description
 */
@Data
public class NativeRowDto {

    /**
     * 序号
     */
    private Integer seq;

    /**
     * 左边标题
     */
    private String title;

    /**
     * 右边的值
     */
    private String value;

    /**
     * 属性名
     */
    private String name;


    /**
     * 行的属性类型
     * 0：普通
     * 1：左边
     * 2：右边
     */
    private Integer rowType;

    /**
     * 块类型
     * 1:日期类型
     */
    private Integer blockType;

    /**
     * currency marketValue
     * 组 marketValue
     */
    private String groupId;


    /**
     * 分割符号
     */
    private String splitter;


    /**
     * 单位
     */
    private String valueUnit;


    /**
     * 扩展配置
     * json格式
     */
    private String ext;


    /**
     * 明细
     */
    private List<List<NativeRowDto>> nativeRowDetailList;
}
