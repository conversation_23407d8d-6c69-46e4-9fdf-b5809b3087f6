package group.za.bank.statement.agent;

import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.common.utils.SpringUtils;
import group.za.bank.sbs.bankfront.common.enums.RateSourceEnum;
import group.za.bank.sbs.bankfront.model.resp.QueryRateResp;
import group.za.bank.sbs.trade.model.resp.feign.StkRateInfoHisResp;
import group.za.bank.statement.base.BaseTestH2Service;
import group.za.bank.statement.service.remote.RateRemoteService;
import group.za.invest.cache.redis.configuration.RedissonAutoConfiguration;
import group.za.invest.json.spring.JsonAutoConfiguration;
import org.junit.Assert;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

import static org.mockito.Mockito.*;

@SpringBootTest(classes = {RateAgent.class, JacksonAutoConfiguration.class, JsonAutoConfiguration.class,
        RedissonAutoConfiguration.class,   TransactionAutoConfiguration.class, JsonUtils.class, RedissonClient.class, SpringUtils.class})
public class RateAgentTest extends BaseTestH2Service {
    @MockBean
    RateRemoteService rateRemoteService;
    @MockBean
    Logger log;
    @Resource
    RateAgent rateAgent;

    @Test
    public void testGetRate() {
        QueryRateResp queryRateResp = new QueryRateResp();
        queryRateResp.setSourceCcy("USD");
        queryRateResp.setTargetCcy("HKD");
        queryRateResp.setRateSource(RateSourceEnum.ZA_BANK_CORE.getValue());
        queryRateResp.setRate(new BigDecimal("8.22"));
        when(rateRemoteService.queryRate(anyString(), anyString())).thenReturn(queryRateResp);

        BigDecimal result = rateAgent.getRate("USD", "HKD");
        Assert.assertEquals(new BigDecimal("8.22"), result);
    }

    @Test
    public void testGetRateHis() {

        StkRateInfoHisResp stkRateInfoHisResp = new StkRateInfoHisResp();
        stkRateInfoHisResp.setSourceCcy("USD");
        stkRateInfoHisResp.setTargetCcy("HKD");
        stkRateInfoHisResp.setRateSource(RateSourceEnum.ZA_BANK_CORE.getValue());
        stkRateInfoHisResp.setRate(new BigDecimal("8.22"));
        stkRateInfoHisResp.setMarketCode("US");
        stkRateInfoHisResp.setTradeDate(new Date());

        when(rateRemoteService.queryRateHis(any())).thenReturn(stkRateInfoHisResp);
        BigDecimal result = rateAgent.getRateHis("USD", "HKD", new Date(), "US");
        Assert.assertEquals(new BigDecimal("8.22"), result);
    }
}


