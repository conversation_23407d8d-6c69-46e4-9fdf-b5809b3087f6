package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.trade.feign.StkInfoFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 股票基本信息
 *
 * <AUTHOR>
 * @date 2022/06/22
 **/
@FeignClient(value = "zabank-sbs-trade-service",
        contextId = "stkInfoFeignService1",
        url = "${sbs.gateway.url}")
public interface StkInfoFeign extends StkInfoFeignService {
}
