package group.za.bank.statement.entity.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 25 11:45
 * @description
 */
@Data
public class FundMonthlyStatementDataDto {

    /**
     * 基本信息——baseInfo
     */
    private BasicInfoDto basicInfo;

    /**
     * 基金持仓——holdingList
     */
    private List<HoldingDto> holdingList = new ArrayList<>();

    /**
     * 已确认交易——confirmedOrderList
     */

    private List<ConfirmedOrderDto> confirmedOrderList = new ArrayList<>();
    /**
     * 处理中交易——pendingOrderList
     */
    private List<PendingOrderDto> pendingOrderList = new ArrayList<>();

    /**
     * 派息——dividendOrderList
     */
    private List<DividendOrderDto> dividendOrderList = new ArrayList<>();

    /**
     * 存入及提取——depositAndWithdrawalList
     */
    private List<DepositAndWithdrawalDto> depositAndWithdrawalList = new ArrayList<>();



    @Getter
    @Setter
    @ToString
    public static class BasicInfoDto {
        //期初日期 DD MMM YYYY
        private String statementStartDate;
        //期末日期 DD MMM YYYY
        private String statementEndDate;
        //结单日期 DD MMM YYYY
        private String statementDate;

        //客户名称
        private String customerName;
        //办公地点
        private String officeLocation;
        //街道
        private String street;
        //地区
        private String area;
        //国家
        private String country;

        //账户号码
        private String accountNo;
        //总市值 1,999.00(格式化的)
        private String totalMarket;

        //币种
        private String currency;
        //总市值，已保留两位小数并四舍五入
        private BigDecimal totalMarketValue;
        //等值期末持仓盈亏, 已保留两位小数并四舍五入
        private BigDecimal totalHoldingProfit;
    }


    @Getter
    @Setter
    @ToString
    public static class HoldingDto {
        private String fundName;
        private String fundEngName;
        //期初结余
        private String openingBalance;
        //期终结余
        private String closingBalance;
        //币种
        private String currency;
        //参考价格
        private String referencePrice;
        //市值
        private String marketValue;
        //等值港币市值，港元基金展示--，若是美元基金展示等值港币市值
        private String equalHkdMarketValue;
        //快照表使用--产品id
        private String productId;
        //快照表使用--isinNo
        private String isinNo;
        //快照表使用--基金中文名
        private String fundZhName;
        //快照表使用--基金繁体名
        private String fundHkName;
    }

    @Getter
    @Setter
    @ToString
    public static class ConfirmedOrderDto {
        private String date;
        //说明
        private String description;
        private String engDescription;
        //单位数目
        private String noOfUnits;
        //币种
        private String currency;
        //单位价格
        private String unitPrice;
        //费用
        private String fee;
        //金额
        private String amount;

        //快照表使用
        private StatementOrderSnapshotDto statementOrderSnapshotDto;
    }



    @Getter
    @Setter
    @ToString
    public static class PendingOrderDto {
        private String date;
        //说明
        private String description;
        private String engDescription;
        //单位数目
        private String noOfUnits;
        //币种
        private String currency;
        //单位价格
        private String unitPrice;
        //费用
        private String fee;
        //金额
        private String amount;

        //快照表使用
        private StatementOrderSnapshotDto statementOrderSnapshotDto;

    }


    @Getter
    @Setter
    @ToString
    public static class DividendOrderDto {
        private String date;
        //说明
        private String description;
        private String engDescription;
        //单位数目
        private String noOfUnits;
        //币种
        private String currency;
        //金额
        private String amount;

        //快照表使用
        private StatementOrderSnapshotDto statementOrderSnapshotDto;
    }


    @Getter
    @Setter
    @ToString
    public static class DepositAndWithdrawalDto {
        private String date;
        //说明
        private String description;
        private String engDescription;
        //单位数目
        private String noOfUnits;
        //币种
        private String currency;
        //费用
        private String fee;

        //快照表使用
        private StatementOrderSnapshotDto statementOrderSnapshotDto;
    }

    @Getter
    @Setter
    @ToString
    public static class StatementOrderSnapshotDto{

        /**
         * 订单id
         */
        private String orderNo;

        /**
         * 类型
         */
        private String bizType;

        /**
         * 结算货币类型
         */
        private String currency;

        /**
         * isin号
         */
        private String isinNo;

        /**
         *产品Id
         */
        private String productId;

        /**
         *产品中文名称
         */
        private String productZhName;

        /**
         * 产品繁体名称
         */
        private String productHkName;

        /**
         * 产品英文名称
         */
        private String productEngName;

        /**
         * 订单类型(10-申购，11-赎回，12基金分红, 20-存 21-取
         */
        private String businessType;

        /**
         *订单所属交易日
         */
        private Date tradeDate;

        /**
         *申请时间
         */
        private Date applyTime;

        /**
         * 申请金额
         */
        private BigDecimal applyAmt;

        /**
         * 申请份额
         */
        private BigDecimal applyShare;

        /**
         * 确认金额
         */
        private BigDecimal confirmAmt;

        /**
         * 确认份额
         */
        private BigDecimal confirmShare;

        /**
         * 成交净值
         */
        private BigDecimal confirmNetValue;

        /**
         * 确认净值日期
         */
        private Date confirmNetValueDate;

        /**
         * 下单时费率
         */
        private BigDecimal feeRate;

        /**
         * 下单时预估交易费用
         */
        private BigDecimal fee;

        /**
         *  最终交易费用
         */
        private BigDecimal finalFee;

        /**
         * 实际上手申购/赎回确认时间
         */
        private Date realConfirmTime;

        /**
         * 实际交收时间
         */
        private Date realDeliveryTime;

        /**
         * 订单状态：0-初始化，1-待支付，2-已支付，3-支付失败 10-下单处理中，11-已提交，20-订单修改中，30-订单撤销中，31-已撤销，40-下单失败，41-上手确认失败，
         * 50-已确认，60-待交收，61-已交收
         */
        private Byte status;
    }





}
