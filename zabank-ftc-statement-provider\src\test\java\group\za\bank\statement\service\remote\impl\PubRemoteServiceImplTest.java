package group.za.bank.statement.service.remote.impl;


import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.pub.entity.req.SendMessageReq;
import group.za.bank.invest.pub.entity.resp.GeneratePdfResp;
import group.za.bank.invest.pub.entity.resp.QueryPdfResp;
import group.za.bank.invest.pub.entity.resp.SendMessageResp;
import group.za.bank.invest.pub.feign.MessageFeignService;
import group.za.bank.invest.pub.feign.PdfFeignService;
import group.za.bank.invest.pub.feign.PubFeignService;
import group.za.bank.statement.base.BaseTestService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;

import java.util.HashMap;

public class PubRemoteServiceImplTest extends BaseTestService {
    @Mock
    PdfFeignService pdfFeignService;
    @Mock
    MessageFeignService messageFeignService;

    @Mock
    PubFeignService pubFeignService;
    @Mock
    Logger log;
    @InjectMocks
    PubRemoteServiceImpl pubRemoteServiceImpl;

    @Test
    public void testGeneratePdf() {
        ResponseData<GeneratePdfResp> generatePdfRespResponseData = new ResponseData<>();
        generatePdfRespResponseData.setCode("0000");
        generatePdfRespResponseData.setValue(new GeneratePdfResp());

        Mockito.when(pdfFeignService.generatePdf(Mockito.any())).thenReturn(generatePdfRespResponseData);
        GeneratePdfResp result = pubRemoteServiceImpl.generatePdf("businessTaskId", "templateCode", new HashMap<String, Object>() {{
            put("String", "paramMap");
        }});
    }

    @Test
    public void testQueryPdf() {
        ResponseData<QueryPdfResp> queryPdfRespResponseData = new ResponseData<>();
        queryPdfRespResponseData.setCode("0000");
        queryPdfRespResponseData.setValue(new QueryPdfResp());

        Mockito.when(pdfFeignService.queryPdf(Mockito.any())).thenReturn(queryPdfRespResponseData);
        QueryPdfResp result = pubRemoteServiceImpl.queryPdf("businessTaskId");
    }
}