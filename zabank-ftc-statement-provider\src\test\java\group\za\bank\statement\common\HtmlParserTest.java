package group.za.bank.statement.common;

import com.alibaba.fastjson.JSONObject;
import com.google.common.io.CharStreams;
import group.za.bank.statement.common.parser.HtmlParser;
import group.za.bank.statement.entity.dto.NativeBusinessDto;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/2/15 15:50
 * @Description
 */
public class HtmlParserTest {

    @Test
    public void testExecute() {
        try(InputStream inputStream = getClass().getResourceAsStream("/template/statement.html")) {
            String entity = CharStreams.toString(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            long start = System.currentTimeMillis();
            Map<String, Object> result = new HtmlParser().execute("account_monthly_conf.json", entity);
            System.out.println("use1:"+ (System.currentTimeMillis()-start));

            long start2 = System.currentTimeMillis();
            Map<String, Object> result2 = new HtmlParser().execute("account_monthly_conf.json", entity);
            System.out.println("use2:"+ (System.currentTimeMillis()-start2));

            String jsonString = JSONObject.toJSONString(result);

            Map<String, NativeBusinessDto> dataTypeKeyAndDtoMap = JSONObject.parseObject(jsonString, Map.class);

            System.out.println(result);

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> rowMap = new HashMap<>();
            rowMap.put("title", "Date 日期");
            rowMap.put("value", "2022/11/17-股票派息");
            rowMap.put("name", "dividendOrderDescription");
            rowMap.put("rowType", 0);

            System.out.println("=============");

            System.out.println(JSONObject.toJSONString(dataTypeKeyAndDtoMap));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
