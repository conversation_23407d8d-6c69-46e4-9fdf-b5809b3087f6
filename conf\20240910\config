zainvest.sharding.group.business-group.beanNamePrefix = business
zainvest.sharding.group.business-group.mybatis.mapper-locations[0] = classpath*:mapper/*.xml
zainvest.sharding.group.business-group.mybatis.mapper-scanner.base-package = group.za.bank.sbs.business.mapper
zainvest.sharding.group.business-group.databases[0].ip = 待补充
zainvest.sharding.group.business-group.databases[0].port = 3303
zainvest.sharding.group.business-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.business-group.databases[0].userName = 待补充
zainvest.sharding.group.business-group.databases[0].password = 待补充
zainvest.sharding.group.business-group.databases[0].dbName = za_bank_stock_business