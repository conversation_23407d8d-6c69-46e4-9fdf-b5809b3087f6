package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @createTime 25 11:45
 * @description
 */
@Data
public class BaseMonthlyStatementDataDto {


    //期初日期 DD MMM YYYY
    private String statementStartDate;
    //期末日期 DD MMM YYYY
    private String statementEndDate;
    //结单日期 DD MMM YYYY
    private String statementDate;

    //客户名称
    private String customerName;
    //办公地点
    private String officeLocation;
    //街道
    private String street;
    //地区
    private String area;
    //国家
    private String country;

    //账户号码
    private String accountNo;
    //投资账户id
    private String clientId;
    //总金额：基金+股票
    private String totalAmount;

    //总金额：基金+股票
    private BigDecimal totalAmountValue;

    //总持仓盈亏：基金+股票
    private BigDecimal totalHoldingProfit;

    /**
     * 基金业务表头展示状态
     */
    private boolean fund = false;

    /**
     * 股票业务表头展示状态
     */
    private boolean stock = false;

    /**
     * 股票业务表头展示状态
     */
    private boolean hkStock = false;

    /**
     * crypto业务表头展示状态
     */
    private boolean crypto = false;

    /**
     * 南向通基金业务表头展示状态
     */
    private boolean sfund = false;

}
