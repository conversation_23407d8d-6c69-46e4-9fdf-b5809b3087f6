package group.za.bank.statement.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.common.config.AliOSSProperties;
import group.za.bank.statement.common.config.SystemConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.when;

public class FileManagerServiceImplTest extends BaseTestService {
    @Mock
    SystemConfig systemConfig;
    @Mock
    AliOSSProperties aliOSSProperties;
    @Mock
    OSS ossClient;
    @Mock
    Logger log;
    @InjectMocks
    FileManagerServiceImpl fileManagerServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testDownloadFile() throws Exception {
        when(systemConfig.getSftpHost()).thenReturn("getSftpHostResponse");
        when(systemConfig.getSftpPort()).thenReturn(0);
        when(systemConfig.getUsername()).thenReturn("getUsernameResponse");
        when(systemConfig.getPassword()).thenReturn("getPasswordResponse");

        boolean result = fileManagerServiceImpl.downloadFile("filePath", "fileName", "fileDir");
    }

    @Test
    public void testUploadAliOSSFile() throws Exception {
        when(aliOSSProperties.getBucketName()).thenReturn("getBucketNameResponse");
        when(aliOSSProperties.getPrefix()).thenReturn("getPrefixResponse");

        String result = fileManagerServiceImpl.uploadAliOSSFile("fileName", null);
    }

    @Test
    public void testDownloadAliOSSFile() throws Exception {
        when(aliOSSProperties.getBucketName()).thenReturn("getBucketNameResponse");

        OSSObject result = fileManagerServiceImpl.downloadAliOSSFile("objectKey");
    }

    //@Test
    public void testDownloadAliOSSFileAndSaveLocal() throws Exception {
        when(aliOSSProperties.getBucketName()).thenReturn("getBucketNameResponse");

        fileManagerServiceImpl.downloadAliOSSFileAndSaveLocal("objectKey", "localPath");
    }

    @Test
    public void testExistOssFile() throws Exception {
        when(aliOSSProperties.getBucketName()).thenReturn("getBucketNameResponse");

        boolean result = fileManagerServiceImpl.existOssFile("objectKey");
    }

    @Test
    public void testGetBaseFilePath() throws Exception {
        when(aliOSSProperties.getPrefix()).thenReturn("getPrefixResponse");

        String result = fileManagerServiceImpl.getBaseFilePath("fileName");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme