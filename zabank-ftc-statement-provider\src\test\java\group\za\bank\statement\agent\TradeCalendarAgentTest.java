package group.za.bank.statement.agent;


import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.common.utils.SpringUtils;
import group.za.bank.sbs.trade.model.resp.feign.MonthFirstTradeDateResp;
import group.za.bank.sbs.trade.model.resp.feign.MonthLastTradeDateResp;
import group.za.bank.statement.base.BaseTest;
import group.za.bank.statement.base.BaseTestH2Service;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import group.za.invest.cache.redis.configuration.RedissonAutoConfiguration;

import group.za.invest.json.spring.JsonAutoConfiguration;

import org.junit.Assert;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.util.Date;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {TradeCalendarAgent.class, JacksonAutoConfiguration.class, JsonAutoConfiguration.class,
        RedissonAutoConfiguration.class,   TransactionAutoConfiguration.class, JsonUtils.class, RedissonClient.class, SpringUtils.class})
public class TradeCalendarAgentTest extends BaseTestH2Service {
    @MockBean
    TradeCalendarRemoteService tradeCalendarRemoteService;
    @Resource
    TradeCalendarAgent tradeCalendarAgent;

    @Test
    public void testGetMonthLastTradeDate() {
        MonthLastTradeDateResp monthLastTradeDateResp = new MonthLastTradeDateResp();
        monthLastTradeDateResp.setTradeDate("20220620");
        when(tradeCalendarRemoteService.getMonthLastTradeDate(anyString(), anyString())).thenReturn(monthLastTradeDateResp);

        Date result = tradeCalendarAgent.getMonthLastTradeDate("US", "202206");
        Assert.assertEquals(DateUtil.strToDate("20220620"), result);
    }

    @Test
    public void testGetMonthFirstTradeDate() {
        MonthFirstTradeDateResp monthFirstTradeDateResp = new MonthFirstTradeDateResp();
        monthFirstTradeDateResp.setTradeDate("20220620");
        when(tradeCalendarRemoteService.getMonthFirstTradeDate(anyString(), anyString())).thenReturn(monthFirstTradeDateResp);

        Date result = tradeCalendarAgent.getMonthFirstTradeDate("US", "202206");
        Assert.assertEquals(DateUtil.strToDate("20220620"), result);
    }
}