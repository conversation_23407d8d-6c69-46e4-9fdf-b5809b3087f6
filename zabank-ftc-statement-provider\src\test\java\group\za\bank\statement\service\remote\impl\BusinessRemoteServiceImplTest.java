package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.business.model.resp.TransferDetailResp;
import group.za.bank.statement.service.remote.feign.BusinessTransferFeign;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class BusinessRemoteServiceImplTest {
    @Mock
    BusinessTransferFeign businessTransferFeign;
    @Mock
    Logger log;
    @InjectMocks
    BusinessRemoteServiceImpl businessRemoteServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testQueryTransferDetailList() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        when(businessTransferFeign.queryTransferDetailList(any())).thenReturn(responseData);
        List<TransferDetailResp> result = businessRemoteServiceImpl.queryTransferDetailList(null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme