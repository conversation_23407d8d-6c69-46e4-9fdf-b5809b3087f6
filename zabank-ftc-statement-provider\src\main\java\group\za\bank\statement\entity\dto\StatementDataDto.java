package group.za.bank.statement.entity.dto;

import group.za.invest.file.reader.annotation.DateFormat;
import group.za.invest.file.reader.annotation.split.SplitField;
import group.za.invest.file.reader.constans.SplitRecordFieldTypeEnum;
import group.za.invest.file.reader.dto.split.SplitFileRecord;
import lombok.Data;

import java.util.Date;

/**
 * 结单文件数据对象
 * <AUTHOR>
 * @date 2022/04/28
 **/
@Data
public class StatementDataDto extends SplitFileRecord {

    @DateFormat(format = "yyyy-MM-dd")
    @SplitField(type = SplitRecordFieldTypeEnum.DATE, columnIndex = 0)
    private Date statementDate;

    @SplitField(columnIndex = 1)
    private String accNo;

    @SplitField(columnIndex = 2)
    private String statementType;

    @SplitField(columnIndex = 3)
    private String formatType;

    @SplitField(columnIndex = 4)
    private String param1;

    @SplitField(columnIndex = 5)
    private String param2;

    @SplitField(columnIndex = 6)
    private String param3;

    @SplitField(columnIndex = 7)
    private String param4;

    @SplitField(columnIndex = 8)
    private String param5;

    @SplitField(columnIndex =9)
    private String param6;

    @SplitField(columnIndex = 10)
    private String param7;

    @SplitField(columnIndex = 11)
    private String param8;

    @SplitField(columnIndex = 12)
    private String param9;

    @SplitField(columnIndex = 13)
    private String param10;

    @SplitField(columnIndex = 14)
    private String param11;

    @SplitField(columnIndex = 15)
    private String param12;

    @SplitField(columnIndex = 16)
    private String param13;

    @SplitField(columnIndex = 17)
    private String param14;

    @SplitField(columnIndex = 18)
    private String param15;

    @SplitField(columnIndex = 19)
    private String param16;

    @SplitField(columnIndex = 20)
    private String param17;

    @SplitField(columnIndex = 21)
    private String param18;

    @SplitField(columnIndex = 22)
    private String param19;

    @SplitField(columnIndex = 23)
    private String param20;

    @SplitField(columnIndex = 24)
    private String param21;

    @SplitField(columnIndex = 25)
    private String param22;

    @SplitField(columnIndex = 26)
    private String param23;

    @SplitField(columnIndex = 27)
    private String param24;

    @SplitField(columnIndex = 28)
    private String param25;

    @SplitField(columnIndex = 29)
    private String param26;

    @SplitField(columnIndex = 30)
    private String param27;

    @SplitField(columnIndex = 31)
    private String param28;

    @SplitField(columnIndex = 32)
    private String param29;

    @SplitField(columnIndex = 33)
    private String param30;

    @SplitField(columnIndex = 34)
    private String param31;

    @SplitField(columnIndex = 35)
    private String param32;

    @SplitField(columnIndex = 36)
    private String param33;

    @SplitField(columnIndex = 37)
    private String param34;

    @SplitField(columnIndex = 38)
    private String param35;


    @SplitField(columnIndex = 39)
    private String param36;


    @SplitField(columnIndex = 40)
    private String param37;

    @SplitField(columnIndex = 41)
    private String param38;

    @SplitField(columnIndex = 42)
    private String param39;

    @SplitField(columnIndex = 43)
    private String param40;
}
