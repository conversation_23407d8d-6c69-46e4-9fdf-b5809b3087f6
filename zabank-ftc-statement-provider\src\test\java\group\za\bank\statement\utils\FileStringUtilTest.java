package group.za.bank.statement.utils;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

public class FileStringUtilTest {
    @Mock
    Logger log;
    @InjectMocks
    FileStringUtil fileStringUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testFileByteString() throws Exception {
        byte[] result = FileStringUtil.fileByteString("path");
    }

    @Test
    public void testDeleteFile() throws Exception {
        FileStringUtil.deleteFile("path");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme