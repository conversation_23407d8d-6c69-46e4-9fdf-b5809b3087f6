package group.za.bank.statement.constants.enums;

/**
 * 结单格式类型枚举
 *
 * <AUTHOR>
 * @date 2022/04/28
 **/
public enum StatementFormatTypeEnum {
    /**   */
    //HK_ORDER(1, "港股成交单据"),
    ORDER(1, "成交单据"),
    TRADE_CHANGE_DETAIL(3, "交易变动明细"),
    ASSET_SUMMARY(4, "证券资产摘要"),
    CASH_SUMMARY(5, "现金摘要"),
    ;

    private Integer formatType;
    private String desc;

    StatementFormatTypeEnum(Integer formatType, String desc) {
        this.formatType = formatType;
        this.desc = desc;
    }

    public Integer getFormatType() {
        return formatType;
    }

    public String getDesc() {
        return desc;
    }


}
