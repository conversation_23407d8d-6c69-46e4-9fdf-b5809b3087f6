package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.trade.model.req.feign.IsTradeDateReq;
import group.za.bank.sbs.trade.model.req.feign.MonthLastTradeDateReq;
import group.za.bank.sbs.trade.model.req.feign.QueryTradeDateCalendarListReq;
import group.za.bank.sbs.trade.model.req.feign.TradeDateDiffReq;
import group.za.bank.sbs.trade.model.resp.feign.*;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import group.za.bank.statement.service.remote.feign.StkTradeCalendarFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 交易日历远程接口
 *
 * <AUTHOR>
 * @date 2022/05/23
 **/
@Slf4j
@Service
public class TradeCalendarRemoteServiceImpl implements TradeCalendarRemoteService {

    @Autowired
    private StkTradeCalendarFeign tradeCalendarFeign;

    @Override
    public MonthLastTradeDateResp getMonthLastTradeDate(String marketCode, String yearMonth) {
        MonthLastTradeDateReq req = new MonthLastTradeDateReq();
        req.setMarketCode(marketCode);
        req.setYearMonth(yearMonth);
        ResponseData<MonthLastTradeDateResp> monthLastTradeDateResp = tradeCalendarFeign.getMonthLastTradeDate(req);
        if(log.isDebugEnabled()){
            log.debug("rpc getMonthLastTradeDate,req:{},resp:{}",req,monthLastTradeDateResp);
        }
        if (!monthLastTradeDateResp.judgeSuccess()) {
            log.info("getMonthLastTradeDate查询交易日历服务失败:{}", monthLastTradeDateResp.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return monthLastTradeDateResp.getValue();
    }

    @Override
    public MonthFirstTradeDateResp getMonthFirstTradeDate(String marketCode, String yearMonth) {
        MonthLastTradeDateReq req = new MonthLastTradeDateReq();
        req.setMarketCode(marketCode);
        req.setYearMonth(yearMonth);
        ResponseData<MonthFirstTradeDateResp> monthFirstTradeDateResp = tradeCalendarFeign.getMonthFirstTradeDate(req);

        if(log.isDebugEnabled()){
            log.debug("rpc getMonthFirstTradeDate,req:{},resp:{}",req,monthFirstTradeDateResp);
        }

        if (!monthFirstTradeDateResp.judgeSuccess()) {
            log.info("getMonthFirstTradeDate查询交易日历服务失败:{}", monthFirstTradeDateResp.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return monthFirstTradeDateResp.getValue();
    }


    /**
     * 查询指定范围内的交易日历
     *
     * @param marketCode
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<TradeCalendarResp> queryTradeDateCalendarList(String marketCode, String startDate, String endDate) {
        QueryTradeDateCalendarListReq queryTradeDateCalendarListReq = new QueryTradeDateCalendarListReq();
        queryTradeDateCalendarListReq.setMarketCode(marketCode);
        queryTradeDateCalendarListReq.setStartDate(startDate);
        queryTradeDateCalendarListReq.setEndDate(endDate);
        ResponseData<QueryTradeDateCalendarListResp> responseData
                = tradeCalendarFeign.queryTradeDateCalendarList(queryTradeDateCalendarListReq);

        if(log.isDebugEnabled()){
            log.debug("rpc queryTradeDateCalendarList,req:{},resp:{}",queryTradeDateCalendarListReq,responseData);
        }

        if (!responseData.judgeSuccess()) {
            log.info("rpc queryTradeDateCalendarList异常:{}", responseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }

        if (responseData.getValue() == null) {
            log.info("rpc queryTradeDateCalendarList返回值为null");
            throw new BusinessException(CommonErrorMsgEnum.RPC_INVOKE_EMPTY);
        }

        return responseData.getValue().getTradeCalendarRespList();

    }

    @Override
    public TradeDateDiffResp tradeDateDiff(String marketCode, String baseDate, Integer diff) {
        TradeDateDiffReq tradeDateDiffReq = new TradeDateDiffReq();
        tradeDateDiffReq.setMarketCode(marketCode);
        tradeDateDiffReq.setBaseDate(baseDate);
        tradeDateDiffReq.setDiff(diff);
        ResponseData<TradeDateDiffResp> responseData = tradeCalendarFeign.tradeDateDiff(tradeDateDiffReq);
        if(log.isDebugEnabled()){
            log.debug("rpc tradeDateDiff,req:{},resp:{}",tradeDateDiffReq,responseData);
        }

        if (!responseData.judgeSuccess()) {
            log.info("rpc tradeDateDiff异常:{}", responseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }

        if (responseData.getValue() == null) {
            log.info("rpc tradeDateDiff返回值为null");
            throw new BusinessException(CommonErrorMsgEnum.RPC_INVOKE_EMPTY);
        }

        return responseData.getValue();
    }

    @Override
    public boolean isTradeDay(String marketCode, Date baseDate) {
        IsTradeDateReq isTradeDateReq=new IsTradeDateReq();
        isTradeDateReq.setDateTime(baseDate);
        isTradeDateReq.setMarketCode(marketCode);
        ResponseData<IsTradeDateResp> responseData= tradeCalendarFeign.isTradeDate(isTradeDateReq);
        if (responseData==null||!responseData.judgeSuccess()||responseData.getValue() == null) {
            log.info("rpc tradeDateDiff返回值为null");
            throw new BusinessException(CommonErrorMsgEnum.RPC_INVOKE_EMPTY);
        }
        if(responseData.judgeSuccess()){

        }
        return responseData.getValue().isTradingDate();


    }
}
