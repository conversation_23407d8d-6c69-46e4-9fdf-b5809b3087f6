package group.za.bank.statement.utils;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.File;

@Component
@Order(1)
public class JarDirConfig implements ApplicationRunner {

    /**
     * 当前jar包所在系统中的目录
     */
    private static String jarDir;

    public static void setJarBasePath(String jarBasePath){
        jarDir = jarBasePath;
    }

    /**
     * 获取配置文件的绝对路径
     * @param path 与jar包同级的目录
     * @return /jarBasePath/path/
     */
    public static String getAbsolutePath(String path){



        return jarDir + File.separator + path;
    }

    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        //获取jar运行所在目录
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        if (jarFile != null && jarFile.getParentFile() != null){
            setJarBasePath(jarFile.getParentFile().toString());
        }else {
            setJarBasePath("C:\\work\\file");
        }
    }
}