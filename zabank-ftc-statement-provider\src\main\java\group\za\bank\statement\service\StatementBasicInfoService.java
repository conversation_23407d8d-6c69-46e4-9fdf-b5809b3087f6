package group.za.bank.statement.service;


import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;

import java.util.List;
import java.util.Map;

/**
 * @createTime 25 10:24
 * @description
 */
public interface StatementBasicInfoService {


    /**
     * 抽取业务结单数据
     * @return
     */
    BaseMonthlyStatementDataDto generateDataDto(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement statementRecord, List<TdMonthlyStatementData> monthlyStatementDataList);


    /**
     * 抽取业务结单数据
     * @return
     */
    Map<String,Object> generateDataMap(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement statementRecord, List<TdMonthlyStatementData> monthlyStatementDataList);



}
