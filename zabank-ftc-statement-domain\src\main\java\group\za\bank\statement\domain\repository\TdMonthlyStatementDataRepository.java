package group.za.bank.statement.domain.repository;


import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 29 14:14
 * @description
 */
@Repository
public interface TdMonthlyStatementDataRepository extends CrudRepository<TdMonthlyStatementBusinessData,String>{


    /**
     * 查询结单下的所有数据
     * @param statementId
     * @return
     */
    List<TdMonthlyStatementBusinessData> findByStatementId(String statementId);



    /**
     * 查询指定的结单数据
     * @param statementId
     * @return
     */
    TdMonthlyStatementBusinessData getByStatementIdAndBusinessType(String statementId,String businessType);

    /**
     * 查询指定的结单数据列表
     * @param statementId
     * @return
     */
    List<TdMonthlyStatementBusinessData> findAllByStatementIdInAndBusinessType(List<String> statementId, String businessType);


    /**
     * 删除指定的结单数据
     * @param statementId
     * @return
     */
    void deleteByStatementIdAndBusinessType(String statementId,String businessType);


}
