<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdStockStatementDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdStockStatementData">
                    <id column="id" jdbcType="BIGINT" property="id"/>
                    <result column="creator" jdbcType="VARCHAR" property="creator"/>
                    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
                    <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
                    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
                    <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
                    <result column="business_id" jdbcType="VARCHAR" property="businessId"/>
                    <result column="statement_date" jdbcType="VARCHAR" property="statementDate"/>
                    <result column="statement_type" jdbcType="INTEGER" property="statementType"/>
                    <result column="acc_no" jdbcType="VARCHAR" property="accNo"/>
                    <result column="format_type" jdbcType="INTEGER" property="formatType"/>
                    <result column="param1" jdbcType="VARCHAR" property="param1"/>
                    <result column="param2" jdbcType="VARCHAR" property="param2"/>
                    <result column="param3" jdbcType="VARCHAR" property="param3"/>
                    <result column="param4" jdbcType="VARCHAR" property="param4"/>
                    <result column="param5" jdbcType="VARCHAR" property="param5"/>
                    <result column="param6" jdbcType="VARCHAR" property="param6"/>
                    <result column="param7" jdbcType="VARCHAR" property="param7"/>
                    <result column="param8" jdbcType="VARCHAR" property="param8"/>
                    <result column="param9" jdbcType="VARCHAR" property="param9"/>
                    <result column="param10" jdbcType="VARCHAR" property="param10"/>
                    <result column="param11" jdbcType="VARCHAR" property="param11"/>
                    <result column="param12" jdbcType="VARCHAR" property="param12"/>
                    <result column="param13" jdbcType="VARCHAR" property="param13"/>
                    <result column="param14" jdbcType="VARCHAR" property="param14"/>
                    <result column="param15" jdbcType="VARCHAR" property="param15"/>
                    <result column="param16" jdbcType="VARCHAR" property="param16"/>
                    <result column="param17" jdbcType="VARCHAR" property="param17"/>
                    <result column="param18" jdbcType="VARCHAR" property="param18"/>
                    <result column="param19" jdbcType="VARCHAR" property="param19"/>
                    <result column="param20" jdbcType="VARCHAR" property="param20"/>
                    <result column="param21" jdbcType="VARCHAR" property="param21"/>
                    <result column="param22" jdbcType="VARCHAR" property="param22"/>
                    <result column="param23" jdbcType="VARCHAR" property="param23"/>
                    <result column="param24" jdbcType="VARCHAR" property="param24"/>
                    <result column="param25" jdbcType="VARCHAR" property="param25"/>
                    <result column="param26" jdbcType="VARCHAR" property="param26"/>
                    <result column="param27" jdbcType="VARCHAR" property="param27"/>
                    <result column="param28" jdbcType="VARCHAR" property="param28"/>
                    <result column="param29" jdbcType="VARCHAR" property="param29"/>
                    <result column="param30" jdbcType="VARCHAR" property="param30"/>
                    <result column="param31" jdbcType="VARCHAR" property="param31"/>
                    <result column="param32" jdbcType="VARCHAR" property="param32"/>
                    <result column="param33" jdbcType="VARCHAR" property="param33"/>
                    <result column="param34" jdbcType="VARCHAR" property="param34"/>
                    <result column="param35" jdbcType="VARCHAR" property="param35"/>
                    <result column="param36" jdbcType="VARCHAR" property="param36"/>
                    <result column="param37" jdbcType="VARCHAR" property="param37"/>
                    <result column="param38" jdbcType="VARCHAR" property="param38"/>
                    <result column="param39" jdbcType="VARCHAR" property="param39"/>
                    <result column="param40" jdbcType="VARCHAR" property="param40"/>
                    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime"/>
                    <result column="remark" jdbcType="VARCHAR" property="remark"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, business_id, statement_date, statement_type, acc_no, format_type, param1, param2, param3, param4, param5, param6, param7, param8, param9, param10
            , param11, param12, param13, param14, param15, param16, param17, param18, param19, param20
            , param21, param22, param23, param24, param25, param26, param27, param28, param29, param30
            , param31, param32, param33, param34, param35, param36, param37, param38, param39, param40
            , record_time, remark, creator, gmt_created, modifier, gmt_modified, is_deleted
        </sql>

        <!-- 自定义通用SQL查询条件 -->
        <sql id="Where_Extra_Condition">
        </sql>
    <delete id="deleteByBusinessId" parameterType="java.lang.String">
        delete from td_stock_statement_data where business_id=#{businessId}
    </delete>

    <!--查询日期范围内的记录-->
    <!--@Param("ttlMarketCode") String ttlMarketCode,@Param("startTradeDate")  String startTradeDate
            ,@Param("endTradeDate")  String endTradeDate, @Param("statementType") Integer statementType,@Param("accNo")  String accNo,@Param("formatType") Integer formatType-->
    <select id="queryPeriodDataByFormatType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_stock_statement_data`
        WHERE is_deleted='N'
        <if test="ttlMarketCode != null">
            <![CDATA[ AND market_code = #{ttlMarketCode} ]]>
        </if>

        <if test="startTradeDate != null">
            <![CDATA[ AND trade_date >= #{startTradeDate} ]]>
        </if>
        <if test="endTradeDate != null">
            <![CDATA[ AND trade_date <= #{endTradeDate} ]]>
        </if>

        <if test="statementType != null">
            <![CDATA[ AND statement_type = #{statementType} ]]>
        </if>
        <if test="accNo != null">
            <![CDATA[ AND acc_no = #{accNo} ]]>
        </if>
        <if test="formatType != null">
            <![CDATA[ AND format_type = #{formatType} ]]>
        </if>

    </select>

</mapper>
