package group.za.bank.statement.service.remote;

import group.za.bank.sbs.business.model.req.TransferDetailReq;
import group.za.bank.sbs.business.model.resp.TransferDetailResp;

import java.util.List;

/**
 * 股票综合服务
 *
 * <AUTHOR>
 * @date 2023/08/22
 **/
public interface BusinessRemoteService {

    /**
     * 查询转仓详情列表
     * @param transferDetailReq
     * @return
     */
    List<TransferDetailResp> queryTransferDetailList(TransferDetailReq transferDetailReq);
}
