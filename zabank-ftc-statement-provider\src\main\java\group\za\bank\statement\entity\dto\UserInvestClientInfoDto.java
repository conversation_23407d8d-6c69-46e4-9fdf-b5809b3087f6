package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 21 15:14
 * @description
 */
@Data
public class UserInvestClientInfoDto {

    /**
     * 银行用户id
     */
    private String bankUserId;

    /**
     * 投资账号id
     */
    private String clientId;

    /**
     * 柜台账户id=brokerId
     */
    private String accountId;



    /**
     * 银行账号id
     * 适用于基金
     */
    private String bankAccountId;



    /**
     * @see group.za.bank.invest.account.constants.enums.AccountTypeEnum
     * 账户类型
     */
    private String accountType;

    /**
     * 开户完成时间
     */
    private Date openFinishedTime;

}
