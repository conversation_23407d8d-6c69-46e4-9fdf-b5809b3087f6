package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> zehua.li
 * @Classname LocalDailyStatementTypeEnum
 * @Description TODO
 * @Date 2024/8/26 14:40
 */
@Getter
@AllArgsConstructor
public enum LocalDailyStatementTypeEnum implements BaseEnum {
    ORDER("order","订单数据"),
    TRADE_CHANGE("trade","交易变动"),
    HOLDING("holding","持仓数据"),
    TAX_FEE("fee","交易税费")
    ;

    private String value;

    private String msg;
}
