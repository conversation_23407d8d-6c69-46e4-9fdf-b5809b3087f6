package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 月结单公司行动待处理数据状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StatementActionPendingStatusEnum implements BaseEnum {

    UN_PROCESSED("0",  "未处理"),

    PROCESSED("1", "已处理"),
    ;

    private String value;

    private String msg;
}
