package group.za.bank.statement.service.impl;

import group.za.bank.sbs.trade.model.resp.feign.TradeCalendarResp;
import group.za.bank.statement.agent.TradeCalendarAgent;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

public class StockTradeDateServiceImplTest extends BaseTestService {
    @Mock
    TradeCalendarAgent tradeCalendarAgent;
    @Mock
    SystemConfig systemConfig;
    @Mock
    TradeCalendarRemoteService tradeCalendarRemoteService;
    @InjectMocks
    StockTradeDateServiceImpl stockTradeDateServiceImpl;

    @Test
    public void testQueryCoreCalendarTradeDateList() throws Exception {
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("202401");
        TradeCalendarResp tradeCalendarResp = new TradeCalendarResp();
        when(tradeCalendarRemoteService.queryTradeDateCalendarList(anyString(), anyString(), anyString())).thenReturn(Arrays.<TradeCalendarResp>asList(tradeCalendarResp));

        List<String> result = stockTradeDateServiceImpl.queryCoreCalendarTradeDateList("202401");
    }

    @Test
    public void testQueryCoreCalendarTradeDateList2() throws Exception {
        TradeCalendarResp tradeCalendarResp = new TradeCalendarResp();

        when(tradeCalendarRemoteService.queryTradeDateCalendarList(anyString(), anyString(), anyString())).thenReturn(Arrays.<TradeCalendarResp>asList(tradeCalendarResp));

        List<String> result = stockTradeDateServiceImpl.queryCoreCalendarTradeDateList(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 35).getTime(), new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 35).getTime());
    }

    @Test
    public void testQueryCalendarTradeDateList() throws Exception {
        TradeCalendarResp tradeCalendarResp = new TradeCalendarResp();
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("202401");
        when(tradeCalendarRemoteService.queryTradeDateCalendarList(anyString(), anyString(), anyString())).thenReturn(Arrays.<TradeCalendarResp>asList(tradeCalendarResp));

        List<String> result = stockTradeDateServiceImpl.queryCalendarTradeDateList("marketCode", "202401");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme