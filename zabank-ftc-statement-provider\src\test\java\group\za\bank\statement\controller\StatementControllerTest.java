package group.za.bank.statement.controller;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.entity.req.AppUserMonthlyStatementListReq;
import group.za.bank.statement.entity.req.NativeDetailReq;
import group.za.bank.statement.entity.req.NativeSummaryReq;
import group.za.bank.statement.entity.resp.NativeDetailResp;
import group.za.bank.statement.entity.resp.NativeSummaryListResp;
import group.za.bank.statement.entity.resp.UserMonthlyStatementListResp;
import group.za.bank.statement.manager.CryptoTradeCoreManager;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.UserStatementNativeService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

public class StatementControllerTest extends BaseTestService {
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    UserStatementNativeService userStatementNativeService;
    @Mock
    CryptoTradeCoreManager cryptoTradeCoreManager;
    @Mock
    Logger log;
    @InjectMocks
    StatementController statementController;


    @Test
    public void testUserMonthlyStatementList() throws Exception {
        when(monthlyStatementService.queryUserMonthlyStatement(any(), any())).thenReturn(new UserMonthlyStatementListResp());

        ResponseData<UserMonthlyStatementListResp> result = statementController.userMonthlyStatementList(new AppUserMonthlyStatementListReq());
    }

    @Test
    public void testNativeSummary() throws Exception {
        when(userStatementNativeService.queryUserMonthlyStatementNativeSummaryData(any(), any())).thenReturn(new NativeSummaryListResp());

        ResponseData<NativeSummaryListResp> result = statementController.nativeSummary(new NativeSummaryReq());
    }

    @Test
    public void testNativeDetail() throws Exception {
        when(userStatementNativeService.queryUserMonthlyStatementNativeDetail(any(), any())).thenReturn(new NativeDetailResp());

        ResponseData<NativeDetailResp> result = statementController.nativeDetail(new NativeDetailReq());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme