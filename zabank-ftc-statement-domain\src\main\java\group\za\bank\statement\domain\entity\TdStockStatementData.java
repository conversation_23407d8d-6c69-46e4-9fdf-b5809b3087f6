package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
* This class corresponds to the database table td_stock_statement_data
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 14:08:20
*/
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_stock_statement_data")
public class TdStockStatementData extends AuditIdEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
    * Database Column Name: td_stock_statement_data.business_id
    * <p>
    * Database Column Remarks: 业务逻辑id
    * </p>
    */
    @Column(name = "business_id")
    private String businessId;

    /**
    * Database Column Name: td_stock_statement_data.statement_date
    * <p>
    * Database Column Remarks: 结单日期:月结单-yyyyMM，日结单-yyyyMMdd
    * </p>
    */
    @Column(name = "statement_date")
    private String statementDate;

    /**
     * Database Column Name: td_stock_statement_data.marketCode
     * <p>
     * Database Column Remarks: 市场编码
     * </p>
     */
    @Column(name = "market_code")
    private String marketCode;

    /**
     * Database Column Name: td_stock_statement_data.trade_date
     * <p>
     * Database Column Remarks: 交易日
     * </p>
     */
    @Column(name = "trade_date")
    private Date tradeDate;
    /**
    * Database Column Name: td_stock_statement_data.statement_type
    * <p>
    * Database Column Remarks: 结单类型:1-日结单，2-月结单
    * </p>
    */
    @Column(name = "statement_type", nullable = false)
    private Integer statementType;

    /**
    * Database Column Name: td_stock_statement_data.acc_no
    * <p>
    * Database Column Remarks: ttl柜台账户
    * </p>
    */
    @Column(name = "acc_no")
    private String accNo;

    /**
    * Database Column Name: td_stock_statement_data.format_type
    * <p>
    * Database Column Remarks: 1-港股成交单,2-美股成交单，3-交易变动明细，4证券资产摘要，5-现金摘要
    * </p>
    */
    @Column(name = "format_type")
    private Integer formatType;

    /**
    * Database Column Name: td_stock_statement_data.param1
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param1")
    private String param1;

    /**
    * Database Column Name: td_stock_statement_data.param2
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param2")
    private String param2;

    /**
    * Database Column Name: td_stock_statement_data.param3
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param3")
    private String param3;

    /**
    * Database Column Name: td_stock_statement_data.param4
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param4")
    private String param4;

    /**
    * Database Column Name: td_stock_statement_data.param5
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param5")
    private String param5;

    /**
    * Database Column Name: td_stock_statement_data.param6
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param6")
    private String param6;

    /**
    * Database Column Name: td_stock_statement_data.param7
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param7")
    private String param7;

    /**
    * Database Column Name: td_stock_statement_data.param8
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param8")
    private String param8;

    /**
    * Database Column Name: td_stock_statement_data.param9
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param9")
    private String param9;

    /**
    * Database Column Name: td_stock_statement_data.param10
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param10")
    private String param10;

    /**
    * Database Column Name: td_stock_statement_data.param11
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param11")
    private String param11;

    /**
    * Database Column Name: td_stock_statement_data.param12
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param12")
    private String param12;

    /**
    * Database Column Name: td_stock_statement_data.param13
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param13")
    private String param13;

    /**
    * Database Column Name: td_stock_statement_data.param14
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param14")
    private String param14;

    /**
    * Database Column Name: td_stock_statement_data.param15
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param15")
    private String param15;

    /**
    * Database Column Name: td_stock_statement_data.param16
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param16")
    private String param16;

    /**
    * Database Column Name: td_stock_statement_data.param17
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param17")
    private String param17;

    /**
    * Database Column Name: td_stock_statement_data.param18
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param18")
    private String param18;

    /**
    * Database Column Name: td_stock_statement_data.param19
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param19")
    private String param19;

    /**
    * Database Column Name: td_stock_statement_data.param20
    * <p>
    * Database Column Remarks: 
    * </p>
    */
    @Column(name = "param20")
    private String param20;


    /**
     * Database Column Name: td_stock_statement_data.param21
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param21")
    private String param21;

    /**
     * Database Column Name: td_stock_statement_data.param22
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param22")
    private String param22;

    /**
     * Database Column Name: td_stock_statement_data.param23
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param23")
    private String param23;

    /**
     * Database Column Name: td_stock_statement_data.param24
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param24")
    private String param24;

    /**
     * Database Column Name: td_stock_statement_data.param25
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param25")
    private String param25;

    /**
     * Database Column Name: td_stock_statement_data.param26
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param26")
    private String param26;

    /**
     * Database Column Name: td_stock_statement_data.param27
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param27")
    private String param27;

    /**
     * Database Column Name: td_stock_statement_data.param28
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param28")
    private String param28;

    /**
     * Database Column Name: td_stock_statement_data.param29
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param29")
    private String param29;

    /**
     * Database Column Name: td_stock_statement_data.param20
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param30")
    private String param30;

    /**
     * Database Column Name: td_stock_statement_data.param31
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param31")
    private String param31;

    /**
     * Database Column Name: td_stock_statement_data.param32
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param32")
    private String param32;

    /**
     * Database Column Name: td_stock_statement_data.param33
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param33")
    private String param33;

    /**
     * Database Column Name: td_stock_statement_data.param34
     * <p>
     * Database Column Remarks: 
     * </p>
     */
    @Column(name = "param34")
    private String param34;



    /**
     * Database Column Name: td_stock_statement_data.param35
     * <p>
     * Database Column Remarks:
     * </p>
     */
    @Column(name = "param35")
    private String param35;

    /**
     * Database Column Name: td_stock_statement_data.param36
     * <p>
     * Database Column Remarks:
     * </p>
     */
    @Column(name = "param36")
    private String param36;

    /**
     * Database Column Name: td_stock_statement_data.param37
     * <p>
     * Database Column Remarks:
     * </p>
     */
    @Column(name = "param37")
    private String param37;

    /**
     * Database Column Name: td_stock_statement_data.param38
     * <p>
     * Database Column Remarks:
     * </p>
     */
    @Column(name = "param38")
    private String param38;

    /**
     * Database Column Name: td_stock_statement_data.param39
     * <p>
     * Database Column Remarks:
     * </p>
     */
    @Column(name = "param39")
    private String param39;

    /**
     * Database Column Name: td_stock_statement_data.param40
     * <p>
     * Database Column Remarks:
     * </p>
     */
    @Column(name = "param40")
    private String param40;
    /**
    * Database Column Name: td_stock_statement_data.record_time
    * <p>
    * Database Column Remarks: 结单记录生成时间
    * </p>
    */
    @Column(name = "record_time")
    private Date recordTime;

    /**
    * Database Column Name: td_stock_statement_data.remark
    * <p>
    * Database Column Remarks: 备注
    * </p>
    */
    @Column(name = "remark")
    private String remark;

}