package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;

/**
 * 公司行动类别枚举值
 *
 * <AUTHOR>
 * @date 2025/03/29
 **/
@AllArgsConstructor
public enum StatementActionEventTypeEnum implements BaseEnum {

    RIGHTS_ISSUE("1", "Rights Issue 派送供股權", "派送供股权","Rights Issue","派送供股權"),
    OPEN_OFFER("2", "Open Offer 派送公開發售股權", "派送公开发售股权","Open Offer","派送公開發售股權"),
    PREFERENTIAL_OFFER("3", "Preferential Offer派送優先發售股權", "派送优先发售股权","Preferential Offer","派送優先發售股權"),
    EQUITY_WARRANT("4", "Equity Warrant派送股票認股權", "派送股票认股权","Equity Warrant","派送股票認股權"),

    //Rights Subscription 認購供股權
    // Open Offer Subscription 認購公開發售股權
    // Preferential Offer Subscription 認購優先發售股權
    //Equity Warrant Subscription 認購股票認股權
    RIGHTS_SUBSCRIPTION("11", "Rights Subscription 認購供股權", "认购供股权","Rights Subscription","認購供股權"),
    OPEN_OFFER_SUBSCRIPTION("12", "Open Offer Subscription 認購公開發售股權", "认购公开发售股权","Open Offer Subscription","認購公開發售股權"),
    PREFERENTIAL_OFFER_SUBSCRIPTION("13", "Preferential Offer Subscription 認購優先發售股權", "认购优先发售股权","Preferential Offer Subscription","認購優先發售股權"),
    EQUITY_WARRANT_SUBSCRIPTION("14", "Equity Warrant Subscription 認購股票認股權", "认购股票认购权","Equity Warrant Subscription","認購股票認股權"),
    RIGHTS_SUBSCRIPTION_FEE("15", "Rights Subscription - Corporate Action Fee 認購供股權", "认购供股权","Rights Subscription","認購供股權"),
    OPEN_OFFER_SUBSCRIPTION_FEE("16", "Open Offer Subscription 認購公開發售股權", "认购公开发售股权","Open Offer Subscription","認購公開發售股權"),
    PREFERENTIAL_OFFER_SUBSCRIPTION_FEE("17", "Preferential Offer Subscription 認購優先發售股權", "认购优先发售股权","Preferential Offer Subscription","認購優先發售股權"),
    EQUITY_WARRANT_SUBSCRIPTION_FEE("18", "Equity Warrant Subscription 認購股票認股權", "认购股票认购权","Equity Warrant Subscription","認購股票認股權"),

    //Rights Issue Allotment and Refunds 認購供股權分配及退款
    // Open Offer Allotment and Refunds 認購公開發售股權分配及退款
    // Preferential Offer Allotment and Refunds 認購優先發售股權分配及退款
    //Equity Warrant Allotment and Refunds 股票認股權分配及退款
    RIGHTS_ISSUE_ALLOTMENT("21", "Rights Issue Allotment  認購供股權分配", "认购供股权分配","Rights Issue Allotment","認購供股權分配"),
    OPEN_OFFER_ALLOTMENT("22", "Open Offer Allotment 認購公開發售股權分配", "认购公开发售股权分配","Open Offer Allotment","認購公開發售股權分配"),
    PREFERENTIAL_OFFER_ALLOTMENT("23", "Preferential Offer Allotment 認購優先發售股權分配", "认购优先发售股权分配","Preferential Offer Allotment","認購優先發售股權分配"),
    EQUITY_WARRANT_ALLOTMENT("24", "Equity Warrant Allotment and Refunds 股票認股權分配", "认购股票认购权分配","Equity Warrant Allotment","認購股票認股權分配"),

    RIGHTS_ISSUE_REFUNDS("31", "Rights Issue Refunds 認購供股權退款", "认购供股权退款","Rights Issue Refunds","認購供股權退款"),
    OPEN_OFFER_REFUNDS("32", "Open Offer Refunds 認購公開發售股權退款", "认购公开发售股权退款","Open Offer Refunds","認購公開發售股權退款"),
    PREFERENTIAL_OFFER_REFUNDS("33", "Preferential Offer Refunds 認購優先發售股權退款", "认购优先发售股权退款","Preferential Offer Refunds","認購優先發售股權退款"),
    EQUITY_WARRANT_REFUNDS("34", "Equity Warrant Refunds 股票認股權退款", "认购股票认购权退款","Equity Warrant Refunds","認購股票認股權退款"),

    //Derivative Warrant Exercised 行使槓桿認股權證
    // CBBC Exercised  行使牛熊證
    DERIVATIVE_WARRANT_EXERCISED("41", "Derivative Warrant Exercised 行使槓桿認股權證", "行使杠杆认股权证","Derivative Warrant Exercised","行使槓桿認股權證"),
    CBBC_EXERCISED("42", "CBBC Exercised  行使牛熊證", "行使牛熊证","CBBC Exercised ","行使牛熊證"),

    //CBBC Forced Redemption 牛熊證強制回收
    //Rights Issue Lapsed 供股權失效
    //Open Offer Lapsed 公開發售股權失效
    //Preferential Offer Lapsed 優先發售股權失效
    //Equity Warrant Expiration股票認股權證失效
    CBBC_FORCED_REDEMPTION("51", "CBBC Forced Redemption 牛熊證強制回收", "牛熊证强退","CBBC Forced Redemption","牛熊證強制回收"),
    RIGHTS_ISSUE_LAPSED("52", "Rights Issue Lapsed 供股權失效", "供股权失效","Rights Issue Lapsed","供股權失效"),
    OPEN_OFFER_LAPSED("53", "Open Offer Lapsed 公開發售股權失效", "公开售股权失效","Open Offer Lapsed","公開發售股權失效"),
    PREFERENTIAL_OFFER_LAPSED("54", "Preferential Offer Lapsed 優先發售股權失效", "优先发售股权失效","Preferential Offer Lapsed","優先發售股權失效"),
    EQUITY_WARRANT_EXPIRATION("55", "Equity Warrant Expiration股票認股權證失效", "股票认股权证失效","Equity Warrant Expiration","股票認股權證失效"),
    DERIVATIVE_WARRANT_EXPIRATION("56", "Derivative Warrant槓桿認股權證失效", "杠杆认股权证失效","Derivative Warrant Expiration","槓桿認股權證失效"),

    ;


    private String value;
    private String msg;
    private String typeNameCn;
    private String typeNameEn;
    private String typeNameHk;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    public String getTypeNameCn() {
        return typeNameCn;
    }

    public String getTypeNameEn() {
        return typeNameEn;
    }

    public String getTypeNameHk() {
        return typeNameHk;
    }

}
