package group.za.bank.statement.constants.enums;

import lombok.Getter;

/**
 * 方向枚举，出账入账
 * <AUTHOR>
 * @date 2022/05/11
 **/
@Getter
public enum StatementInOutEnum {
    IN(0, "In存入", "In存入"),
    OUT(1, "Out提取", "Out提取"),
    ;

    private Integer direction;
    private String desc;
    private String descHk;

    StatementInOutEnum(Integer direction, String desc, String descHk) {
        this.direction = direction;
        this.desc = desc;
        this.descHk = descHk;
    }
}
