package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.service.MonthlyStatementService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

public class MonthlyStatementDocGenerateJobTest extends BaseTestService {
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    SystemConfig systemConfig;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    MonthlyStatementDocGenerateJob monthlyStatementDocGenerateJob;

    @Test
    public void testExecute() throws Exception {

        ReturnT<String> result = monthlyStatementDocGenerateJob.execute("{\"period\":\"202203\",businessType:\"fund,stock\"}");
    }
}

