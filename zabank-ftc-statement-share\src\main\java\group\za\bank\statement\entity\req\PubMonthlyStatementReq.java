package group.za.bank.statement.entity.req;

import group.za.bank.invest.basecommon.entity.req.BaseReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/25
 * @Description 查询已发送月结单
 * @Version v1.0
 */
@Data
public class PubMonthlyStatementReq extends BaseReq {
    /**
     * 投资账户id
     */
    @NotBlank(message = "clientId can't empty")
    private String clientId;
    /**
     * 结单期数
     */
    @NotEmpty(message = "periods can't empty")
    private List<String> periods;
    /**
     * 文档语言
     */
    private String docLang;
}
