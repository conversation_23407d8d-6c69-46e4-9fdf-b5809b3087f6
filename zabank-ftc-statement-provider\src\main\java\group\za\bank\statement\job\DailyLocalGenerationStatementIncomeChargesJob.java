package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.quote.common.enums.MarketEnum;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR> zehua.li
 * @Classname DailyLocalGenerationStatementIncomeChargesJob
 * @Description 从本地归集结单的红股红利数据
 * CDV：现金股息
 * SOP：以股带息
 * BSH：派发红股
 * SCAS：派发股票
 * ESB：认购股权
 * TPA：股票回购
 * https://km.in.za/pages/viewpage.action?pageId=********
 * @Date 2024/8/26 11:04
 */
@Component
@Slf4j
@JobHandler(value = "dailyLocalGenerationStatementIncomeChargesJob")
public class DailyLocalGenerationStatementIncomeChargesJob extends IJobHandler {
    @Autowired
    private TradeCalendarRemoteService tradeCalendarRemoteService;
    @Autowired
    private TdStockStatementDataService stockStatementDataService;

    @Override
    public ReturnT<String> execute(String tradeDateStr) throws Exception {
        //yyyy-MM-dd
        log.info("【dailyLocalGenerationStatementIncomeChargesJob】 param:{}", tradeDateStr);
        Date tradeDate=DateUtil.parse(new Date(),DateUtil.FORMAT_SHORT);
        if(StringUtils.isNotBlank(tradeDateStr)){
            tradeDate= DateUtil.parse(tradeDateStr,DateUtil.FORMAT_SHORT);
            if(tradeDate==null){
                log.error("job 传入日期错误");
                return ReturnT.FAIL;
            }
        }
        boolean isTradeDay= tradeCalendarRemoteService.isTradeDay(MarketEnum.US.getValue(),tradeDate);
        if(isTradeDay){
            log.info("开始同步费用数据tradeDate={}",tradeDate);
            stockStatementDataService.dailyLocalGenerationStatementIncomeCharges(tradeDate);
            log.info("结束同步费用数据tradeDate={}",tradeDate);
        }else{
            log.info("非交易日,不执行同步tradeDate={}",tradeDate);
        }


        return ReturnT.SUCCESS;
    }
}
