package group.za.bank.statement.entity.dto;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 07 18:21
 * @description
 */
@Data
public class UserMonthlyStatementQueryDto {

    /**
     * 用户id
     */
    String userId ;
    /**
     * 银行账户id
     */
    String clientId;
    /**
     * 结单起始期数
     */
    String startPeriod;
    /**
     * 结单截止期数
     */
    String endPeriod;

    /**
     * 年份
     */
    String year;

    /**
     * 查询结果
     */
    Map<String, TdFundMonthlyStatement> fundMonthlyStatementMap ;

}
