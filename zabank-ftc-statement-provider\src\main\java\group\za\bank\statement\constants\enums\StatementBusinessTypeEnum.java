package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 结单交易类型
 *
 * <AUTHOR>
 * @date 2022/05/07f
 **/
@Getter
public enum StatementBusinessTypeEnum {
    /**
     * 结单交易类型
     */
    SDP("SDP", "股票存入", "股票存入", "Stock Deposit", StatementInOutEnum.IN),
    SWD("SWD", "股票转出/提取", "股票轉出/提取", "Stock transfer out/withdrawal", StatementInOutEnum.OUT),
    CDP("CDP", "现金存入", "現金存入", "Cash Deposit", StatementInOutEnum.IN),
    CWD("CWD", "现金提取", "現金提取", "Cash Withdraw", StatementInOutEnum.OUT),
    CDV("CDV", "现金股息", "現金股息", "Cash Dividend", StatementInOutEnum.IN),
    CDV_F("CDV_F", "现金股息代收费", "現金股息代收費", "Cash Dividend Collection Fee", StatementInOutEnum.OUT),
    SOP("SOP", "以股代息", "以股代息", "Scrip Options", StatementInOutEnum.IN),
    BSH("BSH", "派发红股", "派發紅股", "Bonus Shares", StatementInOutEnum.IN),
    SCAS("SCAS", "派发股票", "派發股票", "Bonus Others", StatementInOutEnum.IN),
    BRT("BRT", "派送股权", "派送股權", "Rights Issue", StatementInOutEnum.IN),
    BWT("BWT", "派送涡轮", "派送渦輪", "Rights Warrant", StatementInOutEnum.IN),
    ESB("ESB", "认购股权", "認購股權", "Rights Subscription", StatementInOutEnum.OUT),
    ESB_F("ESB_F", "公司行動費用", "公司行動費用", "Corporate Action Fee", StatementInOutEnum.OUT),
    EAL("EAL", "认购股权分配及退款", "認購股權分配及退款", "Rights Issues Allotment and Refund", StatementInOutEnum.IN),
    EAL_R("EAL_R", "认购股权退款", "認購股權退款", "Rights Issues Refund", StatementInOutEnum.IN),

    IWD("IWD", "新股认购", "新股認購", "IPO Withdrawal", StatementInOutEnum.IN),
    IDP("IDP", "新股配发/退款", "新股配發/退款", "IPO Deposit", StatementInOutEnum.IN),
    SSP("SSP", "股票分拆", "股票分拆", "Stock Split", StatementInOutEnum.IN),
    SCS("SCS", "股票合并", "股票合併", "Stock Consolidate", StatementInOutEnum.OUT),
    TPS("TPS", "股票回购-股票提取", "股票回購-股票提取", "Buy-back offer-Stock Withdrawal", StatementInOutEnum.OUT),
    TPA("TPA", "股票回购", "股票回购", "Buy-back offer", StatementInOutEnum.IN),
    TPA_F1("TPA_F1", "现金收购-印花税", "現金收購-印花稅", "Cash Offer - Stamp Duty", StatementInOutEnum.OUT),
    TPA_F2("TPA_F2", "现金收购-代收费用", "現金收購-代收費用", "Cash Offer - Cash Collection Charge", StatementInOutEnum.OUT),
    DBC("DBC", "过户费", "過戶費", "Scrip Fee", StatementInOutEnum.OUT),
    SCH("SCH", "股票换股", "股票換股", "Stock Conversion", StatementInOutEnum.OUT),
    ADR("ADE", "美国预托证券收费", "美國預托證券收費", "ADR Fee", StatementInOutEnum.OUT),
    FTT("FTE", "外国金融交易税", "外國金融交易稅", "Foreign FTT Fee", StatementInOutEnum.OUT),
    SCPSW("SCPSW", "持仓移除", "持倉移除", "Holding Removal", StatementInOutEnum.OUT),
    ADDTAX("ADDTAX", "股息预扣税", "股息预扣税", "Dividend Withholding Tax", StatementInOutEnum.OUT),
    REBATE("REBATE", "股息预扣税退款", "股息預扣稅退款", "Dividend Withholding Tax Refund", StatementInOutEnum.IN),

    CAPITAL_IN("CAPITAL_IN", "股票资金转账", "股票資金轉賬", "Stock Money Transfer", StatementInOutEnum.IN),

    CAPITAL_OUT("CAPITAL_OUT", "股票资金转账", "股票資金轉賬", "Stock Money Transfer", StatementInOutEnum.OUT),

    STOCK_MOVEMENT("STOCK_MOVEMENT", "股票股份变动", "股票股份變動", "Stock Movement", StatementInOutEnum.IN),

    STOCK_IPO("STOCK_IPO", "IPO新股认购", "IPO新股認購", "IPO Application", StatementInOutEnum.OUT),
    STOCK_IPO_FEE("STOCK_IPO_FEE", "IPO新股认购费用", "IPO新股認購費用", "IPO Application Fee", StatementInOutEnum.OUT),
    STOCK_IPO_REFUND("STOCK_IPO_REFUND", "IPO退款", "IPO退款", "IPO Refund", StatementInOutEnum.IN),
    STOCK_IPO_ALLOTMENT("STOCK_IPO_ALLOTMENT", "IPO中签", "IPO中簽", "IPO Allotment", StatementInOutEnum.IN),

    CBBCED("CBBCED", "行使牛熊证", "行使牛熊證", "CBBC Exercided", StatementInOutEnum.IN),

    CBBCE("CBBCE", "行使牛熊证", "行使牛熊證", "CBBC Exercided", StatementInOutEnum.OUT),
    CBBCFR("CBBCFR", "牛熊证强制回收", "牛熊證強制回收", "CBBC Forced Redemption", StatementInOutEnum.OUT),

    ;


    private String businessType;
    private String desc;
    private String descHk;
    private String descEn;
    private StatementInOutEnum directionEnum;

    StatementBusinessTypeEnum(String businessType, String desc, String descHk, String descEn, StatementInOutEnum directionEnum) {
        this.businessType = businessType;
        this.desc = desc;
        this.descHk = descHk;
        this.descEn = descEn;
        this.directionEnum = directionEnum;
    }


    /**
     * 是否需要生成结单的判断类型
     *
     * @return
     */
    public static List<String> getIsNeedGenerateBusinessList() {
        return Arrays.asList(CDV.businessType, SOP.businessType, BSH.businessType, BRT.businessType, BWT.businessType,
                SSP.businessType, SCS.businessType, TPS.businessType, SCH.businessType, ESB.businessType, EAL.businessType, TPA.businessType, SCPSW.businessType, SCAS.businessType);
    }


    /**
     * 派息相关业务类型
     *
     * @return
     */
    public static List<String> getDividendBusinessList() {
        return Arrays.asList(CDV.businessType, SOP.businessType, BSH.businessType, SCAS.businessType, ADR.businessType, FTT.businessType, ESB.businessType, TPA.businessType);
    }

    /**
     * 港股派息相关业务类型
     *
     * @return
     */
    public static List<String> getHkDividendBusinessList() {
        return Arrays.asList(CDV.businessType, SOP.businessType, BSH.businessType, SCAS.businessType, ESB.businessType, EAL.businessType, TPA.businessType);
    }

    /**
     * 持仓变动相关业务类型
     *
     * @return
     */
    public static List<String> getHoldChangeBusinessList() {
        return Arrays.asList(SSP.businessType, SCS.businessType, TPS.businessType,
                BRT.businessType, BWT.businessType, ESB.businessType, EAL.businessType, SCPSW.businessType);
    }

    /**
     * 公司行动资金相关业务类型
     *
     * @return
     */
    public static List<String> getActionCapitalBusinessList() {
        return Arrays.asList(TPA.businessType, CDV.businessType, ESB.businessType);
    }

    public static List<String> getActionBookCloseBusinessList() {
        return Arrays.asList(SSP.businessType, SCS.businessType, BSH.businessType, SCAS.businessType, BRT.businessType, BWT.businessType);
    }

    public static List<String> getActionExerciseHoldChangeBusinessList() {
        return Arrays.asList(ESB.businessType, EAL.businessType, TPS.businessType, TPA.businessType);
    }


    public static List<String> getHkActionFeeBusinessList() {
        return Arrays.asList(CDV.businessType, ESB.businessType, TPA.businessType);
    }

    public static List<String> getHkActionRightsBusinessList() {
        return Arrays.asList(EAL.businessType, ESB.businessType);
    }

    public static List<String> getHkActionRebildEventTypeBusinessList() {
        return Arrays.asList(EAL.businessType, ESB.businessType, ESB_F.businessType, CBBCE.businessType, BRT.businessType, BWT.businessType);
    }

    public static List<String> getHkIpoBusinessList() {
        return Arrays.asList(STOCK_IPO.businessType, STOCK_IPO_FEE.businessType, STOCK_IPO_REFUND.businessType, STOCK_IPO_ALLOTMENT.businessType);
    }


    public static List<String> getAllBusinessTypeList() {
        List<String> result = new ArrayList<>();
        for (StatementBusinessTypeEnum value : StatementBusinessTypeEnum.values()) {
            result.add(value.businessType);
        }
        return result;
    }

    public static StatementBusinessTypeEnum getByBusinessType(String businessType) {
        for (StatementBusinessTypeEnum value : StatementBusinessTypeEnum.values()) {
            if (value.businessType.equals(businessType)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据语言类型获取对应的描述
     *
     * @param businessTypeEnum
     * @param dockLang
     * @return
     */
    public static String getDescByDocLang(StatementBusinessTypeEnum businessTypeEnum, String dockLang) {
        if (StringUtils.isEmpty(dockLang)) {
            return businessTypeEnum.getDescEn();
        }
        if (I18nSupportEnum.CN_ZH.getName().equals(dockLang) || I18nSupportEnum.CN_ZH.getNameOfBank().equals(dockLang)) {
            return businessTypeEnum.getDesc();
        } else if (I18nSupportEnum.CN_HK.getName().equals(dockLang) || I18nSupportEnum.CN_HK.getNameOfBank().equals(dockLang)) {
            return businessTypeEnum.getDescHk();
        } else {
            return businessTypeEnum.getDescEn();
        }
    }

}
