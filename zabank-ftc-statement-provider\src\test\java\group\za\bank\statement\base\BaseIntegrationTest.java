package group.za.bank.statement.base;

import group.za.bank.statement.StatementApplication;
import group.za.bank.statement.category.IntegrationTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {StatementApplication.class},
        properties = {"apollo.meta:http://172.16.2.16:8080"})
@WebAppConfiguration
@Category(IntegrationTest.class)
public class BaseIntegrationTest {

    protected MockMvc mockMvc;

    @Autowired
    protected WebApplicationContext webApplicationContext;


    static {
        System.setProperty("env", "dev");
    }

    @Before
    public void setUp() throws Exception {
        // 初始化测试用例类中由Mockito的注解标注的所有模拟对象
        MockitoAnnotations.initMocks(this);

        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    public MvcResult testPut(String url, String requestStr) {
        try {
            MvcResult mvcResult = mockMvc
                    .perform(MockMvcRequestBuilders.post(url).contentType(MediaType.APPLICATION_JSON).content(requestStr))
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk())
                    // .andExpect(jsonPath("$.code",is("0000")))
                    .andReturn();

            return mvcResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public MvcResult testPut(String url, String requestStr, Map<String, String> headMap) {
        try {

            HttpHeaders heads = new HttpHeaders();
            if (headMap != null) {
                for (String key : headMap.keySet()) {
                    String value = headMap.get(key);
                    heads.add(key, value);
                }
            }
            MvcResult mvcResult = mockMvc
                    .perform(MockMvcRequestBuilders.post(url).contentType(MediaType.APPLICATION_JSON).content(requestStr)
                            .headers(heads))
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk())
                    // .andExpect(jsonPath("$.code",is("0000")))
                    .andReturn();

            return mvcResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
