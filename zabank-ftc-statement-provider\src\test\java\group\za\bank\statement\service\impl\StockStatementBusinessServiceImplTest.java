package group.za.bank.statement.service.impl;

import com.google.common.collect.Lists;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.common.utils.SpringUtils;
import group.za.bank.sbs.trade.common.constant.enums.StockCodeTypeEnum;
import group.za.bank.sbs.trade.model.dto.StkBusinessRecordDTO;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.sbs.tradedata.model.req.ActionFractionOrderQueryReq;
import group.za.bank.sbs.tradedata.model.resp.ActionFractionOrderResp;
import group.za.bank.statement.agent.RateAgent;
import group.za.bank.statement.agent.StkInfoAgent;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.MonthlyStatementInfoStatusEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.entity.dto.StatementAccountAssetDto;
import group.za.bank.statement.entity.dto.StatementUsOrderDto;
import group.za.bank.statement.entity.dto.StockMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.StockStatementTradeDateInfo;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.StockStatementActionService;
import group.za.bank.statement.service.StockTradeDateService;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.StockMarketRemoteService;
import group.za.bank.statement.service.remote.TradedataRemoteService;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StockStatementBusinessServiceImplTest {

    @InjectMocks
    private StockStatementBusinessServiceImpl stockStatementBusinessService;

    @Mock
    private TdStatementFileRecordService statementFileRecordService;

    @Mock
    private StockTradeDateService stockTradeDateService;

    @Mock
    private StockStatementActionService stockStatementActionService;

    @Mock
    private TradedataRemoteService tradedataRemoteService;

    @Mock
    private SystemConfig systemConfig;

    @Mock
    private StkInfoAgent stkInfoAgent;

    @Mock
    private RateAgent rateAgent;

    @Mock
    private StockTradeManager stockTradeManager;

    @Mock
    private StockMarketRemoteService stockMarketRemoteService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Object> rBucket;

    @Mock
    private TdStockStatementDataService stockStatementDataService;

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJSON;
    private MockedStatic<DateUtil> mockedDateUtil;
    private MockedStatic<SpringUtils> mockedSpringUtils;

    private final String TEST_PERIOD = "202311";

    @Before
    public void setUp() {
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        // Mock JSON
        mockedJSON = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        // Mock DateUtil
        mockedDateUtil = mockStatic(DateUtil.class);
        when(DateUtil.parse(anyString(), anyString())).thenReturn(new Date());
        when(DateUtil.format(any(Date.class), anyString())).thenReturn(TEST_PERIOD);

        // Mock SystemConfig
        when(systemConfig.getStockCheck()).thenReturn(true);
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");

        // Mock SpringUtils
        mockedSpringUtils = mockStatic(SpringUtils.class);
        when(SpringUtils.getBean(RedissonClient.class)).thenReturn(redissonClient);

        // Mock RedissonClient and RBucket
        RBucket<Object> mockBucket = mock(RBucket.class);
        when(redissonClient.getBucket(anyString())).thenReturn(mockBucket);
        when(mockBucket.trySet(any(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        doNothing().when(mockBucket).set(any());
        when(mockBucket.isExists()).thenReturn(false);

        // Mock StockTradeDateService
        List<String> tradeDateList = Lists.newArrayList("20231101", "20231102", "20231103");
        when(stockTradeDateService.queryCoreCalendarTradeDateList(TEST_PERIOD))
            .thenReturn(tradeDateList);
        when(stockTradeDateService.queryCoreCalendarTradeDateList(anyString()))
            .thenReturn(tradeDateList);
        when(stockTradeDateService.queryCalendarTradeDateList(anyString(), anyString()))
            .thenReturn(tradeDateList);

        // Mock DateUtil for specific formats
        when(DateUtil.parse(eq(TEST_PERIOD), eq("yyyyMM")))
            .thenReturn(new Date());
        when(DateUtil.addMonth(any(Date.class), anyInt()))
            .thenReturn(new Date());
        when(DateUtil.format(any(Date.class), eq("yyyyMM")))
            .thenReturn(TEST_PERIOD);
        when(DateUtil.startOfDay(any(Date.class)))
            .thenReturn(new Date());
        when(DateUtil.endOfDay(any(Date.class)))
            .thenReturn(new Date());
        when(DateUtil.getFirstDayOfMonth(any(Date.class)))
            .thenReturn(new Date());
        when(DateUtil.getLastDayOfMonth(any(Date.class)))
            .thenReturn(new Date());
    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        if (mockedJSON != null) {
            mockedJSON.close();
        }
        if (mockedDateUtil != null) {
            mockedDateUtil.close();
        }
        if (mockedSpringUtils != null) {
            mockedSpringUtils.close();
        }
        redissonClient.shutdown();
    }

    @Test
    public void testGetBusinessType() {
        assertEquals(AccountTypeEnum.STOCK.getValue(), stockStatementBusinessService.getBusinessType());
    }

    @Test
    public void testIsDataPrepared_WhenCheckDisabled() {
        // Mock systemConfig
        when(systemConfig.getStockCheck()).thenReturn(false);
        
        TdFundMonthlyStatementInfo info = new TdFundMonthlyStatementInfo();
        info.setPeriod(TEST_PERIOD);
        
        assertTrue(stockStatementBusinessService.isDataPrepared(info, true));
    }

    //@Test
    public void testIsDataPrepared_WhenNoFileRecords() {
        TdFundMonthlyStatementInfo info = new TdFundMonthlyStatementInfo();
        info.setPeriod(TEST_PERIOD);
        
        // Mock Redis operations
        RBucket<Object> mockBucket = mock(RBucket.class);
        when(redissonClient.getBucket(anyString())).thenReturn(mockBucket);
        when(mockBucket.get()).thenReturn(null);
        when(mockBucket.trySet(any(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        
        when(statementFileRecordService.queryDailyFileRecords(anyString(), anyString()))
            .thenReturn(new ArrayList<>());
        
        stockStatementBusinessService.isDataPrepared(info, true);
    }

    //@Test
    public void testIsDataPrepared_Success() {
        TdFundMonthlyStatementInfo info = new TdFundMonthlyStatementInfo();
        info.setPeriod(TEST_PERIOD);
        info.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());

        // Mock Redis operations
        RBucket<Object> mockBucket = mock(RBucket.class);
        when(redissonClient.getBucket(anyString())).thenReturn(mockBucket);
        when(mockBucket.get()).thenReturn(null);
        when(mockBucket.trySet(any(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        // Mock file records
        List<TdStatementFileRecord> dailyRecords = Lists.newArrayList();
        TdStatementFileRecord record = new TdStatementFileRecord();
        record.setDataStatus(1); // SUCCESS
        record.setStatementDate(new Date());
        dailyRecords.add(record);

        List<TdStatementFileRecord> monthlyRecords = Lists.newArrayList();
        TdStatementFileRecord monthlyRecord = new TdStatementFileRecord();
        monthlyRecord.setDataStatus(2); // FINISHED
        monthlyRecords.add(monthlyRecord);

        when(statementFileRecordService.queryDailyFileRecords(anyString(), anyString()))
            .thenReturn(dailyRecords);
        when(statementFileRecordService.queryMonthlyFileRecords(anyString()))
            .thenReturn(monthlyRecords);

        stockStatementBusinessService.isDataPrepared(info, true);
    }

    //@Test
    public void testGenerateDataMap() {
        TdFundMonthlyStatementInfo info = new TdFundMonthlyStatementInfo();
        info.setPeriod(TEST_PERIOD);
        info.setBusinessType(AccountTypeEnum.STOCK.getValue());
        
        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setPeriod(TEST_PERIOD);
        statement.setBankUserId("TEST_USER");
        statement.setDocLang("CN");

        TdMonthlyStatementData statementData = new TdMonthlyStatementData();
        statementData.setAccountId("TEST001");
        
        // Mock Redis operations
        RBucket<Object> mockBucket = mock(RBucket.class);
        when(redissonClient.getBucket(anyString())).thenReturn(mockBucket);
        when(mockBucket.get()).thenReturn(null);
        when(mockBucket.trySet(any(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(mockBucket.delete()).thenReturn(true);
        doNothing().when(mockBucket).set(any());
        
        // Mock stockTradeDateService
        when(stockMarketRemoteService.queryStockKline(any(), any()))
            .thenReturn(new HashMap<>());
        
        // Mock TdStockStatementDataService
        when(stockStatementDataService.queryAccountAssetData(anyString(), any(), anyString(), any()))
            .thenReturn(new ArrayList<>());
        when(stockStatementDataService.queryUsOrderData(any(), any(), anyString(), any()))
            .thenReturn(new ArrayList<>());
        when(stockStatementDataService.queryTradeChangeDetailData(anyString(), any(), any(), anyString(), any(), any()))
            .thenReturn(new ArrayList<>());

        // Mock tradedataRemoteService
        ActionFractionOrderResp actionFractionOrderResp = new ActionFractionOrderResp();
        actionFractionOrderResp.setOrderList(new ArrayList<>());
        when(tradedataRemoteService.actionFractionOrderQuery(any(ActionFractionOrderQueryReq.class)))
            .thenReturn(actionFractionOrderResp);

        // Mock systemConfig
        when(systemConfig.getSecFeeKey()).thenReturn("secFee");
        when(systemConfig.getFinraFeeKey()).thenReturn("finraFee");
        when(systemConfig.getGenerateStatementWhenFeeNull()).thenReturn(false);
        
        Map<String, Object> result = stockStatementBusinessService.generateDataMap(info, statement, statementData, "US");
    }

    //@Test
    public void testGenerateDataMap_WithTemporaryStockCode() {
        // 基本设置同testGenerateDataMap
        TdFundMonthlyStatementInfo info = new TdFundMonthlyStatementInfo();
        info.setPeriod(TEST_PERIOD);
        info.setBusinessType(AccountTypeEnum.STOCK.getValue());
        
        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setPeriod(TEST_PERIOD);
        statement.setBankUserId("TEST_USER");
        statement.setDocLang("CN");

        TdMonthlyStatementData statementData = new TdMonthlyStatementData();
        statementData.setAccountId("TEST001");

        // Mock Redis operations
        RBucket<Object> mockBucket = mock(RBucket.class);
        when(redissonClient.getBucket(anyString())).thenReturn(mockBucket);
        when(mockBucket.get()).thenReturn(null);
        when(mockBucket.trySet(any(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(mockBucket.delete()).thenReturn(true);
        doNothing().when(mockBucket).set(any());

        // Mock 资产数据
        List<StatementAccountAssetDto> assetDtos = new ArrayList<>();
        StatementAccountAssetDto assetDto = new StatementAccountAssetDto();
        assetDto.setStockCode("TEMP001");
        assetDto.setExchangeCode("US");
        assetDto.setClosingSettleHolding("100");
        assetDtos.add(assetDto);
        
        when(stockStatementDataService.queryAccountAssetData(anyString(), any(), anyString(), any()))
            .thenReturn(assetDtos);

        // Mock 临时股票代码
        StockInfoResp stockInfo = new StockInfoResp();
        stockInfo.setStockCodeType(StockCodeTypeEnum.TEMPORARY.getValue());
        when(stkInfoAgent.getStkInfo(any(), any())).thenReturn(stockInfo);

        // 其他mock保持不变
        when(stockMarketRemoteService.queryStockKline(any(), any())).thenReturn(new HashMap<>());
        when(stockStatementDataService.queryUsOrderData(any(), any(), anyString(), any())).thenReturn(new ArrayList<>());
        when(stockStatementDataService.queryTradeChangeDetailData(anyString(), any(), any(), anyString(), any(), any()))
            .thenReturn(new ArrayList<>());

        ActionFractionOrderResp actionFractionOrderResp = new ActionFractionOrderResp();
        actionFractionOrderResp.setOrderList(new ArrayList<>());
        when(tradedataRemoteService.actionFractionOrderQuery(any())).thenReturn(actionFractionOrderResp);

        // 执行测试
        Map<String, Object> result = stockStatementBusinessService.generateDataMap(info, statement, statementData, "US");
    }

    @Test
    public void testQueryConfirmedOrderDto() {
        // Mock data
        String period = "202311";
        String bankUserId = "testUser";
        String accountId = "testAccount";
        String docLang = "en";
        
        // Mock StatementUsOrderDto
        StatementUsOrderDto orderDto = new StatementUsOrderDto();
        orderDto.setCurrency("USD");
        orderDto.setStockCode("AAPL");
        orderDto.setTradeDate("2023-11-15");
        orderDto.setClearDate("2023-11-17");
        orderDto.setTradeType("BUY");  // Buy order
        orderDto.setReferenceNo("refNo");
        orderDto.setAvgPrice("100");
        orderDto.setQty("100");
        orderDto.setAmount("10000");

        StatementUsOrderDto orderDto2 = new StatementUsOrderDto();
        orderDto2.setCurrency("USD");
        orderDto2.setStockCode("AAPL");
        orderDto2.setTradeDate("2023-11-15");
        orderDto2.setClearDate("2023-11-17");
        orderDto2.setTradeType("BUY");  // Buy order
        orderDto2.setReferenceNo("fraction_refNo");
        orderDto2.setAvgPrice("100");
        orderDto2.setQty("100");
        orderDto2.setAmount("10000");
        
        // Mock stockStatementDataService
        when(stockStatementDataService.queryUsOrderData(any(), any(), anyString(), any()))
            .thenReturn(Lists.newArrayList(orderDto, orderDto2));
            
        // Mock fraction order query
        when(tradedataRemoteService.actionFractionOrderQuery(any(ActionFractionOrderQueryReq.class)))
            .thenReturn(new ActionFractionOrderResp());

        StkBusinessRecordDTO stkBusinessRecordDTO = new StkBusinessRecordDTO();
        stkBusinessRecordDTO.setBusinessAmount(new BigDecimal("100"));
        stkBusinessRecordDTO.setBusinessQty(new BigDecimal("1"));
        stkBusinessRecordDTO.setBusinessPrice(new BigDecimal("98.64"));
        when(stockTradeManager.queryStkBusinessRecord(anyString())).thenReturn(Arrays.asList(stkBusinessRecordDTO));
            
        // Mock stock info
        StockInfoResp stockInfoResp = new StockInfoResp();
        stockInfoResp.setStockNameEng("Apple Inc");
        stockInfoResp.setStockCode("AAPL");
        when(stkInfoAgent.getStkInfo(anyString(), anyString()))
            .thenReturn(stockInfoResp);
            
        // Mock date conversion
        when(DateUtil.stringToDate(anyString()))
            .thenReturn(new Date());
            
        // Create StockStatementTradeDateInfo
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketCodeAndTradeDateInfo = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        marketCodeAndTradeDateInfo.setStartTradeDate("2023-11-01");
        marketCodeAndTradeDateInfo.setEndTradeDate("2023-11-30");

        StockStatementTradeDateInfo tradeDateInfo = new StockStatementTradeDateInfo();
        HashMap<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put(period, marketCodeAndTradeDateInfo);
        tradeDateInfo.setMarketCodeAndTradeDateInfoMap(objectObjectHashMap);

        // Execute private method using reflection
        List<StockMonthlyStatementDataDto.ConfirmedOrderDto> result = ReflectionTestUtils.invokeMethod(
            stockStatementBusinessService,
            "queryConfirmedOrderDto",
            period,
            bankUserId,
            accountId,
            docLang,
            tradeDateInfo
        );
        
        // Verify results
//        assertNotNull(result);
//        assertFalse(result.isEmpty());
//        StockMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrder = result.get(0);
//        assertEquals("USD", confirmedOrder.getCurrency());
//        assertEquals("AAPL", confirmedOrder.getStockCode());
////        assertEquals("Apple Inc", confirmedOrder.getStockName());
//        assertEquals("B", confirmedOrder.getDirection());
    }
}