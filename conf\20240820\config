## 新增
# 数字货币tradecore
zainvest.sharding.group.cryptoTrade-group.beanNamePrefix = cryptoTrade
zainvest.sharding.group.cryptoTrade-group.mybatis.mapper-locations[0] = classpath*:mapper/*.xml
zainvest.sharding.group.cryptoTrade-group.mybatis.mapper-scanner.base-package = group.za.bank.ccs.tradecore.mapper
zainvest.sharding.group.cryptoTrade-group.databases[0].ip = 待补充
zainvest.sharding.group.cryptoTrade-group.databases[0].port = 待补充
zainvest.sharding.group.cryptoTrade-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.cryptoTrade-group.databases[0].userName = 待补充
zainvest.sharding.group.cryptoTrade-group.databases[0].password = 待补充
zainvest.sharding.group.cryptoTrade-group.databases[0].dbName = 待补充

#数字货币行情请求路径
ccs.quota.gateway.url = 待补充


## 修改
#结单业务类型
monthly.statement.defaultMonthlyStatementBusinessType = fund,stock,crypto

monthly.statement.doc.hkTempKey = zabank_invest_monthly_hk_20240820_v1.html
monthly.statement.doc.zhTempKey = zabank_invest_monthly_zh_20240820_v1.html

