<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="false" scanPeriod="600 seconds" debug="false">

    <property name="project.name" value="zabank-ftc-statement-service"/>
    <include resource="invest-appender.xml"/>

    <logger name="group.za.bank.statement">
        <level value="${invest.logging.level}"/>
    </logger>

    <logger name="group.za.bank.sbs.trade.mapper" level="INFO" />

    <root level="INFO">
        <appender-ref ref="errorAppenderAsync"/>
        <appender-ref ref="infoAppenderAsync"/>
        <appender-ref ref="userKeyAccessAppenderAsync"/>
        <if condition='",local,dev".indexOf(property("env")) > 0'>
            <then>
                <appender-ref ref="STDOUT"/>
            </then>
        </if>
    </root>

</configuration>