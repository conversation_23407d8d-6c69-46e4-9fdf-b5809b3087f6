package group.za.bank.statement.job;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.ShardingUtil;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.service.MonthlyStatementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 26 16:32
 * @description
 */
@Slf4j
@JobHandler(value = "monthlyStatementDocPublishJob")
@Component
public class MonthlyStatementDocPublishJob extends IJobHandler {

    @Autowired
    MonthlyStatementService monthlyStatementService;

    @Autowired
    SystemConfig systemConfig;

    /**
     * @Date  2021/8/3 18:04
     * @Returun com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @Version v1.0
     */
    @Override
    public ReturnT<String> execute(String statementInfo) throws Exception {
        log.info("定时任务开始推送月结单， 入参 = {},shardingVO:{}"
                , statementInfo, ShardingUtil.getShardingVo());
        String period = null;
        if(Strings.isEmpty(statementInfo)){
            Date currentDate = new Date();
            Date monthlyStatementDate = DateUtil.getLastDayOfLastMonth(currentDate);
            period = DateUtil.format(monthlyStatementDate,"yyyyMM");
        }else {
            TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = JSONObject.parseObject(statementInfo, TdFundMonthlyStatementInfo.class);
            period = tdFundMonthlyStatementInfo.getPeriod();
        }

        monthlyStatementService.pubMonthlyStatement(period);
        log.info("定时任务推送月结单结束， 入参 = {}", statementInfo);
        return ReturnT.SUCCESS;
    }
}
