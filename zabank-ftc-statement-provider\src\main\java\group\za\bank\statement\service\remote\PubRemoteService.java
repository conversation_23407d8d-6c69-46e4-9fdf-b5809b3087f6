package group.za.bank.statement.service.remote;

import group.za.bank.invest.pub.entity.dto.GrayDetail;
import group.za.bank.invest.pub.entity.req.GrayQueryReq;
import group.za.bank.invest.pub.entity.req.SendSingleMessageReq;
import group.za.bank.invest.pub.entity.resp.GeneratePdfResp;
import group.za.bank.invest.pub.entity.resp.QueryPdfResp;

import java.util.Map;

public interface PubRemoteService {


    /**
     * pdf 自动生成填充模板
     * bo.li
     */
    GeneratePdfResp generatePdf(String businessTaskId, String templateCode, Map<String,Object> paramMap);

    /**
     * pdf 查询
     * bo.li
     */
    QueryPdfResp queryPdf(String businessTaskId);

    /**
     * 获取businessData
     */
    String queryBusinessData(String businessTaskId);

    /**
     * 查询灰度
     */
    GrayDetail queryGray(GrayQueryReq req);

    /**
     * 发送单条消息
     */
    boolean sendSingleMessage(SendSingleMessageReq sendSingleMessageReq, boolean isThrowException);
}
