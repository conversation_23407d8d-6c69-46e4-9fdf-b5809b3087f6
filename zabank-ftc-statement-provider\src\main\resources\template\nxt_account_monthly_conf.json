{"targets": [{"name": "holding", "desc": "投資帳戶持倉", "isSkip": "0", "index": 0, "path": "div[class=primary mt0]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundHolding", "type": "sfund", "typeName": "assetType", "isArray": "1", "titleIndex": 0, "titleName": "assetTypeTitle", "titlePath": "div[class=table-title-desc]", "index": 0, "path": "table[class=table-title]", "fieldBasePath": "tbody tr", "fieldsName": "nativeRowList", "fields": [{"groupId": "row1", "rowType": 211, "seq": 0, "name": "fundName", "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "1", "ext": {"fundNameOrNot": 1}}, {"groupId": "row1", "rowType": 221, "name": "currency1", "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 0}, {"groupId": "row1", "rowType": 222, "name": "marketValue", "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "seq": 0}, {"name": "fundOpeningBalance", "titlePath": "thead tr th:eq(1)", "path": "td:eq(1)", "isHtml": "1", "skipErr": "1", "seq": 1, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}, {"name": "fundClosingBalance", "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "1", "seq": 2, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}, {"groupId": "row4", "rowType": 10, "name": "referencePrice", "isHtml": "1", "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "seq": 4}, {"groupId": "row4", "rowType": 121, "name": "currency2", "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 3}]}]}, {"name": "confirmedOrder", "desc": "已確認交易", "isSkip": "0", "index": 0, "path": "div[class=primary]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundConfirmed", "type": "sfund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 1, "titlePath": "div[class=table-title-desc]", "path": "table.table-title.table-special", "fieldBasePath": "tbody tr", "index": 0, "fieldsName": "nativeRowList", "fields": [{"name": "fundConDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "fundDescription", "groupId": "row1", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p ", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($this, ' ', ' -')", "ext": {"fundNameOrNot": 1}}, {"name": "fundCurrency", "groupId": "row1", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 1}, {"name": "fundAmount", "groupId": "row1", "rowType": 222, "titlePath": "thead tr th:eq(6)", "path": "td:eq(6)", "isHtml": "0", "seq": 1}, {"name": "fundNoOfUnits", "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "0", "valueUnit": "单位|單位|Unit*Units*Unit(s)", "seq": 2}, {"name": "fundUnitPrice", "groupId": "row3", "rowType": 1, "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 3}, {"name": "fundCurrency2", "groupId": "row3", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 3}, {"name": "fundFee", "groupId": "row4", "rowType": 1, "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "isHtml": "1", "seq": 4}, {"name": "fundCurrency3", "groupId": "row4", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 4}]}]}, {"name": "pendingOrder", "desc": "基金處理中交易", "isSkip": "0", "index": 0, "path": "div[class=primary mt0-fund]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundPendingOrder", "type": "sfund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 0, "titlePath": "div[class=primary mt0-fund]>div[class=bg]", "path": "table.table-title.table-special", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 1, "fields": [{"name": "fundPendingDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "tr td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "fundPendingDescription", "groupId": "row1", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "tr td:eq(1) p", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($ParserUtil.appendAfter($this, ' ', ' -'),'<br> ', ' ', ' -')", "ext": {"fundNameOrNot": 1}}, {"name": "fundPendingCurrency", "groupId": "row1", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "tr td:eq(3)", "isHtml": "0", "seq": 2, "ext": {"unitOrNot": 1}}, {"name": "fundPendingAmount", "groupId": "row1", "rowType": 222, "titlePath": "thead tr th:eq(6)", "path": "tr td:eq(6)", "isHtml": "0", "seq": 1}, {"name": "fundPendingNoOfUnits", "titlePath": "thead tr th:eq(2)", "path": "tr td:eq(2)", "isHtml": "1", "seq": 2, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}, {"name": "fundPendingUnitPrice", "groupId": "row3", "rowType": 1, "titlePath": "thead tr th:eq(4)", "path": "tr td:eq(4)", "isHtml": "1", "seq": 1}, {"name": "fundPendingCurrency2", "groupId": "row3", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "tr td:eq(3)", "isHtml": "0", "seq": 2, "ext": {"unitOrNot": 1}}, {"name": "fundPendingFee", "groupId": "row4", "rowType": 1, "titlePath": "thead tr th:eq(5)", "path": "tr td:eq(5)", "isHtml": "1", "seq": 1}, {"name": "fundPendingCurrency3", "groupId": "row4", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "tr td:eq(3)", "isHtml": "0", "seq": 2, "ext": {"unitOrNot": 1}}]}]}, {"name": "dividendOrder", "desc": "派息", "isSkip": "0", "index": 0, "path": "div[class=primary mt0-invest]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundDividendOrder", "type": "sfund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 2, "titlePath": "div[class=table-title-desc]", "path": "table.table-title.table-special", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 2, "fields": [{"name": "dividendOrderDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "dividendOrderDescription", "groupId": "row4", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($this, ' ', ' -').replace('Dividend', 'Dividend -')", "ext": {"fundNameOrNot": 1}}, {"name": "dividendOrderNoOfUnits", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "0", "valueUnit": "单位|單位|Unit*Units*Unit(s)", "seq": 2}, {"name": "dividendOrderCurrency", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 3}, {"name": "dividendOrderAmount", "groupId": "row4", "rowType": 222, "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 4}]}]}, {"name": "holdingChange", "desc": "持倉變動", "isSkip": "0", "index": 1, "path": "div[class=primary mt0-invest]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundHoldingChange", "type": "sfund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 3, "titlePath": "div[class=table-title-desc]", "path": "table.table-title.table-special", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 3, "fields": [{"name": "fundHoldingChangeDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "fundHoldingChangeDescription", "groupId": "row4", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($ParserUtil.appendAfter($this, ' ', ' -'),'<br> ', ' ', ' -')", "ext": {"fundNameOrNot": 1}}, {"name": "fundHoldingChangeNoOfUnits", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "0", "seq": 2, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}]}]}]}