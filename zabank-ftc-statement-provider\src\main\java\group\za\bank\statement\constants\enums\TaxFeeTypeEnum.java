package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 税费类型
 *
 * <AUTHOR>
 * @date 2023/05/24
 **/
@Getter
@AllArgsConstructor
public enum TaxFeeTypeEnum implements BaseEnum {
    ADR("1", "ADR预托证券收费"),
    FTT("2", "FTT金融税"),
    ;

    private String value;

    private String msg;
}
