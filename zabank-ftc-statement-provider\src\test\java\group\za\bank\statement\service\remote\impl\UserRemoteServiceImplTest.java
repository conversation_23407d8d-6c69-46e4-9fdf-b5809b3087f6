package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.HabitancyInfoDto;
import group.za.bank.invest.account.entity.dto.QueryAccountInfoDto;
import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.account.entity.resp.QueryBankUserInfoResp;
import group.za.bank.invest.account.feign.UserFeignService;
import group.za.bank.invest.basecommon.entity.resp.BaseListResp;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.base.BaseTestService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class UserRemoteServiceImplTest extends BaseTestService {
    @Mock
    UserFeignService userFeignService;
    @Mock
    Logger log;
    @InjectMocks
    UserRemoteServiceImpl userRemoteServiceImpl;

    @Test
    public void testQueryUserAllInvestAccountInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        BaseListResp<UserInvestAccountInfo> userInvestAccountInfoBaseListResp = new BaseListResp<>();
        userInvestAccountInfoBaseListResp.setList(Arrays.asList(new UserInvestAccountInfo()));
        resp.setValue(userInvestAccountInfoBaseListResp);
        when(userFeignService.listUserInvestAccountInfo(any())).thenReturn(resp);
        List<UserInvestAccountInfo> result = userRemoteServiceImpl.queryUserAllInvestAccountInfo("bankUserId");
    }

    @Test
    public void testQueryUserInvestAccountInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new UserInvestAccountInfo());
        when(userFeignService.queryUserInvestAccountInfo(any())).thenReturn(resp);
        UserInvestAccountInfo result = userRemoteServiceImpl.queryUserInvestAccountInfo("bankUserId", AccountTypeEnum.FUND);
    }



    @Test
    public void testQueryUserLanguage() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        when(userFeignService.queryUserLanguage(any())).thenReturn(resp);
        String result = userRemoteServiceImpl.queryUserLanguage("bankUserId");
    }

    @Test
    public void testQueryHabitancyInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        when(userFeignService.queryHabitancyInfo(any())).thenReturn(resp);
        HabitancyInfoDto result = userRemoteServiceImpl.queryHabitancyInfo("bankUserId");
    }

    @Test
    public void testQueryContactAddressInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new AddressInfoResp());
        when(userFeignService.queryAddressInfo(any())).thenReturn(resp);
        AddressInfoResp result = userRemoteServiceImpl.queryContactAddressInfo("bankUserId");
    }

    @Test
    public void testQueryByBankUserIdList() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new HashMap<>());
        when(userFeignService.queryByBankUserIdList(any())).thenReturn(resp);
        Map<String, QueryAccountInfoDto> result = userRemoteServiceImpl.queryByBankUserIdList(Arrays.<String>asList("String"));
    }

    @Test
    public void testIsUserMcv() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(true);
        when(userFeignService.verifyMcv(any())).thenReturn(resp);
        boolean result = userRemoteServiceImpl.isUserMcv("bankUserId");
    }

    @Test
    public void testQueryUserInvestCloseAccountInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(Arrays.asList(new UserInvestAccountInfo()));
        when(userFeignService.queryUserInvestCloseAccountInfo(any())).thenReturn(resp);
        List<UserInvestAccountInfo> result = userRemoteServiceImpl.queryUserInvestCloseAccountInfo("bankUserId");
    }

    @Test
    public void testJudgeBankAccountClosed() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(true);
        when(userFeignService.judgeBankAccountClosed(any())).thenReturn(resp);
        Boolean result = userRemoteServiceImpl.judgeBankAccountClosed("bankUserId");
    }

    @Test
    public void testQueryBankUserInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new HashMap<>());
        when(userFeignService.queryBankUserInfo(any())).thenReturn(resp);
        Map<String, QueryBankUserInfoResp> result = userRemoteServiceImpl.queryBankUserInfo("bankUserId");
    }

    @Test
    public void testQueryBankUserInfoList() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new HashMap<>());
        when(userFeignService.queryBankUserInfo(any())).thenReturn(resp);
        Map<String, QueryBankUserInfoResp> result = userRemoteServiceImpl.queryBankUserInfoList(null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme