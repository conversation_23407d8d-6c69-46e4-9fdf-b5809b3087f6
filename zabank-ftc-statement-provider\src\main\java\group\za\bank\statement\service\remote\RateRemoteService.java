package group.za.bank.statement.service.remote;

import group.za.bank.sbs.bankfront.model.resp.QueryRateResp;
import group.za.bank.sbs.trade.model.req.feign.StkRateInfoHisReq;
import group.za.bank.sbs.trade.model.resp.feign.StkRateInfoHisResp;

/**
 * 汇率相关
 * <AUTHOR>
 * @date 2022/05/12
 **/
public interface RateRemoteService {

    /**
     * 获取汇率信息
     * @param sourceCcy
     * @param targetCcy
     * @return
     */
    QueryRateResp queryRate(String sourceCcy, String targetCcy);

    /**
     * 查询历史汇率
     * @param req
     * @return
     */
    StkRateInfoHisResp queryRateHis(StkRateInfoHisReq req);

    /**
     * 查询最新一条汇率历史记录
     * @param req
     * @return
     */
    StkRateInfoHisResp getLatestHisRateInfo(StkRateInfoHisReq req);
}
