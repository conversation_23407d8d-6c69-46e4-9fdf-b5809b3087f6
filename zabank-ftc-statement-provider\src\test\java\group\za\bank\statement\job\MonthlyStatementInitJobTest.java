package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.service.MonthlyStatementService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.Mockito.when;

public class MonthlyStatementInitJobTest extends BaseTestService {
    @Mock
    SystemConfig systemConfig;
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    MonthlyStatementInitJob monthlyStatementInitJob;

    @Test
    public void testExecute() throws Exception {
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("getDefaultMonthlyStatementBusinessTypeResponse");

        ReturnT<String> result = monthlyStatementInitJob.execute("{\"period\":\"202203\",businessType:\"fund,stock\"}");
    }
}

