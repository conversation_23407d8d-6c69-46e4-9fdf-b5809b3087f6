package group.za.bank.statement.manager.impl;

import group.za.bank.fund.trade.constants.enums.LanguageTemEnum;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.EnumYesOrNo;
import group.za.bank.invest.common.utils.IdWorker;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.*;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementDataMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.domain.mapper.TdStockStatementDataMapper;
import group.za.bank.statement.domain.repository.TdMonthlyStatementDataRepository;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.entity.dto.UserStatementRecordDto;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.invest.core.InvestConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.*;

/**
 * <AUTHOR>
 * @createTime 26 13:51
 * @description
 */

@Slf4j
@Component
@Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class)
public class MonthlyStatementManagerImpl implements MonthlyStatementManager {

    @Autowired
    private TdMonthlyStatementDataMapper tdMonthlyStatementDataMapper;

    @Autowired
    private TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;

    @Autowired
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;


    @Autowired
    private TdMonthlyStatementDataRepository tdMonthlyStatementDataRepository;

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private TdStockStatementDataMapper stockStatementDataMapper;


    /**
     * 查询指定月份已确认发送的月结单信息
     *
     * @param period
     * @return
     */
    @Override
    public TdFundMonthlyStatementInfo getPubConfirmedFundStatementInfo(String period) {
        TdFundMonthlyStatementInfo condition = new TdFundMonthlyStatementInfo();
        condition.setPeriod(period);
        condition.setPubConfirmStatus(MonthlyStatementPubConfirmStatusEnum.CONFIRMED.getDbValue());
        condition.setIsDeleted(EnumYesOrNo.NO.getValue());
        return tdMonthlyStatementInfoMapper.selectOne(condition);
    }

    /**
     * 生成结单
     *
     * @param business
     * @param period
     * @param zhTempKey
     * @param hkTempKey
     * @return
     */
    @Override
    public TdFundMonthlyStatementInfo createStatement(String business, String period
            , String hkTempKey, String zhTempKey, String nxtHkTempKey, String nxtZhTempKey) {

        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setBusinessId(IdWorker.idworker.nextIdStr());
        tdFundMonthlyStatementInfo.setBusinessType(business);
        tdFundMonthlyStatementInfo.setPeriod(period);
        tdFundMonthlyStatementInfo.setHkTempKey(hkTempKey);
        tdFundMonthlyStatementInfo.setZhTempKey(zhTempKey);
        tdFundMonthlyStatementInfo.setNxtHkTempKey(nxtHkTempKey);
        tdFundMonthlyStatementInfo.setNxtZhTempKey(nxtZhTempKey);
        tdFundMonthlyStatementInfo.setRecordTime(new Date());
        tdFundMonthlyStatementInfo.setStatus(MonthlyStatementInfoStatusEnum.PROCESSING.getDbValue());
        tdFundMonthlyStatementInfo.setInitStatus(MonthlyStatementInfoInitStatusEnum.PROCESSING.getDbValue());
        tdFundMonthlyStatementInfo.setPubConfirmStatus(MonthlyStatementPubConfirmStatusEnum.WAIT_CONFIRM.getDbValue());
        tdMonthlyStatementInfoMapper.insert(tdFundMonthlyStatementInfo);
        return tdFundMonthlyStatementInfo;
    }


    /**
     * 结单初始化完成
     *
     * @param business
     * @param period
     */
    @Override
    public void statementInitFinish(String business, String period) {
        TdFundMonthlyStatementInfo conditionFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        conditionFundMonthlyStatementInfo.setPeriod(period);
        conditionFundMonthlyStatementInfo.setBusinessType(business);
        conditionFundMonthlyStatementInfo.setIsDeleted(EnumYesOrNo.NO.getValue());

        TdFundMonthlyStatementInfo newFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        newFundMonthlyStatementInfo.setInitStatus(MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue());
        newFundMonthlyStatementInfo.setGmtModified(new Date());
        int updateNumber = tdMonthlyStatementInfoMapper.updateByCondition(newFundMonthlyStatementInfo, conditionFundMonthlyStatementInfo);

        if (updateNumber == 1) {
            log.info("更新结单初始化状态为完成. business:{},period:{}"
                    , business, period);
        } else {
            log.warn("更新结单初始化状态为完成失败.  business:{},period:{},updateNumber:{}"
                    , business, period, updateNumber);
            throw new BusinessException(STATEMENT_INFO_STATUS_EXCEPTION);
        }
    }

    /**
     * 确认结单可以推送
     *
     * @return
     */
    @Override
    public void statementPubConfirm(String period, String operatorId) {
        TdFundMonthlyStatementInfo conditionFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        conditionFundMonthlyStatementInfo.setPeriod(period);
        conditionFundMonthlyStatementInfo.setIsDeleted(EnumYesOrNo.NO.getValue());


        TdFundMonthlyStatementInfo newFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        newFundMonthlyStatementInfo.setPubConfirmStatus(MonthlyStatementPubConfirmStatusEnum.CONFIRMED.getDbValue());
        newFundMonthlyStatementInfo.setConfirmOperatorId(operatorId);
        newFundMonthlyStatementInfo.setGmtModified(new Date());
        int updateNumber = tdMonthlyStatementInfoMapper.updateByCondition(newFundMonthlyStatementInfo, conditionFundMonthlyStatementInfo);

        if (updateNumber == 1) {
            log.info("更新结单发送确认状态为已确认状态成功. period:{},confirmOperatorId:{}"
                    , period, operatorId);
        } else {
            log.warn("更新结单发送确认状态为已确认失败.period:{},confirmOperatorNameId:{}"
                    , period, operatorId);
            throw new BusinessException(STATEMENT_INFO_STATUS_EXCEPTION);
        }

    }


    /**
     * 查询结单记录
     *
     * @param statementId
     * @return
     */
    @Override
    public TdFundMonthlyStatement getStatementRecord(String statementId) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setIsDeleted(InvestConstants.N);
        condition.setBusinessId(statementId);
        return tdMonthlyStatementMapper.selectOne(condition);

    }


    /**
     * 查询结单记录
     *
     * @param period
     * @param business
     * @param clientId
     * @param docLang
     * @return
     */
    @Override
    public TdFundMonthlyStatement getStatementRecordByDocLang(String period, String business, String clientId, String docLang) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setIsDeleted(InvestConstants.N);
        condition.setBusinessType(business);
        condition.setPeriod(period);
        condition.setClientId(clientId);
        condition.setDocLang(docLang);
        return tdMonthlyStatementMapper.selectOne(condition);
    }

    /**
     * 获取指定账户下的当前对应业务的结单记录
     *
     * @param period   期数
     * @param clientId 投资账户
     * @return
     */
    @Override
    public List<TdFundMonthlyStatement> listUserStatementRecords(String period, String clientId) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setIsDeleted(InvestConstants.N);
        condition.setPeriod(period);
        condition.setClientId(clientId);
        return tdMonthlyStatementMapper.selectList(condition);
    }

    @Override
    public List<TdFundMonthlyStatement> listUserStatementRecordsByUserId(String business, String period, String userId) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setIsDeleted(InvestConstants.N);
        condition.setBusinessType(business);
        condition.setPeriod(period);
        condition.setBankUserId(userId);
        return tdMonthlyStatementMapper.selectList(condition);
    }

    /**
     * 创建用户的月结单记录
     *
     * @param docLang
     * @param tdFundMonthlyStatementInfo
     * @param userInvestClientInfoDto
     * @param statementDate
     * @return
     */
    @Override
    public TdFundMonthlyStatement createUserMonthlyStatementRecords(String docLang, TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , UserInvestClientInfoDto userInvestClientInfoDto, boolean notifyUser, Date statementDate) {

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setBusinessId(IdWorker.idworker.nextIdStr());
        tdFundMonthlyStatement.setBankUserId(userInvestClientInfoDto.getBankUserId());
        tdFundMonthlyStatement.setClientId(userInvestClientInfoDto.getClientId());
        tdFundMonthlyStatement.setPeriod(tdFundMonthlyStatementInfo.getPeriod());
        tdFundMonthlyStatement.setDocStatus(MonthlyStatementDocStatusEnum.INIT.getDbValue());
        tdFundMonthlyStatement.setPubStatus(MonthlyStatementPubStatusEnum.PROCESSING.getDbValue());
        if (AccountTypeEnum.S_FUND.getValue().equals(userInvestClientInfoDto.getAccountType())) {
            // 南向通
            tdFundMonthlyStatement.setChannel(ChannelEnum.SOUTH_BOUND.getValue());
        } else {
            // 普通基金
            tdFundMonthlyStatement.setChannel(ChannelEnum.NORMAL.getValue());
        }
        //是否需要通知客户
        if(!notifyUser){
            tdFundMonthlyStatement.setPubStatus(MonthlyStatementPubStatusEnum.FINISHED.getDbValue());
            log.info("createUserMonthlyStatementRecords userid:{},notifyUser:{}", userInvestClientInfoDto.getBankUserId(), notifyUser);
        }
        tdFundMonthlyStatement.setPeriod(tdFundMonthlyStatementInfo.getPeriod());
        tdFundMonthlyStatement.setDocLang(docLang);
        tdFundMonthlyStatement.setBusinessType(tdFundMonthlyStatementInfo.getBusinessType());
        tdFundMonthlyStatement.setRecordTime(statementDate);
        tdMonthlyStatementMapper.insert(tdFundMonthlyStatement);

        return tdFundMonthlyStatement;
    }

    /**
     * 用户业务数据处理完毕
     *
     * @param tdMonthlyStatementData
     * @param hasData
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void finishUserDataPrepare(TdMonthlyStatementData tdMonthlyStatementData, boolean hasData) {
        TdMonthlyStatementData condition = new TdMonthlyStatementData();
        condition.setBusinessType(tdMonthlyStatementData.getBusinessType());
        condition.setStatementId(tdMonthlyStatementData.getStatementId());

        TdMonthlyStatementData newTdMonthlyStatementData = new TdMonthlyStatementData();
        if (hasData) {
            newTdMonthlyStatementData.setDataStatus(MonthlyStatementDataStatusEnum.FINISHED.getDbValue());
        } else {
            newTdMonthlyStatementData.setDataStatus(MonthlyStatementDataStatusEnum.NONE.getDbValue());
        }
        newTdMonthlyStatementData.setGmtModified(new Date());

        int updateNumber = tdMonthlyStatementDataMapper.updateByCondition(newTdMonthlyStatementData, condition);
        if (updateNumber != 1) {
            throw new BusinessException(STATEMENT_DATA_STATUS_EXCEPTION);
        }
    }

    /**
     * 结单初始化完成
     *
     * @param statementId
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void userStatementInitFinish(String statementId) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setBusinessId(statementId);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocStatus(MonthlyStatementDocStatusEnum.UN_NOTIFIED.getDbValue());
        tdFundMonthlyStatement.setGmtModified(new Date());

        int updateNumber = tdMonthlyStatementMapper.updateByCondition(tdFundMonthlyStatement, condition);
        if (updateNumber != 1) {
            throw new BusinessException(STATEMENT_RECORD_STATUS_EXCEPTION);
        }

    }

    /**
     * 完成生成通知
     *
     * @param statementId
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void statementDocNotified(String statementId) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setBusinessId(statementId);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocStatus(MonthlyStatementDocStatusEnum.PROCESSING.getDbValue());
        tdFundMonthlyStatement.setGmtModified(new Date());

        int updateNumber = tdMonthlyStatementMapper.updateByCondition(tdFundMonthlyStatement, condition);
        if (updateNumber != 1) {
            throw new BusinessException(STATEMENT_RECORD_STATUS_EXCEPTION);
        }

    }

    /**
     * 结单文件已生成,更新状态到已生成
     *
     * @param businessId
     * @param pdfPath
     */
    @Override
    public void userStatementDocGenerated(String businessId, String pdfPath,String htmlPath) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setBusinessId(businessId);
        condition.setIsDeleted(InvestConstants.N);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocStatus(MonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        tdFundMonthlyStatement.setDocUrl(pdfPath);
        tdFundMonthlyStatement.setHtmlUrl(htmlPath);
        tdFundMonthlyStatement.setDocTime(new Date());
        tdFundMonthlyStatement.setGmtModified(new Date());

        int updateNumber = tdMonthlyStatementMapper.updateByCondition(tdFundMonthlyStatement, condition);
        if (updateNumber != 1) {
            throw new BusinessException(STATEMENT_RECORD_STATUS_EXCEPTION);
        }

    }


    /**
     * 结单已推送状态
     *
     * @param statementId
     */
    @Override
    public void userStatementPubFinished(String statementId, String remark) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setBusinessId(statementId);
        condition.setIsDeleted(InvestConstants.N);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setPubStatus(MonthlyStatementPubStatusEnum.FINISHED.getDbValue());
        tdFundMonthlyStatement.setPubTime(new Date());
        tdFundMonthlyStatement.setGmtModified(new Date());
        if (StringUtils.isNotEmpty(remark)) {
            tdFundMonthlyStatement.setRemark(remark);
        }
        int updateNumber = tdMonthlyStatementMapper.updateByCondition(tdFundMonthlyStatement, condition);
        if (updateNumber != 1) {
            throw new BusinessException(STATEMENT_RECORD_STATUS_EXCEPTION);
        }
    }


    /**
     * 未处理完成的结单
     *
     * @param period
     * @return
     */
    @Override
    public TdFundMonthlyStatementInfo getStatement(String period) {
        TdFundMonthlyStatementInfo statementInfoCondition = new TdFundMonthlyStatementInfo();
        statementInfoCondition.setIsDeleted(InvestConstants.N);
        statementInfoCondition.setPeriod(period);

        return tdMonthlyStatementInfoMapper.selectOne(statementInfoCondition);
    }


    /**
     * 用户结单对应的数据记录
     *
     * @param statementId
     */
    @Override
    public List<TdMonthlyStatementData> listUserStatementDataRecord(String statementId) {
        TdMonthlyStatementData condition = new TdMonthlyStatementData();
        condition.setIsDeleted(InvestConstants.N);
        condition.setStatementId(statementId);
        return tdMonthlyStatementDataMapper.selectList(condition);
    }

    /**
     * 创建用户的结单数据
     *
     * @param statementInfo
     * @param monthlyStatement
     * @param businessTypeAndUserInvestClientInfoDtoMap 各个业务类型的账户
     * @return
     */
    @Override
    public List<TdMonthlyStatementData> createUserStatementBusinessData(TdFundMonthlyStatementInfo statementInfo
            , TdFundMonthlyStatement monthlyStatement, Map<String, UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap) {
        String businessType = statementInfo.getBusinessType();
        String[] businessSubtypeArray = businessType.split(systemConfig.businessSubTypeSeparator);
        List<TdMonthlyStatementData> monthlyStatementDataList = new ArrayList<>(businessSubtypeArray.length);
        for (String businessSubtype : businessSubtypeArray) {
            UserInvestClientInfoDto userInvestClientInfoDto = businessTypeAndUserInvestClientInfoDtoMap.get(businessSubtype);

            TdMonthlyStatementData tdMonthlyStatementData = new TdMonthlyStatementData();
            tdMonthlyStatementData.setBusinessId(IdWorker.idworker.nextIdStr());
            tdMonthlyStatementData.setStatementId(monthlyStatement.getBusinessId());
            tdMonthlyStatementData.setBusinessType(businessSubtype);
            //未开户的直接将数据设置成没数据终态了
            if (userInvestClientInfoDto == null){
                tdMonthlyStatementData.setDataStatus(MonthlyStatementDataStatusEnum.NONE.getDbValue());
            }else {
                if (AccountTypeEnum.FUND.getValue().equals(businessSubtype) || AccountTypeEnum.S_FUND.getValue().equals(businessSubtype)) {
                    tdMonthlyStatementData.setAccountId(userInvestClientInfoDto.getBankAccountId());
                } else {
                    tdMonthlyStatementData.setAccountId(userInvestClientInfoDto.getAccountId());
                }
                tdMonthlyStatementData.setDataStatus(MonthlyStatementDataStatusEnum.PROCESSING.getDbValue());
            }
            tdMonthlyStatementData.setRecordTime(new Date());

            tdMonthlyStatementDataMapper.insert(tdMonthlyStatementData);
            monthlyStatementDataList.add(tdMonthlyStatementData);
        }
        return monthlyStatementDataList;

    }

    /**
     * 是否本期所有结单都生成完毕
     *
     * @param business
     * @param period
     * @return
     */
    @Override
    public boolean isAllStatementDocGenerated(String business, String period) {
        Integer countNumber = tdMonthlyStatementMapper.countDocUnFinishedStatement( period,business);
        return  countNumber==0;
    }


    /**
     * 保存结单数据
     *
     * @param statementId
     * @param businessType
     * @param dataMap
     */
    @Override
    public void saveUserStatementBusinessData(String statementId, String businessType, Map<String, Object> dataMap) {
        TdMonthlyStatementBusinessData tdMonthlyStatementBusinessData = new TdMonthlyStatementBusinessData();
        tdMonthlyStatementBusinessData.setBusinessType(businessType);
        tdMonthlyStatementBusinessData.setData(dataMap);
        tdMonthlyStatementBusinessData.setStatementId(statementId);
        tdMonthlyStatementBusinessData.setId(IdWorker.idworker.nextIdStr());
        tdMonthlyStatementBusinessData.setRecordDate(new Date());
        tdMonthlyStatementDataRepository.save(tdMonthlyStatementBusinessData);
    }


    /**
     * mongodb中的结单数据
     *
     * @param statementId 结单id
     */
    @Override
    public List<TdMonthlyStatementBusinessData> listUserStatementBusinessData(String statementId) {
        return tdMonthlyStatementDataRepository.findByStatementId(statementId);
    }

    @Override
    public void insertBatchStatementData(List<TdStockStatementData> batchList) {
        stockStatementDataMapper.insertBatch(batchList);
    }



    /**
     * 获取指定的结单数据
     *
     * @param statementId
     * @param businessType
     * @return
     */
    @Override
    public TdMonthlyStatementBusinessData getUserStatementBusinessData(String statementId, String businessType) {
        return tdMonthlyStatementDataRepository.getByStatementIdAndBusinessType(statementId, businessType);
    }

    /**
     * 获取指定的结单数据列表
     */
    @Override
    public List<TdMonthlyStatementBusinessData> getStatementIdsBusinessData(List<String> statementIds, String businessType) {
        return tdMonthlyStatementDataRepository.findAllByStatementIdInAndBusinessType(statementIds, businessType);
    }

    /**
     * 删除指定的结单数据
     *
     * @param statementId
     * @param businessType
     * @return
     */
    @Override
    public void deleteUserStatementBusinessData(String statementId, String businessType) {
        tdMonthlyStatementDataRepository.deleteByStatementIdAndBusinessType(statementId, businessType);
    }


    @Override
    public List<TdFundMonthlyStatement> queryUserInvestMonthlyStatementList(String bankUserId, String clientId, String year, String period) {
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setIsDeleted(InvestConstants.N);
        condition.setPeriod(period);
        condition.setClientId(clientId);
        return tdMonthlyStatementMapper.selectList(condition);
    }

    @Override
    public List<UserStatementRecordDto> initUserStatementRecord(TdFundMonthlyStatementInfo monthlyStatementInfo, Map<String, UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap, String businessSubtype) {
        return initUserStatementRecordBase(monthlyStatementInfo, businessTypeAndUserInvestClientInfoDtoMap, businessSubtype, true, new Date());
    }

    @Override
    public List<UserStatementRecordDto> reInitUserStatementRecord(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, Map<String,
            UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap, String businessSubtype, List<TdFundMonthlyStatement> monthlyStatements, boolean notifyUser, Date statementDate) {
        for (TdFundMonthlyStatement tdFundMonthlyStatement : monthlyStatements) {
            //逻辑删除td_fund_monthly_statement
            tdMonthlyStatementMapper.deleteByPrimaryKey(tdFundMonthlyStatement.getId());

            //逻辑删除td_monthly_statement_data
            TdMonthlyStatementData condition = new TdMonthlyStatementData();
            condition.setStatementId(tdFundMonthlyStatement.getBusinessId());
            List<TdMonthlyStatementData> tdMonthlyStatementData = tdMonthlyStatementDataMapper.selectList(condition);

            tdMonthlyStatementData.forEach(t -> tdMonthlyStatementDataMapper.deleteByPrimaryKey(t.getId()));
        }

        return initUserStatementRecordBase(tdFundMonthlyStatementInfo, businessTypeAndUserInvestClientInfoDtoMap, businessSubtype, notifyUser, statementDate);
    }

    /**
     * 生成基础表数据
     *
     * @param monthlyStatementInfo
     * @param businessTypeAndUserInvestClientInfoDtoMap
     * @param businessSubtype
     * @return
     */
    private List<UserStatementRecordDto> initUserStatementRecordBase(TdFundMonthlyStatementInfo monthlyStatementInfo
            , Map<String, UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap, String businessSubtype, boolean notifyUser, Date statementDate) {
        List<String> docLangList = new ArrayList<>(2);

        docLangList.add(LanguageTemEnum.CN_HK.getValue());
        docLangList.add(LanguageTemEnum.CN_ZH.getValue());
        List<UserStatementRecordDto> userStatementRecordDtoList = new ArrayList<>(docLangList.size());

        UserInvestClientInfoDto userInvestClientInfoDto = businessTypeAndUserInvestClientInfoDtoMap.get(businessSubtype);

        for (String docLang : docLangList) {
            UserStatementRecordDto userStatementRecordDto = new UserStatementRecordDto();
            userStatementRecordDto.setPeriod(monthlyStatementInfo.getPeriod());
            userStatementRecordDto.setBusiness(monthlyStatementInfo.getBusinessType());
            userStatementRecordDto.setClientId(userInvestClientInfoDto.getClientId());
            //用户结单记录
            TdFundMonthlyStatement monthlyStatement = createUserMonthlyStatementRecords(docLang, monthlyStatementInfo, userInvestClientInfoDto, notifyUser, statementDate);

            userStatementRecordDto.setStatementRecord(monthlyStatement);

            //结单数据记录
            List<TdMonthlyStatementData> monthlyStatementBusinessDataList = createUserStatementBusinessData(monthlyStatementInfo, monthlyStatement, businessTypeAndUserInvestClientInfoDtoMap);

            userStatementRecordDto.setMonthlyStatementDataList(monthlyStatementBusinessDataList);

            userStatementRecordDto.setDocLang(docLang);
            userStatementRecordDto.setStatementId(monthlyStatement.getBusinessId());

            userStatementRecordDtoList.add(userStatementRecordDto);
        }

        return userStatementRecordDtoList;
    }
}
