package group.za.bank.statement.service.impl;

import cn.hutool.core.lang.Pair;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.sbs.business.model.resp.TransferDetailResp;
import group.za.bank.sbs.counterfront.common.enums.EntrustTypeEnum;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.quotasup.share.service.entity.resp.DayKlineRespDTO;
import group.za.bank.sbs.trade.common.constant.enums.FeeNameEnum;
import group.za.bank.sbs.trade.common.constant.enums.StockCodeTypeEnum;
import group.za.bank.sbs.trade.mapper.StkActionDetailSubMapper;
import group.za.bank.sbs.trade.model.dto.StkBusinessRecordDTO;
import group.za.bank.sbs.trade.model.dto.TaxQueryDTO;
import group.za.bank.sbs.trade.model.entity.StkHoldingHistory;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.sbs.tradedata.model.dto.ActionCapitalInfoDTO;
import group.za.bank.sbs.tradedata.model.dto.ActionFractionOrderDTO;
import group.za.bank.sbs.tradedata.model.req.ActionCapitalInfoQueryReq;
import group.za.bank.sbs.tradedata.model.resp.ActionCapitalInfoResp;
import group.za.bank.sbs.tradedata.model.resp.ActionFractionOrderResp;
import group.za.bank.statement.agent.RateAgent;
import group.za.bank.statement.agent.StkInfoAgent;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.*;
import group.za.bank.statement.entity.dto.*;
import group.za.bank.statement.manager.StockActionManager;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.*;
import group.za.bank.statement.service.remote.StockMarketRemoteService;
import group.za.bank.statement.service.remote.TradeRemoteService;
import group.za.bank.statement.service.remote.TradedataRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.bank.statement.utils.StatementDataSortUtils;
import group.za.bank.statement.utils.StatementNumberFormatUtils;
import group.za.bank.statement.utils.StockStatementNumberFormatUtils;
import group.za.invest.web.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static group.za.bank.statement.common.constants.StatementConstants.*;
import static group.za.bank.statement.constants.StatementRedisCacheKey.STOCK_MONTHLY_STATEMENT_CALENDAR_KEY;
import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.*;
import static group.za.bank.statement.utils.MarketUtils.transferMarketCode;
import static group.za.bank.statement.utils.MarketUtils.transferMarketCodeByCcy;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.dateFormat2EngStr;

/**
 * <AUTHOR>
 * @createTime 01 17:01
 * @description
 */
@Slf4j
@Service("stockStatementBusinessService")
public class StockStatementBusinessServiceImpl implements StatementBusinessService<StockMonthlyStatementDataDto> {

    @Autowired
    private TdStatementFileRecordService statementFileRecordService;
    @Autowired
    private TdStockStatementDataService stockStatementDataService;
    @Autowired
    private StkInfoAgent stkInfoAgent;
    @Autowired
    private RateAgent rateAgent;
    @Autowired
    private StockTradeDateService stockTradeDateService;
    @Autowired
    private StockMarketRemoteService stockMarketRemoteService;
    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private ExchangeRateInfoService exchangeRateInfoService;
    @Autowired
    private TradeRemoteService tradeRemoteService;
    @Autowired
    private StatementActionPendingDataService statementActionPendingDataService;
    @Autowired
    private StockStatementActionService stockStatementActionService;
    @Autowired
    private TradedataRemoteService tradedataRemoteService;
    @Autowired
    private StockTradeManager stockTradeManager;
    @Autowired
    private StkActionDetailSubMapper stkActionDetailSubMapper;
    @Autowired
    private StockActionManager stockActionManager;

    @Override
    public String getBusinessType() {
        return AccountTypeEnum.STOCK.getValue();
    }


    /**
     * 分红选股选息，默认选息备注文案
     */
    public static final String CASH_DIVIDED_WITH_SCRIP_OPTION_REMARK = "cash dividend with scrip option";
    public static final String CASH_DIVIDED_WITH_SCRIP_OPTION_REMARK_ZH = "Dividend with scrip option (Default Option: Cash Opt) 选股选息（默认：现金）";
    public static final String CASH_DIVIDED_WITH_SCRIP_OPTION_REMARK_HK = "Dividend with scrip option (Default Option: Cash Opt) 選股選息（默認：現金）";

    /**
     * 本期数据是否已经准备完毕
     *
     * @param tdFundMonthlyStatementInfo
     * @param useCache
     * @return
     */
    @Override
    public boolean isDataPrepared(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, boolean useCache) {
        // 股票数据校验开关(默认true校验)
        if (!systemConfig.getStockCheck()) {
            return Boolean.TRUE;
        }
        String period = tdFundMonthlyStatementInfo.getPeriod();

        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);

        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketCodeAndTradeDateInfoMap = stockStatementTradeDateInfo.getMarketCodeAndTradeDateInfoMap();
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo usMarketStatementTradeDateInfo = marketCodeAndTradeDateInfoMap.get(MarketCodeEnum.US.getValue());

        List<String> coreTradeDateList = stockStatementTradeDateInfo.getCoreTradeDateList();

        String startDate = usMarketStatementTradeDateInfo.getPeriodStartCoreTradeDate();
        String endDate = usMarketStatementTradeDateInfo.getPeriodEndCoreTradeDate();

        //确保每个交易日的日结单都被解析出来
        List<TdStatementFileRecord> statementDailyFileRecordList = statementFileRecordService.queryDailyFileRecords(startDate, endDate);

        Map<String, TdStatementFileRecord> fileDateAndRecordMap = new HashMap<>(statementDailyFileRecordList.size());
        for (TdStatementFileRecord statementDailyFileRecord : statementDailyFileRecordList) {
            fileDateAndRecordMap.put(DateUtil.format(statementDailyFileRecord.getStatementDate(), DateUtil.FORMATDAY), statementDailyFileRecord);
        }

        coreTradeDateList.add(stockStatementTradeDateInfo.getNextPeriodCoreTradeDateList().get(0));

        for (String tradeDateString : coreTradeDateList) {
            TdStatementFileRecord fileRecord = fileDateAndRecordMap.get(tradeDateString);
            if (fileRecord == null || !StatementFileStatusEnum.SUCCESS.getFileStatus().equals(fileRecord.getDataStatus())) {
                log.info("找不到ttl {}日结单文件解析记录,当期结单({})未能开始生成!", tradeDateString, period);
                return false;
            }
        }
        return true;
    }


    /**
     * 获取结单当期的日历信息
     *
     * @param period
     * @return
     */
    StockStatementTradeDateInfo getStatementTradeDateInfo(String period) {
        String calendarKey = STOCK_MONTHLY_STATEMENT_CALENDAR_KEY.key(period);
        return AbstractCacheTemplate.queryFromCache(calendarKey, 7, TimeUnit.DAYS, () -> {
            return queryStatementTradeDateInfo(period);
        }, false);
    }


    /**
     * 查询指定市场指定月份的第一个
     *
     * @param period
     * @return
     */
    private StockStatementTradeDateInfo queryStatementTradeDateInfo(String period) {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = new StockStatementTradeDateInfo();
        Date periodDate = DateUtil.parse(period, systemConfig.getMonthlyStatementPeriodFormat());
        Date nextPeriodDate = DateUtil.addMonth(periodDate, 1);
        String nextPeriod = DateUtil.format(nextPeriodDate, systemConfig.getMonthlyStatementPeriodFormat());

        //美股当月交易日转成hk日历
        List<String> monthCoreTradeDateList = stockTradeDateService.queryCoreCalendarTradeDateList(period);
        if (CollectionUtils.isEmpty(monthCoreTradeDateList)) {
            throw new RuntimeException("核心交易日历结果不能为空!period:" + period + "");
        }

        List<String> usMonthTradeDateList = stockTradeDateService.queryCalendarTradeDateList(MarketCodeEnum.US.getValue(), period);
        if (CollectionUtils.isEmpty(monthCoreTradeDateList)) {
            throw new RuntimeException(MarketCodeEnum.US.getValue() + "交易日历结果不能为空!period:" + period);
        }

        List<String> hkMonthTradeDateList = stockTradeDateService.queryCalendarTradeDateList(MarketCodeEnum.HK.getValue(), period);
        if (CollectionUtils.isEmpty(monthCoreTradeDateList)) {
            throw new RuntimeException(MarketCodeEnum.HK.getValue() + "交易日历结果不能为空!period:" + period);
        }

        List<String> nextMonthCoreTradeDateList = stockTradeDateService.queryCoreCalendarTradeDateList(nextPeriod);
        if (CollectionUtils.isEmpty(monthCoreTradeDateList)) {
            throw new RuntimeException("核心交易日历结果不能为空!period:" + nextPeriod);
        }

        stockStatementTradeDateInfo.setPeriod(period);
        stockStatementTradeDateInfo.setCoreTradeDateList(monthCoreTradeDateList);
        stockStatementTradeDateInfo.setNextPeriodCoreTradeDateList(nextMonthCoreTradeDateList);


        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketCodeAndTradeDateInfoMap = new HashMap<>();
        stockStatementTradeDateInfo.setMarketCodeAndTradeDateInfoMap(marketCodeAndTradeDateInfoMap);

        //初始化美股日历信息
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo usMarketStatementTradeDateInfo = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        //美股第一个交易日的下一个交易日
        String usStartDate = monthCoreTradeDateList.get(0);
        String usEndDate = nextMonthCoreTradeDateList.get(0);

        usMarketStatementTradeDateInfo.setMarketCode(MarketCodeEnum.US.getValue());
        usMarketStatementTradeDateInfo.setPeriodStartCoreTradeDate(usStartDate);
        usMarketStatementTradeDateInfo.setPeriodEndCoreTradeDate(usEndDate);

        usMarketStatementTradeDateInfo.setTradeDateList(usMonthTradeDateList);
        String startTradeDate = usMonthTradeDateList.get(0);
        String endTradeDate = usMonthTradeDateList.get(usMonthTradeDateList.size() - 1);
        usMarketStatementTradeDateInfo.setStartTradeDate(startTradeDate);
        usMarketStatementTradeDateInfo.setEndTradeDate(endTradeDate);
        marketCodeAndTradeDateInfoMap.put(MarketCodeEnum.US.getValue(), usMarketStatementTradeDateInfo);


        //初始化港股日历信息
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo hkMarketStatementTradeDateInfo = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();

        String hkStartDate = monthCoreTradeDateList.get(0);
        String hkEndDate = monthCoreTradeDateList.get(monthCoreTradeDateList.size() - 1);

        hkMarketStatementTradeDateInfo.setMarketCode(MarketCodeEnum.HK.getValue());
        hkMarketStatementTradeDateInfo.setPeriodStartCoreTradeDate(hkStartDate);
        hkMarketStatementTradeDateInfo.setPeriodEndCoreTradeDate(hkEndDate);

        hkMarketStatementTradeDateInfo.setTradeDateList(hkMonthTradeDateList);
        hkMarketStatementTradeDateInfo.setStartTradeDate(hkStartDate);
        hkMarketStatementTradeDateInfo.setEndTradeDate(hkEndDate);
        marketCodeAndTradeDateInfoMap.put(MarketCodeEnum.HK.getValue(), hkMarketStatementTradeDateInfo);

        log.info("月结单股票业务当期{}交易日历数据。stockStatementTradeDateInfo:{}", period, stockStatementTradeDateInfo);
        return stockStatementTradeDateInfo;
    }

    /**
     * 抽取业务结单数据
     *
     * @param statementInfo
     * @param statementData
     * @return
     */
    @Override
    public Map<String, Object> generateDataMap(TdFundMonthlyStatementInfo statementInfo
            , TdFundMonthlyStatement statementRecord, TdMonthlyStatementData statementData, String marketCode) {
        String accountId = statementData.getAccountId();

        StockMonthlyStatementDataDto stockMonthlyStatementDataDto
                = getUserStockStatementData(statementInfo, accountId, statementRecord, marketCode);

        return JSONObject.parseObject(JsonUtils.toFormatJsonString(stockMonthlyStatementDataDto));
    }


    /**
     * 将map数据解析成对应的data
     *
     * @param businessDataMap
     * @return
     */
    @Override
    public StockMonthlyStatementDataDto parseDataMap2Dto(Map<String, Object> businessDataMap) {
        if (businessDataMap == null) {
            return null;
        }
        return JSONObject.parse(JsonUtils.toJsonString(businessDataMap), StockMonthlyStatementDataDto.class);
    }

    /**
     * 抽取股票业务数据
     *
     * @param statementInfo
     * @param accountId
     * @param statementRecord
     * @return
     */
    private StockMonthlyStatementDataDto getUserStockStatementData(TdFundMonthlyStatementInfo statementInfo, String accountId
            , TdFundMonthlyStatement statementRecord, String marketCode) {

        String period = statementInfo.getPeriod();
        String businessType = statementInfo.getBusinessType();
        String bankUserId = statementRecord.getBankUserId();

        StockMonthlyStatementDataDto stockMonthlyStatementDataDto = new StockMonthlyStatementDataDto();
        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);

        //0.基本信息
        StockMonthlyStatementDataDto.BasicInfoDto basicInfoDto = queryBasicInfo(statementInfo, statementRecord, accountId, marketCode);
        stockMonthlyStatementDataDto.setBasicInfoDto(basicInfoDto);

        //1.查询客户持仓信息——holdingList
        List<StockMonthlyStatementDataDto.HoldingDto> holdingList = queryHoldList(period, businessType, statementRecord.getBankUserId(), accountId, statementRecord.getDocLang()
                , stockStatementTradeDateInfo, marketCode);
        stockMonthlyStatementDataDto.setHoldingList(holdingList);


        //2.查询已确认交易——confirmedOrderList
        List<StockMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderList = queryConfirmedOrderDto(period, bankUserId, accountId, statementRecord.getDocLang(),
                stockStatementTradeDateInfo, marketCode);
        stockMonthlyStatementDataDto.setConfirmedOrderList(confirmedOrderList);

        //3.查询派息——dividendOrderList
        List<StockMonthlyStatementDataDto.DividendOrderDto> dividendOrderList;
        if (MarketCodeEnum.US.getValue().equals(marketCode)) {
            dividendOrderList = queryDividendOrderDto(period, bankUserId, accountId, statementRecord.getDocLang()
                    , stockStatementTradeDateInfo);
        } else {
            dividendOrderList = queryHkDividendOrderDto(period, bankUserId, accountId, statementRecord.getDocLang()
                    , stockStatementTradeDateInfo);
        }
        stockMonthlyStatementDataDto.setDividendOrderList(dividendOrderList);

        //4.查询持仓变动——holdingChangeList
        List<StockMonthlyStatementDataDto.HoldingChangeDto> holdingChangeList;
        if (MarketCodeEnum.US.getValue().equals(marketCode)) {
            holdingChangeList = queryHoldChangerDto(period, accountId, statementRecord.getDocLang()
                    , stockStatementTradeDateInfo);
        }else{
            holdingChangeList = queryHkHoldChangerDto(period, accountId, statementRecord.getDocLang()
                    , stockStatementTradeDateInfo);
        }
        stockMonthlyStatementDataDto.setHoldingChangeList(holdingChangeList);

        return stockMonthlyStatementDataDto;
    }

    /**
     * 查询基础信息
     *
     * @param statementInfo
     * @param statementRecord
     * @param accountId
     * @return
     */
    private StockMonthlyStatementDataDto.BasicInfoDto queryBasicInfo(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement statementRecord,
                                                                     String accountId, String marketCode) {
        StockMonthlyStatementDataDto.BasicInfoDto basicInfoDto = new StockMonthlyStatementDataDto.BasicInfoDto();

        //查询持仓总市值和总持仓盈亏
        Pair<BigDecimal, BigDecimal> userPeriodEndData = getUserPeriodEndTotalMarketAndProfit(statementInfo, statementRecord, CurrencyEnum.HKD, accountId, marketCode);

        basicInfoDto.setCurrency(CurrencyEnum.HKD.getCurrency());
        basicInfoDto.setTotalMarketValue(userPeriodEndData.getKey());
        basicInfoDto.setHoldingProfit(userPeriodEndData.getValue());

        return basicInfoDto;
    }

    /**
     * 填充股票的名称
     *
     * @param
     * @param stockCode
     * @param docLang
     * @param dto
     * @param <T>
     */
    private <T extends StockMonthlyStatementDataDto.StockBasicInfoDto> void fillStockBasicInfo(String exchangeCode
            , String stockCode, String docLang, T dto) {
        StockInfoResp stkInfo = stkInfoAgent.getStkInfo(exchangeCode, stockCode);

        //股票名称多语言转换
        String stockName = stkInfoAgent.getStockNameAbbr(stkInfo, docLang);
        dto.setStockName(stockName);
        //获取股票英文名称，固定传US_EN
        String stockNameEnglish = stkInfoAgent.getStockNameAbbr(stkInfo, I18nSupportEnum.US_EN.getName());
        dto.setStockEngName(stockNameEnglish);
        dto.setStockCode(stockCode);
        dto.setExchangeCode(exchangeCode);
    }

    private <T extends StockMonthlyStatementDataDto.StockBasicInfoDto> void fillIpoStockInfo(StatementTradeChangeDetailDto detailDto, String docLang, T dto) {
        dto.setStockName(stkInfoAgent.getIpoStockNameAbbr(detailDto.getStockNameCn(), detailDto.getStockNameHk(), detailDto.getStockNameHk(), docLang));
        dto.setStockEngName(detailDto.getStockNameEn());
        dto.setStockCode(detailDto.getStockCode());
        dto.setExchangeCode(MarketCodeEnum.HK.getValue());
    }



    /**
     * 查询用户期末的资产信息
     *
     * @param period
     * @param accountId
     * @return
     */
    private List<StatementAccountAssetDto> queryUserPeriodEndAssetDtoList(String period, String accountId, String marketCode) {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);

        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo = stockStatementTradeDateInfo.getMarketCodeAndTradeDateInfoMap().get(marketCode);
        String endTradeDate = marketStatementTradeDateInfo.getPeriodEndCoreTradeDate();
        String endCoreTradeDate = marketStatementTradeDateInfo.getEndTradeDate();

        //资产摘要数据只会从日结单的最后一个交易日查询，不用月结单数据兜底
        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);
        List<StatementAccountAssetDto> periodEndAssetDtoList
                = stockStatementDataService.queryAccountAssetData(endTradeDate, StatementTypeEnum.DAILY.getType(), accountId, ttlMarketCode);

        List<String> assetIdSet = new ArrayList<>();
        for (StatementAccountAssetDto statementAccountAssetDto : periodEndAssetDtoList) {
            assetIdSet.add(generateAssetId(marketCode, statementAccountAssetDto.getStockCode()));
        }

        Map<String, DayKlineRespDTO> assetIdAndKlineRespDtoMap = stockMarketRemoteService.queryStockKline(DateUtil.parse(endCoreTradeDate, DateUtil.FORMATDAY), assetIdSet);

        for (StatementAccountAssetDto statementAccountAssetDto : periodEndAssetDtoList) {
            String assetId = generateAssetId(marketCode, statementAccountAssetDto.getStockCode());
            DayKlineRespDTO klineRespDTO = assetIdAndKlineRespDtoMap.get(assetId);

            //如果没有查到行情数据，则判断是否是临时代码
            if (klineRespDTO == null || klineRespDTO.getClose() == null) {
                StockInfoResp stkInfo = stkInfoAgent.getStkInfo(marketCode, statementAccountAssetDto.getStockCode());
                //是临时代码，则把收盘价和市值置为空
                if (null != stkInfo && StockCodeTypeEnum.TEMPORARY.getValue().equals(stkInfo.getStockCodeType())) {
                    statementAccountAssetDto.setClosePrice(null);
                    statementAccountAssetDto.setMarketValue(null);
                    log.info("临时代码行情特殊处理，stockCode:{}", statementAccountAssetDto.getStockCode());
                } else {
                    log.error("结单获取股票行情失败! accountId:{},assetId:{},klineRespDTO:{}", accountId, assetId, klineRespDTO);
                    throw new BusinessException(GET_STOCK_KLINE_FAIL);
                }
            } else {
                //有行情数据，则直接取，无需校验是否是临时代码
                String qtyString = statementAccountAssetDto.getClosingSettleHolding();
                BigDecimal qty = qtyString == null ? null : new BigDecimal(qtyString);
                BigDecimal closePrice = klineRespDTO.getClose();

                BigDecimal marketValue;
                //如果数量为空或者数量为0或者收盘价为空，则市值都为空
                if (qty == null || BigDecimal.ZERO.compareTo(qty) == 0 || closePrice == null) {
                    marketValue = null;
                } else {
                    marketValue = qty.multiply(closePrice);
                }

                statementAccountAssetDto.setClosePrice(closePrice == null ? null : closePrice.toPlainString());
                statementAccountAssetDto.setMarketValue(marketValue == null ? null : marketValue.toPlainString());
            }
        }

        return periodEndAssetDtoList;
    }


    /**
     * 生成资产id，stockCode.marketCode
     *
     * @param marketCode
     * @param stockCode
     * @return
     */
    private String generateAssetId(String marketCode, String stockCode) {
        return stockCode + "." + marketCode;
    }

    /**
     * 计算上个月的
     *
     * @param currentPeriod
     * @return
     */
    private String calcLastPeriod(String currentPeriod) {
        Date periodDate = DateUtil.parse(currentPeriod, systemConfig.getMonthlyStatementPeriodFormat());
        Date lastPeriodDate = DateUtil.addMonth(periodDate, -1);
        return DateUtil.format(lastPeriodDate, systemConfig.getMonthlyStatementPeriodFormat());
    }

    /**
     * 查询持仓信息
     *
     * @param period
     * @param accountId
     * @return
     */
    private List<StockMonthlyStatementDataDto.HoldingDto> queryHoldList(String period, String businessType, String userId
            , String accountId, String docLang, StockStatementTradeDateInfo stockStatementTradeDateInfo, String marketCode) {

        Map<String, Pair<StatementAccountAssetDto, StatementAccountAssetDto>> stockCodeAndAssetDtoMap = new HashMap<>();

        String lastPeriod = calcLastPeriod(period);

        //期初
        List<StatementAccountAssetDto> periodStartAssetDtoList = queryUserPeriodEndAssetDtoList(lastPeriod, accountId, marketCode);
        periodStartAssetDtoList.forEach(statementAccountAssetDto -> {
            String stockCode = statementAccountAssetDto.getStockCode();
            stockCodeAndAssetDtoMap.putIfAbsent(stockCode, Pair.of(statementAccountAssetDto, null));
        });


        //期末
        List<StatementAccountAssetDto> periodEndAssetDtoList = queryUserPeriodEndAssetDtoList(period, accountId, marketCode);

        periodEndAssetDtoList.forEach(statementAccountAssetDto -> {
            String stockCode = statementAccountAssetDto.getStockCode();
            Pair<StatementAccountAssetDto, StatementAccountAssetDto> pair = stockCodeAndAssetDtoMap.get(stockCode);
            if (pair == null) {
                pair = Pair.of(null, statementAccountAssetDto);
            } else {
                pair = Pair.of(pair.getKey(), statementAccountAssetDto);
            }
            stockCodeAndAssetDtoMap.put(stockCode, pair);
        });


        List<StockMonthlyStatementDataDto.HoldingDto> holdingList = new ArrayList<>();

        stockCodeAndAssetDtoMap.values().forEach(pair -> {
            StatementAccountAssetDto periodStartAssetDto = pair.getKey();
            StatementAccountAssetDto periodEndAssetDto = pair.getValue();
            StatementAccountAssetDto baseAssetDto = (periodStartAssetDto == null ? periodEndAssetDto : periodStartAssetDto);

            if (isPeriodStartAndEndBothZero(periodStartAssetDto, periodEndAssetDto)) {
                log.info("用户当期{}结单期初、期末交收持仓结余都为0! userId:{}", period, userId);
            } else {
                StockMonthlyStatementDataDto.HoldingDto holdingDto = new StockMonthlyStatementDataDto.HoldingDto();
                String exchangeCode = transferMarketCode(baseAssetDto.getExchangeCode());
                if (StringUtils.isEmpty(exchangeCode)) {
                    log.error("queryHoldList用户当期{}结单找不到市场代码! userId:{},stockCode:{}", period, userId, baseAssetDto.getStockCode());
                    throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "找不到市场代码");
                }

                //股票基本信息
                fillStockBasicInfo(exchangeCode, baseAssetDto.getStockCode(), docLang, holdingDto);

                //币种
                holdingDto.setCurrency(baseAssetDto.getCurrency());

                holdingDto.setOpeningBalance(StatementNumberFormatUtils.statementQtyFormatFourNoZero(periodStartAssetDto == null ? null : new BigDecimal(periodStartAssetDto.getClosingSettleHolding()), "0"));
                holdingDto.setClosingBalance(StatementNumberFormatUtils.statementQtyFormatFourNoZero(periodEndAssetDto == null ? null : new BigDecimal(periodEndAssetDto.getClosingSettleHolding()), "0"));

                holdingDto.setReferencePrice(StockStatementNumberFormatUtils.statementPriceFormat(periodEndAssetDto == null ? null : periodEndAssetDto.getClosePrice(), STATEMENT_NULL_VALUE_PLACEHOLDER));

                String marketValueString = (periodEndAssetDto != null ? periodEndAssetDto.getMarketValue() : null);
                holdingDto.setMarketValue(StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(marketValueString, STATEMENT_NULL_VALUE_PLACEHOLDER));

                //等值港币
                if (!Strings.isEmpty(marketValueString)) {
                    BigDecimal exchangeHkdMarketValue = exchange(new BigDecimal(marketValueString), baseAssetDto.getCurrency()
                            , period, businessType);
                    holdingDto.setHkdMarketValue(StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(exchangeHkdMarketValue, STATEMENT_NULL_VALUE_PLACEHOLDER));
                } else {
                    holdingDto.setHkdMarketValue(STATEMENT_NULL_VALUE_PLACEHOLDER);
                }

                holdingList.add(holdingDto);
            }

        });

        //排序规则：按照股票代码的首字母从小到大（A_Z）排列，若首字母大小相同则对比第二个字母大小，以此类推；
        return StatementDataSortUtils.sortByStringAsc(StockMonthlyStatementDataDto.HoldingDto::getStockCode, holdingList);
    }


    /**
     * 判断期初期末是否都为0
     *
     * @param periodStartAssetDto
     * @param periodEndAssetDto
     * @return
     */
    private boolean isPeriodStartAndEndBothZero(StatementAccountAssetDto periodStartAssetDto, StatementAccountAssetDto periodEndAssetDto) {

        BigDecimal openBalance = null;
        if (periodStartAssetDto != null && !Strings.isEmpty(periodStartAssetDto.getClosingSettleHolding())) {
            openBalance = new BigDecimal(periodStartAssetDto.getClosingSettleHolding());
        } else {
            openBalance = BigDecimal.ZERO;
        }

        BigDecimal closingBalance = null;
        if (periodEndAssetDto != null && !Strings.isEmpty(periodEndAssetDto.getClosingSettleHolding())) {
            closingBalance = new BigDecimal(periodEndAssetDto.getClosingSettleHolding());
        } else {
            closingBalance = BigDecimal.ZERO;
        }


        boolean isPeriodStarHoldingIsZero = (openBalance.compareTo(BigDecimal.ZERO) == 0);
        boolean isPeriodEndHoldingIsZero = (closingBalance.compareTo(BigDecimal.ZERO) == 0);


        return isPeriodStarHoldingIsZero && isPeriodEndHoldingIsZero;
    }


    /**
     * 查询成交数据
     *
     * @param period
     * @param accountId
     * @return
     */
    private List<StockMonthlyStatementDataDto.ConfirmedOrderDto> queryConfirmedOrderDto(String period, String bankUserId, String accountId, String docLang
            , StockStatementTradeDateInfo stockStatementTradeDateInfo, String marketCode) {

        List<StockMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderList = new ArrayList<>();


        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);
        List<StatementUsOrderDto> statementUsOrderDtos = stockStatementDataService.queryUsOrderData(stockStatementTradeDateInfo
                , StatementTypeEnum.DAILY.getType(), accountId, ttlMarketCode);

        //查询小数股订单数据
        List<StatementUsOrderDto> fractionOrderList = this.queryFractionOrderList(accountId, period, marketCode, bankUserId);
        log.info("queryConfirmedOrderDto fractionOrderList size:{}", fractionOrderList.size());
        statementUsOrderDtos.addAll(fractionOrderList);

        //查询按照交易日期排序
        statementUsOrderDtos = StatementDataSortUtils.sortByDateAsc(dto -> DateUtil.stringToDate(dto.getTradeDate()), statementUsOrderDtos);

        statementUsOrderDtos.forEach(statementUsOrderDto -> {
            StockMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto = new StockMonthlyStatementDataDto.ConfirmedOrderDto();
            //无市场代码，用币种转市场
            String exchangeCode = transferMarketCodeByCcy(statementUsOrderDto.getCurrency());
            //股票基本信息
            fillStockBasicInfo(exchangeCode, statementUsOrderDto.getStockCode(), docLang, confirmedOrderDto);
            confirmedOrderDto.setCurrency(statementUsOrderDto.getCurrency());

            //日期格式化为英文日期格式
            confirmedOrderDto.setTradeDate(dateFormat2EngStr(DateUtil.stringToDate(statementUsOrderDto.getTradeDate())));

            //交收日期兼容--展示
            String clearDateStr = StatementConstants.DOUBLE_SHORT_BAR_FLAG.equals(statementUsOrderDto.getClearDate())
                    ? statementUsOrderDto.getClearDate() : dateFormat2EngStr(DateUtil.stringToDate(statementUsOrderDto.getClearDate()));
            confirmedOrderDto.setClearDate(clearDateStr);

            //获取买卖方向
            StatementDirectionEnum direction = StatementDirectionEnum.getByTradeType(statementUsOrderDto.getTradeType());
            confirmedOrderDto.setDirection(direction.getTradeType());


            //private String description;
            //private String engDescription;
            //获取描述信息
            String directionStr = docLang.equals(I18nSupportEnum.CN_ZH.getName()) ? direction.getDesc() : direction.getDescHk();
            String desc = directionStr + StatementConstants.SHIFT_LINE + statementUsOrderDto.getStockCode() + " "
                    + confirmedOrderDto.getStockEngName() + " " + confirmedOrderDto.getStockName();
            confirmedOrderDto.setDescription(desc);

            //构建成交明细
            buildOrderDetails(confirmedOrderDto, statementUsOrderDto);

            //格式化数字数据
            formatConfirmOrderDto(exchangeCode, confirmedOrderDto, statementUsOrderDto);


            confirmedOrderList.add(confirmedOrderDto);
        });
        return confirmedOrderList;
    }


    /**
     * 根据BO订单号查询成交明细信息
     * 并构建对象
     *
     * @param confirmedOrderDto
     * @param statementUsOrderDto
     */
    private void buildOrderDetails(StockMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto, StatementUsOrderDto statementUsOrderDto) {
        String referenceNo = statementUsOrderDto.getReferenceNo();
        List<StockMonthlyStatementDataDto.OrderDetailDto> detailDtos = new ArrayList<>();
        //公司行动小数股数据特殊处理
        if (referenceNo.startsWith(StatementConstants.ACTION_FRACTION_ORDER_PREFIX)) {
            StockMonthlyStatementDataDto.OrderDetailDto orderDetailDto = new StockMonthlyStatementDataDto.OrderDetailDto();
            orderDetailDto.setSeq("1");
            orderDetailDto.setTransQty(StatementNumberFormatUtils.statementQtyFormatFourNoZero(new BigDecimal(statementUsOrderDto.getQty())));
            orderDetailDto.setTransPrice(StockStatementNumberFormatUtils.statementPriceFormat(new BigDecimal(statementUsOrderDto.getAvgPrice()),
                    StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER));
            orderDetailDto.setTransAmount(StatementNumberFormatUtils.statementMoneyFormat(new BigDecimal(statementUsOrderDto.getAmount())));

            detailDtos.add(orderDetailDto);
            log.info("公司行动小数股成交明细组装完成,orderDetailDto:{}", JsonUtils.toJsonString(orderDetailDto));
        } else {
            //查询成交明细，先去cash库查找对应关系，查不到再从远程接口查询
            List<StkBusinessRecordDTO> stkBusinessRecordDTOS = stockTradeManager.queryStkBusinessRecord(referenceNo);
            if (CollectionUtils.isEmpty(stkBusinessRecordDTOS)) {
                log.info("查询订单成交明细为空,boOrderNo:{}", referenceNo);
                throw new BusinessException(USER_STATEMENT_DATA_NOT_COMPLETE);
            }
            for (int i = 0; i < stkBusinessRecordDTOS.size(); i++) {
                StkBusinessRecordDTO stkBusinessRecordDTO = stkBusinessRecordDTOS.get(i);
                StockMonthlyStatementDataDto.OrderDetailDto orderDetailDto = new StockMonthlyStatementDataDto.OrderDetailDto();
                orderDetailDto.setSeq(String.valueOf(i + 1));
                orderDetailDto.setTransQty(StatementNumberFormatUtils.statementQtyFormatFourNoZero(stkBusinessRecordDTO.getBusinessQty()));
                orderDetailDto.setTransPrice(StockStatementNumberFormatUtils.statementPriceFormat(stkBusinessRecordDTO.getBusinessPrice(),
                        StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER));
                orderDetailDto.setTransAmount(StatementNumberFormatUtils.statementMoneyFormat(stkBusinessRecordDTO.getBusinessAmount()));

                detailDtos.add(orderDetailDto);
            }
        }

        confirmedOrderDto.setDetails(detailDtos);
        log.info("buildOrderDetails success, boOrderNo:{}", referenceNo);
    }


    /**
     * 格式化已确认订单
     *
     * @param confirmedOrderDto
     * @param statementUsOrderDto
     */
    private void formatConfirmOrderDto(String exchangeCode, StockMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto
            , StatementUsOrderDto statementUsOrderDto) {

        if (MarketCodeEnum.US.getValue().equals(exchangeCode)) {
            formatUsConfirmOrderDto(confirmedOrderDto, statementUsOrderDto);
        }

        if (MarketCodeEnum.HK.getValue().equals(exchangeCode)) {
            formatHkConfirmOrderDto(confirmedOrderDto, statementUsOrderDto);
        }

    }


    private void formatUsConfirmOrderDto(StockMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto, StatementUsOrderDto statementUsOrderDto) {
        StatementDirectionEnum statementDirectionEnum = StatementDirectionEnum.getByTradeType(statementUsOrderDto.getTradeType());
        EntrustTypeEnum entrustTypeEnum = (StatementDirectionEnum.BUY.equals(statementDirectionEnum)
                ? EntrustTypeEnum.BUY : EntrustTypeEnum.SELL);

        String commissionString = statementUsOrderDto.getCommission();
        String netAmountString = statementUsOrderDto.getNetAmount();

        confirmedOrderDto.setAvgPrice(StockStatementNumberFormatUtils.statementPriceFormat(statementUsOrderDto.getAvgPrice(), null));
        confirmedOrderDto.setQty(StockStatementNumberFormatUtils.statementQtyFormat(statementUsOrderDto.getQty(), null));
        confirmedOrderDto.setAmount(StockStatementNumberFormatUtils.statementMoneyFormat(statementUsOrderDto.getAmount(), null));

        if (confirmedOrderDto.getAvgPrice() == null) {
            throw new BusinessException(STOCK_CONFIRM_ORDER_AVG_PRICE_CAN_NOT_BE_NULL);
        }
        if (confirmedOrderDto.getQty() == null) {
            throw new BusinessException(STOCK_CONFIRM_ORDER_QTY_CAN_NOT_BE_NULL);
        }

        if (confirmedOrderDto.getAmount() == null) {
            throw new BusinessException(STOCK_CONFIRM_ORDER_AMOUNT_CAN_NOT_BE_NULL);
        }

        BigDecimal qty = new BigDecimal(statementUsOrderDto.getQty());
        //取数量的绝对值
        BigDecimal qtyAbs = qty.abs();

        BigDecimal feeSum = BigDecimal.ZERO;
        //如果是卖出，且卖出数量小于1，则把佣金置为0
        if (EntrustTypeEnum.SELL.equals(entrustTypeEnum) && qtyAbs.compareTo(BigDecimal.ONE) < 0) {
            //先把佣金加到总费用上，再佣金置为0
            feeSum = feeSum.add(new BigDecimal(commissionString));

            commissionString = ZERO_DEFAULT;
        }
        //佣金
        confirmedOrderDto.setCommission(StockStatementNumberFormatUtils.statementMoneyFormat(commissionString
                , STATEMENT_NULL_VALUE_PLACEHOLDER));

        log.info("订单{}卖出净金额:{},佣金:{}", statementUsOrderDto.getReferenceNo(), netAmountString, commissionString);

        //相关费用
        boolean generateStatementWhenFeeNull = systemConfig.getGenerateStatementWhenFeeNull();

        if (EntrustTypeEnum.SELL.equals(entrustTypeEnum)) {
            Map<String, StatementFeeDto> feeMap = statementUsOrderDto.getFeeMap();
            String secFeeKey = systemConfig.getSecFeeKey();
            String finraFeeKey = systemConfig.getFinraFeeKey();
            StatementFeeDto secFeeDto = feeMap.get(secFeeKey);
            StatementFeeDto finraFeeDto = feeMap.get(finraFeeKey);

            String secFeeString = (secFeeDto == null ? null : secFeeDto.getFee());
            String finraFeeString = (finraFeeDto == null ? null : finraFeeDto.getFee());

            if (!generateStatementWhenFeeNull) {
                if (Strings.isEmpty(secFeeString)) {
                    log.error("股票成交单据缺少secFee!,secFeeKey:{},参考编号:{},feeMap:{}"
                            , secFeeKey, statementUsOrderDto.getReferenceNo(), feeMap);
                    throw new BusinessException(STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND);
                }
                if (Strings.isEmpty(finraFeeString)) {
                    log.error("股票成交单据缺少finraFee!,finraFeeKey:{},参考编号:{},feeMap:{}"
                            , finraFeeDto, statementUsOrderDto.getReferenceNo(), feeMap);
                    throw new BusinessException(STOCK_CONFIRM_ORDER_FINRA_FEE_NOT_FOUND);
                }
            }

            //如果是卖出小于1股的订单，则先把费用汇总，再把费用置为0
            if (qtyAbs.compareTo(BigDecimal.ONE) < 0) {
                BigDecimal secFee = Strings.isEmpty(secFeeString) ? BigDecimal.ZERO : new BigDecimal(secFeeString);
                BigDecimal finraFee = Strings.isEmpty(finraFeeString) ? BigDecimal.ZERO : new BigDecimal(finraFeeString);

                //计算总费用
                feeSum = feeSum.add(secFee).add(finraFee);

                secFeeString = ZERO_DEFAULT;
                finraFeeString = ZERO_DEFAULT;
            }

            confirmedOrderDto.setSecFee(StockStatementNumberFormatUtils.statementMoneyFormat(secFeeString
                    , STATEMENT_NULL_VALUE_PLACEHOLDER));
            confirmedOrderDto.setFinraFee(StockStatementNumberFormatUtils.statementMoneyFormat(finraFeeString
                    , STATEMENT_NULL_VALUE_PLACEHOLDER));

            //交收金额
            BigDecimal netAmount = Strings.isEmpty(netAmountString) ? BigDecimal.ZERO : new BigDecimal(netAmountString);

            //如果是卖出小于1股的订单，交收金额需加上总费用
            if (qtyAbs.compareTo(BigDecimal.ONE) < 0) {
                netAmount = netAmount.add(feeSum);
                log.info("小数股订单，netAmount:{},feeSum:{}", netAmount, feeSum);
            }

            confirmedOrderDto.setAmount(StockStatementNumberFormatUtils
                    .statementMoneyFormat(netAmount, null));
        } else {
            //买入交收金额
            BigDecimal netAmount = Strings.isEmpty(netAmountString) ? BigDecimal.ZERO : new BigDecimal(netAmountString);
            confirmedOrderDto.setAmount(StockStatementNumberFormatUtils
                    .statementMoneyFormat(netAmount, null));
        }
    }

    private void formatHkConfirmOrderDto(StockMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto, StatementUsOrderDto statementUsOrderDto) {
        confirmedOrderDto.setAvgPrice(StockStatementNumberFormatUtils.statementPriceFormat(statementUsOrderDto.getAvgPrice(), null));
        confirmedOrderDto.setQty(StockStatementNumberFormatUtils.statementQtyFormat(statementUsOrderDto.getQty(), null));
        confirmedOrderDto.setAmount(StockStatementNumberFormatUtils.statementMoneyFormat(statementUsOrderDto.getAmount(), null));

        if (confirmedOrderDto.getAvgPrice() == null) {
            throw new BusinessException(STOCK_CONFIRM_ORDER_AVG_PRICE_CAN_NOT_BE_NULL);
        }
        if (confirmedOrderDto.getQty() == null) {
            throw new BusinessException(STOCK_CONFIRM_ORDER_QTY_CAN_NOT_BE_NULL);
        }

        if (confirmedOrderDto.getAmount() == null) {
            throw new BusinessException(STOCK_CONFIRM_ORDER_AMOUNT_CAN_NOT_BE_NULL);
        }

        //佣金
        String commissionString = statementUsOrderDto.getCommission();
        confirmedOrderDto.setCommission(StockStatementNumberFormatUtils.statementMoneyFormat(commissionString
                , STATEMENT_NULL_VALUE_PLACEHOLDER));

        //相关费用校验参数
        boolean generateStatementWhenFeeNull = systemConfig.getGenerateStatementWhenFeeNull();
        Map<String, StatementFeeDto> feeMap = statementUsOrderDto.getFeeMap();

        String stampDutyKey = FeeNameEnum.STAMP_DUTY.getName();
        StatementFeeDto stampDutyDto = feeMap.get(stampDutyKey);
        String stampDuty = (stampDutyDto == null ? null : stampDutyDto.getFee());

        String sfcTransactionLevyKey = FeeNameEnum.TRANSACTION_LEVY.getName();
        StatementFeeDto sfcTransactionLevyDto = feeMap.get(sfcTransactionLevyKey);
        String sfcTransactionLevy = (sfcTransactionLevyDto == null ? null : sfcTransactionLevyDto.getFee());

        String afrcTransactionLevyKey = FeeNameEnum.AFRC_TRANSACTION_LEVY.getName();
        StatementFeeDto afrcTransactionLevyDto = feeMap.get(afrcTransactionLevyKey);
        String afrcTransactionLevy = (afrcTransactionLevyDto == null ? null : afrcTransactionLevyDto.getFee());

        String hkexClearingKey = FeeNameEnum.HK_CLEARING_FEE.getName();
        StatementFeeDto hkexClearingFeeDto = feeMap.get(hkexClearingKey);
        String hkexClearingFee = (hkexClearingFeeDto == null ? null : hkexClearingFeeDto.getFee());

        String hkexTradingFeeKey = FeeNameEnum.TRADING_FEE.getName();
        StatementFeeDto hkexTradingFeeDto = feeMap.get(hkexTradingFeeKey);
        String hkexTradingFee = (hkexTradingFeeDto == null ? null : hkexTradingFeeDto.getFee());

        //是否强校验费用信息
        if (!generateStatementWhenFeeNull) {
//            if (Strings.isEmpty(stampDuty)) {
//                log.error("股票成交单据缺少stampDuty!,secFeeKey:{},参考编号:{},feeMap:{}"
//                        , stampDutyKey, statementUsOrderDto.getReferenceNo(), feeMap);
//                throw new BusinessException(STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND);
//            }
            if (Strings.isEmpty(sfcTransactionLevy)) {
                log.error("股票成交单据缺少sfcTransactionLevy!,sfcTransactionLevyKey:{},参考编号:{},feeMap:{}"
                        , sfcTransactionLevyKey, statementUsOrderDto.getReferenceNo(), feeMap);
                throw new BusinessException(STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND);
            }
            if (Strings.isEmpty(afrcTransactionLevy)) {
                log.error("股票成交单据缺少afrcTransactionLevy!,afrcTransactionLevyKey:{},参考编号:{},feeMap:{}"
                        , afrcTransactionLevyKey, statementUsOrderDto.getReferenceNo(), feeMap);
                throw new BusinessException(STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND);
            }
            if (Strings.isEmpty(hkexClearingFee)) {
                log.error("股票成交单据缺少hkexClearingFee!,hkexClearingKey:{},参考编号:{},feeMap:{}"
                        , hkexClearingKey, statementUsOrderDto.getReferenceNo(), feeMap);
                throw new BusinessException(STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND);
            }
            if (Strings.isEmpty(hkexTradingFee)) {
                log.error("股票成交单据缺少hkexTradingFee!,hkexTradingFeeKey:{},参考编号:{},feeMap:{}"
                        , hkexTradingFeeKey, statementUsOrderDto.getReferenceNo(), feeMap);
                throw new BusinessException(STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND);
            }
        }
        confirmedOrderDto.setStampDuty(StockStatementNumberFormatUtils.statementMoneyFormat(stampDuty, DEFAULT_FEE));
        confirmedOrderDto.setSfcTransactionLevy(StockStatementNumberFormatUtils.statementMoneyFormat(sfcTransactionLevy, STATEMENT_NULL_VALUE_PLACEHOLDER));
        confirmedOrderDto.setAfrcTransactionLevy(StockStatementNumberFormatUtils.statementMoneyFormat(afrcTransactionLevy, STATEMENT_NULL_VALUE_PLACEHOLDER));
        confirmedOrderDto.setHkexClearingFee(StockStatementNumberFormatUtils.statementMoneyFormat(hkexClearingFee, STATEMENT_NULL_VALUE_PLACEHOLDER));
        confirmedOrderDto.setHkexTradingFee(StockStatementNumberFormatUtils.statementMoneyFormat(hkexTradingFee, STATEMENT_NULL_VALUE_PLACEHOLDER));

        String netAmountString = statementUsOrderDto.getNetAmount();
        BigDecimal netAmount = Strings.isEmpty(netAmountString) ? BigDecimal.ZERO : new BigDecimal(netAmountString);
        confirmedOrderDto.setAmount(StockStatementNumberFormatUtils
                .statementMoneyFormat(netAmount, null));
    }

    /**
     * 查询持仓变动数据
     *
     * @param period
     * @param accountId
     * @return
     */
    private List<StockMonthlyStatementDataDto.HoldingChangeDto> queryHoldChangerDto(String period, String accountId, String docLang, StockStatementTradeDateInfo stockStatementTradeDateInfo) {

        String marketCode = MarketCodeEnum.US.getValue();
        String ttlMarketCode = MarketCodeEnum.US.getTtlValue();

        List<StockMonthlyStatementDataDto.HoldingChangeDto> holdingChangeDtos = new ArrayList<>();
        List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos = stockStatementDataService.queryTradeChangeDetailData(period, stockStatementTradeDateInfo
                , StatementTypeEnum.DAILY.getType(), accountId, StatementBusinessTypeEnum.getHoldChangeBusinessList(), ttlMarketCode);
        log.info("queryHoldChangerDto sources size:{}", statementTradeChangeDetailDtos.size());

        //重新构建公司行动类型（换股类型判断）
        stockStatementActionService.reBuildActionBusinessTypeData(statementTradeChangeDetailDtos);

        //补充股份变动数据
        List<StatementTradeChangeDetailDto> businessStockMovementData = stockStatementActionService.getBusinessStockMovementData(marketCode, accountId, period);
        statementTradeChangeDetailDtos.addAll(businessStockMovementData);

        //排序
        statementTradeChangeDetailDtos = StatementDataSortUtils.sortByDateAsc(dto -> DateUtil.stringToDate(dto.getTradeDate())
                , statementTradeChangeDetailDtos);

        statementTradeChangeDetailDtos.forEach(t -> {
            StockMonthlyStatementDataDto.HoldingChangeDto holdingChangeDto = new StockMonthlyStatementDataDto.HoldingChangeDto();
            // 无市场代码，用币种转市场
            String exchangeCode = MarketCodeEnum.getValueByTtlValue(t.getTtlMarketCode());
            if (StringUtils.isEmpty(exchangeCode)) {
                log.error("queryHoldChangerDto市场代码转换为空, 市场代码:{}, businessId:{},businessType:{}", t.getTtlMarketCode(), t.getBusinessId(), t.getBusinessType());
                throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "市场代码转换为空");
            }
            //股票基本信息
            fillStockBasicInfo(exchangeCode, t.getStockCode(), docLang, holdingChangeDto);

            //交易日
            holdingChangeDto.setTradeDate(dateFormat2EngStr(DateUtil.stringToDate(t.getTradeDate())));

            // 出账入账类型转换
            StatementInOutEnum statementInOutEnum = StatementInOutEnum.IN;
            BigDecimal qty = new BigDecimal(null == t.getQty() ? "0" : t.getQty());
            if (qty.compareTo(BigDecimal.ZERO) < 0) {
                statementInOutEnum = StatementInOutEnum.OUT;
            }
            holdingChangeDto.setQty(StatementNumberFormatUtils.statementQtyFormatFourNoZero(qty, "0"));

            String direction = I18nSupportEnum.CN_ZH.getName().equals(docLang) ? statementInOutEnum.getDesc()
                    : statementInOutEnum.getDescHk();

            holdingChangeDto.setDirection(direction);

            //description
            StatementBusinessTypeEnum statementBusinessTypeEnum = StatementBusinessTypeEnum.getByBusinessType(t.getBusinessType());
            if (null != statementBusinessTypeEnum) {
                String businessTypeDesc = I18nSupportEnum.CN_ZH.getName().equals(docLang) ? statementBusinessTypeEnum.getDesc()
                        : statementBusinessTypeEnum.getDescHk();

                String sourceStockNameEnglish =
                        stkInfoAgent.getStockNameAbbr(exchangeCode, t.getStockCode(), I18nSupportEnum.US_EN.getName());
                String sourceStockName = stkInfoAgent.getStockNameAbbr(exchangeCode, t.getStockCode(), docLang);

                businessTypeDesc = statementBusinessTypeEnum.getDescEn() + " " + businessTypeDesc;
                // Scrip Dividend 股票股息
                // Stock code Stock Name
                String desc = businessTypeDesc + StatementConstants.SHIFT_LINE + t.getStockCode() + " " + sourceStockNameEnglish + " " + sourceStockName;


                holdingChangeDto.setDescription(desc);
            }


            holdingChangeDtos.add(holdingChangeDto);
        });

        return holdingChangeDtos;
    }

    private List<StockMonthlyStatementDataDto.HoldingChangeDto> queryHkHoldChangerDto(String period, String accountId, String docLang, StockStatementTradeDateInfo stockStatementTradeDateInfo) {
        log.info("queryHkHoldChangerDto period:{},accountId:{}", period, accountId);
        String marketCode = MarketCodeEnum.HK.getValue();
        String ttlMarketCode = MarketCodeEnum.HK.getTtlValue();

        List<StockMonthlyStatementDataDto.HoldingChangeDto> holdingChangeDtos = new ArrayList<>();
        List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos = stockStatementDataService.queryTradeChangeDetailData(period, stockStatementTradeDateInfo
                , StatementTypeEnum.DAILY.getType(), accountId, StatementBusinessTypeEnum.getHoldChangeBusinessList(), ttlMarketCode);

        //重新构建公司行动类型（换股类型判断）
        stockStatementActionService.reBuildActionBusinessTypeData(statementTradeChangeDetailDtos);

        //处理供股数据，过滤EAL供股派发的数据，并且把供股退款数据重新赋值业务类型
        stockStatementActionService.dealHkRightsDividendData(statementTradeChangeDetailDtos, false);

        //获取公司行动中台回收数据
        List<StatementTradeChangeDetailDto> actionRecycleData = stockStatementActionService.getActionRecycleData(marketCode, accountId, period);
        statementTradeChangeDetailDtos.addAll(actionRecycleData);

        //IPO 中签数据
        List<StatementTradeChangeDetailDto> ipoAllotmentData = stockTradeManager.queryIpoAllottedInfo(accountId, period);
        statementTradeChangeDetailDtos.addAll(ipoAllotmentData);

        //排序
        statementTradeChangeDetailDtos = StatementDataSortUtils.sortByDateAsc(dto -> DateUtil.stringToDate(dto.getTradeDate())
                , statementTradeChangeDetailDtos);

        statementTradeChangeDetailDtos.forEach(t -> {
            StockMonthlyStatementDataDto.HoldingChangeDto holdingChangeDto = new StockMonthlyStatementDataDto.HoldingChangeDto();
            // 无市场代码，用币种转市场
            String exchangeCode = MarketCodeEnum.getValueByTtlValue(t.getTtlMarketCode());
            if (StringUtils.isEmpty(exchangeCode)) {
                log.error("queryHkHoldChangerDto市场代码转换为空, 市场代码:{}, businessId:{},businessType:{}", t.getTtlMarketCode(), t.getBusinessId(), t.getBusinessType());
                throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "市场代码转换为空");
            }
            //股票基本信息,ipo
            if(StatementBusinessTypeEnum.getHkIpoBusinessList().contains(t.getBusinessType())){
                fillIpoStockInfo(t, docLang, holdingChangeDto);
            }else{
                fillStockBasicInfo(exchangeCode, t.getStockCode(), docLang, holdingChangeDto);
            }

            //交易日
            holdingChangeDto.setTradeDate(dateFormat2EngStr(DateUtil.stringToDate(t.getTradeDate())));

            // 出账入账类型转换
            StatementInOutEnum statementInOutEnum = StatementInOutEnum.IN;
            BigDecimal qty = new BigDecimal(null == t.getQty() ? "0" : t.getQty());
            if (qty.compareTo(BigDecimal.ZERO) < 0) {
                statementInOutEnum = StatementInOutEnum.OUT;
            }
            holdingChangeDto.setQty(StatementNumberFormatUtils.statementQtyFormatFourNoZero(qty, "0"));

            String direction = I18nSupportEnum.CN_ZH.getName().equals(docLang) ? statementInOutEnum.getDesc()
                    : statementInOutEnum.getDescHk();

            holdingChangeDto.setDirection(direction);

            //description
            StatementBusinessTypeEnum statementBusinessTypeEnum = StatementBusinessTypeEnum.getByBusinessType(t.getBusinessType());
            if (null != statementBusinessTypeEnum) {
                String sourceStockName = "";
                String stockNameEnglish = "";
                if(StatementBusinessTypeEnum.getHkIpoBusinessList().contains(t.getBusinessType())){
                    sourceStockName= stkInfoAgent.getIpoStockNameAbbr(t.getStockNameCn(), t.getStockNameHk(), t.getStockNameHk(), docLang);
                    stockNameEnglish = stkInfoAgent.getIpoStockNameAbbr(t.getStockNameCn(), t.getStockNameHk(), t.getStockNameHk(), I18nSupportEnum.US_EN.getName());
                }else{
                    sourceStockName = stkInfoAgent.getStockNameAbbr(exchangeCode, t.getStockCode(), docLang);
                    stockNameEnglish = stkInfoAgent.getStockNameAbbr(exchangeCode, t.getStockCode(), I18nSupportEnum.US_EN.getName());
                }

                // 组装businessType业务描述
                String businessDesc = "";
                if(StatementBusinessTypeEnum.CBBCFR.equals(statementBusinessTypeEnum)){
                    businessDesc=stockStatementActionService.buildHkActionRecycleBusinessDesc(t, statementBusinessTypeEnum, docLang);
                }else{
                    businessDesc = stockStatementActionService.buildHkActionBusinessDesc(t, statementBusinessTypeEnum, docLang);
                }

                String desc = businessDesc + StatementConstants.SHIFT_LINE + t.getStockCode() + " " + stockNameEnglish + " " + sourceStockName;

                holdingChangeDto.setDescription(desc);
            }

            holdingChangeDtos.add(holdingChangeDto);
        });
        //补充客户的证券变动数据(税费)
        log.info("queryHkHoldChangerDto end");
        return holdingChangeDtos;
    }


    /**
     * 查询派息数据
     *
     * @param period
     * @param accountId
     * @return
     */
    private List<StockMonthlyStatementDataDto.DividendOrderDto> queryDividendOrderDto(String period, String bankUserId, String accountId, String docLang, StockStatementTradeDateInfo stockStatementTradeDateInfo) {

        String marketCode = MarketCodeEnum.HK.getValue();
        List<StockMonthlyStatementDataDto.DividendOrderDto> dividendOrderDtoList = new ArrayList<>();

        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);
        List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos = stockStatementDataService
                .queryTradeChangeDetailData(period, stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(), accountId,
                        StatementBusinessTypeEnum.getDividendBusinessList(), ttlMarketCode);
        log.info("queryDividendOrderDto sources size:{},userId:{},period:{}", statementTradeChangeDetailDtos.size(), bankUserId, period);

        //过滤公司行动资金入账的月结单数据
        statementTradeChangeDetailDtos = filterAndAttachActionCapital(marketCode, period, bankUserId, accountId, statementTradeChangeDetailDtos);
        log.info("queryDividendOrderDto filter size:{},userId:{},period:{}", statementTradeChangeDetailDtos.size(), bankUserId, period);


        //补充客户的证券变动数据(税费)
        List<StatementTradeChangeDetailDto> extraTradeChangeDetailDtos = extraTradeChangeDetailData(bankUserId, accountId, period, marketCode);
        log.info("queryDividendOrderDto extra size:{},userId:{},period:{}", extraTradeChangeDetailDtos.size(), bankUserId, period);
        statementTradeChangeDetailDtos.addAll(extraTradeChangeDetailDtos);

        //按照交易日升序排列
        statementTradeChangeDetailDtos = StatementDataSortUtils.sortByDateAsc(
                statementTradeChangeDetailDto -> DateUtil.stringToDate(statementTradeChangeDetailDto.getTradeDate())
                , statementTradeChangeDetailDtos);


        statementTradeChangeDetailDtos.forEach(t -> {
            StockMonthlyStatementDataDto.DividendOrderDto dividendOrderDto = new StockMonthlyStatementDataDto.DividendOrderDto();
            // 无市场代码，用币种转市场
            String exchangeCode = MarketCodeEnum.getValueByTtlValue(t.getTtlMarketCode());
            if (StringUtils.isEmpty(exchangeCode)) {
                log.error("queryDividendOrderDto市场代码转换为空, 市场代码:{}, businessId:{},businessType:{}", t.getTtlMarketCode(), t.getBusinessId(), t.getBusinessType());
                throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "市场代码转换为空");
            }

            //股票基本信息
            fillStockBasicInfo(exchangeCode, t.getStockCode(), docLang, dividendOrderDto);

            String qtyString = t.getQty();
            if (Strings.isEmpty(qtyString)) {
                dividendOrderDto.setQty(null);
            } else {
                dividendOrderDto.setQty(StatementNumberFormatUtils.statementQtyFormatFourNoZero(new BigDecimal(qtyString), null));
            }
            //格式化
            dividendOrderDto.setTradeDate(dateFormat2EngStr(DateUtil.stringToDate(t.getTradeDate())));

            dividendOrderDto.setCurrency(Strings.isEmpty(t.getCurrency()) ? STATEMENT_NULL_VALUE_PLACEHOLDER : t.getCurrency());

            // 出账入账类型转换
            StatementBusinessTypeEnum statementBusinessTypeEnum = StatementBusinessTypeEnum.getByBusinessType(t.getBusinessType());
            if (null != statementBusinessTypeEnum) {
                //供股和回购特殊处理
                if (StatementBusinessTypeEnum.ESB.equals(statementBusinessTypeEnum) || StatementBusinessTypeEnum.TPA.equals(statementBusinessTypeEnum)) {
                    //设置源股票代码
                    t.setSourceStockCode(t.getStockCode());
                    //设置数量为空
                    dividendOrderDto.setQty(null);
                    log.info("queryDividendOrderDto供股和回购特殊处理 type:{},qty:{},amount:{}", statementBusinessTypeEnum.getBusinessType(), t.getQty(), t.getAmount());
                }
                //金额处理
                String amount = StockStatementNumberFormatUtils.statementMoneyFormat(t.getAmount(), null);
                if (statementBusinessTypeEnum.getDirectionEnum().equals(StatementInOutEnum.OUT) && !StringUtils.isEmpty(amount)) {
                    //先取绝对值，然后金额后面加负号
                    BigDecimal amt = new BigDecimal(t.getAmount());
                    amount = StockStatementNumberFormatUtils.statementMoneyFormat(amt.abs().toPlainString(), null) + "-";
                    log.info("queryDividendOrderDto负数处理 type:{},,format amount:{},qty:{}", statementBusinessTypeEnum.getBusinessType(), amount, dividendOrderDto.getQty());
                }
                dividendOrderDto.setAmount(amount);

                String direction = I18nSupportEnum.CN_ZH.getName().equals(docLang) ? statementBusinessTypeEnum.getDesc()
                        : statementBusinessTypeEnum.getDescHk();
                direction = statementBusinessTypeEnum.getDescEn() + " " + direction;

                //股票名称多语言转换
                String sourceStockCode = t.getSourceStockCode();
                log.info("queryDividendOrderDto sourceStockCode:{},type:{},userId:{}", sourceStockCode, statementBusinessTypeEnum.getBusinessType(), bankUserId);
                String sourceStockName = stkInfoAgent.getStockNameAbbr(exchangeCode, sourceStockCode, docLang);
                String stockNameEnglish = stkInfoAgent.getStockNameAbbr(exchangeCode, sourceStockCode, I18nSupportEnum.US_EN.getName());

                // Scrip Dividend 股票股息
                // Stock code Stock Name
                String desc = null;

                String remark = t.getRemark();
                if (!Strings.isEmpty(remark)) {
                    remark = "Remark:" + remark;
                }
                if (StatementBusinessTypeEnum.CDV.equals(statementBusinessTypeEnum) && CASH_DIVIDED_WITH_SCRIP_OPTION_REMARK.equalsIgnoreCase(t.getRemark())) {
                    String type = I18nSupportEnum.CN_ZH.getName().equals(docLang) ? CASH_DIVIDED_WITH_SCRIP_OPTION_REMARK_ZH : CASH_DIVIDED_WITH_SCRIP_OPTION_REMARK_HK;
                    desc = type + StatementConstants.SHIFT_LINE + sourceStockCode + " " + stockNameEnglish + " " + sourceStockName;
                    desc = desc + StatementConstants.SHIFT_LINE + remark;
                    log.info("desc:{}", desc);
                } else {
                    desc = direction + StatementConstants.SHIFT_LINE + sourceStockCode
                            + " " + stockNameEnglish + " " + sourceStockName;
                    if (!Strings.isEmpty(remark)) {
                        desc = desc + StatementConstants.SHIFT_LINE + remark;
                    }
                }
                dividendOrderDto.setDescription(desc);
            }

            dividendOrderDtoList.add(dividendOrderDto);
        });

        return dividendOrderDtoList;
    }


    private List<StockMonthlyStatementDataDto.DividendOrderDto> queryHkDividendOrderDto(String period, String bankUserId, String accountId, String docLang, StockStatementTradeDateInfo stockStatementTradeDateInfo) {

        String marketCode = MarketCodeEnum.HK.getValue();
        List<StockMonthlyStatementDataDto.DividendOrderDto> dividendOrderDtoList = new ArrayList<>();

        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);
        List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos = stockStatementDataService
                .queryTradeChangeDetailData(period, stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(), accountId,
                        StatementBusinessTypeEnum.getHkDividendBusinessList(), ttlMarketCode);
        log.info("queryHkDividendOrderDto sources size:{},userId:{},period:{}", statementTradeChangeDetailDtos.size(), bankUserId, period);

        //过滤公司行动资金入账的月结单数据
        statementTradeChangeDetailDtos = filterAndAttachActionCapital(marketCode, period, bankUserId, accountId, statementTradeChangeDetailDtos);
        log.info("queryHkDividendOrderDto filter size:{},userId:{},period:{}", statementTradeChangeDetailDtos.size(), bankUserId, period);

        //处理供股数据，过滤EAL供股派发的数据，并且把供股退款数据重新赋值业务类型
        stockStatementActionService.dealHkRightsDividendData(statementTradeChangeDetailDtos, true);

        //拆分港股现金和税费数据
        stockStatementActionService.splitHkCashAndFee(statementTradeChangeDetailDtos);

        //查询IPO扣款数据和退款数据
        List<StatementTradeChangeDetailDto> ipoApplyInfoList = stockTradeManager.queryIpoApplyInfo(accountId, period);
        statementTradeChangeDetailDtos.addAll(ipoApplyInfoList);

        //按照特定的顺序升序排列
        statementTradeChangeDetailDtos = sortHkDividendList(statementTradeChangeDetailDtos);


        statementTradeChangeDetailDtos.forEach(t -> {
            log.info("queryHkDividendOrderDto StatementTradeChangeDetailDto:{}", t);
            StockMonthlyStatementDataDto.DividendOrderDto dividendOrderDto = new StockMonthlyStatementDataDto.DividendOrderDto();
            // 无市场代码，用币种转市场
            String exchangeCode = MarketCodeEnum.getValueByTtlValue(t.getTtlMarketCode());
            if (StringUtils.isEmpty(exchangeCode)) {
                log.error("queryHkDividendOrderDto市场代码转换为空, 市场代码:{}, businessId:{},businessType:{}", t.getTtlMarketCode(), t.getBusinessId(), t.getBusinessType());
                throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "市场代码转换为空");
            }

            if(StringUtils.isEmpty(t.getStockCode())){
                log.error("queryHkDividendOrderDto股票代码为空,  StatementTradeChangeDetailDto:{}", t);
                throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "股票代码为空");
            }
            //股票基本信息
            if(StatementBusinessTypeEnum.getHkIpoBusinessList().contains(t.getBusinessType())){
                fillIpoStockInfo(t, docLang, dividendOrderDto);
            }else{
                fillStockBasicInfo(exchangeCode, t.getStockCode(), docLang, dividendOrderDto);
            }

            String qtyString = t.getQty();
            if (Strings.isEmpty(qtyString)) {
                dividendOrderDto.setQty(null);
            } else {
                dividendOrderDto.setQty(StatementNumberFormatUtils.statementQtyFormatFourNoZero(new BigDecimal(qtyString), null));
            }
            //格式化
            dividendOrderDto.setTradeDate(dateFormat2EngStr(DateUtil.stringToDate(t.getTradeDate())));

            dividendOrderDto.setCurrency(Strings.isEmpty(t.getCurrency()) ? STATEMENT_NULL_VALUE_PLACEHOLDER : t.getCurrency());

            // 出账入账类型转换
            StatementBusinessTypeEnum statementBusinessTypeEnum = StatementBusinessTypeEnum.getByBusinessType(t.getBusinessType());
            if (null != statementBusinessTypeEnum) {
                //供股和回购特殊处理
                if (StatementBusinessTypeEnum.ESB.equals(statementBusinessTypeEnum)
                        || StatementBusinessTypeEnum.ESB_F.equals(statementBusinessTypeEnum)
                        || StatementBusinessTypeEnum.TPA.equals(statementBusinessTypeEnum)
                            || StatementBusinessTypeEnum.CBBCED.equals(statementBusinessTypeEnum)) {
                    //设置源股票代码
                    t.setSourceStockCode(t.getStockCode());
                    //设置数量为空
                    dividendOrderDto.setQty(null);
                    log.info("queryHkDividendOrderDto供股和回购特殊处理 type:{},qty:{},amount:{}", statementBusinessTypeEnum.getBusinessType(), t.getQty(), t.getAmount());
                }
                //金额处理
                String amount = StockStatementNumberFormatUtils.statementMoneyFormat(t.getAmount(), null);
                if (statementBusinessTypeEnum.getDirectionEnum().equals(StatementInOutEnum.OUT) && !StringUtils.isEmpty(amount)) {
                    //先取绝对值，然后金额后面加负号
                    BigDecimal amt = new BigDecimal(t.getAmount());
                    amount = StockStatementNumberFormatUtils.statementMoneyFormat(amt.abs().toPlainString(), null) + "-";
                }
                dividendOrderDto.setAmount(amount);

                // 组装businessType业务描述
                String businessDesc = stockStatementActionService.buildHkActionBusinessDesc(t, statementBusinessTypeEnum, docLang);

                //股票名称多语言转换
                String sourceStockCode = t.getSourceStockCode();
                if(StringUtils.isEmpty(sourceStockCode)){
                    sourceStockCode = t.getStockCode();
                }
                String sourceStockName = "";
                String stockNameEnglish = "";
                if(StatementBusinessTypeEnum.getHkIpoBusinessList().contains(t.getBusinessType())){
                    sourceStockName= stkInfoAgent.getIpoStockNameAbbr(t.getStockNameCn(), t.getStockNameHk(), t.getStockNameHk(), docLang);
                    stockNameEnglish = stkInfoAgent.getIpoStockNameAbbr(t.getStockNameCn(), t.getStockNameHk(), t.getStockNameHk(), I18nSupportEnum.US_EN.getName());
                }else{
                    sourceStockName = stkInfoAgent.getStockNameAbbr(exchangeCode, sourceStockCode, docLang);
                    stockNameEnglish = stkInfoAgent.getStockNameAbbr(exchangeCode, sourceStockCode, I18nSupportEnum.US_EN.getName());
                }

                //只有CDV类型需要拼接remark
                String desc = businessDesc + StatementConstants.SHIFT_LINE + sourceStockCode
                        + " " + stockNameEnglish + " " + sourceStockName;
                if (StatementBusinessTypeEnum.CDV.equals(statementBusinessTypeEnum) && !Strings.isEmpty(t.getRemark())) {
                    String remark = "Remark:" + t.getRemark();
                    desc = desc + StatementConstants.SHIFT_LINE + remark;
                }
                dividendOrderDto.setDescription(desc);
            }

            dividendOrderDtoList.add(dividendOrderDto);
        });

        log.info("queryHkDividendOrderDto dividendOrderDtoList size:{},userId:{},period:{}", dividendOrderDtoList.size(), bankUserId, period);
        return dividendOrderDtoList;
    }

    /**
     * 排序港股分红数据
     *
     * @param statementTradeChangeDetailDtos
     * @return
     */
    private List<StatementTradeChangeDetailDto> sortHkDividendList(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos) {
        // 定义排序函数
        Function<StatementTradeChangeDetailDto, Comparable> tradeDateFunc = StatementTradeChangeDetailDto::getTradeDate;
        //Function<StatementTradeChangeDetailDto, Comparable> businessIdFunc = StatementTradeChangeDetailDto::getReferenceNo;
        //Function<StatementTradeChangeDetailDto, Comparable> businessTypeFunc = StatementTradeChangeDetailDto::getBusinessType;

        return StatementDataSortUtils.sortByMultipleFields(statementTradeChangeDetailDtos, tradeDateFunc);
    }
    /**
     * 处理公司行动资金入账
     *
     * @param period
     * @param bankUserId
     * @param accountId
     * @param statementTradeChangeDetailDtos
     * @return
     */
    private List<StatementTradeChangeDetailDto> filterAndAttachActionCapital(String marketCode, String period, String bankUserId, String accountId,
                                                                             List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos) {
        return stockStatementActionService.filterAndAttachActionCapitalForPeriod(marketCode, period, bankUserId, accountId, statementTradeChangeDetailDtos);
    }

    /**
     * 其他客户的证券变动数据
     *
     * @param bankUserId
     * @param accountId
     * @return
     */
    private List<StatementTradeChangeDetailDto> extraTradeChangeDetailData(String bankUserId, String accountId, String period, String marketCode) {
        List<StatementTradeChangeDetailDto> extraTradeChangeDetailDtos = new ArrayList<>();
        //增加税费变动明细
        List<StatementTradeChangeDetailDto> taxTradeChangeDetailDataList =
                stockStatementActionService.getTaxTradeChangeDetailData(marketCode, accountId, period);
        extraTradeChangeDetailDtos.addAll(taxTradeChangeDetailDataList);

        //增加转仓手续费变动明细
        List<StatementTradeChangeDetailDto> transferFeeTradeChangeDetailData =
                stockStatementActionService.getTransferFeeTradeChangeDetailData(bankUserId, accountId, period);
        extraTradeChangeDetailDtos.addAll(transferFeeTradeChangeDetailData);
        //补充 股息追缴和补偿
        List<StatementTradeChangeDetailDto> dividendPressAndRebateData = stockStatementActionService.getDividendPressAndRebateDataByAccountId(marketCode, accountId, period);
        extraTradeChangeDetailDtos.addAll(dividendPressAndRebateData);

        //补充资金转账数据
        List<StatementTradeChangeDetailDto> businessCapitalData = stockStatementActionService.getBusinessCapitalData(marketCode, accountId, period);
        extraTradeChangeDetailDtos.addAll(businessCapitalData);

        return extraTradeChangeDetailDtos;
    }


    /**
     * 筛选出换股的
     *
     * @param statementTradeChangeDetailDtos
     * @return
     */
    private List<String> buildChangeStockList(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos) {
        //返回参考编号列表
        List<String> referenceNoList = new ArrayList<>();
        // 过滤出合拆股类型的数据，并按照 参考编号进行分组
        Map<String, List<StatementTradeChangeDetailDto>> listMap = statementTradeChangeDetailDtos.stream().filter(t -> {
            StatementBusinessTypeEnum statementBusinessTypeEnum = StatementBusinessTypeEnum.getByBusinessType(t.getBusinessType());
            return StatementBusinessTypeEnum.SSP.equals(statementBusinessTypeEnum) || StatementBusinessTypeEnum.SCS.equals(statementBusinessTypeEnum);
        }).collect(Collectors.groupingBy(StatementTradeChangeDetailDto::getReferenceNo));

        //找到一对合拆类型里，股票代码不一样的，判定为换股类型
        for (Map.Entry<String, List<StatementTradeChangeDetailDto>> entry : listMap.entrySet()) {
            List<StatementTradeChangeDetailDto> changeDetailList = entry.getValue();
            if (changeDetailList.size() == 2) {
                String stockCode1 = changeDetailList.get(0).getStockCode();
                String stockCode2 = changeDetailList.get(1).getStockCode();
                if (!stockCode1.equals(stockCode2)) {
                    referenceNoList.add(entry.getKey());
                    log.info("换股 referenceNo：{}", entry.getKey());
                }
            }
        }
        return referenceNoList;
    }


    /**
     * 用户期末总市值
     *
     * @param tdFundMonthlyStatementInfo
     * @param targetCurrency
     * @return
     */
    @Override
    public Pair<BigDecimal, BigDecimal> getUserPeriodEndTotalMarketAndProfit(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement statementRecord, CurrencyEnum targetCurrency, String accountId, String marketCode) {
        String period = tdFundMonthlyStatementInfo.getPeriod();
        String businessType = tdFundMonthlyStatementInfo.getBusinessType();

        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo
                = stockStatementTradeDateInfo.getMarketCodeAndTradeDateInfoMap().get(marketCode);

        BigDecimal sumAsset = BigDecimal.ZERO;
        BigDecimal sumHoldingProfit = BigDecimal.ZERO;

        //1.查询上月最后一个交易日
        String yearMonth = statementRecord.getPeriod();

        Date lastMonthTradeDate = DateUtil.parse(marketStatementTradeDateInfo.getPeriodEndCoreTradeDate(), DateUtil.FORMATDAY);
        String endTradeDate = marketStatementTradeDateInfo.getEndTradeDate();
        log.info("yearMonth:{},lastMonthTradeDate:{} endTradeDate:{}", yearMonth, lastMonthTradeDate, endTradeDate);

        //2.查询资产列表
        List<StatementAccountAssetDto> accountAssetDtoList = queryUserPeriodEndAssetDtoList(stockStatementTradeDateInfo.getPeriod(), accountId, marketCode);
        for (StatementAccountAssetDto accountAssetDto : accountAssetDtoList) {
            if (StringUtils.isBlank(accountAssetDto.getMarketValue())) {
                log.info("过滤市值为空数据，stockCode:{}", accountAssetDto.getStockCode());
                continue;
            }
            BigDecimal marketValue = new BigDecimal(accountAssetDto.getMarketValue());
            if (marketValue.compareTo(BigDecimal.ZERO) == 0) {
                log.info("过滤市值为0数据，stockCode:{}", accountAssetDto.getStockCode());
                continue;
            }
            //3.币种转换及汇总
            BigDecimal exchangeMarketValue = exchange(marketValue, accountAssetDto.getCurrency(), period, businessType);
            //与等值港币计算规则保持一致,先round down后累加
            BigDecimal marketValueHkd = exchangeMarketValue.setScale(2, RoundingMode.DOWN);

            sumAsset = sumAsset.add(marketValueHkd);

            //汇总持仓盈亏
            BigDecimal holdingProfit = calHoldingProfit(statementRecord.getBankUserId(), accountId, endTradeDate, accountAssetDto.getStockCode());
            //与等值港币计算规则保持一致,先round down后累加
            BigDecimal exchangeHoldingProfit = exchange(holdingProfit, accountAssetDto.getCurrency(), period, businessType)
                    .setScale(2, RoundingMode.DOWN);
            sumHoldingProfit = sumHoldingProfit.add(exchangeHoldingProfit);

        }
        log.info("查询到股票证券资产摘要数据{}条，累计市值及盈亏:{},{},accountId:{},period:{}"
                , accountAssetDtoList.size(), sumAsset.toPlainString(), sumHoldingProfit.toPlainString(), accountId, period);

        //重要!:总金额无需加上可用资金 ******** byfyf
        return Pair.of(sumAsset, sumHoldingProfit);
    }


    /**
     * 查询持仓盈亏
     *
     * @param userId
     * @param accountId
     * @param tradeDateStr
     * @param stockCode
     * @return
     */
    private BigDecimal calHoldingProfit(String userId, String accountId, String tradeDateStr, String stockCode) {
        Date tradeDate = DateUtil.stringToDate(tradeDateStr);
        StkHoldingHistory stkHoldingHistory = stockTradeManager.queryHoldingHistoryByStatementCheck(accountId, tradeDate, stockCode);
        if (null == stkHoldingHistory) {
            log.info("未查询到历史持仓盈亏数据，默认为0，userId：{}，accountId:{},tradeDate:{}，stockCode:{}", userId, accountId, tradeDate, stockCode);
            return BigDecimal.ZERO;
        }
        return stkHoldingHistory.getProfit();
    }

    /**
     * 查询用户期末的现金摘要
     *
     * @param period
     * @param accountId
     * @return
     */
    private List<StatementCashSummaryDto> queryUserPeriodEndCashDtoList(String period, String accountId) {

        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);


        List<StatementCashSummaryDto> statementCashSummaryDtoList = stockStatementDataService
                .queryCashSummaryData(stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(), accountId);
        if (!CollectionUtils.isEmpty(statementCashSummaryDtoList)) {
            log.info("从ttl日结单数据里查询到用户现金摘要数据{}条", statementCashSummaryDtoList.size());
            return statementCashSummaryDtoList;
        }

        statementCashSummaryDtoList = stockStatementDataService.queryCashSummaryData(stockStatementTradeDateInfo, StatementTypeEnum.MONTHLY.getType(), accountId);
        log.info("从ttl月结单数据里查询到用户现金摘要数据{}条", statementCashSummaryDtoList.size());
        if (CollectionUtils.isEmpty(statementCashSummaryDtoList)) {
            return new ArrayList<>(0);
        }

        return statementCashSummaryDtoList;
    }


    /**
     * 根据汇率转换金额
     *
     * @param amount
     * @param sourceCurrency
     * @param period
     * @param businessType
     * @return
     */
    private BigDecimal exchange(BigDecimal amount, String sourceCurrency, String period, String businessType) {
        BigDecimal rate = exchangeRateInfoService.getExchangeHkdRate(sourceCurrency, period, businessType);
        log.info("StkRateInfoHisResp sourceCurrency:{},period:{},businessType:{},exchange rate:{}", sourceCurrency, period, businessType, rate);
        return amount.multiply(rate);
    }


    /**
     * 判断用户本期该业务是否需要生成月结单
     *
     * @param tdFundMonthlyStatementInfo
     * @param userId
     * @param accountId
     * @return
     */
    @Override
    public boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, String userId, String accountId, String marketCode) {
        String period = tdFundMonthlyStatementInfo.getPeriod();
        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);

        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);

        //1.判断是否有成交记录
        List<StatementUsOrderDto> statementUsOrderDtos = stockStatementDataService
                .queryUsOrderData(stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(), accountId, ttlMarketCode);
        if (!CollectionUtils.isEmpty(statementUsOrderDtos)) {
            log.info("isUserNeedGenerateStatement有成交记录，accountId:{}", accountId);
            return true;
        }

        //2.判断有没有资产信息
        if (this.checkAsset(period, accountId, marketCode)) {
            log.info("isUserNeedGenerateStatement有资产信息，accountId:{}", accountId);
            return true;
        }

        //3.判断是否有交易变动
        List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos = stockStatementDataService
                .queryTradeChangeDetailData(period, stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(),
                        accountId, StatementBusinessTypeEnum.getIsNeedGenerateBusinessList(), ttlMarketCode);
        if (!CollectionUtils.isEmpty(statementTradeChangeDetailDtos)) {
            //如果有交易变动数据，需要先判断是否有派息数据，且派息的资金派发情况需要满足生成结单条件
            List<StatementTradeChangeDetailDto> capitalList = statementTradeChangeDetailDtos.stream()
                    .filter(t -> StatementBusinessTypeEnum.getActionCapitalBusinessList().contains(t.getBusinessType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(capitalList)) {
                log.info("isUserNeedGenerateStatement有派息数据，但不是派息的资金派发情况需要满足生成结单条件，accountId:{}", accountId);
                return true;
            } else {
                //如果有派息数据，需要判断派息的资金派发情况是否满足生成结单条件
                if (checkAndSaveCapitalTimeInPeriod(capitalList, accountId, userId, period)) {
                    log.info("isUserNeedGenerateStatement派息的资金派发情况满足生成结单条件，accountId:{}", accountId);
                    return true;
                }
            }
        }


        //4.判断是否有公司行动小数股订单数据
        ActionFractionOrderResp actionFractionOrderResp = actionFractionOrderQuery(marketCode, accountId, userId, period);
        if (!CollectionUtils.isEmpty(actionFractionOrderResp.getOrderList())) {
            log.info("isUserNeedGenerateStatement有公司行动小数股订单数据，accountId:{}", accountId);
            return true;
        }

        //5.判断是否有公司行动当月派息入账记录
        int statementActionPendingCnt = statementActionPendingDataService.countPendingListForPeriod(marketCode, accountId, period);
        if (statementActionPendingCnt > 0) {
            log.info("isUserNeedGenerateStatement有公司行动当月派息入账记录，accountId:{}", accountId);
            return true;
        }
        //6.待处理的月结单公司行动ID
        List<TdStatementActionPendingData> statementActionPendingDatas = statementActionPendingDataService.queryPendingList(marketCode, accountId,
                StatementActionPendingStatusEnum.UN_PROCESSED.getValue());
        Set<String> statementPendingReferenceNos = statementActionPendingDatas.stream().map(pendingData ->
                pendingData.getActionId()).collect(Collectors.toSet());
        if (!statementPendingReferenceNos.isEmpty()) {
            //8.查询公司行动资金入账时间
            //该期月结单的公司行动资金入账的次数
            long capTimeInPeriodCnt = countCapitalTimeInPeriod(accountId, period, statementPendingReferenceNos);
            if (capTimeInPeriodCnt > 0) {
                log.info("isUserNeedGenerateStatement派息的资金入账情况满足生成结单条件，accountId:{}", accountId);
                return true;
            }
        }

        //7.判断是否有公司行动金融税记录(美股独有)
        if (MarketCodeEnum.US.getValue().equals(marketCode)) {
            String startDate = MonthlyUtils.getMonthFirstDay(period);
            String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;
            //查询税费（ADR和金融税）
            List<TaxQueryDTO> taxQueryDTOs = tradeRemoteService.queryTaxList(accountId, startDate, endDate);
            if (!taxQueryDTOs.isEmpty()) {
                log.info("isUserNeedGenerateStatement有公司行动金融税记录，accountId:{}", accountId);
                return true;
            }
            //8.查询是否有转仓收费记录
            List<TransferDetailResp> transferDetailList = stockStatementActionService.queryTransferDetailList(accountId, period);
            if (!CollectionUtils.isEmpty(transferDetailList)) {
                log.info("isUserNeedGenerateStatement有转仓收费记录，accountId:{}", accountId);
                return true;
            }

            //9.查询是否有股息追缴扣款
            List<StatementTradeChangeDetailDto> DividendPressAndRebateDataList = stockStatementActionService.getDividendPressAndRebateDataByAccountId(marketCode, accountId, period);
            if (!ObjectUtils.isEmpty(DividendPressAndRebateDataList)) {
                log.info("isUserNeedGenerateStatement有股息追缴扣款，accountId:{}", accountId);
                return true;
            }

            //10.查询是否有资金转账数据
            List<StatementTradeChangeDetailDto> businessCapitalData = stockStatementActionService.getBusinessCapitalData(marketCode, accountId, period);
            if (!ObjectUtils.isEmpty(businessCapitalData)) {
                log.info("isUserNeedGenerateStatement有资金转账数据，accountId:{}", accountId);
                return true;
            }

            //11.查询是否有股票变动数据
            List<StatementTradeChangeDetailDto> businessStockMovementData = stockStatementActionService.getBusinessStockMovementData(marketCode, accountId, period);
            if (!ObjectUtils.isEmpty(businessStockMovementData)) {
                log.info("isUserNeedGenerateStatement有股票变动数据，accountId:{}", accountId);
                return true;
            }
        }


        if (MarketCodeEnum.HK.getValue().equals(marketCode)) {
            //12.是否有IPO分配记录
            List<StatementTradeChangeDetailDto> ipoAllotmentList = stockTradeManager.queryIpoAllottedInfo(accountId, period);
            if (!ObjectUtils.isEmpty(ipoAllotmentList)) {
                log.info("isUserNeedGenerateStatement有IPO分配记录，accountId:{}", accountId);
                return true;
            }
        }

        //所有条件都不满足，则不生成结单
        log.info("用户本月无需生成股票月结单! period:{},bankUserId:{},accountId:{}", period, userId, accountId);
        return false;
    }

    /**
     * 判断该期月结单的公司行动资金入账的次数
     *
     * @param accountId
     * @param period
     * @param statementPendingReferenceNos
     * @return
     */
    private long countCapitalTimeInPeriod(String accountId, String period, Set<String> statementPendingReferenceNos) {
        ActionCapitalInfoQueryReq queryReq = new ActionCapitalInfoQueryReq();
        queryReq.setAccountId(accountId);
        queryReq.setReferenceNoSet(statementPendingReferenceNos);
        ActionCapitalInfoResp actionCapitalInfoResp = tradedataRemoteService.actionCapitalInfoQuery(queryReq);
        //该期月结单的公司行动资金入账的次数
        long capTimeInPeriodCnt = actionCapitalInfoResp.getDetailList().stream()
                .filter(capInfo -> capInfo.getCapitalTime() != null
                        && period.equals(DateUtil.format(capInfo.getCapitalTime(), StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT)))
                .count();

        return capTimeInPeriodCnt;

    }

    /**
     * 判断该期月结单的公司行动资金入账的次数，如果获取不到资金入账时间或者资金入账时间不是当前结单月份,则剔除掉该数据，放到pengding表
     *
     * @param capitalList
     * @param accountId
     * @param bankUserId
     * @param period
     * @return
     */
    private boolean checkAndSaveCapitalTimeInPeriod(List<StatementTradeChangeDetailDto> capitalList, String accountId, String bankUserId, String period) {
        Set<String> referenceNos = capitalList.stream()
                .map(StatementTradeChangeDetailDto::getReferenceNo)
                .collect(Collectors.toSet());

        ActionCapitalInfoQueryReq queryReq = new ActionCapitalInfoQueryReq();
        queryReq.setAccountId(accountId);
        queryReq.setReferenceNoSet(referenceNos);
        ActionCapitalInfoResp actionCapitalInfoResp = tradedataRemoteService.actionCapitalInfoQuery(queryReq);

        // 过滤出资金时间在指定周期内的记录
        List<ActionCapitalInfoDTO> validCapitalInfos = actionCapitalInfoResp.getDetailList().stream()
                .filter(capInfo -> capInfo.getCapitalTime() != null
                        && period.equals(DateUtil.format(capInfo.getCapitalTime(), StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT)))
                .collect(Collectors.toList());

        // 如果存在符合条件的记录，直接返回 true
        if (!validCapitalInfos.isEmpty()) {
            return true;
        }

        // 将资金信息转换为 Map，方便快速查找
        Map<String, ActionCapitalInfoDTO> actionCapitalMap = actionCapitalInfoResp.getDetailList().stream()
                .collect(Collectors.toMap(ActionCapitalInfoDTO::getReferenceNo, Function.identity()));

        List<StatementTradeChangeDetailDto> pendingDetails = new ArrayList<>();

        for (StatementTradeChangeDetailDto changeDetailDto : capitalList) {
            ActionCapitalInfoDTO capitalInfoDTO = actionCapitalMap.get(changeDetailDto.getReferenceNo());

            // 如果找不到对应的资金信息，或者资金时间不符合要求，则添加到待处理列表
            if (capitalInfoDTO == null || (capitalInfoDTO.getCapitalTime() == null
                    && !period.equals(DateUtil.format(capitalInfoDTO.getCapitalTime(), StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT)))) {
                pendingDetails.add(changeDetailDto);
            }
        }

        // 批量处理待处理的记录
        for (StatementTradeChangeDetailDto pendingDetail : pendingDetails) {
            statementActionPendingDataService.create(pendingDetail.getStatementId(),
                    bankUserId, accountId, pendingDetail.getReferenceNo(), pendingDetail.getBusinessType(), period, pendingDetail.getTtlMarketCode(), true);
        }

        return false;
    }

    /**
     * 判断用户是否需要生成结单
     *
     * @param tdFundMonthlyStatementInfo
     * @param userInvestClientInfoDto
     * @return
     */
    @Override
    public boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , UserInvestClientInfoDto userInvestClientInfoDto, String marketCode) {

        String bankUserId = userInvestClientInfoDto.getBankUserId();
        String accountId = userInvestClientInfoDto.getAccountId();
        return isUserNeedGenerateStatement(tdFundMonthlyStatementInfo, bankUserId, accountId, marketCode);
    }

    /**
     * 判断资产信息
     *
     * @param period
     * @param accountId
     * @return
     */
    private Boolean checkAsset(String period, String accountId, String marketCode) {
        String lastPeriod = calcLastPeriod(period);

        //期初
        List<StatementAccountAssetDto> periodStartAssetDtoList = queryUserPeriodEndAssetDtoList(lastPeriod, accountId, marketCode);
        for (StatementAccountAssetDto accountAssetDto : periodStartAssetDtoList) {
            BigDecimal closingBalance = StringUtils.isEmpty(accountAssetDto.getClosingSettleHolding()) ? BigDecimal.ZERO : new BigDecimal(accountAssetDto.getClosingSettleHolding());
            //4.只要期初或者期末资产不为0,则需要发送月结单
            if (closingBalance.compareTo(BigDecimal.ZERO) != 0) {
                log.info("账户{}本期({})结单存在证券资产,需要生成结单文件.accountAssetDto:{}", accountId, period, accountAssetDto);
                return true;
            }
        }
        //期末
        List<StatementAccountAssetDto> periodEndAssetDtoList = queryUserPeriodEndAssetDtoList(period, accountId, marketCode);
        for (StatementAccountAssetDto accountAssetDto : periodEndAssetDtoList) {
            BigDecimal closingBalance = StringUtils.isEmpty(accountAssetDto.getClosingSettleHolding()) ? BigDecimal.ZERO : new BigDecimal(accountAssetDto.getClosingSettleHolding());
            //4.只要期初或者期末资产不为0,则需要发送月结单
            if (closingBalance.compareTo(BigDecimal.ZERO) != 0) {
                log.info("账户{}本期({})结单存在证券资产,需要生成结单文件.accountAssetDto:{}", accountId, period, accountAssetDto);
                return true;
            }
        }

        return false;
    }

    /**
     * 校验现金
     *
     * @param period
     * @param accountId
     * @return
     */
    private Boolean checkCash(String period, String accountId) {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = getStatementTradeDateInfo(period);
        List<StatementCashSummaryDto> statementCashSummaryDtos = stockStatementDataService.queryCashSummaryData(stockStatementTradeDateInfo, StatementTypeEnum.MONTHLY.getType(), accountId);
        if (!CollectionUtils.isEmpty(statementCashSummaryDtos)) {
            for (StatementCashSummaryDto statementCashSummaryDto : statementCashSummaryDtos) {
                BigDecimal cash = null == statementCashSummaryDto.getWithdrawAmount() ? BigDecimal.ZERO : new BigDecimal(statementCashSummaryDto.getWithdrawAmount());
                if (cash.compareTo(BigDecimal.ZERO) != 0) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 本期月结单全部处理完毕，包括推送。
     * 留个口子，给业务服务清除缓存等信息
     *
     * @param tdFundMonthlyStatementInfo
     */
    @Override
    public void statementAllProcessFinishHook(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo) {
        //结单推送完毕后清除相关缓存


    }

    /**
     * 查询公司行动小数股订单数据
     *
     * @param accountId
     * @param period
     * @return
     */
    private List<StatementUsOrderDto> queryFractionOrderList(String accountId, String period, String marketCode, String bankUserId) {
        List<StatementUsOrderDto> resultList = new ArrayList<>();

        //发起数据查询
        ActionFractionOrderResp actionFractionOrderResp = actionFractionOrderQuery(marketCode, accountId, bankUserId, period);
        if (!CollectionUtils.isEmpty(actionFractionOrderResp.getOrderList())) {
            List<ActionFractionOrderDTO> orderList = actionFractionOrderResp.getOrderList();
            for (int i = 0; i < orderList.size(); i++) {
                ActionFractionOrderDTO actionFractionOrderDTO = orderList.get(i);

                StatementUsOrderDto statementUsOrderDto = new StatementUsOrderDto();
                statementUsOrderDto.setTradeDate(DateUtil.format(actionFractionOrderDTO.getTradeDate(), DateUtil.FORMAT_SHORT));

                //如果交收日期为空，则用短横杠展示
                String clearDate = null != actionFractionOrderDTO.getSettleDate()
                        ? DateUtil.format(actionFractionOrderDTO.getSettleDate(), DateUtil.FORMAT_SHORT)
                        : StatementConstants.DOUBLE_SHORT_BAR_FLAG;
                statementUsOrderDto.setClearDate(clearDate);

                statementUsOrderDto.setCurrency(actionFractionOrderDTO.getCurrency());
                statementUsOrderDto.setTradeType(StatementDirectionEnum.SELL.getTradeType());
                statementUsOrderDto.setStockCode(actionFractionOrderDTO.getStockCode());
                statementUsOrderDto.setQty(actionFractionOrderDTO.getQty().toPlainString());
                statementUsOrderDto.setAmount(actionFractionOrderDTO.getAmount().toPlainString());
                statementUsOrderDto.setAvgPrice(actionFractionOrderDTO.getPrice().toPlainString());
                statementUsOrderDto.setCommission(actionFractionOrderDTO.getCommission().toPlainString());
                statementUsOrderDto.setSecFee(actionFractionOrderDTO.getSfcFee().toPlainString());
                statementUsOrderDto.setActivityFee(actionFractionOrderDTO.getTradeFee().toPlainString());
                statementUsOrderDto.setNetAmount(actionFractionOrderDTO.getSettleAmount().toPlainString());

                //自定义订单编号
                String orderNo = String.format(StatementConstants.ACTION_FRACTION_ORDER_NO, actionFractionOrderDTO.getActionId(), accountId, i);
                log.info("公司行动小数股订单数据：orderNo:{}", orderNo);
                statementUsOrderDto.setReferenceNo(orderNo);

                //拼接费用串兼容数据
                Map<String, StatementFeeDto> feeMap = new HashMap<>();
                StatementFeeDto feeDto = new StatementFeeDto();
                feeDto.setFee(ZERO_DEFAULT);

                feeMap.put(systemConfig.getSecFeeKey(), feeDto);
                feeMap.put(systemConfig.getFinraFeeKey(), feeDto);
                statementUsOrderDto.setFeeMap(feeMap);

                resultList.add(statementUsOrderDto);
            }
        }
        return resultList;
    }

    /**
     * 组装数据发起查询公司行动小数股订单
     *
     * @param accountId
     * @param period
     * @return
     */
    private ActionFractionOrderResp actionFractionOrderQuery(String marketCode, String accountId, String bankUserId, String period) {
        Date startDate = DateUtil.stringToDate(MonthlyUtils.getMonthFirstDay(period), DateUtil.FORMATDAY);
        Date endDate = DateUtil.stringToDate(MonthlyUtils.getMonthLastDay(period), DateUtil.FORMATDAY);
        //发起查询
        return stockActionManager.actionFractionOrderQuery(marketCode, accountId, bankUserId, startDate, endDate);
    }
}
