<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdStatementFileRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdStatementFileRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
        <result column="statement_type" jdbcType="INTEGER" property="statementType"/>
        <result column="statement_date" jdbcType="DATE" property="statementDate"/>
        <result column="file_last_modified" jdbcType="DATE" property="fileLastModified"/>
        <result column="data_status" jdbcType="INTEGER" property="dataStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, statement_type, statement_date, data_status, creator, gmt_created, modifier, gmt_modified, is_deleted
    </sql>

    <!-- 自定义通用SQL查询条件 -->
    <sql id="Where_Extra_Condition">
    </sql>

    <!--查询日期范围内的解析记录-->
    <select id="queryDailyFileParseRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_statement_file_record`
        WHERE is_deleted='N'
        AND statement_type = 1
        <if test="startDate != null">
            <![CDATA[ AND statement_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null">
            <![CDATA[ AND statement_date <= #{endDate} ]]>
        </if>

    </select>


    <!--查询日期范围内的解析记录-->
    <select id="queryMonthlyFileParseRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_statement_file_record`
        WHERE is_deleted='N'
        AND statement_type = 2
        AND statement_date = #{statementDate}
    </select>

</mapper>
