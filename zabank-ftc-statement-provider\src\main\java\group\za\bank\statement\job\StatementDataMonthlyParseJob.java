package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.agent.TradeCalendarAgent;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.entity.req.StatementDataParseReq;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.StockTradeDateService;
import group.za.bank.statement.service.TdStockStatementDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.FILE_PARSE_REPEATED;


/**
 * 结单数据解析任务
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Component
@Slf4j
@JobHandler(value = "statementDataParseJob")
public class StatementDataMonthlyParseJob extends IJobHandler {

    private final static String DATE_FORMAT_YMD = "yyyyMMdd";
    @Autowired
    private TdStockStatementDataService stockStatementDataService;
    @Autowired
    private TradeCalendarAgent tradeCalendarAgent;

    @Resource(name = "stockStatementBusinessService")
    private StatementBusinessService statementBusinessService;

    @Autowired
    private StockTradeDateService stockTradeDateService;

    @Autowired
    private SystemConfig systemConfig;

    @Override
    public ReturnT<String> execute(String message) throws Exception {
        //默认是不重复解析的
        boolean refresh = false;
        boolean isAutoTask = true;
        //上个月最后一天
        String period = null;
        String filePathString = null;
        if (StringUtils.isNotEmpty(message)) {
            try {
                log.info("statementDataParseJob param:{}", message);
                StatementDataParseReq statementDataParseReq = JsonUtils.toJavaObject(message, StatementDataParseReq.class);
                refresh = statementDataParseReq.getRefresh();
                period = statementDataParseReq.getStatementDate();
                //这里没有校验是否是最后一个交易日，留个口子给交易日历有问题的时候用
                filePathString = statementDataParseReq.getPathDate();
                isAutoTask = false;
            } catch (Exception e) {
                log.info("StatementDataParseJob解析入参错误");
                throw new Exception("StatementDataParseJob解析入参错误", e);
            }
        } else {
            //不传参数的话默认用上个月最后一天来解析月份
            Date lastMonthDate = DateUtil.getLastDayOfLastMonth(new Date());
            period = DateUtil.format(lastMonthDate, DATE_FORMAT_YMD);
            List<String> monthAllTradeDateList = stockTradeDateService.queryCoreCalendarTradeDateList(period.substring(0, 6));

            if (CollectionUtils.isEmpty(monthAllTradeDateList)) {
                log.error("解析ttl月结单任务异常,当月交易日列表为空,请检查! period:{}", period);
                return ReturnT.FAIL;
            }
            //按日期升序排列，取最后一个代表最后一个交易日
            filePathString = monthAllTradeDateList.get(monthAllTradeDateList.size() - 1);
            //月结单日期和文件夹路径日期保持一致
            period = filePathString;
        }


        try {
            // 拉取月结单数据 获取结单月份最后一个交易日作为文件夹名
            stockStatementDataService.parseStatementMonthlyData(StatementTypeEnum.MONTHLY, period, filePathString, refresh);
        } catch (BusinessException e) {
            if (isAutoTask) {
                //不是通过参数指定日期的定时任务，每天都会重复跑，如果发生重复解析，是正常现象，无需处理
                if (FILE_PARSE_REPEATED.getCode().equals(e.getCode())) {
                    log.info("月结单已经解析完毕! statementDate:{}", period);
                    return ReturnT.SUCCESS;
                }
            }
            throw e;
        }
        return ReturnT.SUCCESS;
    }


}
