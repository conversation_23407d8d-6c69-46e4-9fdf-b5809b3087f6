package group.za.bank.statement.utils;

import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;

/**
 * <AUTHOR>
 * @createTime 14 13:41
 * @description
 */
public class MarketUtils {



    /**
     *
     * @param ttlCode
     * @return
     */
    public static String transferMarketCode(String ttlCode) {
        for (MarketCodeEnum value : MarketCodeEnum.values()) {
            if (value.getTtlValue().equals(ttlCode)) {
                return value.getValue();
            }
        }
        return null;
    }



    /**
     * 币种转市场
     *
     * @param currency
     * @return
     */
    public static  String transferMarketCodeByCcy(String currency) {
        for (MarketCodeEnum value : MarketCodeEnum.values()) {
            if(value.getCurrency().equals(currency)){
                return value.getValue();
            }
        }
        throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "币种转市场异常：{}" + currency);
    }
}
