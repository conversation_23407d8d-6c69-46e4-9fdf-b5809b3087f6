package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description 结单native显示里边每个模块里边每个订单模块
 */
@Data
public class NativeModuleDto {
    /**
     * 资产类型
     * 基金
     * 股票
     */
    private String assetType;

    /**
     * 资产类型标题
     * 基金
     * 股票
     */
    private String assetTypeTitle;

    /**
         * 记录数
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 总记录数
     */
    private Integer totalNum;
    /**
     * 每个模块由多个订单组成，
     * 每个订单是一个块
     */
    private List<NativeBlockDto> nativeBlockList;


    /**
     * 每个模块由多个订单组成，
     * 每个订单是一个块
     */
    private List<List<NativeRowDto>> nativeRowList;

}
