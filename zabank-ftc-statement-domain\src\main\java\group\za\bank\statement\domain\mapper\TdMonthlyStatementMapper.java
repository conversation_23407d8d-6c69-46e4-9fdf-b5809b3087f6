package group.za.bank.statement.domain.mapper;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.dto.RemarkDto;
import group.za.invest.mybatis.mapper.template.CrudMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * This mapper interface access the database table td_fund_monthly_statement
 * <p>
 * Database Table Remarks:
 * </p>
 *
 * <AUTHOR>
 * @since 2021年08月24日 11:36:52
 */
@Repository
public interface TdMonthlyStatementMapper extends CrudMapper<TdFundMonthlyStatement> {



    /**
     * 已初始化未通知生成
     * @param period
     * @param latestId
     * @param pageSize
     * @return
     */
    List<TdFundMonthlyStatement> queryDocUnNotifyStatementList(@Param("period") String period
            , @Param("latestId") Long latestId
            , @Param("pageSize") Integer pageSize);

    /**
     * 已通知生成未同步状态
     *
     * @param period
     * @param latestId
     * @param pageSize
     * @return
     */
    List<TdFundMonthlyStatement> queryDocUnGeneratedStatementList(@Param("period") String period
            , @Param("latestId") Long latestId
            , @Param("pageSize") Integer pageSize);


    /**
     * 未完成的结单记录
     *
     * @param period
     * @param business
     * @return
     */
    Integer countDocUnFinishedStatement(@Param("period") String period,@Param("business") String business);


    /**
     * 已生成文档未推送
     *
     * @param period
     * @param latestId
     * @param pageSize
     * @return
     */
    List<TdFundMonthlyStatement> queryDocUnPublishedStatementList(@Param("period") String period
            , @Param("latestId") Long latestId
            , @Param("pageSize") Integer pageSize);


    /**
     * 查询用户的月结单列表
     *
     * @param bankUserId
     * @param year
     * @param period
     * @return
     */
    List<TdFundMonthlyStatement> queryUserFundMonthlyStatementList(@Param("bankUserId") String bankUserId
            , @Param("clientId") String clientId
            , @Param("docLang") String docLang
            , @Param("year") String year
            , @Param("period") String period);


    /**
     * 查询发送用户成功的数量
     */
    int countPublishSuccessAccountNumber(@Param("period") String period);

    /**
     * 查询发送成功的remark数量
     */
    List<RemarkDto> publishSuccessAccountNumberRemark(@Param("period") String period);


    /**
     * 查询已发送月结单记录
     */
    List<TdFundMonthlyStatement> queryPubMonthlyStatement(@Param("clientId") String clientId,
                                                          @Param("periods") List<String> periods,
                                                          @Param("docLang") String docLang);

    List<TdFundMonthlyStatement> queryStatementByPeriodAndClientId(@Param("period") String period,
                                                                   @Param("clientIdList") List<String> clientIdList,
                                                                   @Param("pageSize")int pageSize,
                                                                   @Param("id")long id);

}
