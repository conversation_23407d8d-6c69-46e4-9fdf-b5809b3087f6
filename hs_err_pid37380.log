#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 176736 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=37380, tid=25092
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.5******.28-jcef (21.0.5+8) (build 21.0.5+8-b631.28)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.5******.28-jcef (21.0.5+8-b631.28, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 

Host: Intel(R) Core(TM) i5-10505 CPU @ 3.20GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Thu Jun  5 07:54:52 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 1.053117 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x00000223c9156270):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25092, stack(0x0000006750400000,0x0000006750500000) (1024K)]


Current CompileTask:
C2:1053 1118       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)

Stack: [0x0000006750400000,0x0000006750500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e52b9]
V  [jvm.dll+0x8c3633]
V  [jvm.dll+0x8c5b8e]
V  [jvm.dll+0x8c6273]
V  [jvm.dll+0x288f46]
V  [jvm.dll+0xc66dd]
V  [jvm.dll+0xc6c13]
V  [jvm.dll+0x2feb50]
V  [jvm.dll+0x60c609]
V  [jvm.dll+0x2598a2]
V  [jvm.dll+0x2521fa]
V  [jvm.dll+0x24fc6e]
V  [jvm.dll+0x1cd6a4]
V  [jvm.dll+0x25f5dc]
V  [jvm.dll+0x25db26]
V  [jvm.dll+0x3ff5e6]
V  [jvm.dll+0x86b248]
V  [jvm.dll+0x6e3abd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000223c92cf5e0, length=16, elements={
0x00000223e10f7fa0, 0x00000223c914f140, 0x00000223c914fe50, 0x00000223c9150f70,
0x00000223c91529d0, 0x00000223c9153420, 0x00000223c9153e70, 0x00000223c9156270,
0x00000223c9160f70, 0x00000223c92a7790, 0x00000223c92affc0, 0x00000223ce0d1ef0,
0x00000223ce162670, 0x00000223ce21b920, 0x00000223ce176020, 0x00000223ce176ac0
}

Java Threads: ( => current thread )
  0x00000223e10f7fa0 JavaThread "main"                              [_thread_blocked, id=59424, stack(0x000000674f600000,0x000000674f700000) (1024K)]
  0x00000223c914f140 JavaThread "Reference Handler"          daemon [_thread_blocked, id=39168, stack(0x000000674fe00000,0x000000674ff00000) (1024K)]
  0x00000223c914fe50 JavaThread "Finalizer"                  daemon [_thread_blocked, id=50164, stack(0x000000674ff00000,0x0000006750000000) (1024K)]
  0x00000223c9150f70 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=35020, stack(0x0000006750000000,0x0000006750100000) (1024K)]
  0x00000223c91529d0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=28152, stack(0x0000006750100000,0x0000006750200000) (1024K)]
  0x00000223c9153420 JavaThread "Service Thread"             daemon [_thread_blocked, id=5848, stack(0x0000006750200000,0x0000006750300000) (1024K)]
  0x00000223c9153e70 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=8896, stack(0x0000006750300000,0x0000006750400000) (1024K)]
=>0x00000223c9156270 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=25092, stack(0x0000006750400000,0x0000006750500000) (1024K)]
  0x00000223c9160f70 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21148, stack(0x0000006750500000,0x0000006750600000) (1024K)]
  0x00000223c92a7790 JavaThread "Notification Thread"        daemon [_thread_blocked, id=28040, stack(0x0000006750600000,0x0000006750700000) (1024K)]
  0x00000223c92affc0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=12052, stack(0x0000006750700000,0x0000006750800000) (1024K)]
  0x00000223ce0d1ef0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=34896, stack(0x0000006750800000,0x0000006750900000) (1024K)]
  0x00000223ce162670 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=44284, stack(0x0000006750a00000,0x0000006750b00000) (1024K)]
  0x00000223ce21b920 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=15944, stack(0x0000006750b00000,0x0000006750c00000) (1024K)]
  0x00000223ce176020 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=52384, stack(0x0000006750c00000,0x0000006750d00000) (1024K)]
  0x00000223ce176ac0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=45560, stack(0x0000006750d00000,0x0000006750e00000) (1024K)]
Total: 16

Other Threads:
  0x00000223c9135920 VMThread "VM Thread"                           [id=39252, stack(0x000000674fd00000,0x000000674fe00000) (1024K)]
  0x00000223faa71160 WatcherThread "VM Periodic Task Thread"        [id=10736, stack(0x000000674fc00000,0x000000674fd00000) (1024K)]
  0x00000223e3376ac0 WorkerThread "GC Thread#0"                     [id=10428, stack(0x000000674f700000,0x000000674f800000) (1024K)]
  0x00000223e3389940 ConcurrentGCThread "G1 Main Marker"            [id=3172, stack(0x000000674f800000,0x000000674f900000) (1024K)]
  0x00000223e338cb00 WorkerThread "G1 Conc#0"                       [id=69396, stack(0x000000674f900000,0x000000674fa00000) (1024K)]
  0x00000223fa93e2b0 ConcurrentGCThread "G1 Refine#0"               [id=27440, stack(0x000000674fa00000,0x000000674fb00000) (1024K)]
  0x00000223fa940c40 ConcurrentGCThread "G1 Service"                [id=59920, stack(0x000000674fb00000,0x000000674fc00000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  1113 1118       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
C2 CompilerThread1  1113 1128       4       sun.security.ec.ECOperations::setDouble (463 bytes)
C2 CompilerThread2  1113 1127       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000604400000, size: 8124 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000022388000000-0x0000022388d00000-0x0000022388d00000), size 13631488, SharedBaseAddress: 0x0000022388000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000022389000000-0x00000223c9000000, reserved size: 1073741824
Narrow klass base: 0x0000022388000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32480M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8124M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 12288K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 0 survivors (0K)
 Metaspace       used 8329K, committed 8576K, reserved 1114112K
  class space    used 964K, committed 1088K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   1|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   2|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   3|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   4|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   5|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   6|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   7|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|   8|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|   9|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  10|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  11|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  12|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  13|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  14|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  15|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  16|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  17|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  18|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  19|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  20|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  21|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  22|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  23|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  24|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  25|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  26|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  27|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  28|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  29|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  30|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  31|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  32|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  33|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  34|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  35|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  36|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  37|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  38|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  39|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  40|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  41|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  42|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  43|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  44|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  45|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  46|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  47|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  48|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  49|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  50|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  51|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  52|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  53|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  54|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  55|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  56|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  57|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  58|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  59|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  60|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  61|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  62|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  63|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  64|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  65|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  66|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  67|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  68|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  69|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  70|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  71|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  72|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  73|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  74|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  75|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  76|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  77|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  78|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  79|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  80|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  81|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  82|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  83|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  84|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  85|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  86|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  87|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  88|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  89|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  90|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  91|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  92|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  93|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  94|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  95|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  96|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  97|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  98|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  99|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 100|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 101|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 102|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 103|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 104|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 105|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 106|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 107|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 108|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 109|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 110|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 111|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 112|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 113|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 114|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 115|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 116|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 117|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 118|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 119|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 120|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 121|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 122|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 123|0x0000000623000000, 0x00000006233d99c8, 0x0000000623400000| 96%| E|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 124|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 125|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 
| 126|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000| PB 0x0000000623c00000| Complete 

Card table byte_map: [0x00000223f7790000,0x00000223f8770000] _byte_map_base: 0x00000223f476e000

Marking Bits: (CMBitMap*) 0x00000223e33770d0
 Bits: [0x0000022380000000, 0x0000022387ef0000)

Polling page: 0x00000223e12a0000

Metaspace:

Usage:
  Non-class:      7.34 MB used.
      Class:    980.70 KB used.
       Both:      8.30 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.50 MB ( 12%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.12 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.62 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.39 MB
       Class:  14.73 MB
        Both:  23.12 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 226.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 138.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 380.
num_chunk_merges: 0.
num_chunk_splits: 220.
num_chunks_enlarged: 95.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=382Kb max_used=382Kb free=119617Kb
 bounds [0x00000223eed30000, 0x00000223eefa0000, 0x00000223f6260000]
CodeHeap 'profiled nmethods': size=120000Kb used=1833Kb max_used=1833Kb free=118166Kb
 bounds [0x00000223e7260000, 0x00000223e74d0000, 0x00000223ee790000]
CodeHeap 'non-nmethods': size=5760Kb used=1432Kb max_used=1458Kb free=4327Kb
 bounds [0x00000223ee790000, 0x00000223eea00000, 0x00000223eed30000]
 total_blobs=1670 nmethods=1153 adapters=422
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.040 Thread 0x00000223c9156270 1111       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSum (6 bytes)
Event: 1.041 Thread 0x00000223ce176ac0 1105       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setProduct (6 bytes)
Event: 1.041 Thread 0x00000223c9160f70 1121       3       sun.security.ec.ECOperations::setSum (549 bytes)
Event: 1.043 Thread 0x00000223c9160f70 nmethod 1121 0x00000223e7418f10 code [0x00000223e74196e0, 0x00000223e741e4d0]
Event: 1.043 Thread 0x00000223c9160f70 1122       1       sun.security.util.math.intpoly.IntegerPolynomial$Element::getField (5 bytes)
Event: 1.043 Thread 0x00000223c9160f70 nmethod 1122 0x00000223eed8a810 code [0x00000223eed8a9a0, 0x00000223eed8aa70]
Event: 1.046 Thread 0x00000223c9160f70 1123       3       sun.security.util.math.intpoly.IntegerPolynomial$ImmutableElement::<init> (7 bytes)
Event: 1.046 Thread 0x00000223ce176020 nmethod 1098 0x00000223eed8ab10 code [0x00000223eed8acc0, 0x00000223eed8b798]
Event: 1.046 Thread 0x00000223ce176020 1106       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setProduct (58 bytes)
Event: 1.046 Thread 0x00000223c9160f70 nmethod 1123 0x00000223e741fd10 code [0x00000223e741fec0, 0x00000223e741fff8]
Event: 1.046 Thread 0x00000223c9156270 nmethod 1111 0x00000223eed8ba10 code [0x00000223eed8bbc0, 0x00000223eed8bee8]
Event: 1.046 Thread 0x00000223c9156270 1114       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSquare (52 bytes)
Event: 1.047 Thread 0x00000223c9156270 nmethod 1114 0x00000223eed8c090 code [0x00000223eed8c240, 0x00000223eed8c390]
Event: 1.047 Thread 0x00000223c9160f70 1124       3       sun.security.util.math.intpoly.IntegerPolynomial$Element::<init> (40 bytes)
Event: 1.047 Thread 0x00000223c9156270 1119       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSquare (5 bytes)
Event: 1.047 Thread 0x00000223ce176ac0 nmethod 1105 0x00000223eed8c490 code [0x00000223eed8c660, 0x00000223eed8caf8]
Event: 1.047 Thread 0x00000223ce176ac0 1116       4       sun.security.util.math.intpoly.IntegerPolynomialP256::square (613 bytes)
Event: 1.047 Thread 0x00000223c9160f70 nmethod 1124 0x00000223e7420090 code [0x00000223e7420240, 0x00000223e7420590]
Event: 1.048 Thread 0x00000223c9156270 nmethod 1119 0x00000223eed8ce10 code [0x00000223eed8cfc0, 0x00000223eed8d110]
Event: 1.048 Thread 0x00000223c9156270 1118       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.018 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.dll
Event: 0.063 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\zip.dll

Deoptimization events (14 events):
Event: 0.437 Thread 0x00000223e10f7fa0 DEOPT PACKING pc=0x00000223e72a6087 sp=0x000000674f6fdfb0
Event: 0.437 Thread 0x00000223e10f7fa0 DEOPT UNPACKING pc=0x00000223ee7e4e42 sp=0x000000674f6fd448 mode 0
Event: 0.549 Thread 0x00000223e10f7fa0 DEOPT PACKING pc=0x00000223e727b1cc sp=0x000000674f6fdac0
Event: 0.549 Thread 0x00000223e10f7fa0 DEOPT UNPACKING pc=0x00000223ee7e4e42 sp=0x000000674f6fcee0 mode 0
Event: 0.632 Thread 0x00000223e10f7fa0 DEOPT PACKING pc=0x00000223e72799cc sp=0x000000674f6fb760
Event: 0.632 Thread 0x00000223e10f7fa0 DEOPT UNPACKING pc=0x00000223ee7e4e42 sp=0x000000674f6fabc8 mode 0
Event: 0.732 Thread 0x00000223e10f7fa0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000223eed6c454 relative=0x0000000000000194
Event: 0.732 Thread 0x00000223e10f7fa0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000223eed6c454 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.732 Thread 0x00000223e10f7fa0 DEOPT PACKING pc=0x00000223eed6c454 sp=0x000000674f6ff040
Event: 0.732 Thread 0x00000223e10f7fa0 DEOPT UNPACKING pc=0x00000223ee7e46a2 sp=0x000000674f6ff010 mode 2
Event: 0.865 Thread 0x00000223ce162670 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000223eed7e930 relative=0x0000000000001030
Event: 0.865 Thread 0x00000223ce162670 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000223eed7e930 method=java.util.concurrent.ConcurrentHashMap.putVal(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 242 c2
Event: 0.865 Thread 0x00000223ce162670 DEOPT PACKING pc=0x00000223eed7e930 sp=0x0000006750afd850
Event: 0.865 Thread 0x00000223ce162670 DEOPT UNPACKING pc=0x00000223ee7e46a2 sp=0x0000006750afd7c0 mode 2

Classes loaded (20 events):
Event: 0.969 Loading class sun/security/ssl/XDHKeyExchange$1
Event: 0.969 Loading class sun/security/ssl/XDHKeyExchange$1 done
Event: 0.969 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry
Event: 0.969 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry done
Event: 0.969 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession
Event: 0.969 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession done
Event: 0.971 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384
Event: 0.971 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384 done
Event: 0.971 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521
Event: 0.971 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521 done
Event: 0.972 Loading class sun/security/util/math/intpoly/P384OrderField
Event: 0.972 Loading class sun/security/util/math/intpoly/P384OrderField done
Event: 0.972 Loading class sun/security/util/math/intpoly/P521OrderField
Event: 0.973 Loading class sun/security/util/math/intpoly/P521OrderField done
Event: 0.974 Loading class java/security/interfaces/ECPrivateKey
Event: 0.974 Loading class java/security/interfaces/ECPrivateKey done
Event: 0.975 Loading class sun/security/util/ArrayUtil
Event: 0.975 Loading class sun/security/util/ArrayUtil done
Event: 0.982 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
Event: 0.982 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.529 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b39b10}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x0000000623b39b10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.530 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b41120}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623b41120) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.530 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b47bb8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000623b47bb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.531 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b4e770}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623b4e770) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.532 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b52df8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623b52df8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.537 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b7cd28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623b7cd28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.538 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b806b8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623b806b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.540 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b8f9d8}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623b8f9d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.544 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bc1680}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000623bc1680) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.545 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bc7f50}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623bc7f50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.546 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bcb370}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623bcb370) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.711 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006235e8418}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006235e8418) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.758 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237eb9c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006237eb9c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.758 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237ef330}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006237ef330) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.765 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062301dbb8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062301dbb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.782 Thread 0x00000223ce0d1ef0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623610b18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623610b18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.790 Thread 0x00000223ce0d1ef0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062364d158}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062364d158) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.794 Thread 0x00000223e10f7fa0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230aae70}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006230aae70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.842 Thread 0x00000223ce162670 Exception <a 'java/lang/NoSuchMethodError'{0x000000062311a3a8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062311a3a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.850 Thread 0x00000223ce162670 Exception <a 'java/lang/NoSuchMethodError'{0x000000062316c260}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062316c260) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.156 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.156 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.166 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.166 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.401 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.401 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.785 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.785 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.076 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c914fe50
Event: 0.076 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c9150f70
Event: 0.076 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c91529d0
Event: 0.077 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c9153420
Event: 0.077 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c9153e70
Event: 0.077 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c9156270
Event: 0.078 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c9160f70
Event: 0.106 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c92a7790
Event: 0.108 Thread 0x00000223e10f7fa0 Thread added: 0x00000223c92affc0
Event: 0.122 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\net.dll
Event: 0.141 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\nio.dll
Event: 0.150 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\zip.dll
Event: 0.224 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jimage.dll
Event: 0.611 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\sunmscapi.dll
Event: 0.698 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\extnet.dll
Event: 0.706 Thread 0x00000223e10f7fa0 Thread added: 0x00000223ce0d1ef0
Event: 0.789 Thread 0x00000223ce0d1ef0 Thread added: 0x00000223ce162670
Event: 0.866 Thread 0x00000223ce0d1ef0 Thread added: 0x00000223ce21b920
Event: 1.020 Thread 0x00000223c9160f70 Thread added: 0x00000223ce176020
Event: 1.040 Thread 0x00000223c9156270 Thread added: 0x00000223ce176ac0


Dynamic libraries:
0x00007ff795e50000 - 0x00007ff795e5a000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.exe
0x00007ff8689b0000 - 0x00007ff868bc7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff867970000 - 0x00007ff867a34000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff865a70000 - 0x00007ff865e43000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff865ff0000 - 0x00007ff866101000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff860ac0000 - 0x00007ff860adb000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\VCRUNTIME140.dll
0x00007ff843ae0000 - 0x00007ff843af8000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jli.dll
0x00007ff867c50000 - 0x00007ff867e01000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8665e0000 - 0x00007ff866606000 	C:\WINDOWS\System32\win32u.dll
0x00007ff850580000 - 0x00007ff85081b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ff867b70000 - 0x00007ff867b99000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff867ba0000 - 0x00007ff867c47000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff866430000 - 0x00007ff866552000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff866110000 - 0x00007ff8661aa000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff868900000 - 0x00007ff868931000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff860ae0000 - 0x00007ff860aec000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\vcruntime140_1.dll
0x00007ff852900000 - 0x00007ff85298d000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\msvcp140.dll
0x00007fffa15f0000 - 0x00007fffa23b1000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\server\jvm.dll
0x00007ff867e10000 - 0x00007ff867ec1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff867a40000 - 0x00007ff867ae7000 	C:\WINDOWS\System32\sechost.dll
0x00007ff865e50000 - 0x00007ff865e78000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff868120000 - 0x00007ff868234000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff867ed0000 - 0x00007ff867f41000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff865380000 - 0x00007ff8653cd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff850900000 - 0x00007ff850934000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff85e4d0000 - 0x00007ff85e4da000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff865360000 - 0x00007ff865373000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff864990000 - 0x00007ff8649a8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff843b10000 - 0x00007ff843b1a000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jimage.dll
0x00007ff853290000 - 0x00007ff8534c2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff866610000 - 0x00007ff8669a3000 	C:\WINDOWS\System32\combase.dll
0x00007ff867540000 - 0x00007ff867617000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff83c580000 - 0x00007ff83c5b2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff866560000 - 0x00007ff8665db000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8313b0000 - 0x00007ff8313d0000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.dll
0x00007ff8669b0000 - 0x00007ff86724d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8662f0000 - 0x00007ff86642f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff863700000 - 0x00007ff86401d000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8687f0000 - 0x00007ff8688fb000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff867620000 - 0x00007ff867686000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8659a0000 - 0x00007ff8659cb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff831390000 - 0x00007ff8313a8000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\zip.dll
0x00007ff843ab0000 - 0x00007ff843ac0000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\net.dll
0x00007ff8649b0000 - 0x00007ff864adc000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff864f30000 - 0x00007ff864f9a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff831160000 - 0x00007ff831176000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\nio.dll
0x00007ff8652a0000 - 0x00007ff8652bb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8648f0000 - 0x00007ff864927000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff864fd0000 - 0x00007ff864ff8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff865210000 - 0x00007ff86521c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff864220000 - 0x00007ff86424d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff867480000 - 0x00007ff867489000 	C:\WINDOWS\System32\NSI.dll
0x00007ff82faf0000 - 0x00007ff82fafe000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\sunmscapi.dll
0x00007ff865e80000 - 0x00007ff865fe7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff865430000 - 0x00007ff86545d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff8653f0000 - 0x00007ff865427000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff800b00000 - 0x00007ff800b08000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff82fa80000 - 0x00007ff82fa89000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\software2\ideaIU-2024.3.1.1.win\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 
java_class_path (initial): C:/software2/ideaIU-2024.3.1.1.win/plugins/vcs-git/lib/git4idea-rt.jar;C:/software2/ideaIU-2024.3.1.1.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8518631424                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8518631424                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/software/Git/mingw64/libexec/git-core;C:/software/Git/mingw64/libexec/git-core;C:\software\Git\mingw64\bin;C:\software\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\software\Git\cmd;C:\Program Files\dotnet\;C:\software2\xshell\;C:\Program Files\nodejs\;C:\software2\python3\Scripts\;C:\software2\python3\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\software2\apache-maven-3.5.4\bin;C:\software2\protoc-21.5-win64\bin;C:\software2\python\Scripts;C:\software2\go\bin;C:\software2\windsurf\Windsurf\bin;C:\software2\Microsoft VS Code\bin
USERNAME=yingfeng.fu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 23, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 77912K (0% of 33259564K total physical memory with 1548684K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 7141K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1628K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16448B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 1 days 21:38 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0x100, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201

Memory: 4k page, system-wide physical 32480M (1511M free)
TotalPageFile size 93174M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 76M, peak: 76M
current process commit charge ("private bytes"): 618M, peak: 619M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+8-b631.28) for windows-amd64 JRE (21.0.5+8-b631.28), built on 2024-11-23 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
