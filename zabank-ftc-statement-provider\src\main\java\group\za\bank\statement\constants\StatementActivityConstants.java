package group.za.bank.statement.constants;

/**
 * <AUTHOR>
 * @date 2021/7/21 10:34
 * @Description
 * @Version v1.0
 */
public class StatementActivityConstants {
    /** 组装Activity内容实现类名称的前缀*/
    public static final String BUILD_ACTIVITY_MESSAGE_SERVICE_PREFIX = "activity_";

    /** 基金结单业务大类*/
    public static final String INVEST_MONTHLY_STATEMENT_BUSINESS_CATEGORY = "investMonthlyStatement";

    /** 基金结单业务子类*/
    public static final String INVEST_MONTHLY_STATEMENT_BUSINESS_SUB_CATEGORY_SEND = "investMonthlyStatementSend";

    /** 南向通投资结单activity业务大类*/
    public static final String SOUTH_INVEST_MONTHLY_STATEMENT_BUSINESS_CATEGORY = "southInvestMonthlyStatement";

    /** 南向通投资结单activity业务子类*/
    public static final String SOUTH_INVEST_MONTHLY_STATEMENT_BUSINESS_SUB_CATEGORY_SEND = "southInvestMonthlyStatementSend";


    /** 基金结单业务子类*/
    public static final String INVEST_MONTHLY_STATEMENT_MONTH = "month";

    public static final String INVEST_STATEMENT_PERIOD = "period";

    public static final String INVEST_MONTHLY_STATEMENT_GROUP = "stm1";

    /**  总市值 **/
    public static final String INVEST_MONTHLY_STATEMENT_TOTAL_MARKET = "investMonthlyStatementTotalMarket";

    /**  总持仓盈亏 **/
    public static final String INVEST_MONTHLY_STATEMENT_TOTAL_HOLDING_PROFIT = "investMonthlyStatementTotalHoldingProfit";

    /**  描述 **/
    public static final String INVEST_MONTHLY_STATEMENT_DESCRIPTION = "investMonthlyStatementDescription";

    /**
     * 月结单发送银行账户销户查邮箱地址失败
     */
    public static final String INVEST_MONTHLY_STATEMENT_SEND_QUERY_EMAIL_FAILED = "银行账户销户查邮箱地址失败";

    /**
     * 月结单发送银行账户销户使用邮件地址发送成功
     */
    public static final String INVEST_MONTHLY_STATEMENT_SEND_USE_EMAIL_SUCCESS = "银行账户销户使用邮件地址发送成功";

    /**
     * 月结单发送银行账户销户使用邮件地址发送失败
     */
    public static final String INVEST_MONTHLY_STATEMENT_SEND_USE_EMAIL_FAILED = "银行账户销户使用邮件地址发送失败";

    // activity 南向通基金产品码，给activity那边区分是否是南向通
    public static final String SOUTH_BOUND_PRODUCT_CODE = "SFUND001";
}
