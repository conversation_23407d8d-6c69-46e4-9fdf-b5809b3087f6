package group.za.bank.statement.service;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.UserMonthlyStatementDataPrepareDto;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.MonthlyStatementDto;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.entity.dto.UserMonthlyStatementQueryDto;
import group.za.bank.statement.entity.req.AppUserMonthlyStatementListReq;
import group.za.bank.statement.entity.req.MonthlyStatementConfirmReq;
import group.za.bank.statement.entity.req.PubMonthlyStatementReq;
import group.za.bank.statement.entity.req.UserMonthlyStatementListReq;
import group.za.bank.statement.entity.resp.MonthlyStatementConfirmResp;
import group.za.bank.statement.entity.resp.UserInvestMonthlyStatementListResp;
import group.za.bank.statement.entity.resp.UserMonthlyStatementListResp;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @createTime 25 10:24
 * @description 基金月结单相关
 */
public interface MonthlyStatementService {


    /**
     * 获取指定期数的结单信息，如果不存在则创建
     *
     * @param business
     * @param period
     * @return
     */
    TdFundMonthlyStatementInfo createStatementInfo(String business, String period);


    /**
     * 1.生成指定期数的月结单
     *
     * @param period
     */
    void initMonthlyStatements(String period);


    /**
     * 1.1.生成月结单记录并调用pub生成月结单pdf
     *
     * @param tdFundMonthlyStatementInfo 当前月结单所在的月份  yyyyMM
     * @return
     */
    void initAllUserMonthlyStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, String businessSubtype);


    /**
     * 处理用户结单
     *
     * @param tdFundMonthlyStatementInfo
     * @param userInvestClientInfoDto
     * @param businessSubtype
     * @return true-用户需要生成月结单， false-用户不需要生成月结单
     */
    boolean initUserMonthlyStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , UserInvestClientInfoDto userInvestClientInfoDto, String businessSubtype);


    /**
     * 准备结单数据
     * @return 当前用户结单数据是否已经都处理完毕
     */
    boolean prepareUserMonthlyStatementData(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement monthlyStatement);


    void userStatementDataPrepareConsume(UserMonthlyStatementDataPrepareDto userMonthlyStatementDataPrepareDto) ;

    /**
     * 准备结单数据
     * @return 当前用户结单数据是否已经都处理完毕
     */
    boolean prepareStatementData(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement monthlyStatement,TdMonthlyStatementData monthlyStatementData);

    /**
     * 2.通知生成pdf
     *
     * @return
     */
    void notifyGeneratePdf(String period);


    /**
     * 通知生成用户pdf
     *
     * @param statementInfo
     * @param docUnGeneratedStatement
     */
    void notifyUserGeneratePdf(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement docUnGeneratedStatement, List<TdMonthlyStatementData> monthlyStatementDataList);


    /**
     * 3.同步结单pdf生成状态
     */
    void synStatementPdfGenerateStatus(String period);


    /**
     * 3.同步用户结单pdf生成状态
     */
    void synUserStatementPdfGenerateStatus(String period, String statementId, String pdfPath,String htmlPath);


    /**
     * 4.发送月结单
     *
     * @return
     */
    void pubMonthlyStatement(String period);


    /**
     * 发送用户月结单
     *
     * @param tdFundMonthlyStatementInfo
     * @param unPubStatement
     */
    void pubUserMonthlyStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, TdFundMonthlyStatement unPubStatement,String lang);


    /**
     * 发送activity
     *  @param statementInfo
     * @param unPubStatement
     * @param baseMonthlyStatementDataDto
     */
    void finishPubUserMonthlyStatement(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement unPubStatement, String remark, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto);


    /**
     * 确认推送结单
     *
     * @param fundMonthlyStatementConfirmReq
     * @return
     */
    MonthlyStatementConfirmResp pubConfirm(MonthlyStatementConfirmReq fundMonthlyStatementConfirmReq);


    /**
     * 获取业务服务
     *
     * @param businessSubType
     * @return
     */
    StatementBusinessService getStatementBusinessService(String businessSubType);


    /**
     * 检查当月月结单生成的情况
     *
     * @param period
     */
    void monthlyStatementsProcessStatusCheck(String period);



    /**
     * 查询用户的月结单，按月份分组
     * @param i18nSupportEnum
     * @param req
     * @return
     */
    UserMonthlyStatementListResp queryUserMonthlyStatement(I18nSupportEnum i18nSupportEnum, AppUserMonthlyStatementListReq req);

    UserMonthlyStatementQueryDto dealWithUserStatementQuery(I18nSupportEnum i18nSupportEnum, AppUserMonthlyStatementListReq req);
    /**
     * 查询用户月结单列表
     * @param req
     * @return
     */
    UserInvestMonthlyStatementListResp queryUserStatementDocList(UserMonthlyStatementListReq req);


    /**
     * 更新结单初始化数量
     * @param statementInfo
     */
    void updateStatementTotalInfoInitStatus( TdFundMonthlyStatementInfo statementInfo);




    /**
     * 缓存中不需要生成结单的用户
     * @param statementInfo
     * @return
     */
    Set<String> queryCacheNoStatementUserKeySet(TdFundMonthlyStatementInfo statementInfo, String businessSubtype);


    /**
     * 清除无需生成结单用户的集合
     * @param statementInfo
     */
    void clearNoStatementUserKeySetCache(TdFundMonthlyStatementInfo statementInfo,String businessSubtype);

    /**
     * 用户维度重新生成月结单
     *
     * @param period
     * @param clientId
     * @param notifyUser
     * @param statementDate
     */
    void rebuildMonthlyStatement(String period, String clientId, Boolean notifyUser, Date statementDate);

    /**
     * 查询已发送的月结单记录
     * @param req
     */
    List<MonthlyStatementDto> queryPubMonthlyStatement(PubMonthlyStatementReq req);

    /**
     * 填充业务表头展示状态
     * @param businessType
     * @param period
     * @param clientId
     * @param monthlyStatementData
     * @return
     */
    boolean showBusinessTitle(String businessType, String period, String clientId, TdMonthlyStatementData monthlyStatementData);
}
