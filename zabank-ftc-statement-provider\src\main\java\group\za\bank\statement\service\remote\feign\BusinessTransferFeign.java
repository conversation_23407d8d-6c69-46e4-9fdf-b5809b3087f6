package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.business.fegin.HoldingTransferFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 综合服务-转仓
 *
 * <AUTHOR>
 * @date 2023/08/22
 **/
@FeignClient(
        value = "zabank-sbs-business-service",
        contextId = "holdingTransferFeignService",
        url = "${sbs.business.gateway.url}"
)
public interface BusinessTransferFeign extends HoldingTransferFeignService {
}
