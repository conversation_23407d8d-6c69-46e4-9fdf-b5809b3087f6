package group.za.bank.statement.utils;

import com.google.common.base.Strings;
import group.za.bank.fund.trade.constants.enums.LanguageTemEnum;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.InvestNumberUtils;
import group.za.bank.statement.common.constants.StatementConstants;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import static group.za.bank.statement.common.constants.StatementConstants.DATE_FORMAT_CN;
import static group.za.bank.statement.common.constants.StatementConstants.DATE_FORMAT_HK;

/**
 * <AUTHOR>
 * @createTime 21 18:27
 * @description
 */
public class StatementNumberFormatUtils {


    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementQtyFormat(BigDecimal number) {
        if (number == null) {
            return "";
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0");
        return decimalFormat.format(number);
    }


    /**
     * 月结单表格中数字的格式化:千分位和精度
     *
     * @return
     */
    public static String statementShareFormat(BigDecimal number, int qtyDecimal) {
        if (number == null) {
            return StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;
        }
        return InvestNumberUtils.thousandsDecimalFormat(number, qtyDecimal);
    }

    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementShareFormat(BigDecimal number) {
        if (number == null) {
            return StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.0000##");
        return decimalFormat.format(number);
    }


    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementMoneyFormat(BigDecimal number) {
        if (number == null) {
            return StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.00");
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
        return decimalFormat.format(number);
    }

    /**
     * 月结单表格中数字的格式化:千分位,保留四位小数
     *
     * @return
     */
    public static String statementQtyFormatFour(BigDecimal number) {
        if (number == null) {
            return StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.0000");
        decimalFormat.setRoundingMode(RoundingMode.DOWN);
        return decimalFormat.format(number);
    }

    /**
     * 格式化数量，千分位，四位小数rounddown,去除末尾的0,默认返回--
     * @param number
     * @return
     */
    public static String statementQtyFormatFourNoZero(BigDecimal number) {
        return statementQtyFormatFourNoZero(number, StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
    }

    /**
     * 格式化数量，千分位，四位小数rounddown,去除末尾的0
     * @param number
     * @return
     */
    public static String statementQtyFormatFourNoZero(BigDecimal number, String defaultStr) {
        if (number == null) {
            return defaultStr;
        }
        //数量不能为负值
        number = number.abs().setScale(4, RoundingMode.DOWN);

        // 设置格式化规则
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.####");
        decimalFormat.setParseBigDecimal(true);

        return decimalFormat.format(number);
    }


    public static String formatMonthlyStatementDate(Date date,String docLang){
        String pattern = DateUtil.FORMAT_LONG;
        if(LanguageTemEnum.CN_HK.getValue().equals(docLang)){
            pattern = DATE_FORMAT_HK;
        }

        if(LanguageTemEnum.CN_ZH.getValue().equals(docLang)){
            pattern = DATE_FORMAT_CN;
        }
        return DateUtil.format(date,pattern);
    }


    /**
     * 将格式化的数字解析成数字
     * @param formattedData
     * @return
     */
    public static BigDecimal parseFormattedData(String formattedData){
        if(Strings.isNullOrEmpty(formattedData)){
            return null;
        }
        formattedData = formattedData.replaceAll("\\,","");
        return new BigDecimal(formattedData);
    }

    /**
     * 日期格式化为英文日期字符串
     * @param date
     * @return
     */
    public static String dateFormat2EngStr(Date date) {
        return DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH);
    }

    /**
     * 英文日期字符串转为date
     * @param engStr
     * @return
     */
    public static Date engStr2Date(String engStr){
        SimpleDateFormat format = new SimpleDateFormat(StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH);
        try {
            return format.parse(engStr);
        } catch (ParseException e) {
            throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR, "engStr2Date日期格式化异常");
        }
    }

    /**
     * 英文日期字符串转为native格式字符串
     * @param engStr
     * @return
     */
    public static String engStr2NativeStr(String engStr) {
        return DateUtil.format(engStr2Date(engStr), StatementConstants.NATIVE_DATE_FORMAT);
    }

    /**
     * 移除格式，只返回金额
     * 比如：HKD 31,741.69 -> 31741.69
     */
    public static BigDecimal removeFormat(String amt) {
        if (StringUtils.isBlank(amt)) {
            return null;
        }
        return new BigDecimal(amt.replaceAll("[^0-9.]", ""));
    }

}
