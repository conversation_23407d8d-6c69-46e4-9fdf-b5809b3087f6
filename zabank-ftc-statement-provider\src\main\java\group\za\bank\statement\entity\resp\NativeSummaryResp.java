package group.za.bank.statement.entity.resp;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import group.za.invest.json.serializer.CustomDateDeserializer;
import group.za.invest.json.serializer.CustomDateSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description
 */
@Data
public class NativeSummaryResp {

    /**
     * 结单id
     */
    private String statementId;


    /**
     * 结单期数
     */
    private String period;

    /**
     * 结单业务
     */
    private String business;

    /**
     * 本期开始日期
     */
    @JsonSerialize(using = CustomDateSerializer.class)
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date periodStartDate;

    /**
     * 本期结束日期
     */
    @JsonSerialize(using = CustomDateSerializer.class)
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date periodEndDate;


    /**
     * 总市值
     */
    private String marketValue;

    /**
     * 币种
     */
    private String currency;



    private List<NativeSubBusinessSummaryResp> subBusinessSummaryList;







    /**
     * 用户结单的状态
     * 标记状态场景： 0-无月结单，1-本期结单生成中，2-本期结单已生成，3、只展示下载pdf
     */
    private Integer statementStatus;


    /**
     * 月结单状态生成日期
     */
    @JsonSerialize(using = CustomDateSerializer.class)
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date generateDate;


    /**
     * 结单地址
     */
    private String obsKey;

}
