package group.za.bank.statement.service.impl;

import com.google.common.collect.Lists;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.TransactionEnum;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.common.utils.TransactionUtils;
import group.za.bank.sbs.bankfront.mapper.CounterFileInfoExtendMapper;
import group.za.bank.sbs.bankfront.model.entity.CounterFileInfo;
import group.za.bank.sbs.business.common.enums.TransferDebitStatusEnum;
import group.za.bank.sbs.business.mapper.BusinessTransferDetailMapper;
import group.za.bank.sbs.business.model.entity.dto.BusinessTransferDetailDto;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.counterfront.common.enums.TtlLinkupTypeEnum;
import group.za.bank.sbs.counterfront.common.enums.TtlTxnTypeIdEnum;
import group.za.bank.sbs.trade.common.constant.enums.*;
import group.za.bank.sbs.trade.mapper.StkActionDetailExtendMapper;
import group.za.bank.sbs.trade.mapper.StkActionDetailSubMapper;
import group.za.bank.sbs.trade.mapper.StkTaxDetailMapper;
import group.za.bank.sbs.trade.model.dto.CompanyActionDetailInfoDTO;
import group.za.bank.sbs.trade.model.dto.CompanyActionExerciseDetailDTO;
import group.za.bank.sbs.trade.model.dto.DailyDividendDataDto;
import group.za.bank.sbs.trade.model.dto.StkActionDetailSubDto;
import group.za.bank.sbs.trade.model.entity.StkHoldingHistory;
import group.za.bank.sbs.trade.model.entity.StkOrder;
import group.za.bank.sbs.trade.model.entity.StkTaxDetail;
import group.za.bank.sbs.trade.model.entity.TaskResult;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.sbs.trade.model.resp.feign.TradeDateDiffResp;
import group.za.bank.statement.agent.StkInfoAgent;
import group.za.bank.statement.common.config.AliOSSProperties;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.StockStatementDataParseRecord;
import group.za.bank.statement.domain.entity.StockStatementIncomeChargesInfo;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.bank.statement.domain.mapper.StockStatementDataParseRecordMapper;
import group.za.bank.statement.domain.mapper.StockStatementIncomeChargesInfoMapper;
import group.za.bank.statement.domain.mapper.TdStatementFileRecordMapper;
import group.za.bank.statement.domain.mapper.TdStockStatementDataMapper;
import group.za.bank.statement.entity.dto.*;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.FileManagerService;
import group.za.bank.statement.service.StockStatementDataBusinessService;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import group.za.bank.statement.utils.AlarmUtil;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.invest.common.utils.DateUtil;
import group.za.invest.common.utils.IdWorker;
import group.za.invest.file.reader.service.impl.split.SplitFileParserImpl;
import group.za.invest.mybatis.repository.service.impl.BaseCrudServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.FILE_PARSE_REPEATED;


/**
 * This service implement access the database table td_stock_statement_data
 * <p>
 * Database Table Remarks:
 * </p>
 *
 * <AUTHOR>
 * @since 2022年05月05日 11:50:13
 */
@Slf4j
@Service
public class TdStockStatementDataServiceImpl extends BaseCrudServiceImpl<TdStockStatementDataMapper, TdStockStatementData>
        implements TdStockStatementDataService {

    public static final String STATEMENT_FILE_NAME = "Statement.dat";

    @Autowired
    private TdStockStatementDataMapper stockStatementDataMapper;
    @Autowired
    private TdStatementFileRecordMapper statementFileRecordMapper;
    @Autowired
    private FileManagerService fileManagerService;
    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private TdStatementFileRecordService statementFileRecordService;
    @Autowired
    private AlarmUtil alarmUtil;
    @Autowired
    private MonthlyStatementManager monthlyStatementManager;
    @Autowired
    private AliOSSProperties aliOSSProperties;
    @Autowired
    private TradeCalendarRemoteService tradeCalendarRemoteService;
    @Autowired
    private StockTradeManager stockTradeManager;
    @Autowired
    private CounterFileInfoExtendMapper counterFileInfoExtendMapper;

    @Resource(name = "statementCheckExecutor")
    private ThreadPoolTaskExecutor statementCheckExecutor;
    @Autowired
    private StkActionDetailExtendMapper stkActionDetailExtendMapper;

    @Autowired
    private StockStatementDataBusinessService stockStatementDataBusinessService;
    @Autowired
    private StockStatementDataParseRecordMapper stockStatementDataParseRecordMapper;
    @Autowired
    private StockStatementIncomeChargesInfoMapper stockStatementIncomeChargesInfoMapper;
    @Autowired
    private StkTaxDetailMapper stkTaxDetailMapper;

    @Autowired
    private StkActionDetailSubMapper stkActionDetailSubMapper;

    @Autowired
    private BusinessTransferDetailMapper businessTransferDetailMapper;
    @Autowired
    private StkInfoAgent stkInfoAgent;

    @Override
    public void parseStatementMonthlyData(StatementTypeEnum statementTypeEnum, String statementDate, String filePathDate, Boolean refresh) {
        log.info("开始解析结单文件，statementType:{}, 日期：{}, 文件路径:{}",
                statementTypeEnum, statementDate, systemConfig.getDir() + filePathDate + "/" + STATEMENT_FILE_NAME);
        TdStatementFileRecord statementFileRecord = null;
        try {
            checkFileExists(filePathDate);

            Date fileLastModifyTime = getFileLastModifyTime(filePathDate);
            log.info("文件最后修改时间：{}", fileLastModifyTime);

            statementFileRecord = createAndUpdateStatus(statementTypeEnum, statementDate, refresh, fileLastModifyTime);

            String fileLocalPath = downloadFileToLocal(filePathDate, statementFileRecord);

            parseAndInsertData(statementFileRecord, fileLocalPath, statementDate, statementTypeEnum);

            updateStatus(statementFileRecord, StatementFileStatusEnum.SUCCESS.getFileStatus());
            log.info("结单文件解析完成，statementType:{}, 日期：{}", statementTypeEnum, statementDate);

        } catch (Exception e) {
            if(e instanceof BusinessException){
                //每天都会重复跑多次，如果发生重复解析，是正常现象，无需处理
                if (FILE_PARSE_REPEATED.getCode().equals(((BusinessException) e).getCode())) {
                    log.info("日结单已经解析完毕! statementDate:{}", statementDate);
                    return;
                }
            }
            handleException(statementFileRecord, statementTypeEnum, statementDate, e);
        }
    }

    /**
     * 检查指定日期的结单文件是否存在。
     *
     * @param filePathDate      文件路径日期
     * @throws BusinessException 如果结单文件不存在，则抛出业务异常
     */
    private void checkFileExists(String filePathDate) {
        String objectKey = aliOSSProperties.getPrefix() + filePathDate + StatementConstants.SLASH_FLAG + STATEMENT_FILE_NAME;
        if (!fileManagerService.existOssFile(objectKey)) {
            log.error("结单文件不存在，无法解析! objectKey:{}", objectKey);
            throw new BusinessException(StatementErrorMsgEnum.FILE_DOWNLOAD_ERROR.getCode(), "月结单文件不存在，无法解析");
        }
        //判断未见大小是否为空
        if (fileManagerService.getOssFileSize(objectKey) == 0) {
            log.error("结单文件大小为0，无法解析! objectKey:{}", objectKey);
            throw new BusinessException(StatementErrorMsgEnum.FILE_DOWNLOAD_ERROR.getCode(), "月结单文件大小为0，无法解析");
        }
    }

    /**
     * 获取文件最后修改时间
     * @param filePathDate
     * @return
     */
    private Date getFileLastModifyTime(String filePathDate) {
        return fileManagerService.getOssFileLastModifyTime(getObjectKey(filePathDate));
    }


    private TdStatementFileRecord createAndUpdateStatus(StatementTypeEnum statementTypeEnum, String statementDate, Boolean refresh, Date fileLastModifyTime) {
        TdStatementFileRecord statementFileRecord = statementFileRecordService.checkFileRecord(statementTypeEnum.getType(), statementDate, refresh, fileLastModifyTime);
        statementFileRecord.setDataStatus(StatementFileStatusEnum.ING.getFileStatus());
        statementFileRecord.setGmtModified(new Date());
        statementFileRecordMapper.updateByPrimaryKey(statementFileRecord);
        return statementFileRecord;
    }

    private String downloadFileToLocal(String filePathDate, TdStatementFileRecord statementFileRecord) {
        String fileLocalPath = getLocalPath(filePathDate) + STATEMENT_FILE_NAME;
        log.info("开始从OSS下载文件，objectKey:{}, fileLocalPath:{}", getObjectKey(filePathDate), fileLocalPath);
        fileManagerService.downloadAliOSSFileAndSaveLocal(getObjectKey(filePathDate), fileLocalPath);
        return fileLocalPath;
    }

    private String getObjectKey(String filePathDate) {
        return aliOSSProperties.getPrefix() + filePathDate + StatementConstants.SLASH_FLAG + STATEMENT_FILE_NAME;
    }

    private void parseAndInsertData(TdStatementFileRecord statementFileRecord, String fileLocalPath, String statementDate, StatementTypeEnum statementTypeEnum) {
        String recordId = statementFileRecord.getId().toString();
        new SplitFileParserImpl<>(StatementConstants.STATEMENT_FILE_SPLIT,
                lineEntityList -> insertData(lineEntityList, statementDate, recordId),
                (line, index) -> filterData(line, statementTypeEnum),
                StatementDataDto.class
        ).parse(fileLocalPath, "UTF-8", StatementConstants.FILE_BATCH_SIZE);
    }

    /**
     * 处理日结单解析异常任务
     * @param statementFileRecord
     * @param statementTypeEnum
     * @param statementDate
     * @param e
     */
    private void handleException(TdStatementFileRecord statementFileRecord, StatementTypeEnum statementTypeEnum, String statementDate, Throwable e) {
        log.error("结单文件处理失败，statementType:{}, 日期：{}, 原因:{}", statementTypeEnum, statementDate, e.getMessage(), e);

        if (null != statementFileRecord) {
            updateStatus(statementFileRecord, StatementFileStatusEnum.FAIL.getFileStatus());
        }

        alarmUtil.wechatAlarm(false,
                String.format("结单文件处理失败! statementType:%s, 日期:%s, 原因:%s", statementTypeEnum, statementDate, e.getMessage()),
                AlarmModuleEnum.STATEMENT_FILE_PARSE_FAIL, e);

        throw new BusinessException(StatementErrorMsgEnum.FILE_PARSE_ERROR, e.getMessage());
    }

    /**
     * 插入数据
     *
     * @param lineEntityList
     * @param statementDate
     * @param businessId
     */
    private void insertData(List<StatementDataDto> lineEntityList, String statementDate, String businessId) {
        // 4.数据批量插入
        List<TdStockStatementData> batchResult = new ArrayList<>();
        for (StatementDataDto statementDataDto : lineEntityList) {
            TdStockStatementData stockStatementData = new TdStockStatementData();
            BeanUtils.copyProperties(statementDataDto, stockStatementData);
            stockStatementData.setId(IdWorker.idworker.nextId());
            stockStatementData.setStatementDate(statementDate);
            stockStatementData.setStatementType(Integer.valueOf(statementDataDto.getStatementType()));
            stockStatementData.setBusinessId(businessId);
            stockStatementData.setFormatType(Integer.valueOf(statementDataDto.getFormatType()));
            stockStatementData.setTradeDate(extractRecordTradeDate(statementDataDto));
            stockStatementData.setMarketCode(extractRecordMarketCode(statementDataDto));
            batchResult.add(stockStatementData);
        }
        // 批量插入，分多次提交事务
        monthlyStatementManager.insertBatchStatementData(batchResult);
    }

    /**
     * 数据过滤
     *
     * @param line
     * @param statementTypeEnum
     * @return
     */
    private Integer filterData(String line, StatementTypeEnum statementTypeEnum) {
        //过滤出月结单数据
        String[] lineContentArray = line.split("\\|");
        String statementType = lineContentArray[2];
        String formatType = lineContentArray[3];
        if (statementTypeEnum.getType().toString().equals(statementType)) {
            String parseFormatType = null;
            if (StatementTypeEnum.MONTHLY.getType().equals(statementTypeEnum.getType())) {
                parseFormatType = systemConfig.getMonthlyStatementFileParseFormatTypes();
            } else {
                parseFormatType = systemConfig.getDailyStatementFileParseFormatTypes();
            }

            if (Strings.isEmpty(parseFormatType)) {
                //没有配置的话就全部都解析
                return 1;
            } else {
                //包含在配置的类型才解析
                String[] parseFormatTypeArray = parseFormatType.split(",");
                List<String> parseFormatTypeList = Arrays.asList(parseFormatTypeArray);
                if (parseFormatTypeList.contains(formatType)) {
                    return 1;
                }
            }
        }
        return 0;
    }

    /**
     * 文件本地保存路径
     *
     * @param filePathDate
     * @return
     */
    private String getLocalPath(String filePathDate) {
        //拼接文件本地保存目录
        return systemConfig.getLocalPath() + filePathDate + StatementConstants.SLASH_FLAG;
    }

    /**
     * 更新状态
     *
     * @param statementFileRecord
     * @param fileStatus
     */
    private void updateStatus(TdStatementFileRecord statementFileRecord, Integer fileStatus) {
        // 6.更新文件解析状态
        statementFileRecord.setDataStatus(fileStatus);
        statementFileRecord.setGmtModified(new Date());
        statementFileRecordMapper.updateByPrimaryKey(statementFileRecord);
    }

    /**
     * 文件下载
     *
     * @param filePathDate
     * @param statementFileRecord
     * @return
     */
    private void downloadFile(String filePathDate, TdStatementFileRecord statementFileRecord) {
        boolean downloadFile = true;
        String fileLocalPath = getLocalPath(filePathDate);
        //oss文件下载开关
        if (systemConfig.isStatementFileAliOssSelector()) {
            String objectKey = aliOSSProperties.getPrefix() + filePathDate + StatementConstants.SLASH_FLAG + STATEMENT_FILE_NAME;
            fileLocalPath = fileLocalPath + STATEMENT_FILE_NAME;
            try {
                log.info("downloadFile objectKey:{},fileLocalPath:{}", objectKey, fileLocalPath);
                if (!fileManagerService.existOssFile(objectKey)) {
                    log.info("对应目录暂无文件，等待下次重试");
                    downloadFile = false;
                } else {
                    fileManagerService.downloadAliOSSFileAndSaveLocal(objectKey, fileLocalPath);
                }
            } catch (Exception e) {
                log.warn("parseStatementMonthlyData oss download error", e);
                downloadFile = false;
            }
        } else {
            // sftp文件下载
            //2.2 拼接ftp目录
            String fileDir = systemConfig.getDir() + filePathDate + StatementConstants.SLASH_FLAG;
            try {
                downloadFile = fileManagerService.downloadFile(fileLocalPath, STATEMENT_FILE_NAME, fileDir);
            } catch (Exception e) {
                log.warn("parseStatementMonthlyData download error", e);
                downloadFile = false;
            }
        }
        if (!downloadFile) {
            log.error("结单文件下载失败，statementType:{},filePathDate:{},fileName:{}", statementFileRecord.getStatementType(), filePathDate, STATEMENT_FILE_NAME);

            //更新解析表状态为失败，等待下次解析
            this.updateStatus(statementFileRecord, StatementFileStatusEnum.FAIL.getFileStatus());
            //告警
            alarmUtil.wechatAlarm(false, "股票结单文件下载失败，statementType:" + statementFileRecord.getStatementType() + ",日期：" + filePathDate, AlarmModuleEnum.STATEMENT_FILE_DOWNLOAD_FAIL, null);
            throw new BusinessException(StatementErrorMsgEnum.FILE_DOWNLOAD_ERROR);
        }
    }

    /**
     * 从各种记录中提取对应的交易日
     *
     * @param statementDataDto
     * @return
     */
    private Date extractRecordTradeDate(StatementDataDto statementDataDto) {
        Integer formatType = Integer.parseInt(statementDataDto.getFormatType());
        if (StatementFormatTypeEnum.ORDER.getFormatType().equals(formatType)
                || StatementFormatTypeEnum.TRADE_CHANGE_DETAIL.getFormatType().equals(formatType)) {
            String tradeDateString = statementDataDto.getParam1();
            return DateUtil.parse(tradeDateString, DateUtil.FORMAT_SHORT);
        }

        //资产和现金就用结单文件的交易日即可
        if (StatementFormatTypeEnum.ASSET_SUMMARY.getFormatType().equals(formatType)) {
            return statementDataDto.getStatementDate();
        }

        if (StatementFormatTypeEnum.CASH_SUMMARY.getFormatType().equals(formatType)) {
            return statementDataDto.getStatementDate();
        }

        return null;
    }


    /**
     * 从各种记录中提取对应的交易日
     *
     * @param statementDataDto
     * @return
     */
    private String extractRecordMarketCode(StatementDataDto statementDataDto) {
        Integer formatType = Integer.parseInt(statementDataDto.getFormatType());
        String ttlExchangeCode = null;
        if (StatementFormatTypeEnum.ORDER.getFormatType().equals(formatType)
                || StatementFormatTypeEnum.TRADE_CHANGE_DETAIL.getFormatType().equals(formatType)) {
            return statementDataDto.getParam6();
        }

        if (StatementFormatTypeEnum.ASSET_SUMMARY.getFormatType().equals(formatType)) {
            return statementDataDto.getParam1();
        }

        if (StatementFormatTypeEnum.CASH_SUMMARY.getFormatType().equals(formatType)) {
            return MarketCodeEnum.US.getTtlValue();
        }

        return null;
    }


    @Override
    public List<TdStockStatementData> queryPeriodDataByFormatType(String ttlMarketCode, String startTradeDate, String endTradeDate, Integer statementType, String accNo, Integer formatType) {
        return stockStatementDataMapper.queryPeriodDataByFormatType(ttlMarketCode, startTradeDate, endTradeDate, statementType, accNo, formatType);
    }


    @Override
    public List<StatementUsOrderDto> queryUsOrderData(StockStatementTradeDateInfo stockStatementTradeDateInfo, Integer statementType, String accNo, String ttlMarketCode) {
        List<StatementUsOrderDto> resultList = new ArrayList<>();

        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo = stockStatementTradeDateInfo.getMarketCodeAndTradeDateInfoMap().get(MarketCodeEnum.US.getValue());
        String startTradeDate = marketStatementTradeDateInfo.getStartTradeDate();
        String endTradeDate = marketStatementTradeDateInfo.getEndTradeDate();

        List<TdStockStatementData> tdStockStatementData = this.queryPeriodDataByFormatType(ttlMarketCode, startTradeDate, endTradeDate, statementType, accNo
                , StatementFormatTypeEnum.ORDER.getFormatType());


        //为了避免ttl模板格式再次发生变化，这里查库的时候不过滤市场，全部查出来再处理
        tdStockStatementData.forEach(stockStatementData -> {
            StatementUsOrderDto statementUsOrderDto = new StatementUsOrderDto();
            buildUsOrderDto(statementUsOrderDto, stockStatementData);

            resultList.add(statementUsOrderDto);
        });
        return resultList;
    }

    private void buildUsOrderDto(StatementUsOrderDto statementUsOrderDto, TdStockStatementData stockStatementData) {
        statementUsOrderDto.setBusinessId(stockStatementData.getBusinessId());
        statementUsOrderDto.setStatementDate(stockStatementData.getStatementDate());
        statementUsOrderDto.setStatementType(stockStatementData.getStatementType());
        statementUsOrderDto.setFormatType(stockStatementData.getFormatType());
        statementUsOrderDto.setAccNo(stockStatementData.getAccNo());
        statementUsOrderDto.setTradeDate(stockStatementData.getParam1());
        statementUsOrderDto.setClearDate(stockStatementData.getParam2());
        statementUsOrderDto.setCurrency(stockStatementData.getParam3());
        statementUsOrderDto.setReferenceNo(stockStatementData.getParam4());
        statementUsOrderDto.setTradeType(stockStatementData.getParam5());

        //市场
        statementUsOrderDto.setStockCode(stockStatementData.getParam6());
        statementUsOrderDto.setStockCode(stockStatementData.getParam7());
        statementUsOrderDto.setStockName(stockStatementData.getParam8());
        statementUsOrderDto.setQty(stockStatementData.getParam9());
        statementUsOrderDto.setAvgPrice(stockStatementData.getParam10());
        statementUsOrderDto.setAmount(stockStatementData.getParam11());
        statementUsOrderDto.setNetAmount(stockStatementData.getParam12());

        statementUsOrderDto.setCommission(stockStatementData.getParam13());



        //费用

        statementUsOrderDto.setFeeMap(new HashMap<>(5));

        //费用1
        if (!Strings.isEmpty(stockStatementData.getParam14())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam14()
                    , stockStatementData.getParam15()
                    , stockStatementData.getParam16());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }
        //费用2
        if (!Strings.isEmpty(stockStatementData.getParam17())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam17()
                    , stockStatementData.getParam18()
                    , stockStatementData.getParam19());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用3
        if (!Strings.isEmpty(stockStatementData.getParam20())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam20()
                    , stockStatementData.getParam21()
                    , stockStatementData.getParam22());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用4
        if (!Strings.isEmpty(stockStatementData.getParam23())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam23()
                    , stockStatementData.getParam24()
                    , stockStatementData.getParam25());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }
        //费用5
        if (!Strings.isEmpty(stockStatementData.getParam26())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam26()
                    , stockStatementData.getParam27()
                    , stockStatementData.getParam28());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }
        //费用6
        if (!Strings.isEmpty(stockStatementData.getParam29())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam29()
                    , stockStatementData.getParam30()
                    , stockStatementData.getParam31());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用7
        if (!Strings.isEmpty(stockStatementData.getParam32())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam32()
                    , stockStatementData.getParam33()
                    , stockStatementData.getParam34());

            statementUsOrderDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }


    }

    @Override
    public List<StatementTradeChangeDetailDto> queryTradeChangeDetailData(String period, StockStatementTradeDateInfo stockStatementTradeDateInfo, Integer statementType, String accNo, List<String> businessTypeList, String ttlMarketCode) {
        List<StatementTradeChangeDetailDto> resultList = new ArrayList<>();

        //20240924 改为完整自然月区间取数据
        String startTradeDate = MonthlyUtils.getMonthFirstDay(period);
        String endTradeDate = MonthlyUtils.getMonthLastDay(period);

        List<TdStockStatementData> tdStockStatementData = this.queryPeriodDataByFormatType(ttlMarketCode, startTradeDate, endTradeDate, statementType, accNo
                , StatementFormatTypeEnum.TRADE_CHANGE_DETAIL.getFormatType());

        log.info("queryTradeChangeDetailData businessTypeList:{}", JsonUtils.toJsonString(businessTypeList));
        tdStockStatementData.stream().filter(t -> businessTypeList.contains(t.getParam5())).forEach(stockStatementData -> {
            StatementTradeChangeDetailDto tradeChangeDetailDto = new StatementTradeChangeDetailDto();
            buildTradeChangeDetailDto(tradeChangeDetailDto, stockStatementData);

            resultList.add(tradeChangeDetailDto);
        });
        log.info("queryTradeChangeDetailData resultList size:{}", resultList.size());
        return resultList;
    }


    @Override
    public void buildTradeChangeDetailDto(StatementTradeChangeDetailDto tradeChangeDetailDto
            , TdStockStatementData stockStatementData) {

        tradeChangeDetailDto.setStatementId(stockStatementData.getId());
        tradeChangeDetailDto.setBusinessId(stockStatementData.getBusinessId());
        tradeChangeDetailDto.setStatementDate(stockStatementData.getStatementDate());
        tradeChangeDetailDto.setStatementType(stockStatementData.getStatementType());
        tradeChangeDetailDto.setFormatType(stockStatementData.getFormatType());
        tradeChangeDetailDto.setAccNo(stockStatementData.getAccNo());

        tradeChangeDetailDto.setTradeDate(stockStatementData.getParam1());
        tradeChangeDetailDto.setClearDate(stockStatementData.getParam2());
        tradeChangeDetailDto.setCurrency(stockStatementData.getParam3());
        tradeChangeDetailDto.setReferenceNo(stockStatementData.getParam4());
        tradeChangeDetailDto.setBusinessType(stockStatementData.getParam5());

        tradeChangeDetailDto.setTtlMarketCode(stockStatementData.getParam6());
        tradeChangeDetailDto.setStockCode(stockStatementData.getParam7());
        tradeChangeDetailDto.setDescription(stockStatementData.getParam8());
        tradeChangeDetailDto.setQty(stockStatementData.getParam9());
        tradeChangeDetailDto.setAmount(stockStatementData.getParam10());


        //源股票代码
        tradeChangeDetailDto.setSourceTtlMarketCode(stockStatementData.getParam11());
        tradeChangeDetailDto.setSourceStockCode(stockStatementData.getParam12());

        tradeChangeDetailDto.setRemark(stockStatementData.getParam13());

        tradeChangeDetailDto.setFeeMap(new HashMap<>(9));

        //费用1
        if (!Strings.isEmpty(stockStatementData.getParam14())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam14()
                    , stockStatementData.getParam15()
                    , stockStatementData.getParam16());
            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }
        //费用2
        if (!Strings.isEmpty(stockStatementData.getParam17())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam17()
                    , stockStatementData.getParam18()
                    , stockStatementData.getParam19());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用3
        if (!Strings.isEmpty(stockStatementData.getParam20())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam20()
                    , stockStatementData.getParam21()
                    , stockStatementData.getParam22());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用4
        if (!Strings.isEmpty(stockStatementData.getParam23())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam23()
                    , stockStatementData.getParam24()
                    , stockStatementData.getParam25());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }
        //费用5
        if (!Strings.isEmpty(stockStatementData.getParam26())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam26()
                    , stockStatementData.getParam27()
                    , stockStatementData.getParam28());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用6
        if (!Strings.isEmpty(stockStatementData.getParam29())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam29()
                    , stockStatementData.getParam30()
                    , stockStatementData.getParam31());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用7
        if (!Strings.isEmpty(stockStatementData.getParam32())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam32()
                    , stockStatementData.getParam33()
                    , stockStatementData.getParam34());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用8
        if (!Strings.isEmpty(stockStatementData.getParam35())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam35()
                    , stockStatementData.getParam36()
                    , stockStatementData.getParam37());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }

        //费用9
        if (!Strings.isEmpty(stockStatementData.getParam38())) {
            StatementFeeDto statementFeeDto = new StatementFeeDto(stockStatementData.getParam38()
                    , stockStatementData.getParam39()
                    , stockStatementData.getParam40());

            tradeChangeDetailDto.getFeeMap().put(statementFeeDto.getFeeName(), statementFeeDto);
        }


    }

    @Override
    public List<StatementAccountAssetDto> queryAccountAssetData(String statementDate, Integer statementType, String accNo, String ttlMarketCode) {

        List<StatementAccountAssetDto> resultList = new ArrayList<>();

        List<TdStockStatementData> tdStockStatementData = this.queryPeriodDataByFormatType(ttlMarketCode
                , statementDate, statementDate, statementType
                , accNo, StatementFormatTypeEnum.ASSET_SUMMARY.getFormatType());

        tdStockStatementData.forEach(stockStatementData -> {
            StatementAccountAssetDto accountAssetDto = new StatementAccountAssetDto();
            buildAccountAssetDto(accountAssetDto, stockStatementData);
            resultList.add(accountAssetDto);
        });
        return resultList;
    }

    private void buildAccountAssetDto(StatementAccountAssetDto accountAssetDto, TdStockStatementData stockStatementData) {
        accountAssetDto.setBusinessId(stockStatementData.getBusinessId());
        accountAssetDto.setStatementDate(stockStatementData.getStatementDate());
        accountAssetDto.setStatementType(stockStatementData.getStatementType());
        accountAssetDto.setFormatType(stockStatementData.getFormatType());
        accountAssetDto.setAccNo(stockStatementData.getAccNo());

        accountAssetDto.setExchangeCode(stockStatementData.getParam1());
        accountAssetDto.setStockCode(stockStatementData.getParam2());
        accountAssetDto.setStockName(stockStatementData.getParam3());

        accountAssetDto.setOpeningBalance(stockStatementData.getParam4());
        accountAssetDto.setCurrentBalance(stockStatementData.getParam5());
        accountAssetDto.setClosingBalance(stockStatementData.getParam6());

        accountAssetDto.setClosePrice(stockStatementData.getParam7());
        accountAssetDto.setCurrency(stockStatementData.getParam8());
        accountAssetDto.setMarketValue(stockStatementData.getParam9());

        /**
         * 期初期末已交收持仓
         */
        accountAssetDto.setOpeningSettleHolding(stockStatementData.getParam10());
        accountAssetDto.setClosingSettleHolding(stockStatementData.getParam11());

    }

    @Override
    public List<StatementCashSummaryDto> queryCashSummaryData(StockStatementTradeDateInfo stockStatementTradeDateInfo, Integer statementType, String accNo) {
        List<StatementCashSummaryDto> resultList = new ArrayList<>();

        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo = stockStatementTradeDateInfo.getMarketCodeAndTradeDateInfoMap().get(MarketCodeEnum.US.getValue());

        String endTradeDate = null;

        if (StatementTypeEnum.MONTHLY.getType().equals(statementType)) {
            endTradeDate = marketStatementTradeDateInfo.getEndTradeDate();
        } else {
            endTradeDate = marketStatementTradeDateInfo.getPeriodEndCoreTradeDate();
        }
        log.info("查询用户现金摘要数据!period:{},statementType:{},accNo:{},endTradeDate:{}", stockStatementTradeDateInfo.getPeriod(), statementType, accNo, endTradeDate);
        List<TdStockStatementData> tdStockStatementData = this.queryPeriodDataByFormatType(MarketCodeEnum.US.getTtlValue()
                , endTradeDate, endTradeDate, statementType, accNo, StatementFormatTypeEnum.CASH_SUMMARY.getFormatType());

        tdStockStatementData.forEach(stockStatementData -> {
            StatementCashSummaryDto cashSummaryDto = new StatementCashSummaryDto();
            buildCashSummaryDto(cashSummaryDto, stockStatementData);

            resultList.add(cashSummaryDto);
        });
        return resultList;
    }

    @Override
    public void dailyStatementDataCheck(String statementDate) {
        //查询交易日
        TradeDateDiffResp tradeDateDiffResp = tradeCalendarRemoteService.tradeDateDiff(MarketCodeEnum.US.getValue(), statementDate, -1);
        Date tradeDate = DateUtil.parse(tradeDateDiffResp.getTradeDate(), DateUtil.FORMATDAY);

        //先检查清算状态，再确定是否开始日结单数据检查
        if (!checkClearStatus(tradeDate)) {
            return;
        }

        //把各个任务放到线程池处理
        List<Future> futureList = new ArrayList<>();

        //1 当日订单数据校验
        futureList.add(statementCheckExecutor.submit(() -> checkOrder(tradeDate)));

        //2 公司行动数据校验
        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMATDAY);
        //2.1 查询TTL数据
        List<TdStockStatementData> tradeChangeStatementData = this.queryPeriodDataByFormatType(MarketCodeEnum.US.getTtlValue(), tradeDateStr, tradeDateStr,
                StatementTypeEnum.DAILY.getType(), null
                , StatementFormatTypeEnum.TRADE_CHANGE_DETAIL.getFormatType());

        //2.2 查询本地数据
        List<CompanyActionDetailInfoDTO> actionDetailInfoDTOS = stockTradeManager.queryActionBookCloseData(tradeDate);

        //2.3 红利派息资金相关
        List<TdStockStatementData> dividendList = tradeChangeStatementData.stream()
                .filter(t -> StatementBusinessTypeEnum.CDV.getBusinessType().equals(t.getParam5()))
                .collect(Collectors.toList());

        List<CompanyActionDetailInfoDTO> localDividendList = actionDetailInfoDTOS.stream()
                .filter(t -> t.getActivityType().equals(ActionTypeEnum.DIVIDEND.getValue()))
                .collect(Collectors.toList());

        futureList.add(statementCheckExecutor.submit(() -> checkActionDividend(statementDate, tradeDate, dividendList, localDividendList)));

        //2.2、拆合换红和派送股权
        List<TdStockStatementData> fractionList = tradeChangeStatementData.stream()
                .filter(t -> StatementBusinessTypeEnum.getActionBookCloseBusinessList().contains(t.getParam5()))
                .collect(Collectors.toList());

        List<CompanyActionDetailInfoDTO> localFractionList = actionDetailInfoDTOS.stream()
                .filter(t -> ActionTypeEnum.FRACTION_TYPE_LIST.contains(t.getActivityType())
                        || ActionTypeEnum.RIGHTS_ISSUE.getValue().equals(t.getActivityType()))
                .collect(Collectors.toList());

        futureList.add(statementCheckExecutor.submit(() -> checkActionFraction(statementDate, tradeDate, fractionList, localFractionList)));


        //2.3、供股权和股票回购
        List<TdStockStatementData> exerciseChangeList = tradeChangeStatementData.stream()
                .filter(t -> StatementBusinessTypeEnum.getActionExerciseHoldChangeBusinessList().contains(t.getParam5()))
                .collect(Collectors.toList());

        futureList.add(statementCheckExecutor.submit(() -> checkActionExercise(tradeDate, exerciseChangeList)));


        //3、持仓数据校验
        futureList.add(statementCheckExecutor.submit(() -> checkHoldingData(statementDate, tradeDate)));

        //汇总所有的结果
        boolean success = true;
        for (Future future : futureList) {
            try {
                boolean result = (boolean) future.get();
                if (!result) {
                    success = false;
                }
            } catch (Exception e) {
                log.error("future线程任务异常人工排查！", e);
                success = false;
            }
        }
        if (success) {
            log.info("【dailyStatementDataCheck】{}-日结单数据检查成功", DateUtil.format(tradeDate, DateUtil.FORMATDAY));
        } else {
            log.error("【dailyStatementDataCheck】{}-日结单数据检查失败", DateUtil.format(tradeDate, DateUtil.FORMATDAY));
        }
    }

    @Override
    public void localGenerationStatementFileData(Date tradeDate, String statementDate, TdStatementFileRecord statementFileRecord) {

        try {
            String businessId = statementFileRecord.getId().toString();
            //1.构建订单数据
            List<TdStockStatementData> orderData = stockStatementDataBusinessService.localGenerationOrderData(businessId, statementDate, tradeDate);
            log.info("【localGenerationStatementFileData】orderData:{}", orderData.size());

            //2.构建交易变动数据
            List<TdStockStatementData> tradeChangeData = stockStatementDataBusinessService.localGenerationTradeChangeData(businessId, statementDate, tradeDate);
            log.info("【localGenerationStatementFileData】tradeChangeData:{}", tradeChangeData.size());

            //3.构建持仓数据
            List<TdStockStatementData> holdingData = stockStatementDataBusinessService.localGenerationHoldingData(businessId, statementDate, tradeDate);
            log.info("【localGenerationStatementFileData】holdingData:{}", holdingData.size());

            //4.在同一个事务下，批量插入数据
            TransactionUtils.execute(TransactionEnum.STATEMENT.getName(), () -> {
                if(!CollectionUtils.isEmpty(orderData)){
                    stockStatementDataMapper.insertBatch(orderData);
                }
                if(!CollectionUtils.isEmpty(tradeChangeData)){
                    stockStatementDataMapper.insertBatch(tradeChangeData);
                }
                if(!CollectionUtils.isEmpty(holdingData)){
                    stockStatementDataMapper.insertBatch(holdingData);
                }
                statementFileRecord.setDataStatus(StatementFileStatusEnum.SUCCESS.getFileStatus());
                statementFileRecord.setGmtModified(new Date());
                statementFileRecordMapper.updateByPrimaryKey(statementFileRecord);
            });
            log.info("【localGenerationStatementFileData】success:{}", statementDate);
        } catch (Exception e) {
            statementFileRecord.setDataStatus(StatementFileStatusEnum.FAIL.getFileStatus());
            statementFileRecord.setGmtModified(new Date());
            statementFileRecordMapper.updateByPrimaryKey(statementFileRecord);

            log.info("autoBuildStatementFileData error", e);
            throw new BusinessException(StatementErrorMsgEnum.STOCK_STATEMENT_AUTO_BUILD_ERROR);
        }
    }

    String getParseId(LocalDailyStatementTypeEnum typeEnum,Date tradeDate){
        return typeEnum.getValue()+"_"+DateUtil.format(tradeDate,DateUtil.FORMATDAY);

    }

    @Override
    public void dailyLocalGenerationStatementIncomeCharges(Date tradeDate) {
        String parseId=getParseId(LocalDailyStatementTypeEnum.TAX_FEE,tradeDate);
        StockStatementDataParseRecord stockStatementDataParseRecord = new StockStatementDataParseRecord();
        stockStatementDataParseRecord.setParseId(parseId);
        StockStatementDataParseRecord record =
            stockStatementDataParseRecordMapper.selectOne(stockStatementDataParseRecord);
        if (record == null) {
            log.info("初始化结单数据同步记录record={}", stockStatementDataParseRecord);
            stockStatementDataParseRecordMapper.insert(stockStatementDataParseRecord);
            return;
        }
        boolean status = checkClearStatus(tradeDate);
        if (!status) {
            log.info("清算中数据还未完成tradeDate={}",tradeDate);
            return;
        }

        if (record.getDataStatus() == Integer.parseInt(LocalDaliyStatementDataStatusEnum.INIT.getValue())) {
            insertFeeData(tradeDate, record,  parseId);
            return;
        }

        if (record.getDataStatus() == Integer.parseInt(LocalDaliyStatementDataStatusEnum.PROCESSING.getValue())) {
            log.error("正在同步中,无需重复同步");
            return;
        }
        if (record.getDataStatus() == Integer.parseInt(LocalDaliyStatementDataStatusEnum.FINISHED.getValue())) {
            log.info("同步已经完成,无需同步");
            return;
        }
        return;









    }

    private void insertFeeData(Date tradeDate,StockStatementDataParseRecord record, String parseId) {
        StockStatementDataParseRecord stockStatementDataParseRecord = new StockStatementDataParseRecord();
        stockStatementDataParseRecord.setId(record.getId());
        stockStatementDataParseRecord.setGmtModified(new Date());
        try {

            stockStatementDataParseRecord
                .setDataStatus(Integer.parseInt(LocalDaliyStatementDataStatusEnum.PROCESSING.getValue()));
            int update = stockStatementDataParseRecordMapper.updateByPrimaryKey(stockStatementDataParseRecord);
            if (update < 1) {
                log.error("并行执行,不同步");
                return;
            }
            List<StockStatementIncomeChargesInfo> list = getLocalDailyStatementFeeData(tradeDate,parseId);
            TransactionUtils.execute(TransactionEnum.STATEMENT.getName(), () -> {
                if(!ObjectUtils.isEmpty(list)){
                    stockStatementIncomeChargesInfoMapper.insertBatch(list);
                }
                stockStatementDataParseRecord.setDataStatus(Integer.parseInt(LocalDaliyStatementDataStatusEnum.FINISHED.getValue()));
                stockStatementDataParseRecord.setGmtModified(new Date());

                stockStatementDataParseRecordMapper.updateByPrimaryKey(stockStatementDataParseRecord);
            });
        } catch (Exception e) {
            log.error("同步税费异常", e);

            stockStatementDataParseRecord
                .setDataStatus(Integer.parseInt(LocalDaliyStatementDataStatusEnum.INIT.getValue()));
            stockStatementDataParseRecord.setGmtModified(new Date());
            stockStatementDataParseRecordMapper.updateByPrimaryKey(stockStatementDataParseRecord);
            StockStatementIncomeChargesInfo stockStatementIncomeChargesInfo=new StockStatementIncomeChargesInfo();
            stockStatementIncomeChargesInfo.setParseId(parseId);
            stockStatementIncomeChargesInfoMapper.deleteByCondition(stockStatementIncomeChargesInfo);

            return;
        }
        log.info("税费数据更新成功date={}", tradeDate);
        return;
    }

    private List<StockStatementIncomeChargesInfo> getLocalDailyStatementFeeData(Date tradeDate,String parseId) {
        List<StockStatementIncomeChargesInfo> result=new ArrayList<>();
        //当日派息相关数据 CDV
        List<DailyDividendDataDto> cdvList=   stkActionDetailExtendMapper.queryDailyDividendData(tradeDate);
        convertStockStatementIncomeChargesInfo(cdvList,result,StatementBusinessTypeEnum.CDV,parseId);

        //查询当日选股选息默认选股数据 SOP
        List<DailyDividendDataDto> sopList  = stkActionDetailExtendMapper.queryDividendDataByType(ActionTypeEnum.SELECTION_STOCK.getValue(),"P",tradeDate);
        convertStockStatementIncomeChargesInfo(sopList,result,StatementBusinessTypeEnum.SOP,parseId);

        //查询当日派发红股的数据 BSH & SCAS

        List<DailyDividendDataDto> bshList  = stkActionDetailExtendMapper.queryDividendDataByType(ActionTypeEnum.BONUS_SHARE.getValue(),"P",tradeDate);
        convertStockStatementIncomeChargesInfo(bshList,result,StatementBusinessTypeEnum.BSH,parseId);

        //ESB 供股权
        setESBData(result,tradeDate,parseId);
        //增加税费变动明细
        setTaxData(result,tradeDate,parseId);

        //增加转仓手续费变动明细
        setTransferData(result,tradeDate,parseId);

        //补充 股息追缴和补偿
        setDividendPressAndRebate(result, tradeDate, parseId, MarketCodeEnum.US.getValue());

        return result;
    }

    private void setTransferData(List<StockStatementIncomeChargesInfo> result, Date tradeDate, String parseId) {
        Date startTime= DateUtil.startOfDay(tradeDate);
        Date endTime= DateUtil.endOfDay(tradeDate);
        List<String> statusList= Arrays.asList(TransferDebitStatusEnum.SUCCESS.getValue(), TransferDebitStatusEnum.REFUND_FAIL.getValue());
       List<BusinessTransferDetailDto> list= businessTransferDetailMapper.getTransferDetailByDate(statusList,startTime,endTime);
        log.info("同步转仓费用数据size={}",list.size());
        list.forEach( businessTransferDetail -> {
            StockStatementIncomeChargesInfo stockStatementIncomeChargesInfo = new StockStatementIncomeChargesInfo();
            stockStatementIncomeChargesInfo.setBizId(businessTransferDetail.getDetailNo());
            stockStatementIncomeChargesInfo.setParseId(parseId);
            stockStatementIncomeChargesInfo.setUserId(businessTransferDetail.getUserId());
            stockStatementIncomeChargesInfo.setAccountId(businessTransferDetail.getAccountId());
            stockStatementIncomeChargesInfo.setStockCode(businessTransferDetail.getStockCode());
            stockStatementIncomeChargesInfo.setMarketCode(businessTransferDetail.getMarketCode());
            StatementBusinessTypeEnum businessTypeEnum = TransferTypeEnum.TRANSFER_IN.getTransferType().equals(businessTransferDetail.getTransferType())
                    ? StatementBusinessTypeEnum.SDP : StatementBusinessTypeEnum.SWD;
            stockStatementIncomeChargesInfo.setBusinessType(businessTypeEnum.getBusinessType());
            stockStatementIncomeChargesInfo.setStatementDate(DateUtil.parse(businessTransferDetail.getDebitDate(),DateUtil.FORMAT_SHORT));

            setName(stockStatementIncomeChargesInfo);
//            stockStatementIncomeChargesInfo.setStockNameEn(businessTransferDetail.getStockName());
//            stockStatementIncomeChargesInfo.setStockNameHk();
//            stockStatementIncomeChargesInfo.setStockNameZh()
            stockStatementIncomeChargesInfo.setAmount(businessTransferDetail.getStockTransferFee());
            stockStatementIncomeChargesInfo.setCurrency(businessTransferDetail.getCurrency());
            result.add(stockStatementIncomeChargesInfo);

        });
    }


    private void setDividendPressAndRebate(List<StockStatementIncomeChargesInfo> result, Date tradeDate, String parseId, String marketCode) {
        Date startTime= DateUtil.startOfDay(tradeDate);
        Date endTime= DateUtil.endOfDay(tradeDate);
        List<StkActionDetailSubDto> list= stkActionDetailSubMapper.queryDividendPressAndRebate(marketCode, startTime,endTime);
        log.info("同步股息追缴费用数据size={}",list.size());
        list.forEach( dailyDividendDataDto -> {

            StockStatementIncomeChargesInfo stockStatementIncomeChargesInfo = new StockStatementIncomeChargesInfo();
            stockStatementIncomeChargesInfo.setBizId(dailyDividendDataDto.getRightsId());
            stockStatementIncomeChargesInfo.setParseId(parseId);
            stockStatementIncomeChargesInfo.setUserId(dailyDividendDataDto.getUserId());
            stockStatementIncomeChargesInfo.setAccountId(dailyDividendDataDto.getAccountId());
            stockStatementIncomeChargesInfo.setStockCode(dailyDividendDataDto.getStockCode());
            stockStatementIncomeChargesInfo.setMarketCode(dailyDividendDataDto.getMarketCode());
            StatementBusinessTypeEnum statementBusinessTypeEnum=  DividendRebateTypeEnum.convertStatementBusinessTypeEnum(dailyDividendDataDto.getBusinessType());
            stockStatementIncomeChargesInfo.setBusinessType(statementBusinessTypeEnum.getBusinessType());



            stockStatementIncomeChargesInfo.setStatementDate(DateUtil.parse(dailyDividendDataDto.getBusinessTime(),DateUtil.FORMAT_SHORT));
//            stockStatementIncomeChargesInfo.setStockNameEn(dailyDividendDataDto.getStockName());
            //todo
//            stockStatementIncomeChargesInfo.setRemark(dailyDividendDataDto.getRemark());
            stockStatementIncomeChargesInfo.setAmount(dailyDividendDataDto.getBusinessAmount());
            stockStatementIncomeChargesInfo.setCurrency(dailyDividendDataDto.getIssueCurrency());
            result.add(stockStatementIncomeChargesInfo);
        });

    }

    private void setTaxData(List<StockStatementIncomeChargesInfo> result, Date tradeDate, String parseId) {
        //ADR
       Date startTime= DateUtil.startOfDay(tradeDate);
        Date endTime= DateUtil.endOfDay(tradeDate);

//        List<StkTaxDetail> adrList= stkTaxDetailMapper.selectList(new LambdaQueryWrapper<StkTaxDetail>().eq(StkTaxDetail::getStatus, TaxDetailStatusEnum.SUCCESS.getValue()).between(StkTaxDetail::getLatestApplyDebitTime,startTime,endTime));
        List<StkTaxDetail> adrList=stkTaxDetailMapper.queryTaxDetailByTime(startTime,endTime,TaxDetailStatusEnum.SUCCESS.getValue());
        log.info("同步税费数据size={}",adrList.size());
        adrList.forEach( stkTaxDetail -> {
            StockStatementIncomeChargesInfo stockStatementIncomeChargesInfo = new StockStatementIncomeChargesInfo();
            stockStatementIncomeChargesInfo.setBizId(stkTaxDetail.getRecordNo());
            stockStatementIncomeChargesInfo.setParseId(parseId);
            stockStatementIncomeChargesInfo.setUserId(stkTaxDetail.getUserId());
            stockStatementIncomeChargesInfo.setAccountId(stkTaxDetail.getAccountId());
            stockStatementIncomeChargesInfo.setStockCode(stkTaxDetail.getStockCode());
            stockStatementIncomeChargesInfo.setMarketCode(MarketCodeEnum.US.getValue());
            String businessType=null;
            if (TaxFeeTypeEnum.ADR.getValue().equals(stkTaxDetail.getTaxType())) {
                businessType = StatementBusinessTypeEnum.ADR.getBusinessType();
            } else if (TaxFeeTypeEnum.FTT.getValue().equals(stkTaxDetail.getTaxType())) {
                businessType = StatementBusinessTypeEnum.FTT.getBusinessType();
            }
            stockStatementIncomeChargesInfo.setBusinessType(businessType);
            stockStatementIncomeChargesInfo.setStatementDate(DateUtil.parse(stkTaxDetail.getLatestApplyDebitTime(),DateUtil.FORMAT_SHORT));
            setName(stockStatementIncomeChargesInfo);

            stockStatementIncomeChargesInfo.setAmount(stkTaxDetail.getAmt());
            stockStatementIncomeChargesInfo.setCurrency(stkTaxDetail.getCurrency());
            result.add(stockStatementIncomeChargesInfo);
        });


    }

    void setESBData(List<StockStatementIncomeChargesInfo> result,Date tradeDate,String parseId){
        List<CompanyActionExerciseDetailDTO> exerciseDetailList = stockTradeManager.queryActionExerciseData(tradeDate);
        for (CompanyActionExerciseDetailDTO exerciseDetailDTO : exerciseDetailList) {
            //供股
            if (ActionTypeEnum.RIGHTS_SUBSCRIPTION.getValue().equals(exerciseDetailDTO.getActionType())) {
               if (CompanyActionActivityTypeEnum.RIGHTS_ISSUE_ALLOTMENT.getValue().equals(exerciseDetailDTO.getActivityType())) {
                    //如果deduct和confirm在同一天，则还需要补充deduct数据
                    if (tradeDate.compareTo(exerciseDetailDTO.getDeductDate()) == 0) {
                        //补充deduct数据
                        result.add(buildESBData( tradeDate, StatementBusinessTypeEnum.ESB.getBusinessType(), exerciseDetailDTO,parseId));
                    }

                }
            }

            //回购
            if (ActionTypeEnum.OFFER_TO_BUY_BACK.getValue().equals(exerciseDetailDTO.getActionType())) {
                if (CompanyActionActivityTypeEnum.OFFER_TO_BUY_BACK_ALLOTMENT.getValue().equals(exerciseDetailDTO.getActivityType())) {
                    //如果deduct和confirm在同一天，则还需要补充deduct数据
                    if (tradeDate.compareTo(exerciseDetailDTO.getDeductDate()) != 0) {
                        //补充confirm数据
                        result.add(buildESBData(tradeDate, StatementBusinessTypeEnum.TPA.getBusinessType(), exerciseDetailDTO, parseId));
                    }

                }

            }
        }

    }

    private StockStatementIncomeChargesInfo buildESBData(Date tradeDate, String businessType, CompanyActionExerciseDetailDTO exerciseDetailDTO,String parseId) {
        StockStatementIncomeChargesInfo stockStatementIncomeChargesInfo=new StockStatementIncomeChargesInfo();
        stockStatementIncomeChargesInfo.setBizId(exerciseDetailDTO.getRightsId());
        stockStatementIncomeChargesInfo.setParseId(parseId);
        stockStatementIncomeChargesInfo.setUserId(exerciseDetailDTO.getUserId());
        stockStatementIncomeChargesInfo.setAccountId(exerciseDetailDTO.getAccountId());
        stockStatementIncomeChargesInfo.setStockCode(exerciseDetailDTO.getStockCode());
        stockStatementIncomeChargesInfo.setMarketCode(exerciseDetailDTO.getMarketCode());
        stockStatementIncomeChargesInfo.setBusinessType(businessType);
        stockStatementIncomeChargesInfo.setStatementDate(tradeDate);
//        stockStatementIncomeChargesInfo.setStockNameEn(exerciseDetailDTO.getStockName());

        setName(stockStatementIncomeChargesInfo);
        stockStatementIncomeChargesInfo.setCurrency(exerciseDetailDTO.getIssueCurrency());
        if (StatementBusinessTypeEnum.ESB.getBusinessType().equals(businessType)) {
            stockStatementIncomeChargesInfo.setAmount(exerciseDetailDTO.getDeductAmt());
        }
        if (StatementBusinessTypeEnum.TPA.getBusinessType().equals(businessType)){
            stockStatementIncomeChargesInfo.setAmount(exerciseDetailDTO.getConfirmAmt());
        }

        return stockStatementIncomeChargesInfo;
    }

    List<StockStatementIncomeChargesInfo>  convertStockStatementIncomeChargesInfo(List<DailyDividendDataDto> cdvList,List<StockStatementIncomeChargesInfo> result,StatementBusinessTypeEnum typeEnum,String parseId){

        cdvList.forEach( dailyDividendDataDto -> {

            StockStatementIncomeChargesInfo stockStatementIncomeChargesInfo = new StockStatementIncomeChargesInfo();
            stockStatementIncomeChargesInfo.setBizId(dailyDividendDataDto.getRightsId());
            stockStatementIncomeChargesInfo.setParseId(parseId);
            stockStatementIncomeChargesInfo.setUserId(dailyDividendDataDto.getUserId());
            stockStatementIncomeChargesInfo.setAccountId(dailyDividendDataDto.getAccountId());
            stockStatementIncomeChargesInfo.setStockCode(dailyDividendDataDto.getStockCode());
            stockStatementIncomeChargesInfo.setMarketCode(dailyDividendDataDto.getMarketCode());
            stockStatementIncomeChargesInfo.setBusinessType(typeEnum.getBusinessType());
            stockStatementIncomeChargesInfo.setStatementDate(dailyDividendDataDto.getActualPaidDate());
//            stockStatementIncomeChargesInfo.setStockNameEn(dailyDividendDataDto.getStockName());
            setName(stockStatementIncomeChargesInfo);
            stockStatementIncomeChargesInfo.setRemark(dailyDividendDataDto.getRemark());
            stockStatementIncomeChargesInfo.setAmount(dailyDividendDataDto.getOverrideAmount());
            stockStatementIncomeChargesInfo.setCurrency(dailyDividendDataDto.getIssueCurrency());
            result.add(stockStatementIncomeChargesInfo);
        });
        return result;
    }

   void  setName(StockStatementIncomeChargesInfo info){

        StockInfoResp stockInfoResp= stkInfoAgent.getStkInfo(info.getMarketCode(),info.getStockCode());
       info.setStockNameZh(stockInfoResp.getStockNameCn());
       info.setStockNameHk(stockInfoResp.getStockNameHk());
       info.setStockNameEn(stockInfoResp.getStockNameEng());
    }
    /**
     * 检查清算状态
     *
     * @param tradeDate
     * @return
     */
    private boolean checkClearStatus(Date tradeDate) {
        boolean check = true;
        //需要检查资金清算完成，持仓归档完成，资产备份完成，公司行动处理完成，成交合同处理完成
        List<String> checkTaskResultList = Arrays.asList(TaskCodeEnum.US_TRADE_CASH_SETTLE.getValue(), TaskCodeEnum.HOLDING_ARCHIVE.getValue(),
                TaskCodeEnum.COMPANY_ACTION_SETTLE_FINISH.getValue(), TaskCodeEnum.ORDER_CONTRACT_NOTE_SETTLE_FINISH.getValue(), TaskCodeEnum.ASSET_ARCHIVE.getValue());

        //查询清算任务结果表
        List<TaskResult> taskResults = stockTradeManager.queryTaskResultList(tradeDate);

        //过滤出需要检查的数据
        List<TaskResult> resultList = taskResults.stream()
                .filter(taskResult -> checkTaskResultList.contains(taskResult.getTaskCode()))
                .collect(Collectors.toList());

        for (TaskResult taskResult : resultList) {
            if (!TaskStatusEnum.SUCCESS.getValue().equals(taskResult.getStatus())) {
                log.info("【checkClearStatus】{}[{}][{}]-清算任务未结束，暂不开始日结单数据检查", tradeDate, taskResult.getTaskCode(), taskResult.getTaskName());
                check = false;
            }
        }
        return check;
    }

    /**
     * 订单数据校验
     *
     * @param tradeDate
     */
    private boolean checkOrder(Date tradeDate) {
        log.info("【checkOrder】start.....");
        boolean result = true;
        //本地数据
        List<StkOrder> localOrders = stockTradeManager.queryOrderListByDate(tradeDate);
        Map<String, StkOrder> localOrderMap = localOrders.stream().collect(Collectors.toMap(StkOrder::getOrderNo, Function.identity()));

        //ttl数据
        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMATDAY);
        List<TdStockStatementData> ttlOrders = this.queryPeriodDataByFormatType(MarketCodeEnum.US.getTtlValue(), tradeDateStr, tradeDateStr,
                StatementTypeEnum.DAILY.getType(), null, StatementFormatTypeEnum.ORDER.getFormatType());

        log.info("【checkOrder】localCount:{},ttlCount:{}", localOrders.size(), ttlOrders.size());
        //转换为map
        Map<String, TdStockStatementData> ttlOrderMap = transferStatementOrderData2Map(ttlOrders);

        //对比
        //用迭代器遍历localOrderMap
        Iterator<Map.Entry<String, StkOrder>> iterator = localOrderMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, StkOrder> next = iterator.next();
            String key = next.getKey();
            StkOrder stkOrder = next.getValue();

            if (ttlOrderMap.containsKey(key)) {
                TdStockStatementData ttlOrder = ttlOrderMap.get(key);
                //比较netAmount
                BigDecimal ttlNetAmount = new BigDecimal(ttlOrder.getParam12());
                if (ttlNetAmount.compareTo(stkOrder.getNetAmount()) != 0) {
                    log.info("【checkOrder】{},{},{},netAmount不一致：TTL:{}-LOCAL:{}", tradeDateStr, stkOrder.getOrderNo(), ttlOrder.getParam4(), ttlNetAmount, stkOrder.getNetAmount());
                    result = false;
                }
                //比较businessQty
                BigDecimal ttlBusinessQty = new BigDecimal(ttlOrder.getParam9()).abs();
                if (ttlBusinessQty.compareTo(stkOrder.getBusinessQty()) != 0) {
                    log.info("【checkOrder】{},{},{},businessQty不一致：TTL:{}-LOCAL:{}", tradeDateStr, stkOrder.getOrderNo(), ttlOrder.getParam4(), ttlBusinessQty, stkOrder.getBusinessQty());
                    result = false;
                }

                iterator.remove();
                ttlOrderMap.remove(key);
            }
        }

        for (Map.Entry<String, StkOrder> entry : localOrderMap.entrySet()) {
            log.info("【checkOrder】{}本地多的数据：{}", tradeDateStr, entry.getKey());
            result = false;
        }

        for (Map.Entry<String, TdStockStatementData> entry : ttlOrderMap.entrySet()) {
            log.info("【checkOrder】{}ttl多的数据：{}", tradeDateStr, entry.getKey());
            result = false;
        }
        if (result) {
            log.info("【dailyStatementDataCheck-checkOrder】{}end,result success.....", tradeDateStr);
        } else {
            log.error("【dailyStatementDataCheck-checkOrder】{}end,result fail.....", tradeDateStr);
        }
        return result;
    }

    /**
     * 检查派息数据
     *
     * @param tradeDate
     * @param dividendList
     * @return
     */
    private boolean checkActionDividend(String statementDate, Date tradeDate, List<TdStockStatementData> dividendList, List<CompanyActionDetailInfoDTO> localDividendList) {
        log.info("【checkActionDividend】start.....");
        boolean result = true;
        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMATDAY);

        //转换本地资金数据为map，key: actionId_accNo, value: CompanyActionCapitalInfoDTO
        Map<String, CompanyActionDetailInfoDTO> localCapitalMap = localDividendList.stream()
                .collect(Collectors.toMap(t -> t.getActionId() + "_" + t.getAccountId(),
                        Function.identity()));

        //转换ttl资金数据为map，key: actionId_accNo, value: TdStockStatementData
        Map<String, TdStockStatementData> ttlCapitalMap = dividendList.stream()
                .collect(Collectors.toMap(t -> t.getParam4() + "_" + t.getAccNo(),
                        Function.identity()));

        //迭代器遍历ttlCapitalMap
        Iterator<Map.Entry<String, TdStockStatementData>> ttlCapitalIterator = ttlCapitalMap.entrySet().iterator();
        while (ttlCapitalIterator.hasNext()) {
            Map.Entry<String, TdStockStatementData> ttlCapitalEntry = ttlCapitalIterator.next();
            String key = ttlCapitalEntry.getKey();
            TdStockStatementData ttlCapitalData = ttlCapitalEntry.getValue();

            CompanyActionDetailInfoDTO dividendData = localCapitalMap.get(key);
            if (dividendData != null) {
                BigDecimal ttlAmt = new BigDecimal(ttlCapitalData.getParam10());
                BigDecimal localAmt = dividendData.getOverrideAmount().subtract(dividendData.getFee()).subtract(dividendData.getUsTaxFee());
                if (ttlAmt.compareTo(localAmt) != 0) {
                    log.info("【checkActionDividend】{}-派息金额不一致，key:{}, ttlAmt:{},localAmt:{}", tradeDateStr, key, ttlAmt, localAmt);
                    result = false;
                }
                ttlCapitalIterator.remove();
                localCapitalMap.remove(key);
            }
        }
        //遍历ttlCapitalMap，剩余数据为ttl多的数据，打印错误日志
        for (Map.Entry<String, TdStockStatementData> entry : ttlCapitalMap.entrySet()) {
            log.info("【checkActionDividend】{}-ttl多的数据：{}", tradeDateStr, entry.getKey());
            result = false;
        }

        List<CompanyActionDetailInfoDTO> generationList = new ArrayList<>();
        //遍历localCapitalMap，剩余数据为local多的数据，打印错误日志
        for (Map.Entry<String, CompanyActionDetailInfoDTO> entry : localCapitalMap.entrySet()) {
            CompanyActionDetailInfoDTO entryValue = entry.getValue();
            if (entryValue.getOverrideAmount().compareTo(BigDecimal.ZERO) == 0) {
                log.info("【checkActionDividend】{}-过滤local派息为0的数据：{}", tradeDateStr, entry.getKey());
                continue;
            }

            log.info("【checkActionDividend】{}-local多的数据：{}", tradeDateStr, entry.getKey());

            if (systemConfig.isStockStatementDataCheckAutoInsert()) {
                generationList.add(entryValue);
            }

            result = false;
        }
        //自动补数据
        if (!CollectionUtils.isEmpty(generationList)) {
            List<TdStockStatementData> tdStockStatementData =
                    stockStatementDataBusinessService.generationTradeChangeDividend(getGenerationBusinessId(tradeDateStr), statementDate, tradeDate, generationList);

            stockStatementDataMapper.insertBatch(tdStockStatementData);
            log.info("【dailyStatementDataCheck-checkActionDividend】{}，补充数据完成，size:{}", tradeDateStr, tdStockStatementData.size());
        }
        if (result) {
            log.info("【dailyStatementDataCheck-checkActionDividend】{}end,result success.....", tradeDateStr);
        } else {
            log.error("【dailyStatementDataCheck-checkActionDividend】{}end,result fail.....", tradeDateStr);
        }

        return result;
    }

    private String getGenerationBusinessId(String tradeDateStr) {
        return "H_" + tradeDateStr;
    }

    /**
     * 校验公司行动小数股类型
     *
     * @param tradeDate
     * @param fractionList
     */
    private boolean checkActionFraction(String statementDate, Date tradeDate, List<TdStockStatementData> fractionList, List<CompanyActionDetailInfoDTO> localFractionList) {
        log.info("【checkActionFraction】start.....");
        boolean result = true;
        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMATDAY);

        //ttl数据转为map
        Map<String, List<TdStockStatementData>> ttlDataList = fractionList.stream().collect(Collectors.groupingBy(t -> t.getParam4() + "_" + t.getAccNo()));

        //遍历本地的所有数据
        for (CompanyActionDetailInfoDTO actionDetailInfoDTO : localFractionList) {
            String key = actionDetailInfoDTO.getActionId() + "_" + actionDetailInfoDTO.getAccountId();

            List<TdStockStatementData> ttlData = ttlDataList.get(key);

            if (isBonusShareOrRightsIssue(actionDetailInfoDTO)) {
                //处理红股和股权
                result = handleBonusShareOrRightsIssue(actionDetailInfoDTO, key, ttlData, statementDate, tradeDate, result);
            } else {
                //处理拆合换
                result = handleOtherActionTypes(actionDetailInfoDTO, ttlData, statementDate, tradeDate, result);
            }
        }
        if (result) {
            log.info("【dailyStatementDataCheck-checkActionFraction】{} end,result success.....", tradeDateStr);
        } else {
            log.error("【dailyStatementDataCheck-checkActionFraction】{} end,result fail.....", tradeDateStr);
        }

        return result;
    }

    private boolean handleDetailResult(boolean result, boolean check) {
        if (!check) {
            return false;
        }
        return result;
    }

    /**
     * 校验公司行动小数股类型详情
     *
     * @param actionDetailInfoDTO
     * @param ttlData
     * @param businessType
     */
    private boolean checkActionFractionDetail(String statementDate, Date tradeDate,
                                              CompanyActionDetailInfoDTO actionDetailInfoDTO, List<TdStockStatementData> ttlData, String businessType) {
        boolean result = true;
        String key = actionDetailInfoDTO.getActionId() + "_" + actionDetailInfoDTO.getAccountId();
        BigDecimal bookCloseQty = actionDetailInfoDTO.getBookCloseQty();
        BigDecimal overrideAmount = actionDetailInfoDTO.getOverrideAmount();

        //源股票和目标股票
        String stockCode = actionDetailInfoDTO.getStockCode();
        String issueStockCode = actionDetailInfoDTO.getIssueStockCode();

        //证券取
        if (bookCloseQty.compareTo(BigDecimal.ZERO) > 0) {
            boolean check = true;
            //证券取要用负数,取的是bookcloseqty，取的是源股票
            actionDetailInfoDTO.setStockCode(issueStockCode);
            actionDetailInfoDTO.setIssueStockCode(stockCode);
            actionDetailInfoDTO.setOverrideAmount(bookCloseQty.negate());
            if (CollectionUtils.isEmpty(ttlData)) {
                check = handleMissingData(StatementInOutEnum.OUT, statementDate, tradeDate, key, actionDetailInfoDTO);
            } else {
                check = handleValidData(StatementInOutEnum.OUT, statementDate, tradeDate, key, ttlData, bookCloseQty, actionDetailInfoDTO, businessType);
            }
            if (!check) {
                result = false;
            }
        }

        //证券存
        if (overrideAmount.compareTo(BigDecimal.ZERO) > 0) {
            boolean check = true;
            //证券存是用正数,存的是override_amount，存的是目标股票
            actionDetailInfoDTO.setStockCode(stockCode);
            actionDetailInfoDTO.setIssueStockCode(issueStockCode);
            actionDetailInfoDTO.setOverrideAmount(overrideAmount);
            if (CollectionUtils.isEmpty(ttlData)) {
                check = handleMissingData(StatementInOutEnum.IN, statementDate, tradeDate, key, actionDetailInfoDTO);
            } else {
                check = handleValidData(StatementInOutEnum.IN, statementDate, tradeDate, key, ttlData, overrideAmount, actionDetailInfoDTO, businessType);
            }
            if (!check) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 判断是红股和派送股权类型
     *
     * @param actionDetailInfoDTO
     * @return
     */
    private boolean isBonusShareOrRightsIssue(CompanyActionDetailInfoDTO actionDetailInfoDTO) {
        return ActionTypeEnum.BONUS_SHARE.getValue().equals(actionDetailInfoDTO.getActivityType())
                || ActionTypeEnum.RIGHTS_ISSUE.getValue().equals(actionDetailInfoDTO.getActivityType());
    }

    /**
     * 处理红股和派送股权类型
     *
     * @param actionDetailInfoDTO
     * @param key
     * @param ttlData
     * @param statementDate
     * @param tradeDate
     * @param result
     * @return
     */
    private boolean handleBonusShareOrRightsIssue(CompanyActionDetailInfoDTO actionDetailInfoDTO, String key,
                                                  List<TdStockStatementData> ttlData, String statementDate, Date tradeDate, boolean result) {
        if (actionDetailInfoDTO.getOverrideAmount().compareTo(BigDecimal.ZERO) == 0) {
            return result;
        }

        StatementBusinessTypeEnum businessTypeEnum = determineBusinessType(actionDetailInfoDTO);

        if (CollectionUtils.isEmpty(ttlData)) {
            return handleMissingData(StatementInOutEnum.IN, statementDate, tradeDate, key, actionDetailInfoDTO);
        } else {
            boolean check = checkActionBonusDetail(statementDate, tradeDate, actionDetailInfoDTO, ttlData, businessTypeEnum.getBusinessType());
            return check && result;
        }
    }

    private StatementBusinessTypeEnum determineBusinessType(CompanyActionDetailInfoDTO actionDetailInfoDTO) {
        return ActionTypeEnum.RIGHTS_ISSUE.getValue().equals(actionDetailInfoDTO.getActivityType())
                ? StatementBusinessTypeEnum.BRT
                : (actionDetailInfoDTO.getStockCode().equals(actionDetailInfoDTO.getIssueStockCode())
                ? StatementBusinessTypeEnum.BSH
                : StatementBusinessTypeEnum.SCAS);
    }

    /**
     * 处理拆合换类型数据
     *
     * @param actionDetailInfoDTO
     * @param ttlData
     * @param statementDate
     * @param tradeDate
     * @param result
     * @return
     */
    private boolean handleOtherActionTypes(CompanyActionDetailInfoDTO actionDetailInfoDTO, List<TdStockStatementData> ttlData, String statementDate, Date tradeDate, boolean result) {
        if (ActionTypeEnum.SHARE_SPLIT.getValue().equals(actionDetailInfoDTO.getActivityType())) {
            //拆股
            return handleDetailResult(result, checkActionFractionDetail(statementDate, tradeDate, actionDetailInfoDTO, ttlData, StatementBusinessTypeEnum.SSP.getBusinessType()));
        } else if (ActionTypeEnum.JOINT_STOCK.getValue().equals(actionDetailInfoDTO.getActivityType())) {
            //合股
            return handleDetailResult(result, checkActionFractionDetail(statementDate, tradeDate, actionDetailInfoDTO, ttlData, StatementBusinessTypeEnum.SCS.getBusinessType()));
        } else if (ActionTypeEnum.SHARE_SWAP.getValue().equals(actionDetailInfoDTO.getActivityType())) {
            //换股
            BigDecimal issueRatioPer = actionDetailInfoDTO.getIssueRatioPer();
            BigDecimal issueRatioDelivery = actionDetailInfoDTO.getIssueRatioDelivery();
            if (issueRatioPer.compareTo(issueRatioDelivery) > 0) {
                return handleDetailResult(result, checkActionFractionDetail(statementDate, tradeDate, actionDetailInfoDTO, ttlData, StatementBusinessTypeEnum.SCS.getBusinessType()));
            } else {
                return handleDetailResult(result, checkActionFractionDetail(statementDate, tradeDate, actionDetailInfoDTO, ttlData, StatementBusinessTypeEnum.SSP.getBusinessType()));
            }
        }
        return result;
    }

    /**
     * 处理数据缺失的情况
     *
     * @param statementInOutEnum
     * @param statementDate
     * @param tradeDate
     * @param key
     * @param actionDetailInfoDTO
     */
    private boolean handleMissingData(StatementInOutEnum statementInOutEnum, String statementDate, Date tradeDate, String key, CompanyActionDetailInfoDTO actionDetailInfoDTO) {
        log.info("【checkActionFraction】{}, ttl缺少证券" + statementInOutEnum.getDesc() + "数据", key);

        if (systemConfig.isStockStatementDataCheckAutoInsert()) {
            log.info("自动补充checkActionFraction证券" + statementInOutEnum.getDesc() + "数据");
            List<TdStockStatementData> dataList = stockStatementDataBusinessService.generationTradeChangeFraction(getGenerationBusinessId(DateUtil.format(tradeDate, DateUtil.FORMATDAY)),
                    statementDate, tradeDate, Collections.singletonList(actionDetailInfoDTO));

            stockStatementDataMapper.insertBatch(dataList);
        }
        return false;
    }

    /**
     * 处理数据校验
     *
     * @param statementInOutEnum
     * @param statementDate
     * @param tradeDate
     * @param key
     * @param ttlData
     * @param actionQty
     * @param actionDetailInfoDTO
     * @param businessType
     */
    private boolean handleValidData(StatementInOutEnum statementInOutEnum, String statementDate, Date tradeDate, String key, List<TdStockStatementData> ttlData, BigDecimal actionQty, CompanyActionDetailInfoDTO actionDetailInfoDTO, String businessType) {
        boolean result = true;
        List<TdStockStatementData> filterData = ttlData.stream()
                .filter(t -> {
                    if (t.getParam5().equals(businessType)) {
                        if (StatementInOutEnum.OUT.equals(statementInOutEnum) && new BigDecimal(t.getParam9()).compareTo(BigDecimal.ZERO) < 0) {
                            return true;
                        }
                        if (StatementInOutEnum.IN.equals(statementInOutEnum) && new BigDecimal(t.getParam9()).compareTo(BigDecimal.ZERO) > 0) {
                            return true;
                        }
                    }
                    return false;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterData)) {
            result = handleMissingData(statementInOutEnum, statementDate, tradeDate, key, actionDetailInfoDTO);
        } else {
            TdStockStatementData actionData = filterData.get(0);
            BigDecimal actionDataQty = new BigDecimal(actionData.getParam9());
            if (actionDataQty.abs().compareTo(actionQty) != 0) {
                log.info("【checkActionFraction】{},证券" + statementInOutEnum.getDesc() + "数量不一致，ttl:{},local:{}", key, actionDataQty.abs(), actionQty);
                result = false;
            }
        }
        return result;
    }

    /**
     * 校验公司行动小数股类型详情(红股)
     *
     * @param statementDate
     * @param tradeDate
     * @param actionDetailInfoDTO
     * @param ttlData
     * @param businessType
     */
    private boolean checkActionBonusDetail(String statementDate, Date tradeDate,
                                           CompanyActionDetailInfoDTO actionDetailInfoDTO, List<TdStockStatementData> ttlData, String businessType) {
        log.info("【checkActionBonusDetail】start.....");
        boolean result = true;

        List<TdStockStatementData> statementData = ttlData.stream()
                .filter(t -> t.getParam5().equals(businessType))
                .collect(Collectors.toList());

        String key = actionDetailInfoDTO.getActionId() + "_" + actionDetailInfoDTO.getAccountId();
        BigDecimal overrideAmount = actionDetailInfoDTO.getOverrideAmount();

        if (CollectionUtils.isEmpty(statementData)) {
            log.info("【checkActionFraction-bonus】{},ttl缺少证券存数据", key);
            if (systemConfig.isStockStatementDataCheckAutoInsert()) {
                log.info("checkActionBonusDetail自动补充证券存数据");
                List<TdStockStatementData> dataList = stockStatementDataBusinessService.generationTradeChangeFraction(getGenerationBusinessId(DateUtil.format(tradeDate, DateUtil.FORMATDAY)),
                        statementDate, tradeDate, Collections.singletonList(actionDetailInfoDTO));

                stockStatementDataMapper.insertBatch(dataList);
            }
            return false;
        }
        TdStockStatementData depositData = statementData.get(0);
        BigDecimal depositQty = new BigDecimal(depositData.getParam9());
        if (depositQty.compareTo(overrideAmount) != 0) {
            log.info("【checkActionFraction-bonus】{},证券存数量不一致，ttl:{},local:{}", key, depositQty, overrideAmount);
            result = false;
        }
        log.info("【checkActionBonusDetail】end,result:{}.....", result);
        return result;
    }

    /**
     * 转换结单订单数据为Map
     *
     * @param tdStockStatementData
     * @return
     */
    private Map<String, TdStockStatementData> transferStatementOrderData2Map(List<TdStockStatementData> tdStockStatementData) {
        return tdStockStatementData.stream().collect(Collectors.toMap(t -> {
                            //先从资金库的订单交收数据查询本地orderNo
                            List<CounterFileInfo> counterFileInfos = counterFileInfoExtendMapper.queryByReleaseReferenceAndLinkupTypeAndTxnTypeId(t.getParam4(),
                                    Arrays.asList(TtlLinkupTypeEnum.CONTRACT.getValue()),
                                    Arrays.asList(TtlTxnTypeIdEnum.BUY_CONTRACT.getValue(), TtlTxnTypeIdEnum.SELL_CONTRACT.getValue()));
                            if (!CollectionUtils.isEmpty(counterFileInfos)) {
                                log.debug("counterFront已查询到本地订单号：{}", counterFileInfos.get(0).getLocalOrderNo());
                                return counterFileInfos.get(0).getLocalOrderNo();
                            }
                            //查不到的话，用接口从TTL查询
                            StkOrder stkOrder = stockTradeManager.queryOrderInByBoNo(t.getParam4());
                            if (null == stkOrder) {
                                log.info("未查询到订单信息，boNo:{}", t.getParam4());
                                return t.getParam4();
                            }
                            return stkOrder.getOrderNo();
                        }, Function.identity()
                )
        );
    }

    /**
     * 校验exercise数据
     *
     * @param tradeDate
     * @param exerciseChangeList
     * @return
     */
    private boolean checkActionExercise(Date tradeDate, List<TdStockStatementData> exerciseChangeList) {
        log.info("【checkActionExercise】start.....");
        boolean result = true;
        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMATDAY);

        //把ttl的数据转成map
        Map<String, List<TdStockStatementData>> ttlDataMap = exerciseChangeList.stream()
                .collect(Collectors.groupingBy(t -> t.getParam4() + "_" + t.getAccNo()));

        //查询本地数据
        List<CompanyActionExerciseDetailDTO> exerciseDetailDTOS = stockTradeManager.queryActionExerciseData(tradeDate);
        Map<String, CompanyActionExerciseDetailDTO> localDataMap = exerciseDetailDTOS.stream()
                .collect(Collectors.toMap(t -> t.getSubscriptionId() + "_" + t.getAccountId(), Function.identity()));

        //遍历localDataMap
        for (Map.Entry<String, CompanyActionExerciseDetailDTO> exerciseDetailDTOEntry : localDataMap.entrySet()) {
            String key = exerciseDetailDTOEntry.getKey();
            CompanyActionExerciseDetailDTO localDetail = exerciseDetailDTOEntry.getValue();

            List<TdStockStatementData> ttlDataList = ttlDataMap.get(key);
            if (CollectionUtils.isEmpty(ttlDataList)) {
                log.info("【checkActionExercise】{}-本地多的数据:{}", tradeDateStr, key);
                result = false;

                continue;
            }
            //供股
            if (ActionTypeEnum.RIGHTS_SUBSCRIPTION.getValue().equals(localDetail.getActionType())) {
                //deduct
                if (CompanyActionActivityTypeEnum.RIGHTS_SUBSCRIPTION_DEDUCT.getValue().equals(localDetail.getActivityType())) {
                    //供股deduct校验
                    result = checkExerciseIssueDeduct(key, tradeDateStr, ttlDataList, localDetail);

                    continue;
                } else if (CompanyActionActivityTypeEnum.RIGHTS_ISSUE_ALLOTMENT.getValue().equals(localDetail.getActivityType())) {
                    //confirm
                    //如果deduct和confirm在同一天，则还需要校验deduct数据
                    if (tradeDate.compareTo(localDetail.getDeductDate()) == 0) {
                        result = checkExerciseIssueDeduct(key, tradeDateStr, ttlDataList, localDetail);
                    }
                    //查找对应EAL类型数据
                    Optional<TdStockStatementData> ealDataOptional = ttlDataList.stream()
                            .filter(t -> t.getParam5().equals(StatementBusinessTypeEnum.EAL.getBusinessType()))
                            .findFirst();
                    if (!ealDataOptional.isPresent()) {
                        log.info("【checkActionExercise】{}-TTL少了供股EAL-allot的数据:{}", tradeDateStr, key);
                        result = false;

                        continue;
                    }
                    TdStockStatementData ealData = ealDataOptional.get();
                    BigDecimal confirmQty = new BigDecimal(ealData.getParam9());
                    if (confirmQty.compareTo(localDetail.getConfirmQty()) != 0) {
                        log.info("【checkActionExercise】{}-供股confirm数量不一致的:{}，ttl:{},local:{}", tradeDateStr, key, confirmQty, localDetail.getConfirmQty());
                        result = false;

                        continue;
                    }
                }
            }

            //回购
            if (ActionTypeEnum.RIGHTS_SUBSCRIPTION.getValue().equals(localDetail.getActionType())) {
                //deduct
                if (CompanyActionActivityTypeEnum.OFFER_TO_BUY_BACK_DEDUCT.getValue().equals(localDetail.getActivityType())) {
                    result = checkExerciseBuybackDeduct(key, tradeDateStr, ttlDataList, localDetail);
                } else if (CompanyActionActivityTypeEnum.OFFER_TO_BUY_BACK_ALLOTMENT.getValue().equals(localDetail.getActivityType())) {
                    //如果deduct和confirm在同一天，则还需要校验deduct数据
                    if (tradeDate.compareTo(localDetail.getDeductDate()) == 0) {
                        result = checkExerciseIssueDeduct(key, tradeDateStr, ttlDataList, localDetail);
                    }
                    //查找对应EAL类型数据
                    Optional<TdStockStatementData> tpaDataOptional = ttlDataList.stream()
                            .filter(t -> t.getParam5().equals(StatementBusinessTypeEnum.TPA.getBusinessType()))
                            .findFirst();
                    if (!tpaDataOptional.isPresent()) {
                        log.info("【checkActionExercise】{}-TTL少了回购TPA-allot的数据:{}", tradeDateStr, key);
                        result = false;

                        continue;
                    }
                    TdStockStatementData ealData = tpaDataOptional.get();
                    BigDecimal confirmAmount = new BigDecimal(ealData.getParam10());
                    if (confirmAmount.compareTo(localDetail.getConfirmAmt()) != 0) {
                        log.info("【checkActionExercise】{}-回购confirm金额不一致的:{}，ttl:{},local:{}", tradeDateStr, key, confirmAmount, localDetail.getConfirmAmt());
                        result = false;
                    }
                }
            }
        }
        if (result) {
            log.info("【dailyStatementDataCheck-checkActionExercise】{} end,result success.....", tradeDateStr);
        } else {
            log.error("【dailyStatementDataCheck-checkActionExercise】{} end,result fail.....", tradeDateStr);
        }
        return result;
    }

    /**
     * 校验供股deduct数据
     *
     * @param key
     * @param tradeDateStr
     * @param ttlDataList
     * @param localDetail
     * @return
     */
    private boolean checkExerciseIssueDeduct(String key, String tradeDateStr, List<TdStockStatementData> ttlDataList, CompanyActionExerciseDetailDTO localDetail) {
        boolean result = true;

        //查找对应ESB类型数据
        Optional<TdStockStatementData> esbDataOptional = ttlDataList.stream()
                .filter(t -> t.getParam5().equals(StatementBusinessTypeEnum.ESB.getBusinessType()))
                .findFirst();
        if (!esbDataOptional.isPresent()) {
            log.info("【checkActionExercise】{}-TTL少了供股ESB-deduct的数据:{}", tradeDateStr, key);
            return false;
        }
        //ttl供股扣券数量
        TdStockStatementData esbData = esbDataOptional.get();
        BigDecimal ttlDeductQty = new BigDecimal(esbData.getParam9());
        BigDecimal ttlDeductAmt = new BigDecimal(esbData.getParam10());

        BigDecimal deductQty = localDetail.getDeductQty();
        if (ttlDeductQty.compareTo(deductQty.negate()) != 0) {
            log.info("【checkActionExercise】{}-供股deduct数量不一致的:{}，ttl:{},local:{}", tradeDateStr, key, ttlDeductQty, deductQty);
            result = false;
        }
        BigDecimal deductAmt = localDetail.getDeductAmt();
        if (ttlDeductAmt.compareTo(deductAmt.negate()) != 0) {
            log.info("【checkActionExercise】{}-供股deduct金额不一致的:{}，ttl:{},local:{}", tradeDateStr, key, ttlDeductAmt, deductAmt);
            result = false;
        }
        return result;
    }

    /**
     * 校验回购deduct数据
     *
     * @param key
     * @param tradeDateStr
     * @param ttlDataList
     * @param localDetail
     * @return
     */
    private boolean checkExerciseBuybackDeduct(String key, String tradeDateStr, List<TdStockStatementData> ttlDataList, CompanyActionExerciseDetailDTO localDetail) {
        boolean result = true;
        //查找对应TPS类型数据
        Optional<TdStockStatementData> tpsDataOptional = ttlDataList.stream()
                .filter(t -> t.getParam5().equals(StatementBusinessTypeEnum.TPS.getBusinessType()))
                .findFirst();
        if (!tpsDataOptional.isPresent()) {
            log.info("【checkActionExercise】{}-TTL少了回购TPS-deduct的数据:{}", tradeDateStr, key);
            return false;
        }
        TdStockStatementData tpsData = tpsDataOptional.get();
        BigDecimal ttlDeductQty = new BigDecimal(tpsData.getParam9());

        BigDecimal deductQty = localDetail.getDeductQty();
        if (ttlDeductQty.compareTo(deductQty.negate()) != 0) {
            log.info("【checkActionExercise】{}-回购deduct数量不一致的:{}，ttl:{},local:{}", tradeDateStr, key, ttlDeductQty, deductQty);
            result = false;
        }
        return result;
    }


    private void buildCashSummaryDto(StatementCashSummaryDto cashSummaryDto, TdStockStatementData stockStatementData) {
        cashSummaryDto.setBusinessId(stockStatementData.getBusinessId());
        cashSummaryDto.setStatementDate(stockStatementData.getStatementDate());
        cashSummaryDto.setStatementType(stockStatementData.getStatementType());
        cashSummaryDto.setFormatType(stockStatementData.getFormatType());
        cashSummaryDto.setAccNo(stockStatementData.getAccNo());
        cashSummaryDto.setCurrency(stockStatementData.getParam1());
        cashSummaryDto.setSettledAmount(stockStatementData.getParam2());
        cashSummaryDto.setCurrentPower(stockStatementData.getParam3());
        cashSummaryDto.setFrozenAmount(stockStatementData.getParam4());
        cashSummaryDto.setWithdrawAmount(stockStatementData.getParam5());
        //银行冻结金额
        cashSummaryDto.setBankFrozenAmount(stockStatementData.getParam6());
    }

    /**
     * 校验持仓数据
     * @param statementDate
     * @param tradeDate
     */
    private boolean checkHoldingData(String statementDate, Date tradeDate) {
        log.info("【checkHoldingData】开始校验持仓数据");
        StopWatch stopWatch = new StopWatch("【checkHoldingData】开始校验持仓数据-{" + statementDate + "}");

        //查询上一个交易日
        TradeDateDiffResp tradeDateDiffResp = tradeCalendarRemoteService.tradeDateDiff(MarketCodeEnum.US.getValue(), DateUtil.format(tradeDate, DateUtil.FORMATDAY), -1);
        Date lastTradeDate = DateUtil.parse(tradeDateDiffResp.getTradeDate(), DateUtil.FORMATDAY);

        // 查询TTL数据
        stopWatch.start("数据查询");
        List<TdStockStatementData> tdStockStatementData = this.queryPeriodDataByFormatType(MarketCodeEnum.US.getTtlValue(),
                statementDate, statementDate, StatementTypeEnum.DAILY.getType(),
                null, StatementFormatTypeEnum.ASSET_SUMMARY.getFormatType());
        stopWatch.stop();

        stopWatch.start("数据处理");
        // tdStockStatementData进行内存分页处理，把list切割成500条数据的列表
        List<List<TdStockStatementData>> partition = Lists.partition(tdStockStatementData, 500);

        // 使用CompletableFuture并行处理每个分区
        List<CompletableFuture<Boolean>> futureList = partition.stream()
                .map(list -> CompletableFuture.supplyAsync(() -> checkHoldingDataByThread(list, lastTradeDate), statementCheckExecutor))
                .collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));

        // 汇总结果
        boolean success = true;
        try {
            // 等待所有CompletableFuture完成
            allFutures.get();

            // 收集并检查每个任务的结果
            for (CompletableFuture<Boolean> future : futureList) {
                boolean result = future.join();
                if (!result) {
                    success = false;
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.info("【checkHoldingData】任务执行过程中出现异常", e);
            success = false;
        }

        if (success) {
            log.info("【checkHoldingData】校验持仓数据成功");
        } else {
            log.error("【checkHoldingData】校验持仓数据失败");
        }
        stopWatch.stop();

        log.info("【checkHoldingData】校验持仓数据-{}耗时:{}", statementDate, stopWatch.prettyPrint());
        return success;
    }


    /**
     * 校验持仓数据
     * @param ttlData
     * @param lastTradeDate
     * @return
     */
    private boolean checkHoldingDataByThread(List<TdStockStatementData> ttlData, Date lastTradeDate) {
        log.info("【checkHoldingDataByThread】开始校验持仓数据");
        AtomicBoolean result = new AtomicBoolean(true);
        StringBuilder failureLog = new StringBuilder();
        //house户列表
        List<String> houseAccountList = systemConfig.getHouseAccountList();

        ttlData.forEach(stockStatementData -> {
            String accNo = stockStatementData.getAccNo();
            String stockCode = stockStatementData.getParam2();
            //如果是house户，则不处理
            if (houseAccountList.contains(accNo)) {
                return;
            }
            //查询本地数据
            StkHoldingHistory stkHoldingHistory = stockTradeManager.queryHoldingHistoryByStatementCheck(accNo, lastTradeDate, stockCode);
            if (stkHoldingHistory == null) {
                BigDecimal settledHolding = new BigDecimal(stockStatementData.getParam11());
                if (settledHolding.compareTo(BigDecimal.ZERO) != 0) {
                    result.set(false);
                    appendFailureLog(failureLog, accNo, lastTradeDate, stockCode, StatementConstants.STATEMENT_FILE_CHECK_SETTLED_QTY_COLUMN, null, settledHolding);
                }
                BigDecimal costPrice = new BigDecimal(stockStatementData.getParam12());
                if (costPrice.compareTo(BigDecimal.ZERO) != 0) {
                    result.set(false);
                    appendFailureLog(failureLog, accNo, lastTradeDate, stockCode, StatementConstants.STATEMENT_FILE_CHECK_COST_PRICE_COLUMN, null, costPrice);
                }

                BigDecimal endQty = new BigDecimal(stockStatementData.getParam6());
                if (endQty.compareTo(BigDecimal.ZERO) != 0) {
                    result.set(false);
                    appendFailureLog(failureLog, accNo, lastTradeDate, stockCode, StatementConstants.STATEMENT_FILE_CHECK_HOLD_QTY_COLUMN, null, endQty);
                }

            } else {
                BigDecimal settledHolding = new BigDecimal(stockStatementData.getParam11());
                if (settledHolding.compareTo(stkHoldingHistory.getSettledQty()) != 0) {
                    result.set(false);
                    appendFailureLog(failureLog, accNo, lastTradeDate, stockCode, StatementConstants.STATEMENT_FILE_CHECK_SETTLED_QTY_COLUMN, stkHoldingHistory.getSettledQty(), settledHolding);
                }
                BigDecimal costPrice = new BigDecimal(stockStatementData.getParam12());
                if (costPrice.compareTo(stkHoldingHistory.getCostPrice()) != 0) {
                    result.set(false);
                    appendFailureLog(failureLog, accNo, lastTradeDate, stockCode, StatementConstants.STATEMENT_FILE_CHECK_COST_PRICE_COLUMN, stkHoldingHistory.getCostPrice(), costPrice);
                }
                BigDecimal holdQty = new BigDecimal(stockStatementData.getParam6());
                if (holdQty.compareTo(stkHoldingHistory.getHoldQty()) != 0) {
                    result.set(false);
                    appendFailureLog(failureLog, accNo, lastTradeDate, stockCode, StatementConstants.STATEMENT_FILE_CHECK_HOLD_QTY_COLUMN, stkHoldingHistory.getHoldQty(), holdQty);
                }
            }
        });

        if (!result.get()) {
            // 在所有数据检查完毕后，一次性记录所有失败的日志信息
            log.info(failureLog.toString());
        }

        return result.get();
    }

    /**
     * 记录失败日志信息
     * @param log
     * @param accNo
     * @param tradeDate
     * @param stockCode
     * @param localQty
     * @param ttlQty
     */
    private void appendFailureLog(StringBuilder log, String accNo, Date tradeDate, String stockCode, String column, BigDecimal localQty, BigDecimal ttlQty) {
        if (log.length() > 0) {
            log.append("\n");
        }
        log.append("【checkHoldingData】持仓数据校验失败，accNo:").append(accNo)
                .append(",tradeDate:").append(DateUtil.format(tradeDate, DateUtil.FORMATDAY))
                .append(",stockCode:").append(stockCode)
                .append(",column:").append(column)
                .append("，local:").append(localQty != null ? localQty.toPlainString() : "None")
                .append("，ttl:").append(ttlQty.toPlainString());
    }
}
