package group.za.bank.statement.service;

import group.za.bank.statement.domain.entity.TdStatementActionPendingData;

import java.util.Date;
import java.util.List;

public interface StatementActionPendingDataService {
    /**
     * 新增待处理公司行动月结单记录
     * @param statementDataId
     * @param bankUserId
     * @param accountId
     * @param actionId
     * @param actionType
     * @param originalPeriod
     */
    void create(Long statementDataId, String bankUserId, String accountId,
                String actionId, String actionType, String originalPeriod, String marketCode, boolean uniqueCheck);

    /**
     * 根据用户ID和处理状态查询
     * @param accountId
     * @param dataStatus
     * @return
     */
    List<TdStatementActionPendingData> queryPendingList(String marketCode, String accountId, String dataStatus);

    /**
     * 根据用户ID和结单期数查询
     * @param accountId
     * @param period
     * @return
     */
    List<TdStatementActionPendingData> queryPendingListForPeriod(String marketCode, String accountId, String period);

    /**
     * 根据用户ID和结单期数数量
     * @param marketCode
     * @param accountId
     * @param period
     * @return
     */
    int countPendingListForPeriod(String marketCode, String accountId, String period);


    /**
     * 处理数据
     * @param old
     * @param period
     * @param capitalTime
     * @param remark
     */
    void processedActionCapitalData(TdStatementActionPendingData old, String period, Date capitalTime, String remark);
}
