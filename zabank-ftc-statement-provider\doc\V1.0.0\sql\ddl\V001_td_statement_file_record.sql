CREATE TABLE `td_statement_file_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
	`statement_type` int(4) DEFAULT '1' COMMENT '1-月结单，2-日结单',
  `statement_date` varchar(12) DEFAULT NULL COMMENT '结单日期:月结单-yyyyMM，日结单-yyyyMMdd',
  `data_status` int(4) DEFAULT '0' COMMENT '文件解析状态:0-未开始,1-处理中,2-成功，3-失败',
  `creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
  `gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE `uk_sfr_type_period` (statement_type,statement_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结单文件记录表';