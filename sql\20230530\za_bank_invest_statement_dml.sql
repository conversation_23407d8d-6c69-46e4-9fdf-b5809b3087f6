-- 补充上月的结单文件解析记录
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (2, '2023-05-31', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-31', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-30', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-26', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-25', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-24', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-23', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-22', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-19', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-18', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-17', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-16', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-15', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-12', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-11', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-10', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-09', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-08', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-05', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-04', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-03', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-02', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-05-01', 2);