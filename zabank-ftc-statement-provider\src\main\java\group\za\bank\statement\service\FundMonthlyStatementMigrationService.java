package group.za.bank.statement.service;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;

/**
 * <AUTHOR>
 * @Date 2023/7/13
 * @Description
 * @Version v1.0
 */
public interface FundMonthlyStatementMigrationService {

    /**
     * 基金月结单迁移
     */
    void fundMonthlyStatementMigration(String period);

    /**
     * 基金月结单迁移obskey刷新
     */
    void fundMonthlyStatementMigrationObsKeyFlush(String period);

    /**
     * 设置汇率
     */
    void setUsdToHkdRate(TdFundMonthlyStatementInfo statementInfo);
}