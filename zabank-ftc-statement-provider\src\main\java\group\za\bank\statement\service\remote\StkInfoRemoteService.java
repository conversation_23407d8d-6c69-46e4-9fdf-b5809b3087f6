package group.za.bank.statement.service.remote;

import group.za.bank.sbs.trade.model.req.feign.StockInfoReq;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;

/**
 * 股票基本信息远程接口
 *
 * <AUTHOR>
 * @date 2022/06/22
 **/
public interface StkInfoRemoteService {

    /**
     * 获取股票基本信息
     * @param stockInfoReq
     * @return
     */
    StockInfoResp getStockInfo(StockInfoReq stockInfoReq);
}
