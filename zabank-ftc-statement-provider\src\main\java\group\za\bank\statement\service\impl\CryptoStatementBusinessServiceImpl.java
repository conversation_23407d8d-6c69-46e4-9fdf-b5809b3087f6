package group.za.bank.statement.service.impl;

import cn.hutool.core.lang.Pair;
import com.google.common.collect.Maps;
import group.za.bank.ccs.tradecore.common.enums.TaskCodeEnum;
import group.za.bank.ccs.tradecore.model.entity.CcBusinessRecord;
import group.za.bank.ccs.tradecore.model.entity.CcCryptoInfo;
import group.za.bank.ccs.tradecore.model.entity.CcOrder;
import group.za.bank.ccs.tradecore.model.entity.TaskResult;
import group.za.bank.ccs.tradecore.model.resp.CryptoInfoResp;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.enums.StatementDirectionEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.dto.CryptoHoldInfoDto;
import group.za.bank.statement.entity.dto.CryptoMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.manager.CryptoTradeCoreManager;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.remote.CryptoInfoRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.bank.statement.utils.StatementDataSortUtils;
import group.za.bank.statement.utils.StockStatementNumberFormatUtils;
import group.za.invest.web.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static group.za.bank.statement.common.constants.StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.dateFormat2EngStr;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("cryptoStatementBusinessService")
public class CryptoStatementBusinessServiceImpl implements StatementBusinessService<CryptoMonthlyStatementDataDto> {

    @Autowired
    private CryptoTradeCoreManager cryptoTradeCoreManager;
    @Autowired
    private CryptoInfoRemoteService cryptoInfoRemoteService;
    @Autowired
    private ExchangeRateInfoService exchangeRateInfoService;

    @Override
    public String getBusinessType() {
        return AccountTypeEnum.CRYPTO.getValue();
    }

    @Override
    public boolean isDataPrepared(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, boolean useCache) {
        String monthLastDayStr = MonthlyUtils.getMonthLastDay(tdFundMonthlyStatementInfo.getPeriod());
        Date monthLastDay = DateUtil.parse(monthLastDayStr, DateUtil.FORMATDAY);

        TaskResult taskResult = cryptoTradeCoreManager.queryByTaskCodeAndDate(TaskCodeEnum.BACKUP_MONTHLY_ASSET_DATE.getValue(), monthLastDay);
        return null != taskResult;
    }

    @Override
    public Map<String, Object> generateDataMap(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement statementRecord,
                                               TdMonthlyStatementData statementData, String marketCode) {
        String accountId = statementData.getAccountId();

        CryptoMonthlyStatementDataDto cryptoMonthlyStatementDataDto
                = getUserCryptoStatementData(statementInfo, accountId, statementRecord);

        return JSONObject.parseObject(JsonUtils.toFormatJsonString(cryptoMonthlyStatementDataDto));
    }

    /**
     * 组装数据
     *
     * @param statementInfo
     * @param accountId
     * @param statementRecord
     * @return
     */
    private CryptoMonthlyStatementDataDto getUserCryptoStatementData(TdFundMonthlyStatementInfo statementInfo, String accountId
            , TdFundMonthlyStatement statementRecord) {

        String period = statementInfo.getPeriod();
        String businessType = statementInfo.getBusinessType();
        String userId = statementRecord.getBankUserId();
        String docLang = statementRecord.getDocLang();
        String monthFirstDay = MonthlyUtils.getMonthFirstDay(period);
        String monthLastDay = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;

        CryptoMonthlyStatementDataDto statementDataDto = new CryptoMonthlyStatementDataDto();

        //查询基本信息
        CryptoMonthlyStatementDataDto.BasicInfoDto basicInfoDto = queryBasicInfo(statementInfo, statementRecord, accountId);
        statementDataDto.setBasicInfoDto(basicInfoDto);

        //已确认交易
        List<CryptoMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderDtoList = queryCryptoOrderList(userId, monthFirstDay, monthLastDay, docLang);
        statementDataDto.setConfirmedOrderList(confirmedOrderDtoList);

        //持仓
        List<CryptoMonthlyStatementDataDto.HoldingDto> holdingDtoList = queryCryptoHoldList(period, accountId, businessType, docLang);
        statementDataDto.setHoldingList(holdingDtoList);

        return statementDataDto;
    }

    /**
     * 查询基本信息
     * @param statementInfo
     * @param statementRecord
     * @param accountId
     * @return
     */
    private CryptoMonthlyStatementDataDto.BasicInfoDto queryBasicInfo(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement statementRecord, String accountId){
        CryptoMonthlyStatementDataDto.BasicInfoDto basicInfoDto = new CryptoMonthlyStatementDataDto.BasicInfoDto();
        //查询期末持仓总市值
        Pair<BigDecimal, BigDecimal> userPeriodEndTotalMarket = getUserPeriodEndTotalMarketAndProfit(statementInfo, statementRecord, CurrencyEnum.HKD, accountId, null);
        basicInfoDto.setTotalMarket(userPeriodEndTotalMarket.getKey());
        basicInfoDto.setCurrency(CurrencyEnum.HKD.getCurrency());

        return basicInfoDto;
    }

    /**
     * 查询持仓数据
     *
     * @param period
     * @param accountId
     * @param businessType
     * @param docLang
     * @return
     */
    private List<CryptoMonthlyStatementDataDto.HoldingDto> queryCryptoHoldList(String period, String accountId, String businessType, String docLang) {
        List<CryptoMonthlyStatementDataDto.HoldingDto> holdingDtoList = new ArrayList<>();

        List<CryptoHoldInfoDto> cryptoHoldInfoDtos = cryptoTradeCoreManager.cryptoHoldList(period, accountId);
        cryptoHoldInfoDtos.forEach(t -> {
            CryptoMonthlyStatementDataDto.HoldingDto holdingDto = new CryptoMonthlyStatementDataDto.HoldingDto();
            holdingDto.setAssetId(t.getAssetId());
            holdingDto.setAssetNameEn(t.getAssetNameEn());
            holdingDto.setAssetName(I18nSupportEnum.CN_ZH.getName().equals(docLang) ? t.getAssetNameCn() : t.getAssetNameHk());
            holdingDto.setExchangeCode(t.getExchangeCode());
            holdingDto.setCurrency(t.getCurrency());
            holdingDto.setOpeningBalance(StockStatementNumberFormatUtils.formatCommaStyleStripTrailingZeros(t.getOpenVolume(), "0"));
            holdingDto.setClosingBalance(StockStatementNumberFormatUtils.formatCommaStyleStripTrailingZeros(t.getCloseVolume(), "0"));
            holdingDto.setReferencePrice(StockStatementNumberFormatUtils.statementPriceFormat(t.getClosePrice(), STATEMENT_NULL_VALUE_PLACEHOLDER));

            //市值
            BigDecimal marketValue = t.getCloseVolume().multiply(t.getClosePrice());
            holdingDto.setMarketValue(StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(marketValue, STATEMENT_NULL_VALUE_PLACEHOLDER));
            //等值港币市值
            if (BigDecimal.ZERO.compareTo(marketValue) == 0) {
                holdingDto.setHkdMarketValue(STATEMENT_NULL_VALUE_PLACEHOLDER);
            } else {
                BigDecimal marketValueHkd = exchange(marketValue, t.getCurrency(), period, businessType);
                holdingDto.setHkdMarketValue(StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(marketValueHkd, STATEMENT_NULL_VALUE_PLACEHOLDER));
            }

            holdingDtoList.add(holdingDto);
        });

        //排序规则：按照股票代码的首字母从小到大（A_Z）排列，若首字母大小相同则对比第二个字母大小，以此类推；
        return StatementDataSortUtils.sortByStringAsc(CryptoMonthlyStatementDataDto.HoldingDto::getAssetId, holdingDtoList);
    }

    /**
     * 根据汇率转换金额
     *
     * @param amount
     * @param sourceCurrency
     * @param period
     * @param businessType
     * @return
     */
    private BigDecimal exchange(BigDecimal amount, String sourceCurrency, String period, String businessType) {
        BigDecimal rate = exchangeRateInfoService.getExchangeHkdRate(sourceCurrency, period, businessType);
        log.info("StkRateInfoHisResp sourceCurrency:{},period:{},businessType:{},exchange rate:{}", sourceCurrency, period, businessType, rate);
        return amount.multiply(rate);
    }

    /**
     * 查询订单列表
     *
     * @param startDate
     * @param endDate
     * @param userId
     * @return
     */
    private List<CryptoMonthlyStatementDataDto.ConfirmedOrderDto> queryCryptoOrderList(String userId, String startDate, String endDate, String docLang) {
        List<CryptoMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderDtoList = new ArrayList<>();
        List<CcOrder> orderList = cryptoTradeCoreManager.cryptoOrderList(userId, startDate, endDate);

        //按照交易日升序
        orderList = StatementDataSortUtils.sortByDateAsc(CcOrder::getLastBusinessTime, orderList);

        orderList.forEach(t -> {
            CryptoMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto = new CryptoMonthlyStatementDataDto.ConfirmedOrderDto();
            confirmedOrderDto.setAssetId(t.getAsset());
            confirmedOrderDto.setExchangeCode(t.getExchangeCode());
            confirmedOrderDto.setCurrency(t.getFiatCurrency());

            //日期格式化为英文日期格式
            confirmedOrderDto.setTradeDate(dateFormat2EngStr(t.getLastBusinessTime()));
            confirmedOrderDto.setClearDate(dateFormat2EngStr(t.getFirstDeliveryTime()));

            //获取买卖方向
            StatementDirectionEnum direction = StatementDirectionEnum.getByTradeType(t.getDirection().toUpperCase());
            confirmedOrderDto.setDirection(direction.getTradeType());

            //获取描述信息
            String directionStr = docLang.equals(I18nSupportEnum.CN_ZH.getName()) ? direction.getDesc() : direction.getDescHk();

            Pair<String, String> cryptoAssetName = getCryptoAssetName(t.getAsset(), t.getExchangeCode(), I18nSupportEnum.US_EN.getName());
            String assetNameEn = cryptoAssetName.getKey();
            String assetName = cryptoAssetName.getValue();

            String desc = directionStr + StatementConstants.SHIFT_LINE + t.getAsset() + " " + assetNameEn;
            confirmedOrderDto.setDescription(desc);

            confirmedOrderDto.setBusinessPrice(StockStatementNumberFormatUtils.statement4RoundDownFormat(t.getDealAvgPrice(), null));
            confirmedOrderDto.setBusinessQty(StockStatementNumberFormatUtils.statementQtyFormat(t.getDealVolume(), null));
            confirmedOrderDto.setBusinessAmount(StockStatementNumberFormatUtils.statementMoneyFormat(t.getLocalDealAmt(), null));
            confirmedOrderDto.setSettleAmount(StockStatementNumberFormatUtils.statementMoneyFormat(t.getDeliveryAmt(), null));
            //佣金
            confirmedOrderDto.setCommission(StockStatementNumberFormatUtils.statementMoneyFormat(t.getDealCommission(), STATEMENT_NULL_VALUE_PLACEHOLDER));
            //交易费
            //confirmedOrderDto.setTradeFee(StockStatementNumberFormatUtils.statementMoneyFormat(t.getDealFee(), STATEMENT_NULL_VALUE_PLACEHOLDER));
            //平台费
            confirmedOrderDto.setPlatformFee(StockStatementNumberFormatUtils.statementMoneyFormat(t.getDealPlatformFee(), STATEMENT_NULL_VALUE_PLACEHOLDER));

            //成交明细
            buildBusinessRecord(t.getOrderNo(), confirmedOrderDto);

            confirmedOrderDtoList.add(confirmedOrderDto);
        });
        return confirmedOrderDtoList;
    }


    /**
     * 构建成交明细
     *
     * @param orderNo
     * @param confirmedOrderDto
     */
    private void buildBusinessRecord(String orderNo, CryptoMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto) {
        List<CryptoMonthlyStatementDataDto.OrderDetailDto> details = new ArrayList<>();

        List<CcBusinessRecord> recordQueryResp = cryptoTradeCoreManager.cryptoBusinessRecordList(orderNo);
        //合并相同价格的成交记录
        List<CcBusinessRecord> ccBusinessRecords = mergeSamePriceBusinessRecord(recordQueryResp);
        for (int i = 0; i < ccBusinessRecords.size(); i++) {
            CcBusinessRecord ccBusinessRecord = ccBusinessRecords.get(i);
            CryptoMonthlyStatementDataDto.OrderDetailDto orderDetailDto = new CryptoMonthlyStatementDataDto.OrderDetailDto();
            orderDetailDto.setSeq(String.valueOf(i + 1));
            orderDetailDto.setTransPrice(StockStatementNumberFormatUtils.formatCommaStyleStripTrailingZeros(ccBusinessRecord.getDealPrice(), null));
            orderDetailDto.setTransQty(StockStatementNumberFormatUtils.formatCommaStyleStripTrailingZeros(ccBusinessRecord.getDealVolume(), null));
            orderDetailDto.setTransAmount(StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(ccBusinessRecord.getDealAmt(), null));

            details.add(orderDetailDto);
        }
        confirmedOrderDto.setDetails(details);
    }

    /**
     * 合并相同价格的成交记录
     *
     * @param ccBusinessRecordList
     * @return
     */
    private List<CcBusinessRecord> mergeSamePriceBusinessRecord(List<CcBusinessRecord> ccBusinessRecordList) {
        List<CcBusinessRecord> resultList = new ArrayList<>();
        //使用有序的map集合
        Map<String, CcBusinessRecord> dataMap = Maps.newTreeMap();
        DecimalFormat df = new DecimalFormat("#.000000");
        for (CcBusinessRecord ccBusinessRecord : ccBusinessRecordList) {
            //金额保留两位小数
            BigDecimal dealAmt = ccBusinessRecord.getDealAmt().setScale(2, RoundingMode.DOWN);
            ccBusinessRecord.setDealAmt(dealAmt);

            //把key格式化成统一格式字符串
            String priceKey = df.format(ccBusinessRecord.getDealPrice());
            if (dataMap.containsKey(priceKey)) {
                CcBusinessRecord existData = dataMap.get(priceKey);
                //叠加成交数量和金额
                existData.setDealVolume(existData.getDealVolume().add(ccBusinessRecord.getDealVolume()));
                existData.setDealAmt(existData.getDealAmt().add(ccBusinessRecord.getDealAmt()));
                dataMap.put(priceKey, existData);
            } else {
                dataMap.put(priceKey, ccBusinessRecord);
            }
        }
        //遍历map得到最终结果
        dataMap.forEach((key, value) -> {
            resultList.add(value);
        });
        return resultList;
    }

    /**
     * 获取币对名称
     *
     * @param assetId
     * @param exchangeCode
     * @param docLang
     * @return
     */
    private Pair<String, String> getCryptoAssetName(String assetId, String exchangeCode, String docLang) {
        CcCryptoInfo ccCryptoInfo = cryptoTradeCoreManager.getCcCryptoInfo(assetId);

        String nameCn = StringUtils.isEmpty(ccCryptoInfo.getNameCn()) ? ccCryptoInfo.getNameEng() : ccCryptoInfo.getNameCn();
        String nameHk = StringUtils.isEmpty(ccCryptoInfo.getNameHk()) ? ccCryptoInfo.getNameEng() : ccCryptoInfo.getNameHk();

        return new Pair<>(ccCryptoInfo.getNameEng(), docLang.equals(I18nSupportEnum.CN_ZH.getName()) ? nameCn : nameHk);
    }

    /**
     * 获取比对名称
     *
     * @param assetId
     * @param exchangeCode
     * @param docLang
     * @return
     */
    private String getCryptoAssetNameByDocLang(String assetId, String exchangeCode, String docLang) {
        CryptoInfoResp cryptoInfoResp = cryptoInfoRemoteService.queryCryptoInfo(exchangeCode, assetId);

        String nameCn = StringUtils.isEmpty(cryptoInfoResp.getNameCn()) ? cryptoInfoResp.getNameEng() : cryptoInfoResp.getNameCn();
        String nameHk = StringUtils.isEmpty(cryptoInfoResp.getNameHk()) ? cryptoInfoResp.getNameEng() : cryptoInfoResp.getNameHk();

        return docLang.equals(I18nSupportEnum.CN_ZH.getName()) ? cryptoInfoResp.getNameEng() + "" + nameCn
                : cryptoInfoResp.getNameEng() + "" + nameHk;
    }


    @Override
    public CryptoMonthlyStatementDataDto parseDataMap2Dto(Map<String, Object> businessDataMap) {
        if (businessDataMap == null) {
            return null;
        }
        return JSONObject.parse(JsonUtils.toJsonString(businessDataMap), CryptoMonthlyStatementDataDto.class);
    }

    @Override
    public boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo,
                                               UserInvestClientInfoDto userInvestClientInfoDto, String marketCode) {
        String bankUserId = userInvestClientInfoDto.getBankUserId();
        String accountId = userInvestClientInfoDto.getAccountId();
        return isUserNeedGenerateStatement(tdFundMonthlyStatementInfo, bankUserId, accountId, marketCode);
    }

    @Override
    public boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, String userId, String accountId, String marketCode) {
        String monthFirstDay = MonthlyUtils.getMonthFirstDay(tdFundMonthlyStatementInfo.getPeriod());
        String monthLastDay = MonthlyUtils.getMonthLastDay(tdFundMonthlyStatementInfo.getPeriod()) + StatementConstants.DAY_END_TIME;

        //查询是否有订单信息
        List<CcOrder> orderQueryResp = cryptoTradeCoreManager.cryptoOrderList(userId, monthFirstDay, monthLastDay);
        if (!CollectionUtils.isEmpty(orderQueryResp)) {
            return true;
        }

        List<CryptoHoldInfoDto> cryptoHoldInfoDtos = cryptoTradeCoreManager.cryptoHoldList(tdFundMonthlyStatementInfo.getPeriod(), accountId);
        if (!CollectionUtils.isEmpty(cryptoHoldInfoDtos)) {
            return true;
        }
        log.info("crypto isUserNeedGenerateStatement不用生成月结单，userId:{},accountId:{}", userId, accountId);
        return false;
    }

    @Override
    public Pair<BigDecimal, BigDecimal> getUserPeriodEndTotalMarketAndProfit(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, TdFundMonthlyStatement statementRecord, CurrencyEnum targetCurrency, String accountId, String marketCode) {
        BigDecimal sumAsset = BigDecimal.ZERO;
        String period = tdFundMonthlyStatementInfo.getPeriod();
        String businessType = tdFundMonthlyStatementInfo.getBusinessType();

        // 获取本月的变动数据
        List<CryptoHoldInfoDto> holdInfoDtoList = cryptoTradeCoreManager.cryptoHoldList(period, accountId);
        for (CryptoHoldInfoDto cryptoHoldInfoDto : holdInfoDtoList) {
            if (cryptoHoldInfoDto.getCloseVolume().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            //市值
            BigDecimal marketValue = cryptoHoldInfoDto.getCloseVolume().multiply(cryptoHoldInfoDto.getClosePrice());
            BigDecimal marketValueHkd = exchange(marketValue, cryptoHoldInfoDto.getCurrency(), period, businessType);

            //与等值港币计算规则保持一致,先round down后累加
            marketValueHkd = marketValueHkd.setScale(2, RoundingMode.DOWN);

            sumAsset = sumAsset.add(marketValueHkd);
        }

        return Pair.of(sumAsset, BigDecimal.ZERO);
    }

    @Override
    public void statementAllProcessFinishHook(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo) {

    }
}
