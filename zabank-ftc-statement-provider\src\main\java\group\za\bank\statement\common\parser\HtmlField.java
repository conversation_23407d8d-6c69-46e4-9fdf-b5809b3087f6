package group.za.bank.statement.common.parser;

import lombok.Data;

import java.util.List;

/**
 * 要抓取的html具体字段
 *
 * <AUTHOR>
 * @Date 2023/2/9 16:37
 * @Description
 */
@Data
public class HtmlField {
    /**
     * 描述
     */
    private String desc;

    /**
     * 字段名称
     */
    private String name;
    /**
     * 数据详情path
     */
    private String dataDetailPath;

    /**
     * title所在path的位置
     */
    private String titleIndex;


    /**
     * title读取路径
     */
    private String titlePath;


    /**
     * 字段读取路径
     */
    private String path;

    /**
     * 引擎表达式
     */
    private String exp;


    /**
     * 字段路径索引值
     */
    private int index;


    /**
     * 行类型（例如: 是否作为标题）
     */
    private int rowType;


    /**
     * 模块的类型(把该字段提到标题之上)
     */
    private int blockType;

    /**
     * 是否输出html(默认只读取文本)
     */
    private String isHtml;

    /**
     * 字段默认值
     */
    private String defaultValue;

    /**
     * 标题默认值
     */
    private String defaultTitleValue;


    /**
     * 组ID（标记不同的字段是否是同一个组）
     */
    private String groupId;

    /**
     * 序号
     */
    private int seq;

    /**
     * 字段切割字符串
     */
    private String splitter;

    /**
     * 字段值单位
     */
    private String valueUnit;

    /**
     * 扩展位（json）
     */
    private String ext;

    /**
     * 是否数组（如果不是数组,则fields可以不用配置）
     */
    private String isArray;

    /**
     * 数组明细名称
     */
    private String detailFieldsName;
    /**
     * 数组明细路径
     */
    private String detailFieldBasePath;
    /**
     * 数组明细
     */
    private List<HtmlField> detailFields;


    /**
     * 支持对应字段名配置,以下是默认值
     */
    private String fieldName = "name";


    private String titleName = "title";

    private String valueName = "value";

    private String rowTypeName = "rowType";

    private String blockTypeName = "blockType";

    private String groupIdName = "groupId";

    private String splitterName = "splitter";

    private String seqName = "seq";

    private String valueUnitName = "valueUnit";

    private String extName = "ext";

}
