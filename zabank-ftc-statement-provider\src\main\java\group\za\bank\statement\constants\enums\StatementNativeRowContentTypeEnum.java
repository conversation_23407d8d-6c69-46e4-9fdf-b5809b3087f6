package group.za.bank.statement.constants.enums;

/**
 * <AUTHOR>
 * @createTime 15 11:42
 * @description
 */
public enum StatementNativeRowContentTypeEnum {


    TITLE(1, "标题类型"),
    VALUE(2, "值类型"),
    ALL(3, "标题加值"),


    ;

    private Integer type;
    private String desc;


    StatementNativeRowContentTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
