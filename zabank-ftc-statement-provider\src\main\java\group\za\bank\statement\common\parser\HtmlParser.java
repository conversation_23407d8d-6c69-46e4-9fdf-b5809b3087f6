package group.za.bank.statement.common.parser;

import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import group.za.bank.invest.basecommon.exception.BusinessException;
import com.greenpineyu.fel.FelEngine;
import com.greenpineyu.fel.FelEngineImpl;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.utils.HtmlParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * HTML解析器
 *
 * <AUTHOR>
 * @Date 2023/2/14 16:36
 * @Description
 */
@Slf4j
public class HtmlParser implements Parser<Map<String, Object>> {

    private static final String YES = "1";
    private static final long CACHE_SIZE = 1000L;
    private static final Charset DEFAULT_CHARSET = Charset.defaultCharset();

    private static FelEngine FEL;

    static {
        //初始化FelEngine
        FEL = new FelEngineImpl();
        //添加自定义函数
        FEL.getContext().set("$ParserUtil", HtmlParseUtils.class);
    }

    /**
     * 配置缓存值(默认不过期)
     */
    private static final Cache<String, String> TEMPLATE_CONF_CACHE = Caffeine.newBuilder()
            .maximumSize(CACHE_SIZE)
            .build();

    @Override
    public Map<String, Object> execute(String confPath, String entity) {
        try {
            //html文本转化成Document
            Document doc = Jsoup.parse(entity, "UTF-8");
            //加载配置
            String conf = loadConf(confPath);
            HtmlAnalyzer analyzer = JSONObject.parseObject(conf, HtmlAnalyzer.class);
            Map<String, Object> result = new LinkedHashMap<>();

            analyzer.getTargets().forEach(target -> {
                Map<String, Object> targetMap = buildTarget(doc, target);
                result.put(target.getName(), targetMap);
            });

            System.out.println(JSONObject.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("解析html文本[{}]异常", entity, e);
            throw new BusinessException(StatementErrorMsgEnum.FILE_HTML_PARSE_ERROR, e.getMessage());
        }
    }

    /**
     * 加载配置内容
     *
     * @param confPath
     * @return
     * @throws IOException
     */
    private String loadConf(String confPath) {
        //优先读取缓存配置
        return TEMPLATE_CONF_CACHE.get(confPath, (key) -> {
            try {
                ClassPathResource resource = new ClassPathResource("template/" + confPath);
                InputStream inputStream = resource.getInputStream();
                // 将InputStream转化为字符串
                String content = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
                return content;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 生成Target部分
     *
     * @param doc
     * @param target
     * @return
     */
    private Map<String, Object> buildTarget(Document doc, HtmlTarget target) {
        Map<String, Object> targetMap = new LinkedHashMap<>();
        //读取target配置path下的值
        Elements dataTitleElements = doc.select(target.getPath());
        targetMap.put("dataTypeTitle", dataTitleElements.size() > 0 ? dataTitleElements.get(target.getIndex()).text() : "");
        targetMap.put("dataType", target.getType());
        //读取model部分,加到target中
        List<Map<String, Object>> modelList = new ArrayList<>();
        targetMap.put(target.getModelName(), modelList);
        target.getModels().forEach(model -> {
            Map<String, Object> modelMap = buildModel(doc, model);
            if (!modelMap.isEmpty()) {
                modelList.add(modelMap);
            }
        });

        return targetMap;
    }

    /**
     * 生成Model部分
     *
     * @param doc
     * @param model
     * @return
     */
    private Map<String, Object> buildModel(Document doc, HtmlModel model) {
        Map<String, Object> modelMap = new LinkedHashMap<>();
        modelMap.put(model.getTypeName(), model.getType());
        //读取model的title部分
        Elements modelTitleElements = doc.select(model.getTitlePath());
        modelMap.put(model.getTitleName(), modelTitleElements.size() > 0 ? modelTitleElements.get(model.getTitleIndex()).text() : "");
        //读取model下的fileds数组,加到model中
        List<List<Map<String, Object>>> fieldList = new ArrayList<>();
        modelMap.put(model.getFieldsName(), fieldList);
        Elements modelElments = doc.select(model.getPath()).eq(model.getIndex());
        if (modelElments.isEmpty()) {
            return modelMap;
        }
        Elements modelTableRows = modelElments;
        if (StringUtils.isNotEmpty(model.getFieldBasePath())) {
            modelTableRows = modelElments.select(model.getFieldBasePath());
        }
        for (Element row : modelTableRows) {
            List<Map<String, Object>> fieldMapList = new ArrayList<>();

            if (row.children().isEmpty()) {
                continue;
            }

            //判断fileds是否数组,如果不是数组,直接输出model的path读取出来的值
            String isArray = model.getIsArray();
            if (YES.equals(isArray)) {
                model.getFields().forEach(field -> {
                    Map<String, Object> fieldMap = buildField(row, modelElments, field);
                    fieldMapList.add(fieldMap);
                });
            } else {
                Map<String, Object> fieldMap = new LinkedHashMap<>();
                fieldMap.put(model.getName(), doc.select(model.getPath()).text());
                fieldMapList.add(fieldMap);
            }
            fieldList.add(fieldMapList);
        }
        return modelMap;
    }

    /**
     * 生成具体Field部分
     *
     * @param row
     * @param modelElments
     * @param field
     * @return
     */
    private Map<String, Object> buildField(Element row, Elements modelElments, HtmlField field) {
        Map<String, Object> fieldMap = new LinkedHashMap<>();
        if(StringUtils.isNotEmpty(field.getDataDetailPath())){
            modelElments = row.select(field.getDataDetailPath());
        }
        Elements fieldElements = modelElments.select(field.getTitlePath());
        fieldMap.put(field.getFieldName(), field.getName());
        if (!fieldElements.isEmpty()) {
            //判断title是否需要输出html格式的文本
            fieldMap.put(field.getTitleName(), YES.equals(field.getIsHtml()) ?
                    fieldElements.get(field.getIndex()).html() : fieldElements.get(field.getIndex()).text());
        } else {
            //读取不到值得话则返回配置的默认值
            fieldMap.put(field.getTitleName(), field.getDefaultValue());
        }

        fieldMap.put(field.getBlockTypeName(), field.getBlockType());
        fieldMap.put(field.getGroupIdName(), field.getGroupId());
        fieldMap.put(field.getSplitterName(), field.getSplitter());
        fieldMap.put(field.getRowTypeName(), field.getRowType());
        fieldMap.put(field.getSeqName(), field.getSeq());
        fieldMap.put(field.getValueUnitName(), field.getValueUnit());
        fieldMap.put(field.getExtName(), field.getExt());

        if(StringUtils.isNotEmpty(field.getDataDetailPath())){
            row = row.select(field.getDataDetailPath()).get(field.getIndex());
        }
        //判断值是否原样输出html文本
        String fieldVal = YES.equals(field.getIsHtml()) ?
                row.select(field.getPath()).html() : row.select(field.getPath()).text();

        //表达引擎函数结果处理
        if (StringUtils.isNotEmpty(fieldVal)
                && StringUtils.isNotEmpty(field.getExp())) {
            FEL.getContext().set("$this", fieldVal);
            fieldVal = (String) FEL.eval(field.getExp());
        }


        fieldMap.put(field.getValueName(), fieldVal);
        //如果字段也有明细,则递归解析
        if (YES.equals(field.getIsArray())) {
            List<HtmlField> detailFields = field.getDetailFields();
            List<List<Map<String, Object>>> detailFieldMapList = new ArrayList<>();
            //字段下的数组明细路径
            Elements fieldDetailElments = row.select(field.getDetailFieldBasePath());
            for (Element fieldDetailRow : fieldDetailElments) {
                //字段下内部数组对象
                List<Map<String, Object>> innerDetailFieldMapList = new ArrayList<>();
                for (HtmlField detailField : detailFields) {
                    Map<String, Object> detailFieldMap = buildField(fieldDetailRow, fieldDetailElments, detailField);
                    //单独处理title，读取外层的title
                    Elements titleFieldElements = modelElments.select(detailField.getTitlePath());
                    if (!titleFieldElements.isEmpty()) {
                        //判断title是否需要输出html格式的文本
                        detailFieldMap.put(detailField.getTitleName(), YES.equals(detailField.getIsHtml()) ?
                                titleFieldElements.get(detailField.getIndex()).html() : titleFieldElements.get(detailField.getIndex()).text());
                    } else {
                        //读取不到值得话则返回配置的默认值
                        detailFieldMap.put(detailField.getTitleName(), detailField.getDefaultValue());
                    }
                    innerDetailFieldMapList.add(detailFieldMap);
                }
                detailFieldMapList.add(innerDetailFieldMapList);
            }
            fieldMap.put(field.getDetailFieldsName(), detailFieldMapList);
        }
        return fieldMap;
    }

}
