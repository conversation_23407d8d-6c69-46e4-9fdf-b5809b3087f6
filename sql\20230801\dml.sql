INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-03', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-05', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-06', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-07', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-10', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-11', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-12', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-13', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-14', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-17', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-18', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-19', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-20', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-21', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-24', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-25', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-26', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-27', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-28', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-31', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-08-01', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (2, '2023-07-31', 2);



-- 由邓伟单独提SQL审批
delete from za_bank_invest_statement.t_message_snapshot0;
delete from za_bank_invest_statement.t_message_snapshot1;
delete from za_bank_invest_statement.t_message_snapshot2;
delete from za_bank_invest_statement.t_message_snapshot3;
delete from za_bank_invest_statement.t_message_snapshot4;
delete from za_bank_invest_statement.t_message_snapshot5;
delete from za_bank_invest_statement.td_fund_monthly_order_tmp;
delete from za_bank_invest_statement.td_fund_monthly_statement;
delete from za_bank_invest_statement.td_fund_monthly_statement_info;
delete from za_bank_invest_statement.td_monthly_statement_data;
delete from za_bank_invest_statement.td_statement_file_record;
delete from za_bank_invest_statement.td_stock_statement_data;
