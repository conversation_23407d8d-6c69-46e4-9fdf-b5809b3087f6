package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;

/**
 * 通道枚举类
 */
@AllArgsConstructor
public enum ChannelEnum implements BaseEnum {
    NORMAL("normal", "普通"),
    SOUTH_BOUND("southBound", "南向通");


    private String value;

    private String msg;


    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
