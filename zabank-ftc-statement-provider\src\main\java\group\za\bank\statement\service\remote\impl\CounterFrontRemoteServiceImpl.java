package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.counterfront.module.entity.order.req.QueryOrderIdByContractIdReq;
import group.za.bank.sbs.counterfront.module.entity.order.resp.QueryOrderIdByContractIdListResp;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.CounterFrontRemoteService;
import group.za.bank.statement.service.remote.feign.CounterOrderFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 柜台网关远程接口
 *
 * <AUTHOR>
 * @date 2024/04/25
 **/
@Service
@Slf4j
public class CounterFrontRemoteServiceImpl implements CounterFrontRemoteService {

    @Autowired
    private CounterOrderFeign counterOrderFeign;

    @Override
    public QueryOrderIdByContractIdListResp queryOrderIdByContractId(String contractId) {
        QueryOrderIdByContractIdReq orderIdByContractIdReq = new QueryOrderIdByContractIdReq();
        orderIdByContractIdReq.setContractId(contractId);

        ResponseData<QueryOrderIdByContractIdListResp> respResponseData = counterOrderFeign.queryOrderIdByContractId(orderIdByContractIdReq);
        if(!respResponseData.judgeSuccess()){
            log.info("queryOrderIdByContractId失败:{}", respResponseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return respResponseData.getValue();
    }
}
