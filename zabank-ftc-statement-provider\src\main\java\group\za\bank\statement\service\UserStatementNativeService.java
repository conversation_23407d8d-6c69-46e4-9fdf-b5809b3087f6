package group.za.bank.statement.service;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.entity.req.NativeDetailReq;
import group.za.bank.statement.entity.req.NativeSummaryReq;
import group.za.bank.statement.entity.resp.NativeDetailResp;
import group.za.bank.statement.entity.resp.NativeSummaryListResp;
import group.za.bank.statement.entity.resp.NativeSummaryResp;
import group.za.bank.statement.entity.resp.UserMonthlyStatementListResp;

/**
 * <AUTHOR>
 * @createTime 09 11:23
 * @description 1.通过statementId去mongodb找解析结果，找到的话直接走步骤6
 * 2.通过statementId去obs上下载html文件：英简和英繁两份（根据参数指定的语言去下载一份即可，加锁防并发[返回处理中给用户]）
 * 3.解析html文件，抽取各模块的table表格（英简和英繁的表格路径不一样）
 * 4.将表格转成json对象，并在这个时候做app显示适配（标题修正、属性值合并）
 * 5.将结果存到mongodb（异步即可）
 * 6.根据参数对数据结果进行分页处理
 */
public interface UserStatementNativeService {


    /**
     * 用户月结单native页面总结数据
     *
     * @return
     */
    NativeSummaryListResp queryUserMonthlyStatementNativeSummaryData(I18nSupportEnum langEnum, NativeSummaryReq req);


    /**
     * 用户月结单native页面各模块数据
     *
     * @return
     */
    NativeDetailResp queryUserMonthlyStatementNativeDetail(I18nSupportEnum langEnum, NativeDetailReq req);



    /**
     * html文件下载、解析、存档
     * 1. 从obs上下载html文件
     * 2. 解析html文件（英简、英繁）
     * 3. 保存解析结果到mongodb（英简、英繁转成简、英、繁）
     */
    void htmlDocParseProcess(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement userStatement);




}
