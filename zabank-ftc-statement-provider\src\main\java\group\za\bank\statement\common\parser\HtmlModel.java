package group.za.bank.statement.common.parser;


import lombok.Data;

import java.util.List;

/**
 * html目标的数据模型
 * <AUTHOR>
 * @Date 2023/2/9 16:37
 * @Description
 */
@Data
public class HtmlModel {

    private String name;
    /**
     * 描述
     */
    private String desc;

    /**
     * 类型
     */
    private String type;

    private String typeName = "type";

    /**
     * 是否数组（如果不是数组,则fields可以不用配置）
     */
    private String isArray;

    /**
     * 标题索引值
     */
    private int titleIndex;

    /**
     * 标题名称
     */
    private String titleName = "title";

    /**
     * 标题读取路径
     */
    private String titlePath;

    /**
     * 读取路径
     */
    private String path;

    /**
     * 路径下的索引值（用于定位具体元素）
     */
    private int index;

    /**
     * field的父path
     */
    private String fieldBasePath;

    private String fieldsName = "fields";

    private List<HtmlField> fields;
}