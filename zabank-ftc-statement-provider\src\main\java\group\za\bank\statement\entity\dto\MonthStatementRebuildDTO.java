package group.za.bank.statement.entity.dto;

import lombok.Data;

/**
 * 月结单重新生成对象
 *
 * <AUTHOR>
 * @date 2023/07/14
 **/
@Data
public class MonthStatementRebuildDTO {

    /**
     * 结单月份
     */
    private String period;

    /**
     * 用户列表
     */
    private String clientIdList;

    /**
     * 结单日期
     */
    private String statementDate;

    /**
     * 是否通知客户，默认为true通知，如果是通知客户之后修复的数据，则设置为false不再重复通知客户
     */
    private Boolean notifyUser = Boolean.FALSE;
}
