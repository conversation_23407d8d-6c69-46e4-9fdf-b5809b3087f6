package group.za.bank.statement.mq;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import static org.mockito.Mockito.*;

public class UserStatementDataPrepareProducerTest {
    @Mock
    Logger log;
    @Mock
    RabbitTemplate rabbitTemplate;
    @InjectMocks
    UserStatementDataPrepareProducer userStatementDataPrepareProducer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSend() throws Exception {
        userStatementDataPrepareProducer.send(null);
    }

    @Test
    public void testConfirm() throws Exception {
        userStatementDataPrepareProducer.confirm(null, true, "cause");
    }
}

