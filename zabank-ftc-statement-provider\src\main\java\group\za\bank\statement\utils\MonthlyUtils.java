package group.za.bank.statement.utils;

import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.statement.common.constants.StatementConstants;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 结单期数工具类
 *
 * <AUTHOR>
 * @Date 2023/6/15 18:40
 * @Description
 */
public class MonthlyUtils {

    private static final Map<String, String> monthDayCache = new ConcurrentHashMap<>();
    private static final String firstDayKeySuffix = "_firstDay";
    private static final String lastDayKeySuffix = "_lastDay";

    /**
     * 获取月份第一天
     * @param period yyyyMM
     * @return
     */
    public static String getMonthFirstDay(String period) {
        return getMonthDayFromCacheOrAdd(period, date ->
                DateUtil.format(DateUtil.getFirstDayOfMonth(date), DateUtil.FORMATDAY), firstDayKeySuffix);
    }

    /**
     * 获取月份最后一天
     * @param period yyyyMM
     * @return
     */
    public static String getMonthLastDay(String period) {
        return getMonthDayFromCacheOrAdd(period, date ->
                DateUtil.format(DateUtil.getLastDayOfMonth(date), DateUtil.FORMATDAY), lastDayKeySuffix);
    }

    /**
     * 转换为美东时间
     * @param date
     * @return
     */
    public static Date getEstDate(Date date){
        // 创建一个日期格式化对象，设置输出日期的格式和时区（美东时间）
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        outputFormat.setTimeZone(TimeZone.getTimeZone("America/New_York"));
        String format = outputFormat.format(date);

        return DateUtil.parse(format);
    }

    private static String getMonthDayFromCacheOrAdd(String period, Function<Date, String> computeFunction, String suffix) {
        String dayKey = period + suffix;
        if (monthDayCache.containsKey(dayKey)) {
            return monthDayCache.get(dayKey);
        }
        Date date = parseDate(period);
        String monthDay = computeFunction.apply(date);
        monthDayCache.put(dayKey, monthDay);
        return monthDay;
    }

    private static Date parseDate(String period) {
        return DateUtil.parse(period, StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT);
    }

}
