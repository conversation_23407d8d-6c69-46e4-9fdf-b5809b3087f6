package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.bankfront.feign.RateFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 汇率远程接口
 *
 * <AUTHOR>
 * @date 2022/05/27
 **/
@FeignClient(
        value = "zabank-sbs-bankfront-service",
        contextId = "rateFeignService1",
        url = "${sbs.gateway.url}"
)
public interface RateFeign extends RateFeignService {

}
