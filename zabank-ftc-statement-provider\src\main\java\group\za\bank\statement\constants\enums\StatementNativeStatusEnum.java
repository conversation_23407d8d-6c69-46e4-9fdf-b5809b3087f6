package group.za.bank.statement.constants.enums;

/**
 * 结单类型
 *
 * <AUTHOR>
 * @date 2022/04/29
 * 标记状态场景： 0-无月结单，1-本期结单生成中，2-本期结单已生成，3、只展示下载pdf
 **/
public enum StatementNativeStatusEnum {
    /**
     * ttl提供月结单数据类型
     * "后期不会使用月结单数据了"
     */
    NONE(0, "无月结单"),
    GENERATING(1, "本期结单生成中"),
    GENERATED(2, "本期结单已生成"),
    ;

    private Integer type;
    private String desc;

    StatementNativeStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
