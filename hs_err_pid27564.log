#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 872176 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=27564, tid=45308
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.5******.28-jcef (21.0.5+8) (build 21.0.5+8-b631.28)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.5******.28-jcef (21.0.5+8-b631.28, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 

Host: Intel(R) Core(TM) i5-10505 CPU @ 3.20GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Sat May 31 21:21:23 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 1.150265 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000026e9f994580):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=45308, stack(0x00000080cf900000,0x00000080cfa00000) (1024K)]


Current CompileTask:
C2:1150 1094       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x00000080cf900000,0x00000080cfa00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e52b9]
V  [jvm.dll+0x8c3633]
V  [jvm.dll+0x8c5b8e]
V  [jvm.dll+0x8c6273]
V  [jvm.dll+0x288f46]
V  [jvm.dll+0xc66dd]
V  [jvm.dll+0xc6c13]
V  [jvm.dll+0x2feb50]
V  [jvm.dll+0x60c609]
V  [jvm.dll+0x2598a2]
V  [jvm.dll+0x259c5f]
V  [jvm.dll+0x252415]
V  [jvm.dll+0x24fc6e]
V  [jvm.dll+0x1cd6a4]
V  [jvm.dll+0x25f5dc]
V  [jvm.dll+0x25db26]
V  [jvm.dll+0x3ff5e6]
V  [jvm.dll+0x86b248]
V  [jvm.dll+0x6e3abd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000026e9f81b420, length=16, elements={
0x0000026e37985d90, 0x0000026e5930d0d0, 0x0000026e5930e3f0, 0x0000026e9f001830,
0x0000026e9f002280, 0x0000026e9f002f30, 0x0000026e9f003980, 0x0000026e9f005b30,
0x0000026e9f0089e0, 0x0000026e9f1578c0, 0x0000026e9f163230, 0x0000026e9f800420,
0x0000026e9f868680, 0x0000026e9f875e30, 0x0000026e9f994580, 0x0000026e9f995120
}

Java Threads: ( => current thread )
  0x0000026e37985d90 JavaThread "main"                              [_thread_blocked, id=33384, stack(0x00000080ce300000,0x00000080ce400000) (1024K)]
  0x0000026e5930d0d0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=45316, stack(0x00000080ceb00000,0x00000080cec00000) (1024K)]
  0x0000026e5930e3f0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=45096, stack(0x00000080cec00000,0x00000080ced00000) (1024K)]
  0x0000026e9f001830 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=47672, stack(0x00000080ced00000,0x00000080cee00000) (1024K)]
  0x0000026e9f002280 JavaThread "Attach Listener"            daemon [_thread_blocked, id=53648, stack(0x00000080cee00000,0x00000080cef00000) (1024K)]
  0x0000026e9f002f30 JavaThread "Service Thread"             daemon [_thread_blocked, id=65144, stack(0x00000080cef00000,0x00000080cf000000) (1024K)]
  0x0000026e9f003980 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=48016, stack(0x00000080cf000000,0x00000080cf100000) (1024K)]
  0x0000026e9f005b30 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=70716, stack(0x00000080cf100000,0x00000080cf200000) (1024K)]
  0x0000026e9f0089e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=72272, stack(0x00000080cf200000,0x00000080cf300000) (1024K)]
  0x0000026e9f1578c0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=41640, stack(0x00000080cf300000,0x00000080cf400000) (1024K)]
  0x0000026e9f163230 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=10072, stack(0x00000080cf400000,0x00000080cf500000) (1024K)]
  0x0000026e9f800420 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=14992, stack(0x00000080cf500000,0x00000080cf600000) (1024K)]
  0x0000026e9f868680 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_blocked, id=38056, stack(0x00000080cf700000,0x00000080cf800000) (1024K)]
  0x0000026e9f875e30 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_in_vm, id=18828, stack(0x00000080cf800000,0x00000080cf900000) (1024K)]
=>0x0000026e9f994580 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=45308, stack(0x00000080cf900000,0x00000080cfa00000) (1024K)]
  0x0000026e9f995120 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=34768, stack(0x00000080cfa00000,0x00000080cfb00000) (1024K)]
Total: 16

Other Threads:
  0x0000026e592eeca0 VMThread "VM Thread"                           [id=38644, stack(0x00000080cea00000,0x00000080ceb00000) (1024K)]
  0x0000026e592dc170 WatcherThread "VM Periodic Task Thread"        [id=25740, stack(0x00000080ce900000,0x00000080cea00000) (1024K)]
  0x0000026e39d146a0 WorkerThread "GC Thread#0"                     [id=6384, stack(0x00000080ce400000,0x00000080ce500000) (1024K)]
  0x0000026e39d27520 ConcurrentGCThread "G1 Main Marker"            [id=57592, stack(0x00000080ce500000,0x00000080ce600000) (1024K)]
  0x0000026e39d2aae0 WorkerThread "G1 Conc#0"                       [id=46160, stack(0x00000080ce600000,0x00000080ce700000) (1024K)]
  0x0000026e591ae940 ConcurrentGCThread "G1 Refine#0"               [id=48220, stack(0x00000080ce700000,0x00000080ce800000) (1024K)]
  0x0000026e591af3c0 ConcurrentGCThread "G1 Service"                [id=54116, stack(0x00000080ce800000,0x00000080ce900000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread1  1443 1094       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe03d8c830] Metaspace_lock - owner thread: 0x0000026e9f875e30

Heap address: 0x0000000604400000, size: 8124 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000026e5a000000-0x0000026e5ad00000-0x0000026e5ad00000), size 13631488, SharedBaseAddress: 0x0000026e5a000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000026e5b000000-0x0000026e9b000000, reserved size: 1073741824
Narrow klass base: 0x0000026e5a000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32480M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8124M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 12288K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 0 survivors (0K)
 Metaspace       used 8290K, committed 8512K, reserved 1114112K
  class space    used 961K, committed 1088K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   1|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   2|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   3|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   4|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   5|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   6|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   7|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|   8|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|   9|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  10|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  11|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  12|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  13|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  14|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  15|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  16|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  17|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  18|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  19|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  20|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  21|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  22|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  23|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  24|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  25|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  26|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  27|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  28|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  29|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  30|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  31|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  32|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  33|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  34|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  35|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  36|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  37|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  38|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  39|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  40|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  41|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  42|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  43|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  44|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  45|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  46|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  47|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  48|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  49|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  50|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  51|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  52|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  53|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  54|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  55|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  56|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  57|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  58|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  59|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  60|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  61|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  62|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  63|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  64|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  65|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  66|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  67|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  68|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  69|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  70|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  71|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  72|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  73|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  74|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  75|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  76|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  77|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  78|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  79|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  80|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  81|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  82|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  83|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  84|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  85|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  86|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  87|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  88|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  89|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  90|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  91|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  92|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  93|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  94|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  95|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  96|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  97|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  98|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  99|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 100|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 101|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 102|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 103|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 104|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 105|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 106|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 107|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 108|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 109|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 110|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 111|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 112|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 113|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 114|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 115|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 116|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 117|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 118|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 119|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 120|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 121|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 122|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 123|0x0000000623000000, 0x00000006233da998, 0x0000000623400000| 96%| E|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 124|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 125|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 
| 126|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000| PB 0x0000000623c00000| Complete 

Card table byte_map: [0x0000026e4e130000,0x0000026e4f110000] _byte_map_base: 0x0000026e4b10e000

Marking Bits: (CMBitMap*) 0x0000026e39d14cb0
 Bits: [0x0000026e4f110000, 0x0000026e57000000)

Polling page: 0x0000026e37c70000

Metaspace:

Usage:
  Non-class:      7.16 MB used.
      Class:    961.60 KB used.
       Both:      8.10 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.25 MB ( 11%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.06 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.31 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.39 MB
       Class:  14.98 MB
        Both:  23.37 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 226.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 133.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 369.
num_chunk_merges: 0.
num_chunk_splits: 216.
num_chunks_enlarged: 104.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=401Kb max_used=401Kb free=119599Kb
 bounds [0x0000026e456d0000, 0x0000026e45940000, 0x0000026e4cc00000]
CodeHeap 'profiled nmethods': size=120000Kb used=1745Kb max_used=1745Kb free=118254Kb
 bounds [0x0000026e3dc00000, 0x0000026e3de70000, 0x0000026e45130000]
CodeHeap 'non-nmethods': size=5760Kb used=1429Kb max_used=1462Kb free=4331Kb
 bounds [0x0000026e45130000, 0x0000026e453a0000, 0x0000026e456d0000]
 total_blobs=1638 nmethods=1122 adapters=421
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.135 Thread 0x0000026e9f0089e0 1104       3       java.lang.Thread::onSpinWait (1 bytes)
Event: 1.135 Thread 0x0000026e9f0089e0 nmethod 1104 0x0000026e3ddafc10 code [0x0000026e3ddafda0, 0x0000026e3ddafe88]
Event: 1.135 Thread 0x0000026e9f0089e0 1108       1       java.security.Provider::getName (5 bytes)
Event: 1.135 Thread 0x0000026e9f0089e0 nmethod 1108 0x0000026e45730410 code [0x0000026e457305a0, 0x0000026e45730670]
Event: 1.136 Thread 0x0000026e9f0089e0 1110       3       java.lang.invoke.MemberName::changeReferenceKind (46 bytes)
Event: 1.136 Thread 0x0000026e9f0089e0 nmethod 1110 0x0000026e3ddaff10 code [0x0000026e3ddb00a0, 0x0000026e3ddb01d0]
Event: 1.138 Thread 0x0000026e9f0089e0 1111       3       java.lang.Boolean::valueOf (14 bytes)
Event: 1.138 Thread 0x0000026e9f0089e0 nmethod 1111 0x0000026e3ddb0290 code [0x0000026e3ddb0440, 0x0000026e3ddb05a8]
Event: 1.140 Thread 0x0000026e9f0089e0 1112       3       java.lang.invoke.MethodHandles$Lookup::resolveOrFail (48 bytes)
Event: 1.141 Thread 0x0000026e9f0089e0 nmethod 1112 0x0000026e3ddb0610 code [0x0000026e3ddb0860, 0x0000026e3ddb0f18]
Event: 1.141 Thread 0x0000026e9f0089e0 1113       3       jdk.internal.ref.PhantomCleanable::<init> (49 bytes)
Event: 1.142 Thread 0x0000026e9f0089e0 nmethod 1113 0x0000026e3ddb1210 code [0x0000026e3ddb1480, 0x0000026e3ddb1fc0]
Event: 1.142 Thread 0x0000026e9f0089e0 1114       3       sun.security.ec.XECOperations::bitAt (23 bytes)
Event: 1.142 Thread 0x0000026e9f0089e0 nmethod 1114 0x0000026e3ddb2310 code [0x0000026e3ddb24c0, 0x0000026e3ddb2658]
Event: 1.142 Thread 0x0000026e9f0089e0 1115       3       sun.security.util.math.intpoly.IntegerPolynomial25519::reduce (40 bytes)
Event: 1.143 Thread 0x0000026e9f995120 1117       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setProduct (6 bytes)
Event: 1.143 Thread 0x0000026e9f0089e0 nmethod 1115 0x0000026e3ddb2790 code [0x0000026e3ddb2960, 0x0000026e3ddb2c60]
Event: 1.143 Thread 0x0000026e9f0089e0 1116       3       sun.security.util.math.intpoly.IntegerPolynomial25519::carryReduce (391 bytes)
Event: 1.143 Thread 0x0000026e9f0089e0 nmethod 1116 0x0000026e3ddb2e10 code [0x0000026e3ddb2fc0, 0x0000026e3ddb3420]
Event: 1.144 Thread 0x0000026e9f0089e0 1121       3       java.lang.invoke.MethodHandles$Lookup::<init> (84 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.043 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.dll
Event: 0.132 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000026e4572a17c relative=0x000000000000031c
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000026e4572a17c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT PACKING pc=0x0000026e4572a17c sp=0x00000080cf8fe610
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT UNPACKING pc=0x0000026e451846a2 sp=0x00000080cf8fe560 mode 2
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000026e457272c0 relative=0x0000000000000280
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000026e457272c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT PACKING pc=0x0000026e457272c0 sp=0x00000080cf8fe590
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT UNPACKING pc=0x0000026e451846a2 sp=0x00000080cf8fe560 mode 2
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000026e457272c0 relative=0x0000000000000280
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000026e457272c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT PACKING pc=0x0000026e457272c0 sp=0x00000080cf8fe590
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT UNPACKING pc=0x0000026e451846a2 sp=0x00000080cf8fe560 mode 2
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000026e457272c0 relative=0x0000000000000280
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000026e457272c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT PACKING pc=0x0000026e457272c0 sp=0x00000080cf8fe590
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT UNPACKING pc=0x0000026e451846a2 sp=0x00000080cf8fe560 mode 2
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000026e457272c0 relative=0x0000000000000280
Event: 1.143 Thread 0x0000026e9f875e30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000026e457272c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT PACKING pc=0x0000026e457272c0 sp=0x00000080cf8fe590
Event: 1.143 Thread 0x0000026e9f875e30 DEOPT UNPACKING pc=0x0000026e451846a2 sp=0x00000080cf8fe560 mode 2

Classes loaded (20 events):
Event: 1.136 Loading class java/security/spec/X509EncodedKeySpec
Event: 1.136 Loading class java/security/spec/EncodedKeySpec
Event: 1.136 Loading class java/security/spec/EncodedKeySpec done
Event: 1.136 Loading class java/security/spec/X509EncodedKeySpec done
Event: 1.138 Loading class sun/security/ssl/XDHKeyExchange
Event: 1.138 Loading class sun/security/ssl/XDHKeyExchange done
Event: 1.138 Loading class sun/security/ssl/XDHKeyExchange$XDHEKAGenerator
Event: 1.138 Loading class sun/security/ssl/XDHKeyExchange$XDHEKAGenerator done
Event: 1.138 Loading class sun/security/ssl/KAKeyDerivation
Event: 1.138 Loading class sun/security/ssl/KAKeyDerivation done
Event: 1.139 Loading class javax/crypto/KeyAgreementSpi
Event: 1.139 Loading class javax/crypto/KeyAgreementSpi done
Event: 1.139 Loading class javax/crypto/ShortBufferException
Event: 1.139 Loading class javax/crypto/ShortBufferException done
Event: 1.139 Loading class javax/crypto/SecretKey
Event: 1.139 Loading class javax/crypto/SecretKey done
Event: 1.144 Loading class javax/crypto/spec/SecretKeySpec
Event: 1.144 Loading class javax/crypto/spec/SecretKeySpec done
Event: 1.144 Loading class jdk/internal/access/JavaxCryptoSpecAccess
Event: 1.144 Loading class jdk/internal/access/JavaxCryptoSpecAccess done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.815 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bc9eb0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623bc9eb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.815 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bd0948}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000623bd0948) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.816 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bd7500}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623bd7500) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.817 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bdbdd0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623bdbdd0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.822 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623405a98}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623405a98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.822 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623409428}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623409428) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.824 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623418748}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623418748) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.828 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x000000062344a3f0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x000000062344a3f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.829 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623450cc0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623450cc0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.829 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x00000006234540e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006234540e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.935 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x00000006235e6858}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006235e6858) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.967 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237e9b30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006237e9b30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.967 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237ed498}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006237ed498) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.974 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x000000062301c048}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062301c048) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.987 Thread 0x0000026e9f800420 Exception <a 'java/lang/NoSuchMethodError'{0x000000062360ec18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000062360ec18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.991 Thread 0x0000026e37985d90 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230aba08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006230aba08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.997 Thread 0x0000026e9f800420 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623648910}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000623648910) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.000 Thread 0x0000026e9f868680 Exception <a 'java/lang/NoSuchMethodError'{0x000000062311b378}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062311b378) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.008 Thread 0x0000026e9f868680 Exception <a 'java/lang/NoSuchMethodError'{0x000000062316d230}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062316d230) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.132 Thread 0x0000026e9f875e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000006231f3e48}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000006231f3e48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.310 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.310 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.320 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.320 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.648 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.648 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.989 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.992 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e5930e3f0
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f001830
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f002280
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f002f30
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f003980
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f005b30
Event: 0.142 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f0089e0
Event: 0.205 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f1578c0
Event: 0.214 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f163230
Event: 0.245 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\net.dll
Event: 0.275 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\nio.dll
Event: 0.301 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\zip.dll
Event: 0.387 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jimage.dll
Event: 0.461 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\sunmscapi.dll
Event: 0.925 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\extnet.dll
Event: 0.931 Thread 0x0000026e37985d90 Thread added: 0x0000026e9f800420
Event: 0.996 Thread 0x0000026e9f800420 Thread added: 0x0000026e9f868680
Event: 1.014 Thread 0x0000026e9f800420 Thread added: 0x0000026e9f875e30
Event: 1.044 Thread 0x0000026e9f005b30 Thread added: 0x0000026e9f994580
Event: 1.059 Thread 0x0000026e9f005b30 Thread added: 0x0000026e9f995120


Dynamic libraries:
0x00007ff7f6660000 - 0x00007ff7f666a000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.exe
0x00007ffeb6430000 - 0x00007ffeb6647000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffeb5260000 - 0x00007ffeb5324000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffeb3c30000 - 0x00007ffeb4003000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffeb3a90000 - 0x00007ffeb3ba1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe060d0000 - 0x00007ffe060e8000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jli.dll
0x00007ffeb59d0000 - 0x00007ffeb5b81000 	C:\WINDOWS\System32\USER32.dll
0x00007ffeb3a60000 - 0x00007ffeb3a86000 	C:\WINDOWS\System32\win32u.dll
0x00007ffeb5400000 - 0x00007ffeb5429000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffeb35b0000 - 0x00007ffeb36d2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe065d0000 - 0x00007ffe065eb000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\VCRUNTIME140.dll
0x00007ffeb3820000 - 0x00007ffeb38ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe9e530000 - 0x00007ffe9e7cb000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffeb58b0000 - 0x00007ffeb5957000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffeb5220000 - 0x00007ffeb5251000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe065f0000 - 0x00007ffe065fc000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\vcruntime140_1.dll
0x00007ffea1df0000 - 0x00007ffea1e7d000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\msvcp140.dll
0x00007ffe030c0000 - 0x00007ffe03e81000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\server\jvm.dll
0x00007ffeb5160000 - 0x00007ffeb5211000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffeb4350000 - 0x00007ffeb43f7000 	C:\WINDOWS\System32\sechost.dll
0x00007ffeb38c0000 - 0x00007ffeb38e8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffeb4ee0000 - 0x00007ffeb4ff4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffeb5330000 - 0x00007ffeb53a1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffeb2d90000 - 0x00007ffeb2ddd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffeae5b0000 - 0x00007ffeae5ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe9ed50000 - 0x00007ffe9ed84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffeb2d70000 - 0x00007ffeb2d83000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffeb2410000 - 0x00007ffeb2428000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe065c0000 - 0x00007ffe065ca000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jimage.dll
0x00007ffea0520000 - 0x00007ffea0752000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffeb5b90000 - 0x00007ffeb5f23000 	C:\WINDOWS\System32\combase.dll
0x00007ffeb40a0000 - 0x00007ffeb4177000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe8a060000 - 0x00007ffe8a092000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffeb3bb0000 - 0x00007ffeb3c2b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe06480000 - 0x00007ffe064a0000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.dll
0x00007ffeb4400000 - 0x00007ffeb4c9d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffeb36e0000 - 0x00007ffeb381f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffeb1230000 - 0x00007ffeb1b4d000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffeb4db0000 - 0x00007ffeb4ebb000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffeb6350000 - 0x00007ffeb63b6000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffeb3420000 - 0x00007ffeb344b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe06040000 - 0x00007ffe06058000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\zip.dll
0x00007ffe064f0000 - 0x00007ffe06500000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\net.dll
0x00007ffeb2430000 - 0x00007ffeb255c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffeb29b0000 - 0x00007ffeb2a1a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe05ef0000 - 0x00007ffe05f06000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\nio.dll
0x00007ffe05ed0000 - 0x00007ffe05ede000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\sunmscapi.dll
0x00007ffeb38f0000 - 0x00007ffeb3a57000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffeb2eb0000 - 0x00007ffeb2edd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffeb2e70000 - 0x00007ffeb2ea7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffeb2d30000 - 0x00007ffeb2d4b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffeb2370000 - 0x00007ffeb23a7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffeb2a50000 - 0x00007ffeb2a78000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffeb2d10000 - 0x00007ffeb2d1c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffeb1c70000 - 0x00007ffeb1c9d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffeb5150000 - 0x00007ffeb5159000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe59790000 - 0x00007ffe59798000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffe05e70000 - 0x00007ffe05e79000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\software2\ideaIU-2024.3.1.1.win\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 
java_class_path (initial): C:/software2/ideaIU-2024.3.1.1.win/plugins/vcs-git/lib/git4idea-rt.jar;C:/software2/ideaIU-2024.3.1.1.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8518631424                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8518631424                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/software/Git/mingw64/libexec/git-core;C:/software/Git/mingw64/libexec/git-core;C:\software\Git\mingw64\bin;C:\software\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\software\Git\cmd;C:\Program Files\dotnet\;C:\software2\xshell\;C:\Program Files\nodejs\;C:\software2\python3\Scripts\;C:\software2\python3\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\software2\apache-maven-3.5.4\bin;C:\software2\protoc-21.5-win64\bin;C:\software2\python\Scripts;C:\software2\go\bin;C:\software2\windsurf\Windsurf\bin;C:\software2\Microsoft VS Code\bin
USERNAME=yingfeng.fu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 82308K (0% of 33259564K total physical memory with 1863872K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
