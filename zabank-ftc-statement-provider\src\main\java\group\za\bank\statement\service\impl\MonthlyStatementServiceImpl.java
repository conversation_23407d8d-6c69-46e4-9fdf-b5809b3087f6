package group.za.bank.statement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import group.za.bank.fund.domain.trade.mapper.TdFundMonthlyStatementInfoMapper;
import group.za.bank.fund.trade.constants.enums.FundMonthlyStatementDocStatusEnum;
import group.za.bank.fund.trade.constants.enums.FundMonthlyStatementPubStatusEnum;
import group.za.bank.fund.trade.constants.enums.LanguageTemEnum;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.EnumYesOrNo;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.domain.account.entity.UserInvestAccount;
import group.za.bank.invest.domain.account.entity.ext.UserInvestAccountConditionHolder;
import group.za.bank.invest.domain.account.mapper.UserInvestAccountMapper;
import group.za.bank.invest.pub.constants.enums.BusinessTaskStatusEnum;
import group.za.bank.invest.pub.entity.req.SendSingleMessageReq;
import group.za.bank.invest.pub.entity.resp.GeneratePdfResp;
import group.za.bank.invest.pub.entity.resp.QueryPdfResp;
import group.za.bank.invest.pub.utils.MessageHelper;
import group.za.bank.sbs.quote.common.enums.MarketCodeEnum;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.MqConstants;
import group.za.bank.statement.constants.StatementActivityConstants;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.domain.entity.dto.RemarkDto;
import group.za.bank.statement.domain.mapper.TdFundMonthlyOrderTmpMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.entity.UserMonthlyStatementDataPrepareDto;
import group.za.bank.statement.entity.dto.*;
import group.za.bank.statement.entity.req.AppUserMonthlyStatementListReq;
import group.za.bank.statement.entity.req.MonthlyStatementConfirmReq;
import group.za.bank.statement.entity.req.PubMonthlyStatementReq;
import group.za.bank.statement.entity.req.UserMonthlyStatementListReq;
import group.za.bank.statement.entity.resp.*;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.mq.UserStatementDataPrepareProducer;
import group.za.bank.statement.service.*;
import group.za.bank.statement.service.remote.FundTradeRemoteService;
import group.za.bank.statement.service.remote.PubRemoteService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.bank.statement.utils.AlarmUtil;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.bank.statement.utils.StatementNumberFormatUtils;
import group.za.invest.cache.redis.lock.DistributedLock;
import group.za.invest.common.constants.enums.CurrencyEnum;
import group.za.invest.core.InvestConstants;
import group.za.invest.rabbitmq.entity.BaseMqMsg;
import group.za.invest.web.json.JSON;
import group.za.invest.web.json.JSONObject;
import group.za.invest.zookeeper.lock.LockException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static group.za.bank.statement.constants.StatementRedisCacheKey.*;
import static group.za.bank.statement.constants.enums.StatementAlarmModuleEnum.MONTHLY_STATEMENT_DOC_GENERATE_ERROR;
import static group.za.bank.statement.constants.enums.StatementAlarmModuleEnum.MONTHLY_STATEMENT_INIT_ERROR;
import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.*;
import static group.za.bank.statement.utils.LangUtils.selectLang;

/**
 * <AUTHOR>
 * @createTime 20 16:41
 * @description
 */
@Service
@Slf4j
public class MonthlyStatementServiceImpl implements MonthlyStatementService {

    @Autowired
    private AlarmUtil alarmUtil;
    @Autowired
    private UserInvestAccountMapper userInvestAccountMapper;
    @Autowired
    private MonthlyStatementManager monthlyStatementManager;
    @Autowired
    private MonthlyStatementService monthlyStatementService;
    @Autowired
    private StatementBasicInfoService statementBasicInfoService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private RedissonClient redissonClient;
    @Resource(name = "statementExecutor")
    private ThreadPoolTaskExecutor statementExecutor;
    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private PubRemoteService pubRemoteService;
    @Autowired
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;
    @Autowired
    private UserRemoteService userRemoteService;
    @Autowired
    private TdMonthlyStatementInfoMapper monthlyStatementInfoMapper;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private List<StatementBusinessService> statementBusinessServiceList;
    @Autowired
    private UserStatementDataPrepareProducer userStatementDataPrepareProducer;
    @Autowired
    private UserStatementNativeService userStatementNativeService;
    @Resource
    private TdFundMonthlyOrderTmpMapper tdFundMonthlyOrderTmpMapper;
    @Resource
    private ExchangeRateInfoService exchangeRateInfoService;
    @Resource
    private FundTradeRemoteService fundTradeRemoteService;
    @Resource
    private TdFundMonthlyStatementInfoMapper tdFundMonthlyStatementInfoMapper;

    /**
     * 月结单基础数据的类型
     */
    public static final String STATEMENT_SUMMARY_DATA_TYPE_KEY = "statementInfo";
    /**
     * 月结单基础数据的总市值
     */
    public static final String BASE_MONTHLY_STATEMENT_DATA_TOTAL_AMOUNT = "totalAmount";

    /**
     * 获取结单汇总信息，如果不存在，就创建
     *
     * @param business 业务类型
     * @param period   结单期数 yyyyMM
     * @return 当月结单信息
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public TdFundMonthlyStatementInfo createStatementInfo(String business, String period) {
        TdFundMonthlyStatementInfo tempStatementInfo
                = monthlyStatementManager.getStatement(period);
        if (tempStatementInfo == null) {
            // 删除基金订单临时表
            tdFundMonthlyOrderTmpMapper.deleteTdFundMonthlyOrderTmp();

            String hkTempKey = systemConfig.getTempPath() + systemConfig.getHkTempKey();
            String zhTempKey = systemConfig.getTempPath() + systemConfig.getZhTempKey();
            String nxtHkTempKey = systemConfig.getTempPath() + systemConfig.getNxtHkTempKey();
            String nxtZhTempKey = systemConfig.getTempPath() + systemConfig.getNxtZhTempKey();
            tempStatementInfo = monthlyStatementManager.createStatement(business, period, hkTempKey, zhTempKey, nxtHkTempKey, nxtZhTempKey);
        }
        return tempStatementInfo;

    }


    /**
     * 初始化前检查
     *
     * @param period        结单期数
     * @param statementInfo 结单信息
     * @return true-检查通过  false-检查不通过
     */
    boolean checkBeforeInit(String period, TdFundMonthlyStatementInfo statementInfo) {
        if (Strings.isEmpty(period)) {
            log.error("月结单初始化period参数不能为空! period:{}", period);
            return false;
        }


        if (statementInfo == null) {
            log.error("初始化前检查，月结单信息不存在! period:{}", period);
            throw new BusinessException(STATEMENT_INFO_NOT_FOUND);
        }

        //已经完成了初始化
        if (MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue().equals(statementInfo.getInitStatus())) {
            log.warn("月结单已经完成初始化! period:{}", period);
            return false;
        }


        //已经确认可以推送
        if (MonthlyStatementPubConfirmStatusEnum.CONFIRMED.getDbValue()
                .equals(statementInfo.getPubConfirmStatus())) {
            log.warn("月结单已经确认可以推送，不重复进行初始化! period:{}", period);
            return false;
        }

        //已经推送完毕
        if (MonthlyStatementInfoStatusEnum.FINISHED.getDbValue().equals(statementInfo.getStatus())) {
            log.warn("月结单已处理完毕,不重复进行处理! period:{}", period);
            return false;
        }

        return true;

    }

    /**
     * 1.生成指定期数的月结单，支持重复跑
     *
     * @param period 结单期数
     * @see group.za.bank.invest.account.constants.enums.AccountTypeEnum
     */
    @Override
    public void initMonthlyStatements(String period) {
        String business = systemConfig.getDefaultMonthlyStatementBusinessType();
        String[] businessSubtypeArray = business.split(systemConfig.getBusinessSubTypeSeparator());
        // 创建月结单总记录信息
        TdFundMonthlyStatementInfo statementInfo = monthlyStatementService.createStatementInfo(business, period);
        //创建或者获取结单信息
        if (!checkBeforeInit(period, statementInfo)) {
            return;
        }

        // 用于判断是否可以开始月结单初始化
        StringBuilder isStartInitBuilder = new StringBuilder();
        String path = MONTHLY_STATEMENT_INIT.key(period, business);
        //结单信息
        distributedLock.tryLock(path, () -> {

            //每个业务单独处理 stock,fund
            for (String businessSubtype : businessSubtypeArray) {
                StatementBusinessService statementBusinessService = this.getStatementBusinessService(businessSubtype);
                // 判断是否可以开始月结单初始化
                if (!statementBusinessService.isDataPrepared(statementInfo, Boolean.TRUE)) {
                    isStartInitBuilder.append(period);
                    isStartInitBuilder.append("月结单数据还未准备完成。businessSubtype: ");
                    isStartInitBuilder.append(businessSubtype);
                    isStartInitBuilder.append("\n");
                    continue;
                }
            }

            // 判断是否可以开始初始化
            if (StringUtils.isNotBlank(isStartInitBuilder)) {
                return;
            }

            // 初始化汇率
            exchangeRateInfoService.initExchangeRate(period);
        });

        // 判断是否可以开始初始化
        if (StringUtils.isNotBlank(isStartInitBuilder)) {
            log.info(isStartInitBuilder.toString());
            return;
        }


        //每个业务单独处理 stock,fund
        for (String businessSubtype : businessSubtypeArray) {
            //进入执行业务逻辑
            monthlyStatementService.initAllUserMonthlyStatement(statementInfo, businessSubtype);
        }

        //是否所有业务都处理完毕了
        for (String businessSubtype : businessSubtypeArray) {
            if (!isBusinessInitFinish(statementInfo, businessSubtype)) {
                log.info("本期月结单数据尚未处理完毕,等待下一轮运行完成! period:{},未完成业务:{}", period, businessSubtype);
                return;
            }
        }

        //全部业务都处理完毕了，可以统计数量
        monthlyStatementService.updateStatementTotalInfoInitStatus(statementInfo);

        log.info("{},{}月结单初始化推送完成! ", period, business);
    }

    /**
     * 更新结单发送数量
     *
     * @param statementInfo
     */
    @Override
    public void updateStatementTotalInfoInitStatus(TdFundMonthlyStatementInfo statementInfo) {
        TdFundMonthlyStatementInfo conditionFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        conditionFundMonthlyStatementInfo.setId(statementInfo.getId());

        TdFundMonthlyStatementInfo newFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        newFundMonthlyStatementInfo.setGmtModified(new Date());


        //统计初始化的结单数量
        TdFundMonthlyStatement totalCondition = new TdFundMonthlyStatement();
        totalCondition.setPeriod(statementInfo.getPeriod());
        totalCondition.setBusinessType(statementInfo.getBusinessType());
        totalCondition.setIsDeleted(InvestConstants.N);
        Integer totalNumber = tdMonthlyStatementMapper.selectCount(totalCondition) / 2;

        TdFundMonthlyStatement successCondition = new TdFundMonthlyStatement();
        successCondition.setPeriod(statementInfo.getPeriod());
        successCondition.setBusinessType(statementInfo.getBusinessType());
        successCondition.setDocStatus(MonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        successCondition.setIsDeleted(InvestConstants.N);
        int successNumber = tdMonthlyStatementMapper.selectCount(successCondition) / 2;


        if (totalNumber > 0 && totalNumber == successNumber) {
            newFundMonthlyStatementInfo.setInitStatus(MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue());

        } else {
            log.warn("月结单仍未完全生成完毕! business:{},period:{},totalNumber:{},successNumber:{}"
                    , statementInfo.getBusinessType(), statementInfo.getPeriod(), totalNumber, successNumber);
        }
        newFundMonthlyStatementInfo.setFinishedNumber(successNumber);
        newFundMonthlyStatementInfo.setTotalNumber(totalNumber);
        int updateNumber = monthlyStatementInfoMapper.updateByCondition(newFundMonthlyStatementInfo, conditionFundMonthlyStatementInfo);
        if (updateNumber != 1) {
            log.warn("更新结单初始化状态失败.  business:{},period:{}", statementInfo.getBusinessType(), statementInfo.getPeriod());
            throw new BusinessException(STATEMENT_INFO_STATUS_EXCEPTION);
        } else {
            log.warn("更新结单初始化状态成功.  business:{},period:{}", statementInfo.getBusinessType(), statementInfo.getPeriod());
        }

        //如果全部初始化完成，发送企业微信通知
        if(Objects.equals(MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue(), newFundMonthlyStatementInfo.getInitStatus())){
            monthStatementInitFinishedWechatNotify(statementInfo.getPeriod(), successNumber);
        }
    }


    /**
     * 1.1.生成月结单记录并调用pub生成月结单pdf
     *
     * @param monthlyStatementInfo 当前月结单所在的月份  yyyyMM
     */
    @Override
    public void initAllUserMonthlyStatement(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype) {
        int pageNum = 1;
        boolean isAllUserSuccess = true;

        long start = System.currentTimeMillis();
        log.info("initAllUserMonthlyStatement start,businessSubtype:{}", businessSubtype);
        //无结单用户缓存
        Set<String> noStatementUserKeySet = queryCacheNoStatementUserKeySet(monthlyStatementInfo, businessSubtype);

        while (pageNum > 0) {
            long startPageTime = System.currentTimeMillis();

            List<UserInvestClientInfoDto> userInvestClientInfoDtoList = queryUserInvestAccountList(monthlyStatementInfo.getPeriod(), businessSubtype, pageNum);
            log.info("initAllUserMonthlyStatement pageNum:{},size:{},耗时：{}", pageNum, userInvestClientInfoDtoList.size(), System.currentTimeMillis() - startPageTime);

            //先计算出下一页
            pageNum = pageNum + 1;
            if (CollectionUtils.isEmpty(userInvestClientInfoDtoList)) {
                break;
            }

            //逐个用户处理
            for (UserInvestClientInfoDto userInvestClientInfoDto : userInvestClientInfoDtoList) {
                String userKey = userInvestClientInfoDto.getBankUserId() + "_" + userInvestClientInfoDto.getClientId();
                if (systemConfig.isCacheNoStatementUserSet() && noStatementUserKeySet.contains(userKey)) {
                    log.info("当前用户缓存中已标记无需生成月结单,直接跳过! allBusiness:{},period:{},businessSubtype:{},userKey:{}"
                            , monthlyStatementInfo.getBusinessType()
                            , monthlyStatementInfo.getPeriod()
                            , businessSubtype
                            , userKey
                    );
                    continue;
                }
                //过滤house账户数据，并把用户标记为无需生成月结单
                if (systemConfig.getHouseAccountList().contains(userInvestClientInfoDto.getAccountId())) {
                    log.info("过滤house账户数据，accountId:{}", userInvestClientInfoDto.getAccountId());
                    noStatementUserKeySet.add(userKey);
                    continue;
                }

                try {
                    String path = USER_MONTHLY_STATEMENT_INIT.key(monthlyStatementInfo.getPeriod()
                            , monthlyStatementInfo.getBusinessType()
                            , userInvestClientInfoDto.getClientId());

                    distributedLock.tryLock(path, () -> {
                        boolean isUserNeedGenerateStatement
                                = monthlyStatementService.initUserMonthlyStatement(monthlyStatementInfo, userInvestClientInfoDto, businessSubtype);

                        if (!isUserNeedGenerateStatement) {
                            noStatementUserKeySet.add(userKey);
                        } else {
                            if (noStatementUserKeySet.contains(userKey)) {
                                noStatementUserKeySet.remove(userKey);
                                log.info("清除缓存中已标记当前用户无需生成月结单! allBusiness:{},period:{},businessSubtype:{},userKey:{}"
                                        , monthlyStatementInfo.getBusinessType()
                                        , monthlyStatementInfo.getPeriod()
                                        , businessSubtype
                                        , userKey)
                                ;
                            }
                        }
                    });
                } catch (Exception e) {
                    isAllUserSuccess = false;
                    log.error("初始化用户结单记录异常,等待重试! userId:{},clientId:{}"
                            , userInvestClientInfoDto.getBankUserId(), userInvestClientInfoDto.getClientId(), e);
                }
            }
        }

        //完成初始化，标记一下
        if (isAllUserSuccess) {
            businessInitFinish(monthlyStatementInfo, businessSubtype);
        }

        log.info("initAllUserMonthlyStatement end,businessSubtype:{},耗时：{}", businessSubtype, System.currentTimeMillis() - start);

    }

    /**
     * 1.初始化用户结单记录
     * 双语言：英繁+英简
     *
     * @param monthlyStatementInfo 当前月结单所在的月份  yyyyMM
     */
    @Override
    public boolean initUserMonthlyStatement(TdFundMonthlyStatementInfo monthlyStatementInfo
            , UserInvestClientInfoDto userInvestClientInfoDto, String businessSubtype) {

        log.info("开始初始化用户月结单! businessType:{},userInvestClientInfoDto:{}", businessSubtype, userInvestClientInfoDto);

        String clientId = userInvestClientInfoDto.getClientId();
        String period = monthlyStatementInfo.getPeriod();
        String business = monthlyStatementInfo.getBusinessType();

        boolean isUserNeedGenerateStatement = false;

        //查当前账户当期的结单记录
        List<TdFundMonthlyStatement> statementList = monthlyStatementManager.listUserStatementRecords(period, clientId);
        //插入数据
        if (CollectionUtils.isEmpty(statementList)) {
            StatementBusinessService statementBusinessService = getStatementBusinessService(businessSubtype);
            String marketCode = "";
            if (AccountTypeEnum.STOCK.getValue().equals(businessSubtype)) {
                marketCode = MarketCodeEnum.US.getValue();
            }else if (AccountTypeEnum.HK_STOCK.getValue().equals(businessSubtype)) {
                marketCode = MarketCodeEnum.HK.getValue();
            }
            //如果需要生成结单且没有数据，那要初始化结单记录
            if (statementBusinessService.isUserNeedGenerateStatement(monthlyStatementInfo, userInvestClientInfoDto, marketCode)) {
                isUserNeedGenerateStatement = true;
                //初始化需要把所有业务的账户查出来
                Map<String, UserInvestClientInfoDto> businessTypeAndUserInfoDtoMap
                        = queryUserInvestAccountMap(period, userInvestClientInfoDto.getBankUserId(), clientId, business);

                List<UserStatementRecordDto> userStatementRecordDtoList = monthlyStatementManager
                        .initUserStatementRecord(monthlyStatementInfo, businessTypeAndUserInfoDtoMap, businessSubtype);

                userStatementRecordDtoList.forEach(userStatementRecordDto -> statementList.add(userStatementRecordDto.getStatementRecord()));
                log.info("完成用户月结单记录创建! period:{},userId:{}，businessSubtype:{}"
                        , period, userInvestClientInfoDto.getBankUserId(), businessSubtype);
            }
        } else {
            isUserNeedGenerateStatement = true;
        }

        //挑初始化状态的出来发送消息给pub去生成文件
        List<TdFundMonthlyStatement> finalStatementList = statementList.stream()
                .filter(statement -> statement.getDocStatus().equals(MonthlyStatementDocStatusEnum.INIT.getDbValue()))
                .collect(Collectors.toList());


        if (!CollectionUtils.isEmpty(finalStatementList)) {
            //发送消息
            userStatementDataPreparePublish(period, business, userInvestClientInfoDto.getBankUserId(), clientId, businessSubtype);
        }

        return isUserNeedGenerateStatement;

    }


    /**
     * 用户结单数据准备发布
     *
     * @param period
     * @param business
     */
    void userStatementDataPreparePublish(String period, String business, String bankUserId, String clientId, String businessSubtype) {
        UserMonthlyStatementDataPrepareDto userMonthlyStatementDataPrepareDto = new UserMonthlyStatementDataPrepareDto();
        userMonthlyStatementDataPrepareDto.setBankUserId(bankUserId);
        userMonthlyStatementDataPrepareDto.setPeriod(period);
        userMonthlyStatementDataPrepareDto.setBusiness(business);
        userMonthlyStatementDataPrepareDto.setClientId(clientId);
        userMonthlyStatementDataPrepareDto.setBusinessSubtype(businessSubtype);
        userStatementDataPrepareProducer.send(BaseMqMsg.getInstance(userMonthlyStatementDataPrepareDto, MqConstants.SERVER_NAME));
        log.info("发布用户结单数据准备消息! userMonthlyStatementDataPrepareDto:{}", userMonthlyStatementDataPrepareDto);
    }

    /**
     * 用户结单数据准备消费
     */
    @Override
    public void userStatementDataPrepareConsume(UserMonthlyStatementDataPrepareDto userMonthlyStatementDataPrepareDto) {
        String period = userMonthlyStatementDataPrepareDto.getPeriod();
        String allBusiness = userMonthlyStatementDataPrepareDto.getBusiness();
        String subBusiness = userMonthlyStatementDataPrepareDto.getBusinessSubtype();
        String bankUserId = userMonthlyStatementDataPrepareDto.getBankUserId();
        String clientId = userMonthlyStatementDataPrepareDto.getClientId();

        TdFundMonthlyStatementInfo monthlyStatementInfo = monthlyStatementManager.getStatement(period);
        if (monthlyStatementInfo == null) {
            log.error("月结单信息不存在! period:{},business:{}", period, allBusiness);
            throw new BusinessException(STATEMENT_INFO_NOT_FOUND);
        }

        if (isCacheUserStatementDataPrepareFinish(monthlyStatementInfo, userMonthlyStatementDataPrepareDto.getBusinessSubtype(), bankUserId, clientId)) {
            log.info("缓存中用户月结单数据已经准备完毕! period:{},business:{},subType:{},userId:{},clientId:{}"
                    , period, allBusiness, userMonthlyStatementDataPrepareDto.getBusinessSubtype(), bankUserId, clientId);
            return;
        }

        String path = USER_MONTHLY_STATEMENT_INIT.key(period, allBusiness, clientId);
        try {
            distributedLock.tryLock(path, 5, TimeUnit.SECONDS, () -> {
                //查当前账户当期的结单记录
                List<TdFundMonthlyStatement> statementList = monthlyStatementManager.listUserStatementRecords(period, clientId);
                if (CollectionUtils.isEmpty(statementList)) {
                    log.warn("未查询到当前账户当期的结单记录，userId:{}", bankUserId);
                    return;
                }

                boolean isAllBusinessAllDataOk = true;
                for (TdFundMonthlyStatement statementRecord : statementList) {
                    if (!statementRecord.getDocStatus().equals(MonthlyStatementDocStatusEnum.INIT.getDbValue())) {
                        continue;
                    }
                    boolean isBusinessDataOk = monthlyStatementService.prepareUserMonthlyStatementData(monthlyStatementInfo, statementRecord);
                    log.info("完成用户月结单记录数据填充! period:{},business:{},bankUserId:{},clientId:{},docLang:{},status:{}",
                            period, allBusiness, bankUserId, clientId, statementRecord.getDocLang(), isBusinessDataOk);
                    if (isBusinessDataOk) {
                        //数据都搞好了就更新结单记录的状态为待通知
                        monthlyStatementManager.userStatementInitFinish(statementRecord.getBusinessId());
                    } else {
                        isAllBusinessAllDataOk = false;
                    }
                }
                if (isAllBusinessAllDataOk) {
                    //全部处理完毕的在缓存标志一下
                    userStatementDataPrepareFinish(monthlyStatementInfo, subBusiness, bankUserId, clientId);
                }
            });
        } catch (LockException e) {
            log.warn("消费用户月结单数据准备任务获取锁失败! period:{},business:{},bankUserId:{},clientId:{}", period, allBusiness, bankUserId, clientId, e);
        } catch (Exception e) {
            log.error("消费用户月结单数据准备任务异常! period:{},business:{},bankUserId:{},clientId:{}", period, allBusiness, bankUserId, clientId, e);
        }
    }


    /**
     * 准备结单数据，单语言维度
     */
    @Override
    public boolean prepareUserMonthlyStatementData(TdFundMonthlyStatementInfo monthlyStatementInfo, TdFundMonthlyStatement monthlyStatement) {
        String statementId = monthlyStatement.getBusinessId();
        //结单对应的所有业务数据记录
        List<TdMonthlyStatementData> statementDataDtoList
                = monthlyStatementManager.listUserStatementDataRecord(monthlyStatement.getBusinessId());

        //是否结单下的所有业务数据都准备好了，都准备好了的话后面可以更新结单状态到等待通知状态
        boolean isAllBusinessAllDataOk = true;
        for (TdMonthlyStatementData statementData : statementDataDtoList) {
            if (!MonthlyStatementDataStatusEnum.PROCESSING.getDbValue().equals(statementData.getDataStatus())) {
                continue;
            }
            StatementBusinessService statementBusinessService = getStatementBusinessService(statementData.getBusinessType());
            if (!statementBusinessService.isDataPrepared(monthlyStatementInfo, true)) {
                log.info("获取用户月结单业务数据时，数据还未准备完成! business:{},statementId:{},dataId:{}"
                        , statementData.getBusinessType(), statementId, statementData.getStatementId());
                isAllBusinessAllDataOk = false;
                continue;
            }
            //抽取数据
            if (!monthlyStatementService.prepareStatementData(monthlyStatementInfo, monthlyStatement, statementData)) {
                isAllBusinessAllDataOk = false;
            }
        }

        return isAllBusinessAllDataOk;
    }


    /**
     * 准备结单数据
     */
    @Override
    public boolean prepareStatementData(TdFundMonthlyStatementInfo monthlyStatementInfo
            , TdFundMonthlyStatement monthlyStatement
            , TdMonthlyStatementData statementData) {

        String statementId = statementData.getStatementId();
        String businessSubtype = statementData.getBusinessType();
        StatementBusinessService statementBusinessService = getStatementBusinessService(statementData.getBusinessType());
        String marketCode = null;
        if (AccountTypeEnum.STOCK.getValue().equals(businessSubtype)) {
            marketCode = MarketCodeEnum.US.getValue();
         }else if (AccountTypeEnum.HK_STOCK.getValue().equals(businessSubtype)) {
            marketCode = MarketCodeEnum.HK.getValue();
        }
        boolean isUserNeedGenerateStatement
                = statementBusinessService.isUserNeedGenerateStatement(monthlyStatementInfo, monthlyStatement.getBankUserId(), statementData.getAccountId(), marketCode);

        if (isUserNeedGenerateStatement) {
            //需要生成结单的
            Map<String, Object> dataMap = statementBusinessService.generateDataMap(monthlyStatementInfo, monthlyStatement, statementData, marketCode);
            if (CollectionUtils.isEmpty(dataMap)) {
                log.error("用户月结单获取数据结果为空,请检查! statementId:{},dataId:{}", statementId, statementData.getBusinessId());
                throw new BusinessException(STATEMENT_SUB_BUSINESS_DATA_IS_EMPTY);
            } else {
                //当前业务需要生成结单的
                TdMonthlyStatementBusinessData monthlyStatementBusinessData = monthlyStatementManager.getUserStatementBusinessData(statementId, businessSubtype);
                if (monthlyStatementBusinessData != null) {
                    monthlyStatementManager.deleteUserStatementBusinessData(statementData.getStatementId(), statementData.getBusinessType());
                    log.info("用户结单数据在mongodb中已存在，删除数据! statementId:{},businessType:{}", statementData.getStatementId(), statementData.getBusinessType());
                }
                // 存入数据
                monthlyStatementManager.saveUserStatementBusinessData(statementData.getStatementId(), statementData.getBusinessType(), dataMap);
                //更新数据状态
                monthlyStatementManager.finishUserDataPrepare(statementData, true);
                log.info("更新用户结单数据状态为已处理! statementId:{},businessSubtype:{},statementDataId:{}"
                        , statementId, businessSubtype, statementData.getBusinessId());
            }
        } else {
            monthlyStatementManager.finishUserDataPrepare(statementData, false);
            log.info("更新用户结单数据状态为已处理-无数据! statementId:{},businessSubtype:{},statementDataId:{}"
                    , statementId, businessSubtype, statementData.getBusinessId());
        }
        return true;
    }


    /**
     * 发送前检查
     * 1.如果发送前检查开关打开且操作员还未确认结单发送，则不通过
     */
    Boolean checkBeforeNotifyStatement(String period
            , TdFundMonthlyStatementInfo fundMonthlyStatementInfo) {
        if (fundMonthlyStatementInfo == null) {
            log.warn("通知生成结单检查时，月结单信息不存在! period:{}", period);
            //无需告警
            return false;
        }
        return true;
    }

    /**
     * 2.通知生成pdf
     *
     * @param period 结单期数
     */
    @Override
    public void notifyGeneratePdf(String period) {
        TdFundMonthlyStatementInfo statementInfo = monthlyStatementManager.getStatement(period);
        //发送前检查
        if (!checkBeforeNotifyStatement(period, statementInfo)) {
            return;
        }

        Long latestId = 0L;
        List<TdFundMonthlyStatement> docUnNotifyStatementList = null;
        //分页
        for (; ; ) {
            //待通知生成的
            docUnNotifyStatementList = tdMonthlyStatementMapper.queryDocUnNotifyStatementList(period, latestId, systemConfig.getPageSize());

            if (CollectionUtils.isEmpty(docUnNotifyStatementList)) {
                log.info("没有需要通知生成月结单的记录! period:{}", period);
                break;
            }

            CountDownLatch countDownLatch = new CountDownLatch(docUnNotifyStatementList.size());
            //分用户
            for (TdFundMonthlyStatement docUnGeneratedStatement : docUnNotifyStatementList) {
                latestId = docUnGeneratedStatement.getId();

                List<TdMonthlyStatementData> monthlyStatementDataList = monthlyStatementManager.listUserStatementDataRecord(docUnGeneratedStatement.getBusinessId());
                //通知pub服务
                statementExecutor.execute(() -> {
                    try {
                        //结单维度
                        String path = USER_MONTHLY_STATEMENT_GENERATE.key(docUnGeneratedStatement.getBusinessId());
                        distributedLock.tryLock(path, () -> {
                            monthlyStatementService.notifyUserGeneratePdf(statementInfo, docUnGeneratedStatement, monthlyStatementDataList);
                        });
                    } catch (Exception e) {
                        log.error("通知生成用户月结单异常!userId:{}", docUnGeneratedStatement.getBankUserId(), e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }

            try {
                log.info("等待批量通知生成月结单任务全部完成");
                countDownLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待批量通知生成月结单任务全部完成被唤醒", e);
            }
        }
    }


    /**
     * 单用户结单生成pdf通知，并更新状态,每个结单一个事务
     *
     * @param docUnGeneratedStatement 未生成的结单
     */
    @Override
    public void notifyUserGeneratePdf(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement docUnGeneratedStatement
            , List<TdMonthlyStatementData> monthlyStatementDataList) {
        log.info("notifyUserGeneratePdf start,userId:{}", docUnGeneratedStatement.getBankUserId());
        TdFundMonthlyStatement statementRecord = monthlyStatementManager.getStatementRecord(docUnGeneratedStatement.getBusinessId());
        if (!MonthlyStatementDocStatusEnum.UN_NOTIFIED.getDbValue().equals(statementRecord.getDocStatus())) {
            log.info("用户结单记录非UN_NOTIFIED状态! statementId:{}", docUnGeneratedStatement.getBusinessId());
            return;
        }

        Map<String, Object> monthlyStatementDataMap = new HashMap<>();

        //结单数据
        List<TdMonthlyStatementBusinessData> statementBusinessDataList
                = monthlyStatementManager.listUserStatementBusinessData(docUnGeneratedStatement.getBusinessId());

        for (TdMonthlyStatementBusinessData tdMonthlyStatementBusinessData : statementBusinessDataList) {
            Map<String, Object> businessDataMap = tdMonthlyStatementBusinessData.getData();
            monthlyStatementDataMap.put(tdMonthlyStatementBusinessData.getBusinessType(), businessDataMap);
        }

        //结单基本信息最后才生成
        Map<String, Object> basicInfoMap = statementBasicInfoService.generateDataMap(statementInfo, docUnGeneratedStatement, monthlyStatementDataList);
        //填充业务开户状态
        //fillOpenAccountStatus(basicInfoMap, statementInfo, docUnGeneratedStatement, monthlyStatementDataList);

        monthlyStatementDataMap.put(STATEMENT_SUMMARY_DATA_TYPE_KEY, basicInfoMap);

        //落地mongodb
        monthlyStatementManager.deleteUserStatementBusinessData(docUnGeneratedStatement.getBusinessId(), STATEMENT_SUMMARY_DATA_TYPE_KEY);
        monthlyStatementManager.saveUserStatementBusinessData(docUnGeneratedStatement.getBusinessId(), STATEMENT_SUMMARY_DATA_TYPE_KEY, basicInfoMap);

        //给thymleaf中无数据列表用
        monthlyStatementDataMap.put("emptyList", new ArrayList<>(0));

        //通知pub服务生成结单
        GeneratePdfResp generatePdfResp
                = remoteNotifyGeneratePdf(statementInfo, docUnGeneratedStatement, monthlyStatementDataMap);

        if (generatePdfResp != null) {
            monthlyStatementManager.statementDocNotified(docUnGeneratedStatement.getBusinessId());
            log.info("已通知pub服务生成用户结单pdf! statementId:{}", docUnGeneratedStatement.getBusinessId());
        }
    }


    /**
     * 通知生成pdf
     */
    protected GeneratePdfResp remoteNotifyGeneratePdf(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, TdFundMonthlyStatement docUnGeneratedStatement
            , Map<String, Object> monthlyStatementDataMap) {
        // 模板
        String templateCode = this.getTemplateCode(tdFundMonthlyStatementInfo, docUnGeneratedStatement);
        GeneratePdfResp generatePdfResp
                = pubRemoteService.generatePdf(docUnGeneratedStatement.getBusinessId()
                , templateCode
                , monthlyStatementDataMap);

        if (BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_INIT.getValue().equals(generatePdfResp.getTaskStatus())
                || BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_WORK.getValue().equals(generatePdfResp.getTaskStatus())
                || BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_SUCCESS.getValue().equals(generatePdfResp.getTaskStatus())) {

            log.info("通知生成月结单成功! statementId:{} "
                    , docUnGeneratedStatement.getBusinessId());
            return generatePdfResp;
        } else if (BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_FAIL.getValue()
                .equals(generatePdfResp.getTaskStatus())) {

            log.error("通知生成月结单失败! statementId:{} ,period:{} , clientId:{}"
                    , docUnGeneratedStatement.getBusinessId()
                    , docUnGeneratedStatement.getPeriod()
                    , docUnGeneratedStatement.getClientId());
        }
        return null;
    }

    /**
     * 获取模板
     */
    private String getTemplateCode(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, TdFundMonthlyStatement docUnGeneratedStatement) {
        // 南向通
        if (ChannelEnum.SOUTH_BOUND.getValue().equals(docUnGeneratedStatement.getChannel())) {
            if (LanguageTemEnum.CN_ZH.getValue().equals(docUnGeneratedStatement.getDocLang())) {
                return tdFundMonthlyStatementInfo.getNxtZhTempKey();
            } else {
                return tdFundMonthlyStatementInfo.getNxtHkTempKey();
            }
        }

        // 非南向通
        if (LanguageTemEnum.CN_ZH.getValue().equals(docUnGeneratedStatement.getDocLang())) {
            return tdFundMonthlyStatementInfo.getZhTempKey();
        } else {
            return tdFundMonthlyStatementInfo.getHkTempKey();
        }
    }


    /**
     * 发送前检查
     * 1.如果发送前检查开关打开且操作员还未确认结单发送，则不通过
     */
    boolean checkBeforeSynStatementStatus(String period
            , TdFundMonthlyStatementInfo fundMonthlyStatementInfo) {
        if (fundMonthlyStatementInfo == null) {
            log.warn("同步结单状态检查时，月结单信息不存在! period:{}", period);
            return false;
        }
        return true;
    }


    /**
     * 3.同步结单pdf生成状态
     *
     * @param period 结单期数
     */
    @Override
    public void synStatementPdfGenerateStatus(String period) {
        //同步前检查下
        TdFundMonthlyStatementInfo fundMonthlyStatementInfo = monthlyStatementManager.getStatement(period);
        boolean checkBeforeSynStatementStatus = checkBeforeSynStatementStatus(period, fundMonthlyStatementInfo);
        if (!checkBeforeSynStatementStatus) {
            return;
        }

        log.info("同步月结单pdf生成状态! period:{}", period);
        Long latestId = 0L;
        for (; ; ) {
            //待生成状态的结单
            List<TdFundMonthlyStatement> docUnGeneratedStatementList = tdMonthlyStatementMapper
                    .queryDocUnGeneratedStatementList(period, latestId, systemConfig.getPageSize());

            if (CollectionUtils.isEmpty(docUnGeneratedStatementList)) {
                log.info("没有需要同步结单pdf文件生成状态的记录! period:{}", period);
                break;
            }
            for (TdFundMonthlyStatement docUnGeneratedStatement : docUnGeneratedStatementList) {
                latestId = docUnGeneratedStatement.getId();
                try {
                    QueryPdfResp queryPdfResp = pubRemoteService.queryPdf(docUnGeneratedStatement.getBusinessId());
                    String taskStatus = queryPdfResp.getTaskStatus();
                    if (BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_INIT.getValue().equals(taskStatus)
                            || BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_WORK.getValue().equals(taskStatus)) {
                        continue;
                    }

                    if (BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_FAIL.getValue().equals(taskStatus)) {
                        log.error("月结单pdf文件生成失败，请检查! statementId:{}"
                                , docUnGeneratedStatement.getBusinessId());
                        continue;
                    }
                    //只同步成功的
                    if (BusinessTaskStatusEnum.BUSINESS_TASK_STATUS_SUCCESS.getValue().equals(taskStatus)
                            && !Strings.isEmpty(queryPdfResp.getPdfPath())) {
                        docUnGeneratedStatement.setHtmlUrl(queryPdfResp.getHtmlPath());
                        //解析html数据
                        userStatementNativeService.htmlDocParseProcess(fundMonthlyStatementInfo, docUnGeneratedStatement);

                        monthlyStatementService.synUserStatementPdfGenerateStatus(period, docUnGeneratedStatement.getBusinessId()
                                , queryPdfResp.getPdfPath(), queryPdfResp.getHtmlPath());
                    }
                } catch (Exception e) {
                    log.error("用户月结单pdf文件生成状态同步异常，请检查! statementId:{}"
                            , docUnGeneratedStatement.getBusinessId(), e);
                }
            }
        }
    }


    /**
     * 3.同步用户结单pdf生成状态
     *
     * @param period
     * @param statementId
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void synUserStatementPdfGenerateStatus(String period, String statementId, String pdfPath, String htmlPath) {
        monthlyStatementManager.userStatementDocGenerated(statementId, pdfPath, htmlPath);
        log.info("结单pdf文件已生成. statementId:{} ", statementId);
    }


    /**
     * 发送前检查
     * 1.如果发送前检查开关打开且操作员还未确认结单发送，则不通过
     */
    boolean checkBeforePubStatement(String period, TdFundMonthlyStatementInfo fundMonthlyStatementInfo) {
        if (fundMonthlyStatementInfo == null) {
            log.warn("推送前检查，月结单信息不存在! period:{}", period);
            return false;
        }

        if (MonthlyStatementInfoStatusEnum.FINISHED.getDbValue().equals(fundMonthlyStatementInfo.getStatus())) {
            log.warn("推送前检查, 月结单已全部完成，不向用户推送月结单！period:{}", period);
            return false;
        }


        //必须是已经确认了的才能推送
        if (systemConfig.isCheckConfirmStatusBeforePub()
                && !MonthlyStatementPubConfirmStatusEnum.CONFIRMED.getDbValue()
                .equals(fundMonthlyStatementInfo.getPubConfirmStatus())) {
            log.warn("推送前检查, 月结单还未经确认，暂不向用户推送月结单！period:{}", period);
            return false;
        }

        return true;
    }

    /**
     * 4.发送月结单
     *
     * @param period 结单期数
     */
    @Override
    public void pubMonthlyStatement(String period) {
        TdFundMonthlyStatementInfo statementInfo = monthlyStatementManager.getStatement(period);
        boolean checkBeforePubStatement = checkBeforePubStatement(period, statementInfo);
        if (!checkBeforePubStatement) {
            return;
        }

        log.info("月结单开始向用户推送! period:{}", period);
        Long latestId = 0L;
        while (true) {
            //每个用户有多条记录，只要发送一份了就算成功了，所以需要根据userId去groupBy,并取pub_status最大值，只要等于1就可以了
            List<TdFundMonthlyStatement> docUnPublishedStatementList
                    = tdMonthlyStatementMapper.queryDocUnPublishedStatementList(period, latestId, systemConfig.getPageSize());

            if (CollectionUtils.isEmpty(docUnPublishedStatementList)) {
                log.info("没有需要推送的月结单记录! period:{}", period);
                break;
            }

            for (TdFundMonthlyStatement unPubStatement : docUnPublishedStatementList) {
                latestId = unPubStatement.getId();
                try {
                    String path = MONTHLY_STATEMENT_PUB.key(period, unPubStatement.getClientId());
                    distributedLock.tryLock(path, () -> {
                        //当前用户设置了什么语言
                        String lang = userRemoteService.queryUserLanguage(unPubStatement.getBankUserId());
                        String finalLang = selectLang(lang);
                        monthlyStatementService.pubUserMonthlyStatement(statementInfo, unPubStatement, finalLang);
                    });
                } catch (LockException e) {
                    log.error("用户月结单推送获取锁失败。请留意! period:{} , userId:{}"
                            , period
                            , unPubStatement.getBankUserId()
                            , e);
                } catch (Exception e) {
                    log.error("用户月结单推送失败,中断后续发送。请留意! period:{} , userId:{}"
                            , period
                            , unPubStatement.getBankUserId()
                            , e);
                }
            }
        }

        // 更新td_fund_monthly_statement_info的状态为处理完成
        if (MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue().equals(statementInfo.getInitStatus())) {
            this.updateStatementInfoStatusToFinish(period, statementInfo);
        }

    }

    private void updateStatementInfoStatusToFinish(String period, TdFundMonthlyStatementInfo statementInfo) {
        // 发送成功的用户数量
        int pubSuccessAccountNumber = tdMonthlyStatementMapper.countPublishSuccessAccountNumber(period);
        if (pubSuccessAccountNumber == statementInfo.getTotalNumber().intValue()) {
            TdFundMonthlyStatementInfo condition = new TdFundMonthlyStatementInfo();
            condition.setId(statementInfo.getId());
            condition.setStatus(MonthlyStatementInfoStatusEnum.PROCESSING.getDbValue());

            TdFundMonthlyStatementInfo target = new TdFundMonthlyStatementInfo();
            target.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());
            target.setGmtModified(new Date());

            int count = monthlyStatementInfoMapper.updateByCondition(target, condition);
            if (count != 1) {
                log.error("update TdFundMonthlyStatementInfo.status PROCESSING TO FINISHED failed, 人工排查, TdFundMonthlyStatementInfo：{}", JSON.toJSONString(statementInfo));
            }

            // 企微通知月结单发送完成
            this.monthStatementSendFinishedWechatNotify(period, pubSuccessAccountNumber);
        }
    }

    /**
     * 企微通知月结单发送完成
     */
    private void monthStatementSendFinishedWechatNotify(String period, Integer totalNumber) {
        try {
            List<RemarkDto> remarkDtoList = tdMonthlyStatementMapper.publishSuccessAccountNumberRemark(period);
            // 银行账户销户使用邮件地址发送成功数量
            int sendUseEmailSuccessNum = 0;
            // 银行账户销户使用邮件地址发送失败数量
            int sendUseEmailFailedNum = 0;
            // 银行账户销户查邮箱地址失败数量
            int sendQueryEmailFailedNum = 0;
            for (RemarkDto remarkDto : remarkDtoList) {
                String remark = remarkDto.getRemark();
                int num = remarkDto.getNum();
                if (remark.contains(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_SEND_USE_EMAIL_SUCCESS)) {
                    sendUseEmailSuccessNum += num;
                } else if (remark.contains(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_SEND_USE_EMAIL_FAILED)) {
                    sendUseEmailFailedNum += num;
                } else if (remark.contains(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_SEND_QUERY_EMAIL_FAILED)) {
                    sendQueryEmailFailedNum += num;
                }
            }

            StringBuilder message = new StringBuilder();
            message.append("ftc-statement月结单发送已完成，请知悉！");
            message.append("\nperiod：");
            message.append(period);
            message.append("\n月结单发送成功数量：");
            message.append(totalNumber);
            message.append("\n银行账户销户使用邮件地址发送成功数量：");
            message.append(sendUseEmailSuccessNum);
            message.append("\n银行账户销户使用邮件地址发送失败数量：");
            message.append(sendUseEmailFailedNum);
            message.append("\n银行账户销户查邮箱地址失败数量：");
            message.append(sendQueryEmailFailedNum);
            alarmUtil.wechatAlarm(false, message.toString(), StatementAlarmModuleEnum.MONTHLY_STATEMENT_SEND_FINISHED, null);
        } catch (Exception e) {
            log.error("monthStatementSendFinishedWechatNotify wechatNotify error, period:{}", period, e);
        }
    }

    /**
     * 企微通知月结单初始化完成
     * @param period
     * @param totalNumber
     */
    private void monthStatementInitFinishedWechatNotify(String period, Integer totalNumber){
        try {
            StringBuilder message = new StringBuilder();
            message.append("ftc-statement月结单初始化已完成，请知悉！");
            message.append("\nperiod：");
            message.append(period);
            message.append("\n月结单初始化总数量：");
            message.append(totalNumber);
            alarmUtil.wechatAlarm(false, message.toString(), StatementAlarmModuleEnum.MONTHLY_STATEMENT_INIT_FINISHED, null);
        } catch (Exception e) {
            log.error("monthStatementInitFinishedWechatNotify wechatNotify error, period:{}", period, e);
        }
    }

    /**
     * 企微通知月结单确认完成
     * @param period
     */
    private void monthStatementConfirmedFinishedWechatNotify(String period){
        try {
            StringBuilder message = new StringBuilder();
            message.append("ftc-statement月结单后管已确认，请知悉！");
            message.append("\nperiod：");
            message.append(period);
            alarmUtil.wechatAlarm(false, message.toString(), StatementAlarmModuleEnum.MONTHLY_STATEMENT_CONFIRM_FINISHED, null);
        } catch (Exception e) {
            log.error("monthStatementConfirmedFinishedWechatNotify wechatNotify error, period:{}", period, e);
        }
    }

    /**
     * 发送用户结单
     *
     * @param statementInfo  结单信息
     * @param unPubStatement 未发送的结单
     */
    @Override
    public void pubUserMonthlyStatement(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement unPubStatement, String lang) {
        String business = statementInfo.getBusinessType();
        String period = statementInfo.getPeriod();
        String clientId = unPubStatement.getClientId();

        TdFundMonthlyStatement tdFundMonthlyStatement
                = monthlyStatementManager.getStatementRecordByDocLang(period, business, clientId, lang);

        if (MonthlyStatementPubStatusEnum.FINISHED.getDbValue().equals(tdFundMonthlyStatement.getPubStatus())) {
            log.info("用户结单已推送，不重复推送! statementId:{}", unPubStatement.getBusinessId());
            return;
        }

        //推送结单
        Pair<Boolean, String> resultPair = pubMonthlyStatement(unPubStatement);

        if (resultPair.getKey()) {
            //获取总市值和持仓盈亏
            TdMonthlyStatementBusinessData monthlyStatementBusinessData = monthlyStatementManager
                    .getUserStatementBusinessData(tdFundMonthlyStatement.getBusinessId(), STATEMENT_SUMMARY_DATA_TYPE_KEY);

            Map<String, Object> statementDataMap = monthlyStatementBusinessData.getData();
            BaseMonthlyStatementDataDto baseMonthlyStatementDataDto = JSONObject.parse(JsonUtils.toJsonString(statementDataMap)
                    , BaseMonthlyStatementDataDto.class);

            monthlyStatementService.finishPubUserMonthlyStatement(statementInfo, tdFundMonthlyStatement, resultPair.getValue(), baseMonthlyStatementDataDto);
        } else {
            log.error("月结单推送失败,请排查! business:{},period:{},clientId:{}"
                    , business, period, clientId);
        }

    }


    /**
     * activity要在事务中执行
     *
     * @param statementInfo
     * @param unPubStatement
     * @param baseMonthlyStatementDataDto
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void finishPubUserMonthlyStatement(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement unPubStatement, String remark, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto) {
        activityService.monthlyStatementActivityPub(statementInfo, unPubStatement, baseMonthlyStatementDataDto);
        monthlyStatementManager.userStatementPubFinished(unPubStatement.getBusinessId(), remark);
    }

    /**
     * 结单发送完毕后出发
     *
     * @param statementInfo
     */
    void removeStatementAllCache(TdFundMonthlyStatementInfo statementInfo) {
        String period = statementInfo.getPeriod();
        String business = statementInfo.getBusinessType();

        String[] businessSubtypeArray = business.split(systemConfig.businessSubTypeSeparator);
        //每个业务单独处理 stock,fund
        for (String businessSubtype : businessSubtypeArray) {
            try {
                StatementBusinessService statementBusinessService = getStatementBusinessService(businessSubtype);

                statementBusinessService.statementAllProcessFinishHook(statementInfo);
                //清除用户准备好数据的缓存
                clearUserStatementDataPrepareStatusCache(statementInfo, businessSubtype);
                //清除本期无需生成结单的缓存标记
                clearNoStatementUserKeySetCache(statementInfo, businessSubtype);

            } catch (Exception e) {
                log.error("{}-{}月结单生成推送完毕后子业务处理异常，请检查!", period, business, e);
                throw e;
            }
        }

        //总的缓存，不区分业务
        try {
            clearStatementBusinessInitCache(statementInfo);
        } catch (Exception e) {
            log.error("清除{}-{}月结单完成状态缓存失败，请检查!", period, business, e);
            throw e;
        }
    }

    /**
     * 发送月结单消息推送和邮件
     */
    Pair<Boolean, String> pubMonthlyStatement(TdFundMonthlyStatement monthlyStatement) {
        // 发送月结单通知
        boolean sendResult = this.sendInvestMonthlyStatementMessage(monthlyStatement.getDocLang(), monthlyStatement.getBankUserId(), monthlyStatement.getPeriod(), monthlyStatement.getChannel());
        String remark = monthlyStatement.getRemark();
        if (!sendResult) {
            log.error("pubMonthlyStatement, 月结单通知使用bankUserId发送失败, bankUserId:{}, clientId:{}", monthlyStatement.getBankUserId(), monthlyStatement.getClientId());
        }
        return new Pair<>(true, remark);
    }

    /**
     * 发送月结单邮件
     *
     * @return
     */
    boolean sendInvestMonthlyStatementMessage(String lang, String receiver, String period, String channel) {
        // 普通基金
        String templateCode = StatementMsgTemplateCodeEnum.MONTHLY_INVEST_STATEMENT.getTemplateCode();
        if (ChannelEnum.SOUTH_BOUND.getValue().equals(channel)) {
            // 南向通
            templateCode = StatementMsgTemplateCodeEnum.SOUTH_BOUND_MONTHLY_INVEST_STATEMENT.getTemplateCode();
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        //yyyy-MM
        String monthlyStatementPeriod = period.substring(0, 4) + "-" + period.substring(4);
        paramMap.put("monthlyStatementPeriod", monthlyStatementPeriod);
        SendSingleMessageReq sendSingleMessageReq = MessageHelper.buildCommonSendSingleMessageReq(templateCode, receiver, lang, paramMap);
        return pubRemoteService.sendSingleMessage(sendSingleMessageReq, false);
    }


    /**
     * 结单推送前确认
     *
     * @param fundMonthlyStatementConfirmReq 结单推送确认
     */
    @Override
    public MonthlyStatementConfirmResp pubConfirm(MonthlyStatementConfirmReq fundMonthlyStatementConfirmReq) {
        String period = fundMonthlyStatementConfirmReq.getPeriod();
        if (Strings.isEmpty(period)) {
            Date currentDate = new Date();
            Date monthlyStatementDate = DateUtil.getLastDayOfLastMonth(currentDate);
            period = DateUtil.format(monthlyStatementDate, systemConfig.getMonthlyStatementPeriodFormat());
        }

        String business = systemConfig.getDefaultMonthlyStatementBusinessType();

        TdFundMonthlyStatementInfo monthlyStatementInfo
                = monthlyStatementManager.getStatement(period);
        //结单不存在
        if (monthlyStatementInfo == null) {
            log.warn("推送时,{}月结单信息不存在! period:{}", business, period);
            throw new BusinessException(STATEMENT_INFO_NOT_FOUND);
        }

        if (MonthlyStatementPubConfirmStatusEnum.CONFIRMED.getDbValue()
                .equals(monthlyStatementInfo.getPubConfirmStatus())) {
            log.warn("月结单已确认可以推送，请勿重复确认! business:{},period:{}", business, period);
            throw new BusinessException(STATEMENT_ALREADY_PUB_CONFIRMED, period);
        }


        //检查下是否有未生成完毕的结单，如果是的话不给确认
        if (systemConfig.isCheckAllStatementDocGenerateStatusBeforePubConfirm()
                && !MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue().equals(monthlyStatementInfo.getInitStatus())) {
            log.warn("完整性检查模式下,存在部分月结单未生成完毕,不允许确认推送! period:{},business:{}"
                    , period, business);
            throw new BusinessException(STATEMENT_DOC_GENERATE_NOT_COMPLETE);
        }


        log.info("{}-{}月结单确认推送，清除本期结单所有涉及缓存!", period, business);
        removeStatementAllCache(monthlyStatementInfo);

        monthlyStatementManager.statementPubConfirm(period, fundMonthlyStatementConfirmReq.getBankUserId());

        //发送企业微信通知
        monthStatementConfirmedFinishedWechatNotify(period);

        return new MonthlyStatementConfirmResp();
    }


    /**
     * 根据业务名称获取对应的数据服务类
     *
     * @param businessSubType 业务类型
     */
    @Override
    public StatementBusinessService getStatementBusinessService(String businessSubType) {
        Optional<StatementBusinessService> statementBusinessServiceOptional = statementBusinessServiceList.stream()
                .filter(e -> e.getBusinessType().equals(businessSubType)).findFirst();

        if (!statementBusinessServiceOptional.isPresent()) {
            throw new BusinessException(StatementErrorMsgEnum.STATEMENT_BUSINESS_DATA_SERVICE_NOT_FOUND);
        }
        StatementBusinessService statementBusinessService = statementBusinessServiceOptional.get();
        log.info("获取结单子业务服务类! businessSubType:{},class:{}", businessSubType, statementBusinessService.getClass());
        return statementBusinessService;
    }


    /**
     * 分页查用户
     * 1.存活状态的-开户时间小于等于本期期末最后时刻的，且未销户
     * 2.销户状态的-销户时间大于等于本期期初最初时刻的，且销户的
     *
     * @param businessSubtype 业务类型
     * @param pageNumber      页数
     */
    List<UserInvestClientInfoDto> queryUserInvestAccountList(String period, String businessSubtype, int pageNumber) {
        long start = System.currentTimeMillis();

        int pageSize = systemConfig.getPageSize();
        UserInvestAccountConditionHolder userInvestAccountConditionHolder = new UserInvestAccountConditionHolder();
        List<String> accountTypeList = new ArrayList<>(1);
        accountTypeList.add(businessSubtype);
        userInvestAccountConditionHolder.setAccountTypes(accountTypeList);
        userInvestAccountConditionHolder.setQueryAllDataFlag(EnumYesOrNo.YES.getValue());

        Date periodDate = DateUtil.parse(period, systemConfig.getMonthlyStatementPeriodFormat());
        Date periodEndDate = DateUtil.getLastDayOfMonth(periodDate);
        periodEndDate = DateUtil.endOfDay(periodEndDate);

        //开户截止期  在本期结单期末前开户的都算   OpenFinishedEndTime<=periodEndDate
        //userInvestAccountConditionHolder.setOpenFinishedEndTime(periodEndDate);

        //销户起始日期 在本期结单期末后销户的都算 CloseStartTime>periodEndDate
        //userInvestAccountConditionHolder.setCloseStartTime(new Date(periodEndDate.getTime()+1));

        Page<UserInvestAccount> resultPage = PageHelper.startPage(pageNumber, pageSize, false)
                .doSelectPage(() -> userInvestAccountMapper.queryUserInvestAccountByCondition(userInvestAccountConditionHolder));


        List<UserInvestAccount> userInvestAccountList = resultPage.getResult();
        if (CollectionUtils.isEmpty(userInvestAccountList)) {
            return new ArrayList<>(0);
        }

        log.info("queryUserInvestAccountList第{}页，耗时:{}ms", pageNumber, System.currentTimeMillis() - start);

        return userInvestAccountList.stream()
                .map(userInvestAccount -> changeUserInvestAccount2Dto(businessSubtype, userInvestAccount))
                .collect(Collectors.toList());
    }

    /**
     * 单个用户账户信息查询
     *
     * @param businessSubtype
     * @param clientId
     * @return
     */
    UserInvestClientInfoDto queryUserInvestAccountSingle(String businessSubtype, String clientId) {
        UserInvestAccountConditionHolder userInvestAccountConditionHolder = new UserInvestAccountConditionHolder();
        List<String> accountTypeList = new ArrayList<>(1);
        accountTypeList.add(businessSubtype);
        userInvestAccountConditionHolder.setAccountTypes(accountTypeList);
        userInvestAccountConditionHolder.setQueryAllDataFlag(EnumYesOrNo.YES.getValue());
        userInvestAccountConditionHolder.setClientId(clientId);

        List<UserInvestAccount> userInvestAccounts = userInvestAccountMapper.queryUserInvestAccountByCondition(userInvestAccountConditionHolder);
        if (CollectionUtils.isEmpty(userInvestAccounts)) {
            return null;
        }
        return changeUserInvestAccount2Dto(businessSubtype, userInvestAccounts.get(0));
    }


    /**
     * 1.存活状态的-开户时间小于等于本期期末最后时刻的，且未销户
     * 2.销户状态的-销户时间大于等于本期期初最初时刻的，且销户的
     */
    public Map<String, UserInvestClientInfoDto> queryUserInvestAccountMap(String period, String bankUserId, String clientId, String business) {
        String[] businessSubtypeArray = business.split(systemConfig.getBusinessSubTypeSeparator());

        List<String> businessSubtypeList = Arrays.asList(businessSubtypeArray);

        UserInvestAccountConditionHolder userInvestAccountConditionHolder = new UserInvestAccountConditionHolder();
        userInvestAccountConditionHolder.setAccountTypes(businessSubtypeList);
        userInvestAccountConditionHolder.setQueryAllDataFlag(EnumYesOrNo.YES.getValue());

        //List<String> userIdList = new ArrayList<>(1);
        //userIdList.add(bankUserId);
        //userInvestAccountConditionHolder.setBankUserIdList(userIdList);
        userInvestAccountConditionHolder.setClientId(clientId);

        Date periodDate = DateUtil.parse(period, systemConfig.getMonthlyStatementPeriodFormat());
        Date periodEndDate = DateUtil.getLastDayOfMonth(periodDate);
        periodEndDate = DateUtil.endOfDay(periodEndDate);

        //开户截止期  在本期结单期末前开户的都算   OpenFinishedEndTime<=periodEndDate
        //userInvestAccountConditionHolder.setOpenFinishedEndTime(periodEndDate);

        //销户起始日期 在本期结单期末后销户的都算 CloseStartTime>periodEndDate
        //userInvestAccountConditionHolder.setCloseStartTime(new Date(periodEndDate.getTime()+1));

        List<UserInvestAccount> userInvestAccountList = userInvestAccountMapper.queryUserInvestAccountByCondition(userInvestAccountConditionHolder);


        if (CollectionUtils.isEmpty(userInvestAccountList)) {
            return new HashMap<>(0);
        }

        return userInvestAccountList.stream()
                .filter(userInvestAccount -> userInvestAccount.getClientId().equals(clientId))
                .filter(userInvestAccount -> userInvestAccount.getBankUserId().contains(bankUserId))
                .map(userInvestAccount -> changeUserInvestAccount2Dto(userInvestAccount.getAccountType(), userInvestAccount))
                .collect(Collectors.toMap(UserInvestClientInfoDto::getAccountType, userInvestClientInfoDto -> userInvestClientInfoDto));
    }


    /**
     * 转换一下账户
     *
     * @param userInvestAccount 用户投资账户信息
     */
    UserInvestClientInfoDto changeUserInvestAccount2Dto(String businessType, UserInvestAccount userInvestAccount) {
        UserInvestClientInfoDto userInvestClientInfoDto = new UserInvestClientInfoDto();
        userInvestClientInfoDto.setClientId(userInvestAccount.getClientId());
        userInvestClientInfoDto.setAccountId(userInvestAccount.getBrokerAccountId());
        userInvestClientInfoDto.setBankAccountId(userInvestAccount.getBankAccountId());
        userInvestClientInfoDto.setOpenFinishedTime(userInvestAccount.getOpenFinishedTime());
        String bankUserId = removeBankUserIdPrefix(userInvestAccount.getBankUserId());
        userInvestClientInfoDto.setBankUserId(bankUserId);
        userInvestClientInfoDto.setAccountType(userInvestAccount.getAccountType());
        return userInvestClientInfoDto;
    }


    /**
     * 检查当月月结单生成的情况
     *
     * @param period
     */
    @Override
    public void monthlyStatementsProcessStatusCheck(String period) {
        TdFundMonthlyStatementInfo condition = new TdFundMonthlyStatementInfo();
        condition.setPeriod(period);
        condition.setIsDeleted(InvestConstants.N);
        TdFundMonthlyStatementInfo fundMonthlyStatementInfo = monthlyStatementInfoMapper.selectOne(condition);
        if (fundMonthlyStatementInfo == null
                || fundMonthlyStatementInfo.getTotalNumber() == null) {
            log.warn("当月月结单初始化仍未完成，请留意! period:{}", period);
            alarmUtil.wechatAlarm(false, "当月" + period + "月结单初始化仍未完成，请留意!"
                    , MONTHLY_STATEMENT_INIT_ERROR
                    , null);
            return;
        }


        //总的账户数量
        Integer totalAccountNumber = fundMonthlyStatementInfo.getTotalNumber();

        //已经生成成功数量
        TdFundMonthlyStatement conditionSuccessGeneratePdf = new TdFundMonthlyStatement();
        conditionSuccessGeneratePdf.setPeriod(period);
        conditionSuccessGeneratePdf.setDocStatus(FundMonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        conditionSuccessGeneratePdf.setIsDeleted(InvestConstants.N);
        int successGeneratePdfNumber = tdMonthlyStatementMapper.selectCount(conditionSuccessGeneratePdf);

        //已发送成功数据量
        TdFundMonthlyStatement conditionSuccessPub = new TdFundMonthlyStatement();
        conditionSuccessPub.setPeriod(period);
        conditionSuccessPub.setPubStatus(FundMonthlyStatementPubStatusEnum.FINISHED.getDbValue());
        conditionSuccessPub.setIsDeleted(InvestConstants.N);
        Integer successPubNumber = tdMonthlyStatementMapper.selectCount(conditionSuccessPub);


        log.info("本期({})月结单需要生成的账户数量为:{}。" +
                        "\n\r当前已成功生成月结单pdf:{}份，仍有{}份未生成成功。" +
                        "\n\r当前已成功发送月结单:{}份，仍有{}个账户未发送成功."
                , period
                , totalAccountNumber
                , (successGeneratePdfNumber), totalAccountNumber * 2 - successGeneratePdfNumber
                , successPubNumber, totalAccountNumber - successPubNumber);

        if (successGeneratePdfNumber < (totalAccountNumber * 2)) {
            String messageBuilder = "当月" + period + "月结单文件仍未生成完毕，请留意!" +
                    "\n\r本期月结单需要生成的账户数量为:" + totalAccountNumber + "。" +
                    "\n\r当前已成功生成月结单pdf:" + successGeneratePdfNumber +
                    "份，仍有" + (totalAccountNumber * 2 - successGeneratePdfNumber) + "份未生成成功。";
            alarmUtil.wechatAlarm(false, messageBuilder
                    , MONTHLY_STATEMENT_DOC_GENERATE_ERROR
                    , null);
            log.warn("当月月结单文件仍未生成完毕，请留意! period:{}", period);
            return;
        }


        if (successPubNumber < totalAccountNumber) {
            String messageBuilder = "当月" + period + "月结单推送仍未生成完毕，请留意!" + "\n\r本期月结单需要生成的账户数量为:" + totalAccountNumber + "。" +
                    "\n\r当前已成功生成月结单pdf:" + successGeneratePdfNumber +
                    "份，仍有" + (totalAccountNumber * 2 - successGeneratePdfNumber) + "份未生成成功。" +
                    "\n\r当前已成功发送月结单:" + successPubNumber +
                    "份，仍有" + (totalAccountNumber - successPubNumber) + "个账户未推送成功。";
            alarmUtil.wechatAlarm(false, messageBuilder
                    , MONTHLY_STATEMENT_DOC_GENERATE_ERROR
                    , null);
            log.warn("当月月结单推送仍未发送完毕，请留意! period:{}", period);
        }


    }


    /**
     * 查询用户的月结单，按月份分组
     *
     * @param i18nSupportEnum
     * @param req
     * @return
     */
    @Override
    public UserMonthlyStatementListResp queryUserMonthlyStatement(I18nSupportEnum i18nSupportEnum, AppUserMonthlyStatementListReq req) {
        //封装查询结果
        UserMonthlyStatementQueryDto userMonthlyStatementQueryDto = dealWithUserStatementQuery(i18nSupportEnum, req);
        //格式化查询结果
        UserMonthlyStatementListResp userFmtListResp = fillStatement(userMonthlyStatementQueryDto);
        log.info("用户月结单列表:{}", JsonUtils.toJsonString(userFmtListResp));
        return userFmtListResp;
    }


    /**
     * 查询月结单列表
     *
     * @param i18nSupportEnum
     * @param req
     * @return
     */
    @Override
    public UserMonthlyStatementQueryDto dealWithUserStatementQuery(I18nSupportEnum i18nSupportEnum, AppUserMonthlyStatementListReq req) {

        String year = req.getYear();
        String bankUserId = req.getBankUserId();
        String lang = selectLang(i18nSupportEnum.getName());
        //账户信息
        List<UserInvestAccountInfo> userInvestAccountInfoList = userRemoteService.queryUserAllInvestAccountInfo(req.getBankUserId());
        UserInvestAccountInfo userInvestAccountInfo = null;
        Date firstOpenTime = null;
        for (UserInvestAccountInfo currentUserInvestAccountInfo : userInvestAccountInfoList) {
            if (firstOpenTime == null) {
                userInvestAccountInfo = currentUserInvestAccountInfo;
                firstOpenTime = userInvestAccountInfo.getOpenFinishedTime();
            }

            if (firstOpenTime.after(userInvestAccountInfo.getOpenFinishedTime())) {
                userInvestAccountInfo = currentUserInvestAccountInfo;
                firstOpenTime = userInvestAccountInfo.getOpenFinishedTime();
            }
        }
        if (userInvestAccountInfo == null) {
            throw new BusinessException(USER_INVEST_ACCOUNT_NOT_FOUND);
        }
        String clientId = userInvestAccountInfo.getClientId();

        //当月最后一天
        Date lastDateOfMonth = DateUtil.getLastDayOfMonth(new Date());

        //用户时间范围内的所有月结单，不管是否已发送
        List<TdFundMonthlyStatement> tdFundMonthlyStatementList
                = tdMonthlyStatementMapper.queryUserFundMonthlyStatementList(bankUserId, clientId, null, year, null);

        Set<String> pubStatementPeriodSet = new HashSet<>();
        tdFundMonthlyStatementList.forEach(monthlyStatement -> {
            if (FundMonthlyStatementPubStatusEnum.FINISHED.getDbValue().equals(monthlyStatement.getPubStatus())) {
                pubStatementPeriodSet.add(monthlyStatement.getPeriod());
            }
        });


        //只要已发送的
        Map<String, TdFundMonthlyStatement> pubPeriodMap = new HashMap<>();

        tdFundMonthlyStatementList = tdFundMonthlyStatementList.stream()
                .filter(tdFundMonthlyStatement -> pubStatementPeriodSet.contains(tdFundMonthlyStatement.getPeriod()))
                .filter(tdFundMonthlyStatement -> lang.equals(tdFundMonthlyStatement.getDocLang())).collect(Collectors.toList());

        tdFundMonthlyStatementList.forEach(tdFundMonthlyStatement -> pubPeriodMap.put(tdFundMonthlyStatement.getPeriod(), tdFundMonthlyStatement));

        String endPeriod = DateUtil.format(lastDateOfMonth, DateUtil.FORMAT_SHORT);
        String startPeriod = DateUtil.format(lastDateOfMonth, DateUtil.FORMAT_SHORT);
        //用户已发送且语言与当前对应的结单
        Map<String, TdFundMonthlyStatement> userPubFundMonthlyStatementMap = new HashMap<>(tdFundMonthlyStatementList.size());

        for (TdFundMonthlyStatement tdFundMonthlyStatement : tdFundMonthlyStatementList) {
            String currentPeriod = tdFundMonthlyStatement.getPeriod();

            if (pubPeriodMap.get(currentPeriod) != null) {
                userPubFundMonthlyStatementMap.put(currentPeriod, tdFundMonthlyStatement);
                //转成 yyyy-MM-dd
                startPeriod = DateUtil.format(tdFundMonthlyStatement.getPeriod()
                        , systemConfig.getMonthlyStatementDateFormat(), DateUtil.FORMAT_SHORT);
            }
        }

        //从用户的开户时间作为起始时间
        if (userInvestAccountInfo.getOpenFinishedTime() != null) {
            startPeriod = DateUtil.format(userInvestAccountInfo.getOpenFinishedTime(), DateUtil.FORMAT_SHORT);
        }

        UserMonthlyStatementQueryDto userMonthlyStatementQueryDto = new UserMonthlyStatementQueryDto();
        userMonthlyStatementQueryDto.setUserId(bankUserId);
        userMonthlyStatementQueryDto.setYear(year);
        userMonthlyStatementQueryDto.setClientId(clientId);
        userMonthlyStatementQueryDto.setStartPeriod(startPeriod);
        userMonthlyStatementQueryDto.setEndPeriod(endPeriod);
        userMonthlyStatementQueryDto.setFundMonthlyStatementMap(userPubFundMonthlyStatementMap);
        return userMonthlyStatementQueryDto;
    }


    /**
     * 填充
     *
     * @param userMonthlyStatementQueryDto
     */
    protected UserMonthlyStatementListResp fillStatement(UserMonthlyStatementQueryDto userMonthlyStatementQueryDto) {

        String userId = userMonthlyStatementQueryDto.getUserId();
        String clientId = userMonthlyStatementQueryDto.getClientId();
        String startPeriod = userMonthlyStatementQueryDto.getStartPeriod();
        String endPeriod = userMonthlyStatementQueryDto.getEndPeriod();
        Map<String, TdFundMonthlyStatement> tdFundMonthlyStatementKeysPeriodMap
                = userMonthlyStatementQueryDto.getFundMonthlyStatementMap();


        List<String> allMonthList = DateUtil.getMonthsList(startPeriod, endPeriod);
        log.info("查询用户月结单. startPeriod:{},endPeriod:{},allMonthList{}"
                , startPeriod, endPeriod, allMonthList);

        UserMonthlyStatementListResp userFmtListResp = new UserMonthlyStatementListResp();
        if (CollectionUtils.isEmpty(allMonthList)) {
            return userFmtListResp;
        }

        //结果集
        List<UserMonthlyStatementResp> userFundMonthlyStatementRespList = new ArrayList<>(allMonthList.size());

        //本月
        Date todayDate = new Date();
        String nowPeriod = DateUtil.format(todayDate, systemConfig.getMonthlyStatementPeriodFormat());

        //倒序排序
        for (int index = allMonthList.size(); index > 0; index--) {
            String currentPeriod = allMonthList.get(index - 1);
            //当前月份无需展示
            if (currentPeriod.equals(nowPeriod)) {
                continue;
            }

            TdFundMonthlyStatement currentStatement = tdFundMonthlyStatementKeysPeriodMap.get(currentPeriod);

            //填充响应
            UserMonthlyStatementResp userFundMonthlyStatementResp
                    = generateUserFundMonthlyStatementResp(userId, clientId, currentPeriod, currentStatement);

            //非空的才需要
            if (userFundMonthlyStatementResp != null) {
                userFundMonthlyStatementRespList.add(userFundMonthlyStatementResp);
            }
        }

        userFmtListResp.setUserMonthlyStatementRespList(userFundMonthlyStatementRespList);
        return userFmtListResp;

    }


    /**
     * 根据结单的信息生成响应内容
     *
     * @param currentStatement
     * @return
     */
    UserMonthlyStatementResp generateUserFundMonthlyStatementResp(String userId, String clientId, String currentPeriod
            , TdFundMonthlyStatement currentStatement) {
        UserMonthlyStatementResp userFundMonthlyStatementResp = new UserMonthlyStatementResp();
        userFundMonthlyStatementResp.setPeriod(currentPeriod);
        userFundMonthlyStatementResp.setYear(currentPeriod.substring(0, 4));


        //上个月的要特殊处理
        Date lastMonth = DateUtil.addMonth(new Date(), -1);
        String lastPeriod = DateUtil.format(lastMonth, systemConfig.getMonthlyStatementPeriodFormat());

        if (currentStatement != null) {
            userFundMonthlyStatementResp.setBusinessId(currentStatement.getBusinessId());
            userFundMonthlyStatementResp.setDocLang(currentStatement.getDocLang());
            userFundMonthlyStatementResp.setDocUrl(currentStatement.getDocUrl());
            userFundMonthlyStatementResp.setStatementStatus(FundMonthlyStatementDocStatusEnum.FINISHED.getValue());
            userFundMonthlyStatementResp.setGenerateDate(DateUtil.format(currentStatement.getDocTime(), DateUtil.FORMATDAY));
        } else {
            // 1.最新的月份如果还未推送，则不显示出来
            // 2.如果有历史数据没推送的，要展示出来，但是提示当月无月结单
            boolean needShow = true;
            if (!currentPeriod.equals(lastPeriod)) {
                userFundMonthlyStatementResp.setStatementStatus(FundMonthlyStatementDocStatusEnum.NONE.getValue());
            } else {
                TdFundMonthlyStatementInfo monthlyStatementInfo
                        = monthlyStatementManager.getPubConfirmedFundStatementInfo(currentPeriod);
                //未点击确认的全部不显示
                if (monthlyStatementInfo == null) {
                    needShow = false;
                } else {
                    needShow = isLatestPeriodNeedShow(userId, clientId, lastPeriod);
                    if (needShow) {
                        userFundMonthlyStatementResp.setStatementStatus(FundMonthlyStatementDocStatusEnum.NONE.getValue());
                    }
                }
            }

            if (!needShow) {
                return null;
            }
        }
        return userFundMonthlyStatementResp;
    }


    /**
     * 判断月结单是否需要显示
     *
     * @param bankUserId
     * @param clientId
     * @return
     */
    protected boolean isLatestPeriodNeedShow(String bankUserId, String clientId, String latestPeriod) {
        boolean needShow = true;
        TdFundMonthlyStatement condition = new TdFundMonthlyStatement();
        condition.setIsDeleted(EnumYesOrNo.NO.getValue());
        condition.setPeriod(latestPeriod);
        condition.setBankUserId(bankUserId);
        condition.setClientId(clientId);
        int statementNumber = tdMonthlyStatementMapper.selectCount(condition);
        //有结单未推送的不显示
        //无结单未推送的要显示
        if (statementNumber > 0) {
            needShow = false;
        }
        return needShow;
    }


    /**
     * 后管-查询用户月结单列表
     *
     * @param req
     * @return
     */
    @Override
    public UserInvestMonthlyStatementListResp queryUserStatementDocList(UserMonthlyStatementListReq req) {
        UserInvestMonthlyStatementListResp userInvestMonthlyStatementListResp = new UserInvestMonthlyStatementListResp();
        String year = req.getYear();
        String period = req.getPeriod();
        String clientId = req.getClientId();

        if(StringUtils.isEmpty(clientId)){
            log.error("queryUserStatementDocList error, clientId is null req:{}", JSON.toJSONString(req));
            throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR);
        }

        Map<String, Map<String, List<UserInvestMonthlyStatementResp>>> investMonthlyStatementListRespGroupByPeriod = new LinkedHashMap<>();
        userInvestMonthlyStatementListResp.setUserInvestMonthlyStatementResps(investMonthlyStatementListRespGroupByPeriod);

        //根据clientId查询账户数据
        UserInvestAccountConditionHolder holder = new UserInvestAccountConditionHolder();
        holder.setClientId(clientId);
        List<UserInvestAccount> userInvestAccountList = userInvestAccountMapper.queryAllUserInvestAccountByCondition(holder);
        if (CollectionUtils.isEmpty(userInvestAccountList)) {
            log.error("queryUserStatementDocList error, userInvestAccountInfo is null req:{}", JSON.toJSONString(req));
            throw new BusinessException(USER_INVEST_ACCOUNT_NOT_FOUND);
        }
        //根据开户时间排序，找到最早开户的账户
        UserInvestAccount userInvestAccountInfo = userInvestAccountList.stream().min(Comparator.comparing(UserInvestAccount::getOpenFinishedTime)).get();
        String bankUserId = userInvestAccountInfo.getBankUserId();
        log.info("用户{}当前最早的投资账户为:{}", bankUserId, userInvestAccountInfo);

        //根据clientId查询结单文件记录
        List<TdFundMonthlyStatement> tdInvestMonthlyStatementList
                = monthlyStatementManager.queryUserInvestMonthlyStatementList(bankUserId, clientId, year, period);
        //把结单数据转成TreeMap<period, tdInvestMonthlyStatementList>，按照period顺序排序
        TreeMap<String, List<TdFundMonthlyStatement>> tdInvestMonthlyStatementListKeysPeriodMap = new TreeMap<>();
        for (TdFundMonthlyStatement tdInvestMonthlyStatement : tdInvestMonthlyStatementList) {
            tdInvestMonthlyStatementListKeysPeriodMap.putIfAbsent(tdInvestMonthlyStatement.getPeriod(), new ArrayList<>());
            List<TdFundMonthlyStatement> currentInvestMonthlyStatementList
                    = tdInvestMonthlyStatementListKeysPeriodMap.get(tdInvestMonthlyStatement.getPeriod());
            currentInvestMonthlyStatementList.add(tdInvestMonthlyStatement);
        }

        //计算起始周期
        String startPeriod = calStartPeriodDate(userInvestAccountInfo.getOpenFinishedTime(), tdInvestMonthlyStatementListKeysPeriodMap);

        //取上个月作为截止日期
        String endPeriod = DateUtil.format(DateUtil.getLastDayOfMonth(new Date()), DateUtil.FORMAT_SHORT);

        log.info("查询月结单. startPeriod:{},endPeriod:{}", startPeriod, endPeriod);
        //按月份升序排列
        List<String> allMonthList = DateUtil.getMonthsList(startPeriod, endPeriod);

        if (!Strings.isEmpty(year)) {
            allMonthList = allMonthList
                    .stream()
                    .filter(statement -> statement.startsWith(year))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(allMonthList) || endPeriod.equals(startPeriod)) {
            return userInvestMonthlyStatementListResp;
        }
        String nowPeriod = DateUtil.format(new Date(), systemConfig.getMonthlyStatementPeriodFormat());
        //倒序
        for (int index = allMonthList.size(); index > 0; index--) {
            String currentPeriod = allMonthList.get(index - 1);
            //当前月份无需展示
            if (nowPeriod.equals(currentPeriod)) {
                continue;
            }

            String currentYear = currentPeriod.substring(0, 4);

            investMonthlyStatementListRespGroupByPeriod.putIfAbsent(currentYear, new LinkedHashMap<>());
            Map<String, List<UserInvestMonthlyStatementResp>> fundMonthlyStatementListRespGroupByMonth
                    = investMonthlyStatementListRespGroupByPeriod.get(currentYear);

            fundMonthlyStatementListRespGroupByMonth.putIfAbsent(currentPeriod, new ArrayList<>());
            List<UserInvestMonthlyStatementResp> eachMonthStatementList = fundMonthlyStatementListRespGroupByMonth.get(currentPeriod);


            List<TdFundMonthlyStatement> currentInvestMonthlyStatementList = tdInvestMonthlyStatementListKeysPeriodMap.get(currentPeriod);
            if (CollectionUtils.isEmpty(currentInvestMonthlyStatementList)) {
                UserInvestMonthlyStatementResp userZhInvestMonthlyStatementResp = new UserInvestMonthlyStatementResp();
                userZhInvestMonthlyStatementResp.setPeriod(currentPeriod);
                eachMonthStatementList.add(userZhInvestMonthlyStatementResp);

                UserInvestMonthlyStatementResp userHkInvestMonthlyStatementResp = new UserInvestMonthlyStatementResp();
                userHkInvestMonthlyStatementResp.setPeriod(currentPeriod);
                eachMonthStatementList.add(userHkInvestMonthlyStatementResp);

            } else {
                currentInvestMonthlyStatementList.forEach(tdInvestMonthlyStatement -> {
                    UserInvestMonthlyStatementResp userInvestMonthlyStatementResp = new UserInvestMonthlyStatementResp();
                    userInvestMonthlyStatementResp.setBusinessId(tdInvestMonthlyStatement.getBusinessId());
                    userInvestMonthlyStatementResp.setDocLang(tdInvestMonthlyStatement.getDocLang());
                    userInvestMonthlyStatementResp.setDocUrl(tdInvestMonthlyStatement.getDocUrl());
                    userInvestMonthlyStatementResp.setPeriod(tdInvestMonthlyStatement.getPeriod());
                    eachMonthStatementList.add(userInvestMonthlyStatementResp);
                });
            }
        }

        return userInvestMonthlyStatementListResp;
    }

    /**
     * 计算开始展示月结单周期的期数(需要考虑最早月结单月份小于开户月份的情况)
     *
     * @param openFinishedTime
     * @param dataMap
     * @return
     */
    private String calStartPeriodDate(Date openFinishedTime,  TreeMap<String, List<TdFundMonthlyStatement>> dataMap){
        String openFinishedDate = DateUtil.format(openFinishedTime, DateUtil.FORMAT_SHORT);

        //获取treeMap的第一个Entry及key，作为最早的月结单月份
        Map.Entry<String, List<TdFundMonthlyStatement>> firstEntry = dataMap.firstEntry();
        if(null == firstEntry){
            return openFinishedDate;
        }
        String firstStatementPeriod = firstEntry.getKey();

        String openFinishedPeriod = DateUtil.format(openFinishedTime, StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT);
        if(firstStatementPeriod.compareTo(openFinishedPeriod) >= 0){
            return openFinishedDate;
        }

        //如果最早的月结单月份小于开户月份，则取月结单月份作为起始周期
        Date firstStatementDate = DateUtil.parse(firstStatementPeriod, StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT);
        return DateUtil.format(firstStatementDate, DateUtil.FORMAT_SHORT);
    }

    /**
     * 销户的bankUser被加上了close还有1,2,3等前后缀，
     * 这里的去掉，不然后边的业务表里查不到数据
     * close_1240612983353124864_1
     *
     * @param bankUserId
     * @return
     */
    protected String removeBankUserIdPrefix(String bankUserId) {
        if (bankUserId.startsWith("close_")) {
            int startIndex = bankUserId.indexOf("_");
            int endIndex = bankUserId.lastIndexOf("_");
            if (startIndex == endIndex) {
                return bankUserId.substring(startIndex + 1);
            } else {
                return bankUserId.substring(startIndex + 1, endIndex);
            }
        }
        return bankUserId;
    }


    /**
     * 逐个业务判断每种子业务每个是否都处理好了
     *
     * @param monthlyStatementInfo 结单信息
     */
    boolean isBusinessInitFinish(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype) {
        boolean isBusinessInitFinish = true;
        String allBusiness = monthlyStatementInfo.getBusinessType();
        String period = monthlyStatementInfo.getPeriod();
        //处理完之后更新完成状态  202204:stock,fund:stock
        String statementBusinessInitStatusKey = MONTHLY_STATEMENT_BUSINESS_INIT_STATUS
                .key(monthlyStatementInfo.getPeriod()
                        , allBusiness
                        , businessSubtype);

        RBucket<Boolean> initStatusBucket = redissonClient.getBucket(statementBusinessInitStatusKey);
        if (initStatusBucket.get() == null || !initStatusBucket.get()) {
            log.info("{},{}月结单子任务初始化未完成!  businessSubtype:{}"
                    , period, allBusiness, businessSubtype);
            return false;
        }
        return isBusinessInitFinish;
    }


    /**
     * 完成子业务的初始化
     *
     * @param monthlyStatementInfo
     * @param businessSubtype
     */
    void businessInitFinish(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype) {
        String allBusiness = monthlyStatementInfo.getBusinessType();
        String period = monthlyStatementInfo.getPeriod();
        //处理完之后更新完成状态  202204:stock,fund:stock:5:1
        String statementBusinessInitStatusKey = MONTHLY_STATEMENT_BUSINESS_INIT_STATUS
                .key(monthlyStatementInfo.getPeriod()
                        , monthlyStatementInfo.getBusinessType()
                        , businessSubtype);

        RBucket<Boolean> initStatusBucket = redissonClient.getBucket(statementBusinessInitStatusKey);
        initStatusBucket.set(true, 1, TimeUnit.DAYS);
        log.info("{}{}月结单子任务初始化完成! ", period, allBusiness);
    }


    /**
     * 用户子业务数据准备完成后标记
     *
     * @param monthlyStatementInfo
     * @param businessSubtype
     */
    void userStatementDataPrepareFinish(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype, String bankUserId, String clientId) {
        String statementDataPrepareFinishKey = MONTHLY_STATEMENT_BUSINESS_DATA_PREPARE_STATUS
                .key(monthlyStatementInfo.getPeriod()
                        , monthlyStatementInfo.getBusinessType()
                        , businessSubtype);
        String userKey = bankUserId + clientId;
        RMap<String, Boolean> userDataPrepareStatusMap = redissonClient.getMap(statementDataPrepareFinishKey);
        userDataPrepareStatusMap.put(userKey, true);
    }

    /**
     * 移除用户数据准备状态缓存标记
     *
     * @param monthlyStatementInfo
     * @param businessSubtype
     * @param bankUserId
     * @param clientId
     */
    void userStatementDataPrepareRemove(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype, String bankUserId, String clientId) {
        String statementDataPrepareFinishKey = MONTHLY_STATEMENT_BUSINESS_DATA_PREPARE_STATUS
                .key(monthlyStatementInfo.getPeriod()
                        , monthlyStatementInfo.getBusinessType()
                        , businessSubtype);
        String userKey = bankUserId + clientId;
        RMap<String, Boolean> userDataPrepareStatusMap = redissonClient.getMap(statementDataPrepareFinishKey);
        userDataPrepareStatusMap.remove(userKey);
    }

    /**
     * 用户子业务数据是否准备完成
     *
     * @param monthlyStatementInfo
     * @param businessSubtype
     */
    boolean isCacheUserStatementDataPrepareFinish(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype, String bankUserId, String clientId) {
        String statementDataPrepareFinishKey = MONTHLY_STATEMENT_BUSINESS_DATA_PREPARE_STATUS
                .key(monthlyStatementInfo.getPeriod()
                        , monthlyStatementInfo.getBusinessType()
                        , businessSubtype);
        String userKey = bankUserId + clientId;
        RMap<String, Boolean> userDataPrepareStatusMap = redissonClient.getMap(statementDataPrepareFinishKey);
        Boolean result = userDataPrepareStatusMap.get(userKey);
        return result != null && result;
    }

    /**
     * 清除用户数据准备状态缓存
     *
     * @param monthlyStatementInfo
     * @param businessSubtype
     */
    void clearUserStatementDataPrepareStatusCache(TdFundMonthlyStatementInfo monthlyStatementInfo, String businessSubtype) {
        String statementDataPrepareFinishKey = MONTHLY_STATEMENT_BUSINESS_DATA_PREPARE_STATUS
                .key(monthlyStatementInfo.getPeriod()
                        , monthlyStatementInfo.getBusinessType()
                        , businessSubtype);
        RMap<String, Boolean> userDataPrepareStatusMap = redissonClient.getMap(statementDataPrepareFinishKey);
        userDataPrepareStatusMap.clear();
    }


    /**
     * 完成子业务的初始化
     *
     * @param monthlyStatementInfo
     */
    void clearStatementBusinessInitCache(TdFundMonthlyStatementInfo monthlyStatementInfo) {
        String allBusiness = monthlyStatementInfo.getBusinessType();
        String period = monthlyStatementInfo.getPeriod();
        String[] businessSubtypeArray = allBusiness.split(systemConfig.businessSubTypeSeparator);
        //每个业务单独处理 stock,fund
        for (String businessSubtype : businessSubtypeArray) {
            String statementBusinessInitStatusKey = MONTHLY_STATEMENT_BUSINESS_INIT_STATUS
                    .key(monthlyStatementInfo.getPeriod()
                            , monthlyStatementInfo.getBusinessType()
                            , businessSubtype);
            RBucket<Boolean> initStatusBucket = redissonClient.getBucket(statementBusinessInitStatusKey);
            if (initStatusBucket != null) {
                initStatusBucket.delete();
            }
        }
        log.info("{}{}月结单子任务初始化清除完毕! ", period, allBusiness);
    }


    /**
     * 缓存中不需要生成结单的用户
     *
     * @param statementInfo
     * @param businessSubtype
     * @return
     */
    @Override
    public Set<String> queryCacheNoStatementUserKeySet(TdFundMonthlyStatementInfo statementInfo, String businessSubtype) {
        if (!systemConfig.isCacheNoStatementUserSet()) {
            return new HashSet<>();
        }
        String statementDataPrepareFinishKey = MONTHLY_STATEMENT_NO_STATEMENT_USER
                .key(statementInfo.getPeriod()
                        , statementInfo.getBusinessType()
                        , businessSubtype);
        return redissonClient.getSet(statementDataPrepareFinishKey);
    }

    /**
     * 清除无需生成结单用户的集合
     *
     * @param statementInfo
     * @param businessSubtype
     */
    @Override
    public void clearNoStatementUserKeySetCache(TdFundMonthlyStatementInfo statementInfo, String businessSubtype) {
        String statementDataPrepareFinishKey = MONTHLY_STATEMENT_NO_STATEMENT_USER
                .key(statementInfo.getPeriod()
                        , statementInfo.getBusinessType()
                        , businessSubtype);
        RSet<String> noStatementUserSet = redissonClient.getSet(statementDataPrepareFinishKey);
        if (noStatementUserSet != null) {
            List<String> userList = new ArrayList<>(noStatementUserSet);
            noStatementUserSet.removeAll(userList);
            log.info("清除无结单用户标记缓存! period:{},allBusiness:{},subBusiness:{}"
                    , statementInfo.getPeriod(), statementInfo.getBusinessType(), businessSubtype);
        }

    }

    @Override
    public void rebuildMonthlyStatement(String period, String clientId, Boolean notifyUser, Date statementDate) {
        //查询月结单主表
        TdFundMonthlyStatementInfo monthlyStatementInfo = monthlyStatementManager.getStatement(period);
        if (null == monthlyStatementInfo) {
            log.info("rebuildMonthlyStatement主表信息为空，period:{}", period);
            return;
        }
        //检查数据是否已经准备好
        String business = systemConfig.getDefaultMonthlyStatementBusinessType();
        String[] businessSubtypeArray = business.split(systemConfig.getBusinessSubTypeSeparator());
        for (String subBusinessType : businessSubtypeArray) {
            StatementBusinessService statementBusinessService = this.getStatementBusinessService(subBusinessType);
            if (!statementBusinessService.isDataPrepared(monthlyStatementInfo, Boolean.TRUE)) {
                log.info("rebuildMonthlyStatement数据还未准备好，period:{},subBusinessType:{}", period, subBusinessType);
                return;
            }
        }

        try {
            String path = USER_MONTHLY_STATEMENT_REBULID_INIT.key(monthlyStatementInfo.getPeriod()
                    , monthlyStatementInfo.getBusinessType()
                    , clientId);

            distributedLock.tryLock(path, 2, TimeUnit.SECONDS, () -> {

                //是否需要跑rebuild方法，rebuild只需要跑一次
                boolean rebuild = true;

                //每个业务单独处理 stock,fund
                for (String businessSubtype : businessSubtypeArray) {
                    //查询账户信息
                    UserInvestClientInfoDto userInvestClientInfoDto = queryUserInvestAccountSingle(businessSubtype, clientId);
                    if (null == userInvestClientInfoDto) {
                        log.info("无开户数据，clientId:{},subBusinessType:{}", clientId, businessSubtype);
                        continue;
                    }

                    StatementBusinessService statementBusinessService = getStatementBusinessService(businessSubtype);
                    String marketCode = null;
                    if (AccountTypeEnum.STOCK.getValue().equals(businessSubtype)) {
                        marketCode = MarketCodeEnum.US.getValue();
                    }else if (AccountTypeEnum.HK_STOCK.getValue().equals(businessSubtype)) {
                        marketCode = MarketCodeEnum.HK.getValue();
                    }
                    //判断是否需要生成月结单
                    boolean generateStatement = statementBusinessService.isUserNeedGenerateStatement(monthlyStatementInfo, userInvestClientInfoDto, marketCode);

                    //rebuild只跑一次
                    if (generateStatement && rebuild) {

                        //1.删除redis缓存
                        userStatementDataPrepareRemove(monthlyStatementInfo, businessSubtype, userInvestClientInfoDto.getBankUserId(), clientId);

                        //2.初始化需要把所有业务的账户查出来
                        Map<String, UserInvestClientInfoDto> businessTypeAndUserInfoDtoMap
                                = queryUserInvestAccountMap(period, userInvestClientInfoDto.getBankUserId(), clientId, business);

                        //3.查当前账户当期的结单记录
                        List<TdFundMonthlyStatement> statementList = monthlyStatementManager.listUserStatementRecords(period, clientId);

                        //4.重新生成
                        monthlyStatementManager.reInitUserStatementRecord(monthlyStatementInfo, businessTypeAndUserInfoDtoMap, businessSubtype, statementList, notifyUser, statementDate);

                        //5.更新rebuild完成标识
                        rebuild = false;

                    }
                    //每个子业务类型分别发送MQ消息
                    if (generateStatement) {

                        userStatementDataPreparePublish(period, business, userInvestClientInfoDto.getBankUserId(), clientId, businessSubtype);

                        log.info("rebuildMonthlyStatement success,userId:{},businessSubtype:{}", userInvestClientInfoDto.getBankUserId(), businessSubtype);

                    }
                }
            });

        } catch (Exception e) {
            log.warn("rebuildMonthlyStatement异常", e);
        }
    }

    /**
     * 填充开户状态
     *
     * @param basicInfoMap
     * @param tdFundMonthlyStatementInfo
     * @param statementRecord
     */
    void fillOpenAccountStatus(Map<String, Object> basicInfoMap, TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo,
                               TdFundMonthlyStatement statementRecord, List<TdMonthlyStatementData> monthlyStatementDataList) {
        String business = tdFundMonthlyStatementInfo.getBusinessType();
        String period = tdFundMonthlyStatementInfo.getPeriod();
        String[] businessSubtypeArray = business.split(systemConfig.getBusinessSubTypeSeparator());
        for (String businessType : businessSubtypeArray) {
            boolean openStatus = false;
            UserInvestClientInfoDto userInvestClientInfoDto = queryUserInvestAccountSingle(businessType, statementRecord.getClientId());
            if (null != userInvestClientInfoDto) {
                //开户时间大于当前月结单周期才会展示对应数据
                Date openFinishedTime = userInvestClientInfoDto.getOpenFinishedTime();
                if (AccountTypeEnum.STOCK.getValue().equals(businessType)) {
                    //美股需要转成对应的美东时间
                    openFinishedTime = MonthlyUtils.getEstDate(openFinishedTime);
                }

                String openDateStr = DateUtil.format(openFinishedTime, systemConfig.getMonthlyStatementPeriodFormat());
                if (period.compareTo(openDateStr) >= 0) {
                    log.info("当前businessType:{},period:{},小于开户日期:{},不展示表头", businessType, period, openDateStr);
                    openStatus = true;
                }
            }
            basicInfoMap.put(businessType, openStatus);
            log.info("fillOpenAccountStatus clientId:{},businessSubType:{},result:{}", statementRecord.getClientId(), businessType, openStatus);
        }
    }

    @Override
    public List<MonthlyStatementDto> queryPubMonthlyStatement(PubMonthlyStatementReq req) {
        try {
            List<TdFundMonthlyStatement> tdFundMonthlyStatementList = tdMonthlyStatementMapper.queryPubMonthlyStatement(req.getClientId(), req.getPeriods(), req.getDocLang());
            if (CollectionUtils.isEmpty(tdFundMonthlyStatementList)) {
                return null;
            }

            // 解析获取statementId列表
            List<String> statementIds = tdFundMonthlyStatementList.stream().map(TdFundMonthlyStatement::getBusinessId).collect(Collectors.toList());
            List<TdMonthlyStatementBusinessData> statementIdsBusinessData = monthlyStatementManager.getStatementIdsBusinessData(statementIds, STATEMENT_SUMMARY_DATA_TYPE_KEY);
            if (CollectionUtils.isEmpty(statementIdsBusinessData)) {
                log.error("queryPubMonthlyStatement, mongo数据缺失, 人工排查, statementIds:{}", JSON.toJSONString(statementIds));
                return null;
            }
            // 获取总市值 key->statementId. value->totalAmount
            Map<String, String> totalAmoutMap = statementIdsBusinessData.stream().collect(Collectors.toMap(TdMonthlyStatementBusinessData::getStatementId, data -> (String) data.getData().get(BASE_MONTHLY_STATEMENT_DATA_TOTAL_AMOUNT)));

            List<MonthlyStatementDto> monthlyStatementDtoList = BeanUtil.copyToList(tdFundMonthlyStatementList, MonthlyStatementDto.class);
            Iterator<MonthlyStatementDto> iterator = monthlyStatementDtoList.iterator();
            while (iterator.hasNext()) {
                MonthlyStatementDto dto = iterator.next();
                String totalMarketValueStr = totalAmoutMap.get(dto.getBusinessId());
                if (StringUtils.isBlank(totalMarketValueStr)) {
                    log.error("queryPubMonthlyStatement, mongo未查到总市值, 人工排查, statementId:{}", dto.getBusinessId());
                    iterator.remove();
                    continue;
                }
                // 总市值
                BigDecimal totalMarketValue = StatementNumberFormatUtils.removeFormat(totalAmoutMap.get(dto.getBusinessId()));
                dto.setTotalMarketValue(totalMarketValue);
                dto.setCurrency(CurrencyEnum.HKD.getCurrency());
            }
            return monthlyStatementDtoList;
        } catch (Exception e) {
            log.error("queryPubMonthlyStatement error, req:{}", JSON.toJSONString(req), e);
            return null;
        }
    }

    @Override
    public boolean showBusinessTitle(String businessType, String period, String clientId, TdMonthlyStatementData monthlyStatementData) {
        //默认基金是需要展示表头的
//        if (AccountTypeEnum.FUND.getValue().equals(businessType)) {
//            return true;
//        }
        //如果对应业务是有数据的情况下，需要展示对应表头
        if (MonthlyStatementDataStatusEnum.FINISHED.getDbValue().equals(monthlyStatementData.getDataStatus())) {
            return true;
        }
        //如果未开户，则不展示
        UserInvestClientInfoDto userInvestClientInfoDto = queryUserInvestAccountSingle(businessType, clientId);
        if (null == userInvestClientInfoDto) {
            return false;
        }
        //如果开户时间小于或等于月结单月份，需展示表头
        String openDateStr = DateUtil.format(userInvestClientInfoDto.getOpenFinishedTime(), systemConfig.getMonthlyStatementPeriodFormat());

        return openDateStr.compareTo(period) <= 0;
    }
}
