package group.za.bank.statement.base;

import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class BaseTestService {

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJSON;
    private MockedStatic<DateUtil> mockedDateUtil;

    private MockedStatic<MonthlyUtils> mockedMonthlyUtils;

    @Before
    public void setUp() {
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        // Mock JSON
        mockedJSON = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        // Mock DateUtil
        mockedDateUtil = mockStatic(DateUtil.class);
        when(DateUtil.parse(anyString(), anyString())).thenReturn(new Date());
        when(DateUtil.format(any(), any())).thenReturn("2024-04-01");
        when(DateUtil.getMonthsList(any(), any())).thenReturn(Arrays.asList("202404"));


        mockedMonthlyUtils = mockStatic(MonthlyUtils.class);

    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        if (mockedJSON != null) {
            mockedJSON.close();
        }
        if (mockedDateUtil != null) {
            mockedDateUtil.close();
        }
        if(mockedMonthlyUtils != null){
            mockedMonthlyUtils.close();
        }
    }




}
