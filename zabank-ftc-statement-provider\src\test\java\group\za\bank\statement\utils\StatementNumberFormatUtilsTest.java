package group.za.bank.statement.utils;

import org.junit.Test;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

public class StatementNumberFormatUtilsTest {

    @Test
    public void testStatementQtyFormat() throws Exception {
        String result = StatementNumberFormatUtils.statementQtyFormat(new BigDecimal(0));
    }

    @Test
    public void testStatementShareFormat() throws Exception {
        String result = StatementNumberFormatUtils.statementShareFormat(new BigDecimal(0), 0);
    }

    @Test
    public void testStatementShareFormat2() throws Exception {
        String result = StatementNumberFormatUtils.statementShareFormat(new BigDecimal(0));
    }

    @Test
    public void testStatementMoneyFormat() throws Exception {
        String result = StatementNumberFormatUtils.statementMoneyFormat(new BigDecimal(0));
    }

    @Test
    public void testStatementQtyFormatFour() throws Exception {
        String result = StatementNumberFormatUtils.statementQtyFormatFour(new BigDecimal(0));
    }

    @Test
    public void testStatementQtyFormatFourNoZero() throws Exception {
        String result = StatementNumberFormatUtils.statementQtyFormatFourNoZero(new BigDecimal(0));
    }

    @Test
    public void testStatementQtyFormatFourNoZero2() throws Exception {
        String result = StatementNumberFormatUtils.statementQtyFormatFourNoZero(new BigDecimal(0), "defaultStr");
    }

    @Test
    public void testFormatMonthlyStatementDate() throws Exception {
        String result = StatementNumberFormatUtils.formatMonthlyStatementDate(new GregorianCalendar(2024, Calendar.DECEMBER, 13, 16, 28).getTime(), "docLang");
    }

    @Test
    public void testParseFormattedData() throws Exception {
        BigDecimal result = StatementNumberFormatUtils.parseFormattedData("1");
    }

    @Test
    public void testDateFormat2EngStr() throws Exception {
        String result = StatementNumberFormatUtils.dateFormat2EngStr(new GregorianCalendar(2024, Calendar.DECEMBER, 13, 16, 28).getTime());
    }

    @Test
    public void testEngStr2Date() throws Exception {
        Date result = StatementNumberFormatUtils.engStr2Date("10 December 2024");
    }

    @Test
    public void testEngStr2NativeStr() throws Exception {
        String result = StatementNumberFormatUtils.engStr2NativeStr("10 December 2024");
    }

    @Test
    public void testRemoveFormat() throws Exception {
        BigDecimal result = StatementNumberFormatUtils.removeFormat("1");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme