package group.za.bank.statement.constants.enums;

import group.za.bank.invest.common.constants.enums.AlarmModule;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum StatementAlarmModuleEnum implements AlarmModule {
    /** */

    MONTHLY_STATEMENT_INIT_ERROR("MONTHLY_STATEMENT_INIT_ERROR","月结单初始化异常"),
    MONTHLY_STATEMENT_DOC_GENERATE_ERROR("MONTHLY_STATEMENT_DOC_GENERATE_ERROR","月结单文件生成异常"),
    MONTHLY_STATEMENT_DOC_PUB_INIT_ERROR("MONTHLY_STATEMENT_DOC_PUB_INIT_ERROR","月结单推送异常"),
    MONTHLY_STATEMENT_NOT_CONFIRM("MONTHLY_STATEMENT_NOT_CONFIRM","月结单还未经确认"),
    MONTHLY_STATEMENT_SEND_FINISHED("MONTHLY_STATEMENT_SEND_FINISHED","月结单发送完成"),

    MONTHLY_STATEMENT_INIT_FINISHED("MONTHLY_STATEMENT_INIT_FINISHED","月结单初始化完成"),

    MONTHLY_STATEMENT_CONFIRM_FINISHED("MONTHLY_STATEMENT_CONFIRM_FINISHED","月结单已确认"),

    ;

    private final String value;
    private final String msg;

    /**
     * 字典值，实际存储在数据库的值
     *
     * @return String 字符串
     */
    @Override
    public String getValue() {
        return value;
    }

    /**
     * 字典描述信息，前端页面展示的值
     *
     * @return String 字符串
     */
    @Override
    public String getMsg() {
        return msg;
    }
}
