package group.za.bank.statement.agent;

import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.trade.model.resp.feign.MonthFirstTradeDateResp;
import group.za.bank.sbs.trade.model.resp.feign.MonthLastTradeDateResp;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 交易日历缓存处理
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Component
public class TradeCalendarAgent {

    /**
     * 最后一个交易日，缓存8小时
     */
    private final static Long TIMEOUT = 60 * 60 * 8L;


    @Autowired
    private TradeCalendarRemoteService tradeCalendarRemoteService;

    /**
     * 获取某月最后一个交易日
     *
     * @param marketCode
     * @param yearMonth
     * @return
     */
    public Date getMonthLastTradeDate(String marketCode, String yearMonth) {
        String redisKey = StatementRedisCacheKey.TRADECALENDAR_MONTH_LAST_TRADEDATE_CACHE_KEY.key(marketCode, yearMonth);
        return AbstractCacheTemplate.queryFromCache(redisKey, TIMEOUT, TimeUnit.SECONDS, () -> {
            MonthLastTradeDateResp monthLastTradeDate = tradeCalendarRemoteService.getMonthLastTradeDate(marketCode, yearMonth);
            return DateUtil.strToDate(monthLastTradeDate.getTradeDate());
        }, false);
    }

    /**
     * 获取某月第一个交易日
     * @param marketCode
     * @param yearMonth
     * @return
     */
    public Date getMonthFirstTradeDate(String marketCode, String yearMonth) {
        String redisKey = StatementRedisCacheKey.TRADECALENDAR_MONTH_LAST_TRADEDATE_CACHE_KEY.key(marketCode, yearMonth);
        return AbstractCacheTemplate.queryFromCache(redisKey, TIMEOUT, TimeUnit.SECONDS, () -> {
            MonthFirstTradeDateResp monthFirstTradeDateResp = tradeCalendarRemoteService.getMonthFirstTradeDate(marketCode, yearMonth);
            return DateUtil.strToDate(monthFirstTradeDateResp.getTradeDate());
        }, false);
    }





}
