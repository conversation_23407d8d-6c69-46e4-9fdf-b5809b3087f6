package group.za.bank.statement.entity.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 25 11:45
 * @description
 */
@Data
public class CryptoMonthlyStatementDataDto {

    /**
     * 基本信息
     */
    private BasicInfoDto basicInfoDto;
    /**
     * 持仓——holdingList
     */
    private List<HoldingDto> holdingList = new ArrayList<>();

    /**
     * 已确认交易——confirmedOrderList
     */
    private List<ConfirmedOrderDto> confirmedOrderList = new ArrayList<>();

    @Getter
    @Setter
    @ToString
    public static class BasicInfoDto {
        //总市值
        private BigDecimal totalMarket;
        //币种
        private String currency;
    }


    @Getter
    @Setter
    @ToString
    public static class HoldingDto {
        private String assetId;

        private String assetName;
        private String assetNameEn;

        private String exchangeCode;
        //期初结余
        private String openingBalance;
        //期终结余
        private String closingBalance;
        //币种
        private String currency;
        //参考价格
        private String referencePrice;
        //市值
        private String marketValue;
        //港币市值
        private String hkdMarketValue;

    }

    @Getter
    @Setter
    @ToString
    public static class ConfirmedOrderDto {
        private String assetId;

        private String exchangeCode;

        //币种
        private String currency;

        /** 买卖方向 */
        private String direction;
        /**
         * 交易日
         */
        private String tradeDate;
        /**
         * 交收日
         */
        private String clearDate;

        //说明
        private String description;

        //单位数目
        private String businessQty;

        //单位价格
        private String businessPrice;

        //佣金
        private String commission;

        //平台费
        private String platformFee;

        //交易金额
        private String businessAmount;

        //交收金额
        private String settleAmount;

        //成交明细
        private List<OrderDetailDto> details;
    }

    @Getter
    @Setter
    @ToString
    public static class OrderDetailDto{

        /** 序号 **/
        private String seq;

        /** 成交数量 **/
        private String transQty;

        /** 成交价 **/
        private String transPrice;

        /** 成交金额 **/
        private String transAmount;
    }

}
