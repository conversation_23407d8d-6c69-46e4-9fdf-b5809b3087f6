package group.za.bank.statement.service.impl;

import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.trade.model.resp.feign.TradeCalendarResp;
import group.za.bank.statement.agent.TradeCalendarAgent;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.service.StockTradeDateService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 14 17:16
 * @description
 */
@Service
public class StockTradeDateServiceImpl implements StockTradeDateService {

    @Autowired
    private TradeCalendarAgent tradeCalendarAgent;

    @Autowired
    private SystemConfig systemConfig;




    @Autowired
    private TradeCalendarRemoteService tradeCalendarRemoteService;

    /**
     * 查询core日历（主要是股票跟从ttl日历中的core日历）中的交易日历
     * 即香港交易日历和美股交易日历中交易日的并集
     *
     * @param period 结单月份 yyyyMM
     * @return
     */
    @Override
    public List<String> queryCoreCalendarTradeDateList(String period) {
        Date periodDate = DateUtil.parse(period, systemConfig.getMonthlyStatementPeriodFormat());
        return queryCoreCalendarTradeDateList(DateUtil.getFirstDayOfMonth(periodDate), DateUtil.getLastDayOfMonth(periodDate));
    }

    /**
     * 查询core日历（主要是股票跟从ttl日历中的core日历）中的交易日历
     * 即香港交易日历和美股交易日历中交易日的并集
     *
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return
     */
    @Override
    public List<String> queryCoreCalendarTradeDateList(Date startDate, Date endDate) {
        String startDateString = DateUtil.format(startDate, DateUtil.FORMATDAY);
        String endDateString = DateUtil.format(endDate, DateUtil.FORMATDAY);

        //港股日历
        List<TradeCalendarResp> hkTradeDateCalendarRespList
                = tradeCalendarRemoteService.queryTradeDateCalendarList(MarketCodeEnum.HK.getValue(), startDateString, endDateString);
        //美股日历
        List<TradeCalendarResp> usTradeDateCalendarRespList
                = tradeCalendarRemoteService.queryTradeDateCalendarList(MarketCodeEnum.US.getValue(), startDateString, endDateString);

        Map<String, List<TradeCalendarResp>> calendarDateStringAndCalendarMap = new HashMap<>();


        //取港股美股并集
        hkTradeDateCalendarRespList.forEach(tradeCalendarResp -> {
            List<TradeCalendarResp> tradeCalendarRespList = new ArrayList<>(2);
            tradeCalendarRespList.add(tradeCalendarResp);
            calendarDateStringAndCalendarMap.put(DateUtil.format(tradeCalendarResp.getCalendarDate(), DateUtil.FORMATDAY), tradeCalendarRespList);
        });

        //取港股美股并集
        usTradeDateCalendarRespList.forEach(tradeCalendarResp -> {
            String calendarDateString = DateUtil.format(tradeCalendarResp.getCalendarDate(), DateUtil.FORMATDAY);
            List<TradeCalendarResp> tradeCalendarRespList = calendarDateStringAndCalendarMap.computeIfAbsent(calendarDateString, k -> new ArrayList<>(1));
            tradeCalendarRespList.add(tradeCalendarResp);
            calendarDateStringAndCalendarMap.put(DateUtil.format(tradeCalendarResp.getCalendarDate(), DateUtil.FORMATDAY), tradeCalendarRespList);
        });

        //按照日期升序排列
        return calendarDateStringAndCalendarMap.keySet()
                .stream()
                .sorted((date1, date2) -> {
                    if (date1 == null || date2 == null) {
                        return -1;
                    }
                    int date1IntegerValue = Integer.parseInt(date1);
                    int date2IntegerValue = Integer.parseInt(date2);
                    if (date1IntegerValue > date2IntegerValue) {
                        return 1;
                    }
                    if (date1IntegerValue < date2IntegerValue) {
                        return -1;
                    }
                    return 0;
                })
                .collect(Collectors.toList());
    }

    /**
     * 指定市场交易日历
     *
     * @param period 结单月份 yyyyMM
     * @return yyyyMMdd
     */
    @Override
    public List<String> queryCalendarTradeDateList(String marketCode, String period) {
        Date periodDate = DateUtil.parse(period, systemConfig.getMonthlyStatementPeriodFormat());
        String startDate = DateUtil.format(DateUtil.getFirstDayOfMonth(periodDate), DateUtil.FORMATDAY);
        String endDate = DateUtil.format(DateUtil.getLastDayOfMonth(periodDate), DateUtil.FORMATDAY);


        //美股日历
        List<TradeCalendarResp> tradeDateCalendarRespList
                = tradeCalendarRemoteService.queryTradeDateCalendarList(marketCode, startDate, endDate);

        return tradeDateCalendarRespList.stream().map(tradeCalendarResp -> {
            return DateUtil.format(tradeCalendarResp.getCalendarDate(), DateUtil.FORMATDAY);
        }).collect(Collectors.toList());
    }



}
