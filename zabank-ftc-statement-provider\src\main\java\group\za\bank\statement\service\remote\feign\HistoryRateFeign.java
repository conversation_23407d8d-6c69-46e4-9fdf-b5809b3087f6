package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.bankfront.feign.RateFeignService;
import group.za.bank.sbs.trade.feign.StkRateInfoHisFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 汇率远程接口
 *
 * <AUTHOR>
 * @date 2022/05/27
 **/
@FeignClient(
        value = "zabank-sbs-trade-service",
        contextId = "historyRateFeign",
        url = "${sbs.gateway.url}"
)
public interface HistoryRateFeign extends StkRateInfoHisFeignService {

}
