package group.za.bank.statement.common.config;

import group.za.bank.statement.constants.MqConstants;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * rabbitmq 配置类
 *
 * @Date 2022/3/11 17:15
 * @Version 1.0
 **/
@Configuration
public class RabbitmqConfig {

    /**
     * 股票交换机
     *
     * @return the exchange
     */
    @Bean("userMonthlyStatementDataPrepareExchange")
    public Exchange userMonthlyStatementDataPrepareExchange() {
        return ExchangeBuilder.directExchange(MqConstants.USER_MONTHLY_STATEMENT_DATA_PREPARE_EXCHANGE).durable(true).build();
    }

    /**
     * 股票订单补偿队列
     *
     * @return the queue
     */
    @Bean("userMonthlyStatementDataPrepareQueue")
    public Queue userMonthlyStatementDataPrepareQueue() {
        return QueueBuilder.durable(MqConstants.USER_MONTHLY_STATEMENT_DATA_PREPARE_QUEUE).build();
    }

    /**
     * 通过绑定键 将指定队列绑定到一个指定的交换机 .
     *
     * @param queue    the queue
     * @param exchange the exchange
     * @return the binding
     */
    @Bean("userMonthlyStatementDataPrepareBinding")
    public Binding userMonthlyStatementDataPrepareBinding(@Qualifier("userMonthlyStatementDataPrepareQueue") Queue queue
            , @Qualifier("userMonthlyStatementDataPrepareExchange") Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(MqConstants.USER_MONTHLY_STATEMENT_DATA_PREPARE_ROUTE_KEY).noargs();
    }


}
