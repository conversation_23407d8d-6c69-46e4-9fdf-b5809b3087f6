package group.za.bank.statement.service.remote.impl;

import group.za.bank.fund.trade.entity.remote.resp.InvestMonthlyStatementSwitchResp;
import group.za.bank.fund.trade.feign.TradeFeignService;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.ResponseValidator;
import group.za.bank.statement.service.remote.FundTradeRemoteService;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 基金交易远程调用
 */
@Service
@Slf4j
public class FundTradeRemoteServiceImpl implements FundTradeRemoteService {

    @Resource
    private TradeFeignService tradeFeignService;

    @Override
    public InvestMonthlyStatementSwitchResp queryInvestMonthlyStatementSwitch() {
        ResponseData<InvestMonthlyStatementSwitchResp> responseData = null;
        try {
            responseData = tradeFeignService.queryInvestMonthlyStatementSwitch();
            log.info("queryInvestMonthlyStatementSwitch, 出参:{}", JSON.toJSONString(responseData));
            // 检查返回信息
            ResponseValidator.checkRemoteResponseValue(responseData);
            InvestMonthlyStatementSwitchResp value = responseData.getValue();
            // 下载statement投资月结单开关为True时，灰度开关不生效
            if (value.getDownloadInvestMonthlyStatementSwitch()) {
                value.setDownloadInvestMonthlyStatementGraySwitch(Boolean.FALSE);
            }
            return responseData.getValue();
        } catch (Exception e) {
            log.error("queryInvestMonthlyStatementSwitch error 出参：{}", JSON.toJSONString(responseData), e);
            throw new BusinessException(CommonErrorMsgEnum.RPC_INVOKE_ERR);
        }
    }
}
