package group.za.bank.statement.service.remote.impl;


import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.pub.entity.resp.SingleFileUploadResp;
import group.za.bank.invest.pub.feign.FileFeignService;
import group.za.bank.statement.service.remote.FileRemoteService;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @ClassName FileRemoteServiceImpl
 * @Description TODO
 * @date 2021/12/1 14:09
 * @Version 1.0
 */

@Service
@Slf4j
public class FileRemoteServiceImpl implements FileRemoteService {

    @Resource
    private FileFeignService fileFeignService;

    @Override
    public String singleFile(MultipartFile file, String storyType) {
        ResponseData<SingleFileUploadResp> respResponseData = fileFeignService.singleFileUpload(file,storyType);
        if (!respResponseData.judgeSuccess()){
            log.error("reportFeign调用公共上传接口返回数据fail={}", JSON.toJSONString(respResponseData));
            return null;
        }
        return respResponseData.getValue().getObsKey();
    }

    @Override
    public String readLongTextFromObs(String obsKey) {
        ResponseData<String> respResponseData = fileFeignService.readLongTextFromObs(obsKey);
        if (!respResponseData.judgeSuccess()){
            log.error("readLongTextFromObs fail={}", JSON.toJSONString(respResponseData));
            return null;
        }
        return respResponseData.getValue();
    }

    @Override
    public String singleFileUploadToObs(MultipartFile file, String storyType) {
        ResponseData<SingleFileUploadResp> respResponseData = fileFeignService.singleFileUploadToObs(file,storyType);
        if (!respResponseData.judgeSuccess()){
            log.error("singleFileUploadToObs调用公共上传接口返回数据fail={}", JSON.toJSONString(respResponseData));
            return null;
        }
        return respResponseData.getValue().getObsKey();
    }
}
