package group.za.bank.statement.service.impl;

import group.za.bank.fund.domain.trade.entity.*;
import group.za.bank.fund.domain.trade.mapper.*;
import group.za.bank.fund.trade.constants.TradeConstants;
import group.za.bank.fund.trade.constants.enums.*;
import group.za.bank.fund.trade.entity.remote.resp.InvestMonthlyStatementSwitchResp;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.constants.enums.TransactionEnum;
import group.za.bank.invest.common.utils.*;
import group.za.bank.invest.pub.entity.dto.GrayDetail;
import group.za.bank.invest.pub.entity.req.GrayQueryReq;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.domain.mapper.TdFundMonthlyOrderTmpMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementDataMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.entity.dto.FundMonthlyStatementDataDto;
import group.za.bank.statement.manager.ActivityStatementManager;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.bank.statement.service.FundMonthlyStatementMigrationService;
import group.za.bank.statement.service.remote.FundTradeRemoteService;
import group.za.bank.statement.service.remote.PubRemoteService;
import group.za.invest.core.InvestConstants;
import group.za.invest.core.dto.Direction;
import group.za.invest.core.dto.Order;
import group.za.invest.web.json.JSON;
import group.za.invest.web.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Future;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.STATEMENT_INFO_STATUS_EXCEPTION;
import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.STATEMENT_SUB_BUSINESS_DATA_IS_EMPTY;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.statementMoneyFormat;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.statementShareFormat;

/**
 * <AUTHOR>
 * @Date 2023/7/13
 * @Description 基金月结单迁移业务实现
 * @Version v1.0
 */
@Slf4j
@Service
public class FundMonthlyStatementMigrationServiceImpl implements FundMonthlyStatementMigrationService {

    // 月结单汇率类型
    private static final String RATE_TYPE = "month";
    // 月结单期数格式
    private static final String MONTHLY_STATEMENT_PERIOD_FORMAT = "yyyyMM";
    // 失败的各种语言
    private static final String ORDER_FAIL_DESC_ZH = "失败";
    private static final String ORDER_FAIL_DESC_HK = "失敗";
    private static final String ORDER_FAIL_DESC_EN = "FAILED";
    private static final String DEFAULT_NO_PRICE = "N/A";

    @Resource
    private MonthlyStatementManager monthlyStatementManager;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private TdFundMonthlyStatementInfoMapper tdFundMonthlyStatementInfoMapper;
    @Resource
    private TdExchangeRateInfoHistoryMapper tdExchangeRateInfoHistoryMapper;
    @Resource
    private TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;
    @Resource
    private ExchangeRateInfoService exchangeRateInfoService;
    @Resource
    private TdFundMonthlyStatementMapper tdFundMonthlyStatementMapper;
    @Resource
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;
    @Resource
    private TdMonthlyStatementDataMapper tdMonthlyStatementDataMapper;
    @Resource
    private TdMonthlyStatementHoldingSnapshotMapper tdMonthlyStatementHoldingSnapshotMapper;
    @Resource
    private TdMonthlyStatementOrderSnapshotMapper tdMonthlyStatementOrderSnapshotMapper;
    @Resource
    private PubRemoteService pubRemoteService;
    @Resource(name = "statementExecutor")
    private ThreadPoolTaskExecutor statementExecutor;
    @Resource
    private TdMonthlyStatementOverviewMapper tdMonthlyStatementOverviewMapper;
    @Resource
    private FundTradeRemoteService fundTradeRemoteService;
    @Resource
    private TdFundMonthlyOrderTmpMapper tdFundMonthlyOrderTmpMapper;
    @Resource
    private ActivityStatementManager activityStatementManager;

    @Override
    public void fundMonthlyStatementMigration(String period) {
        // 查询投资月结单开关
        InvestMonthlyStatementSwitchResp investMonthlyStatementSwitch = fundTradeRemoteService.queryInvestMonthlyStatementSwitch();
        // 下载statement投资月结单开关
        Boolean downloadInvestMonthlyStatementSwitch = investMonthlyStatementSwitch.getDownloadInvestMonthlyStatementSwitch();
        if (downloadInvestMonthlyStatementSwitch) {
            log.info("fundMonthlyStatementMigration.downloadInvestMonthlyStatementSwitch is true, 不执行基金月结单迁移, investMonthlyStatementSwitch:{}", JSON.toJSONString(investMonthlyStatementSwitch));
            return;
        }
        // 下载statement投资月结单灰度开关
        Boolean downloadInvestMonthlyStatementGraySwitch = investMonthlyStatementSwitch.getDownloadInvestMonthlyStatementGraySwitch();

        // 初始化MonthlyStatementInfo
        TdFundMonthlyStatementInfo statementInfo = this.initMonthlyStatementInfo(period, downloadInvestMonthlyStatementGraySwitch);

        // 灰度开关为false，则检查初始化是否完成
        if (!downloadInvestMonthlyStatementGraySwitch && this.checkInitIsFinished(period, statementInfo)) {
            return;
        }

        // 设置美元兑港币汇率
        this.setUsdToHkdRate(statementInfo);

        Long latestId = 0L;
        List<group.za.bank.fund.domain.trade.entity.TdFundMonthlyStatement> tradeStatementList = null;
        List<Future> futureList = new ArrayList<>();
        //分页
        while (true) {
            tradeStatementList = tdFundMonthlyStatementMapper.queryMonthlyStatementList(FundMonthlyStatementDocStatusEnum.FINISHED.getDbValue(), period, latestId, systemConfig.getPageSize());
            if (CollectionUtils.isEmpty(tradeStatementList)) {
                log.warn("fundMonthlyStatementMigration, 没有查到需要生成的月结单! period:{}, latestId:{}", period, latestId);
                break;
            }
            // 用于id分页
            latestId = tradeStatementList.get(tradeStatementList.size() - 1).getId();

            for (TdFundMonthlyStatement tradeStatement : tradeStatementList) {
                // 校验是否跳过该用户迁移月结单
                if (this.isSkipMigration(downloadInvestMonthlyStatementGraySwitch, tradeStatement.getBankUserId())) {
                    continue;
                }

                futureList.add(statementExecutor.submit(() -> {
                    // 数据准备
                    this.prepareData(period, statementInfo, tradeStatement);
                }));
            }
        }
        // 确保所有线程完成
        ThreadUtil.confirmThreadFinished(futureList);

        // 灰度开关为false,则更新数据初始化状态，否则由MonthlyStatementInitJob去更新数据状态
        if (!downloadInvestMonthlyStatementGraySwitch){
            this.updateStatementTotalInfoInitStatus(statementInfo);
        }
    }

    /**
     * 是否跳过该用户迁移月结单
     */
    private boolean isSkipMigration(Boolean downloadInvestMonthlyStatementGraySwitch, String bankUserId) {
        if (!downloadInvestMonthlyStatementGraySwitch) {
            return false;
        }

        // 下载statement投资月结单灰度开关
        GrayQueryReq grayQueryReq = new GrayQueryReq();
        grayQueryReq.setGrayKey(TradeConstants.GRAY_DOWNLOAD_INVEST_STATEMENT_KEY);
        grayQueryReq.setBankUserId(bankUserId);
        GrayDetail grayDetail = pubRemoteService.queryGray(grayQueryReq);
        if (grayDetail.getSwitchOpen()) {
            log.info("isSkipMigration, 该用户在灰度名单内, 跳过迁移月结单, bankUserId:{}, grayDetail:{}", bankUserId, JSON.toJSONString(grayDetail));
            return true;
        } else {
            log.info("isSkipMigration, 该用户不在灰度名单内, bankUserId:{}, grayDetail:{}", bankUserId, JSON.toJSONString(grayDetail));
        }
        return false;
    }

    /**
     * 检查初始化是否完成
     */
    private boolean checkInitIsFinished(String period, TdFundMonthlyStatementInfo statementInfo) {
        // 判断是否初始化已完成
        if (MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue().equals(statementInfo.getInitStatus())) {
            log.info("基金月结单迁移已经完成初始化! period:{},businessType:{}", period, statementInfo.getBusinessType());
            return true;
        }

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement statementInitCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        statementInitCondition.setPeriod(period);
        statementInitCondition.setDocStatus(MonthlyStatementDocStatusEnum.INIT.getDbValue());
        int stateStatementInitCount = tdMonthlyStatementMapper.selectCount(statementInitCondition);
        if (stateStatementInitCount > 0) {
            log.info("基金月结单迁移未完成初始化，存在INIT状态! period:{}, businessType:{}, stateStatementInitCount:{}", period, statementInfo.getBusinessType(), stateStatementInitCount);
            return false;
        }

        TdFundMonthlyStatement tradeStatementCondition = new TdFundMonthlyStatement();
        tradeStatementCondition.setDocStatus(FundMonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        tradeStatementCondition.setPeriod(period);
        int tradeStatementCount = tdFundMonthlyStatementMapper.selectCount(tradeStatementCondition);

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement statementCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        statementCondition.setPeriod(period);
        int stateStatementCount = tdMonthlyStatementMapper.selectCount(statementCondition);

        if (tradeStatementCount == stateStatementCount) {
            // 数据初始化已完成, 判断数据初始化状态
            this.updateStatementTotalInfoInitStatus(statementInfo);
            return true;
        }
        return false;
    }

    private void prepareData(String period, TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement tradeStatement) {
        try {
            // 查询基金月结单概览表
            TdMonthlyStatementOverview condition = new TdMonthlyStatementOverview();
            condition.setPeriod(tradeStatement.getPeriod());
            condition.setBankUserId(tradeStatement.getBankUserId());
            condition.setBankAccountId(tradeStatement.getAccountId());
            TdMonthlyStatementOverview monthlyStatementOverview = tdMonthlyStatementOverviewMapper.selectOne(condition);
            if (Objects.isNull(monthlyStatementOverview)) {
                throw new RuntimeException("za_bank_fund_trade.td_monthly_statement_overview is null");
            }

            // 调用public获取月结单文件填充信息（clientId、customerName、officeLocation、street、area、country）
            group.za.bank.fund.trade.entity.FundMonthlyStatementDataDto tradeStatementDataDto = JsonUtils.toJavaObject(pubRemoteService.queryBusinessData(tradeStatement.getBusinessId()), group.za.bank.fund.trade.entity.FundMonthlyStatementDataDto.class);

            group.za.bank.statement.domain.entity.TdFundMonthlyStatement statement = this.getMonthlyStatement(period, statementInfo, tradeStatement, monthlyStatementOverview.getClientId());

            if (!statement.getDocStatus().equals(MonthlyStatementDocStatusEnum.INIT.getDbValue())) {
                log.info("fundMonthlyStatementMigration, za_bank_invest_statement.td_fund_monthly_statement.doc_status不是初始化状态, 跳过, businessId:{}", statement.getBusinessId());
                return;
            }

            // 组装数据
            Map<String, Object> dataMap = this.generateDataMap(statement, tradeStatementDataDto, monthlyStatementOverview);
            if (org.springframework.util.CollectionUtils.isEmpty(dataMap)) {
                log.error("fundMonthlyStatementMigration, 用户月结单填充文件数据为空,人工排查! statement.tdFundMonthlyStatement:{}", JSON.toJSONString(statement));
                throw new BusinessException(STATEMENT_SUB_BUSINESS_DATA_IS_EMPTY);
            }

            //当前业务需要生成结单的
            TdMonthlyStatementBusinessData monthlyStatementBusinessData = monthlyStatementManager.getUserStatementBusinessData(statement.getBusinessId(), AccountTypeEnum.FUND.getValue());
            if (monthlyStatementBusinessData != null) {
                monthlyStatementManager.deleteUserStatementBusinessData(statement.getBusinessId(), AccountTypeEnum.FUND.getValue());
                log.info("fundMonthlyStatementMigration, 用户结单数据在mongodb中已存在，删除数据! statementId:{}", statement.getBusinessId());
            }
            // 存入数据
            monthlyStatementManager.saveUserStatementBusinessData(statement.getBusinessId(), AccountTypeEnum.FUND.getValue(), dataMap);

            // 完成数据准备
            this.finishUserDataPrepare(statement);
        } catch (Exception e) {
            log.error("fundMonthlyStatementMigration error, tradeStatement:{}", JSON.toJSONString(tradeStatement), e);
        }
    }

    private group.za.bank.statement.domain.entity.TdFundMonthlyStatement getMonthlyStatement(String period, TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement tradeStatement, String clientId) {
        group.za.bank.statement.domain.entity.TdFundMonthlyStatement statement;
        group.za.bank.statement.domain.entity.TdFundMonthlyStatement statementCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        statementCondition.setBankUserId(tradeStatement.getBankUserId());
        statementCondition.setPeriod(period);
        statementCondition.setClientId(clientId);
        statementCondition.setDocLang(tradeStatement.getDocLang());
        statementCondition.setBusinessType(statementInfo.getBusinessType());
        List<group.za.bank.statement.domain.entity.TdFundMonthlyStatement> statementList = tdMonthlyStatementMapper.selectList(statementCondition);
        if (CollectionUtils.isEmpty(statementList)) {
            // 初始化用户月结单记录
            statement = this.initMonthlyStatement(statementInfo, tradeStatement, clientId);
        } else {
            if (statementList.size() > 1) {
                throw new RuntimeException("数据异常, za_bank_invest_statement.td_fund_monthly_statement.size > 1, 人工排查");
            }
            statement = statementList.get(0);
        }
        return statement;
    }

    /**
     * 完成数据准备
     */
    private void finishUserDataPrepare(group.za.bank.statement.domain.entity.TdFundMonthlyStatement statement) {
        TdMonthlyStatementData conditionData = new TdMonthlyStatementData();
        conditionData.setBusinessType(AccountTypeEnum.FUND.getValue());
        conditionData.setStatementId(statement.getBusinessId());
        conditionData.setDataStatus(MonthlyStatementDataStatusEnum.PROCESSING.getDbValue());


        TdMonthlyStatementData targetData = new TdMonthlyStatementData();
        targetData.setDataStatus(MonthlyStatementDataStatusEnum.FINISHED.getDbValue());
        targetData.setGmtModified(new Date());


        group.za.bank.statement.domain.entity.TdFundMonthlyStatement updateStatementCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        updateStatementCondition.setBusinessId(statement.getBusinessId());
        updateStatementCondition.setDocStatus(MonthlyStatementDocStatusEnum.INIT.getDbValue());

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement updateStatementTarget = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        updateStatementTarget.setDocStatus(MonthlyStatementDocStatusEnum.UN_NOTIFIED.getDbValue());
        updateStatementTarget.setGmtModified(new Date());

        TransactionUtils.execute(TransactionEnum.STATEMENT.getName(), () -> {
            int updateNumber = tdMonthlyStatementDataMapper.updateByCondition(targetData, conditionData);
            if (updateNumber != 1) {
                throw new RuntimeException("update TdMonthlyStatementData PROCESSING TO FINISHED Failed, statementId:" + statement.getBusinessId());
            }

            updateNumber = tdMonthlyStatementMapper.updateByCondition(updateStatementTarget, updateStatementCondition);
            if (updateNumber != 1) {
                throw new RuntimeException("update TdFundMonthlyStatement INIT TO UN_NOTIFIED Failed, statementId:" + statement.getBusinessId());
            }
        });
    }

    /**
     * 初始化月结单记录
     */
    private group.za.bank.statement.domain.entity.TdFundMonthlyStatement initMonthlyStatement(TdFundMonthlyStatementInfo statementInfo, TdFundMonthlyStatement tradeStatement, String clientId) {
        group.za.bank.statement.domain.entity.TdFundMonthlyStatement monthlyStatement = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        monthlyStatement.setBusinessId(IdWorker.idworker.nextIdStr());
        monthlyStatement.setBankUserId(tradeStatement.getBankUserId());
        monthlyStatement.setClientId(clientId);
        monthlyStatement.setPeriod(statementInfo.getPeriod());
        monthlyStatement.setDocStatus(MonthlyStatementDocStatusEnum.INIT.getDbValue());
        monthlyStatement.setPubStatus(MonthlyStatementPubStatusEnum.FINISHED.getDbValue());
        monthlyStatement.setDocLang(tradeStatement.getDocLang());
        monthlyStatement.setBusinessType(statementInfo.getBusinessType());
        monthlyStatement.setRecordTime(tradeStatement.getRecordTime());
        monthlyStatement.setRemark(StatementConstants.FUND_MIGRATION_FLAG);
        String[] businessSubtypeArray = statementInfo.getBusinessType().split(systemConfig.businessSubTypeSeparator);
        List<TdMonthlyStatementData> monthlyStatementDataList = new ArrayList<>(businessSubtypeArray.length);
        for (String businessSubtype : businessSubtypeArray) {
            TdMonthlyStatementData tdMonthlyStatementData = new TdMonthlyStatementData();
            tdMonthlyStatementData.setBusinessId(IdWorker.idworker.nextIdStr());
            tdMonthlyStatementData.setStatementId(monthlyStatement.getBusinessId());
            tdMonthlyStatementData.setBusinessType(businessSubtype);
            if (AccountTypeEnum.FUND.getValue().equals(businessSubtype)) {
                tdMonthlyStatementData.setDataStatus(MonthlyStatementDataStatusEnum.PROCESSING.getDbValue());
                tdMonthlyStatementData.setAccountId(tradeStatement.getAccountId());
            } else {
                tdMonthlyStatementData.setDataStatus(MonthlyStatementDataStatusEnum.NONE.getDbValue());
                tdMonthlyStatementData.setAccountId(null);
            }
            tdMonthlyStatementData.setRecordTime(new Date());
            monthlyStatementDataList.add(tdMonthlyStatementData);
        }

        TransactionUtils.execute(TransactionEnum.STATEMENT.getName(), () -> {
            // 保存月结单记录
            tdMonthlyStatementMapper.insert(monthlyStatement);
            // 保存月结单明细数据
            tdMonthlyStatementDataMapper.insertBatch(monthlyStatementDataList);
        });
        return monthlyStatement;
    }

    /**
     * 初始化MonthlyStatementInfo
     */
    private TdFundMonthlyStatementInfo initMonthlyStatementInfo(String period, Boolean downloadInvestMonthlyStatementGraySwitch) {
        TdFundMonthlyStatementInfo statementInfo = monthlyStatementManager.getStatement(period);
        if (Objects.nonNull(statementInfo)) {
            return statementInfo;
        }

        // 没有数据，则插入一条
        group.za.bank.fund.domain.trade.entity.TdFundMonthlyStatementInfo conditionFundMonthlyStatementInfo = new group.za.bank.fund.domain.trade.entity.TdFundMonthlyStatementInfo();
        conditionFundMonthlyStatementInfo.setPeriod(period);
        conditionFundMonthlyStatementInfo.setIsDeleted(InvestConstants.N);
        group.za.bank.fund.domain.trade.entity.TdFundMonthlyStatementInfo tradeStatementInfo = tdFundMonthlyStatementInfoMapper.selectOne(conditionFundMonthlyStatementInfo);
        if (Objects.isNull(tradeStatementInfo)) {
            throw new RuntimeException("za_bank_fund_trade.td_fund_monthly_statement_info is null");
        }
        String hkTempKey = systemConfig.getTempPath() + systemConfig.getHkTempKey();
        String zhTempKey = systemConfig.getTempPath() + systemConfig.getZhTempKey();
        statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setBusinessId(IdWorker.idworker.nextIdStr());
        statementInfo.setBusinessType(systemConfig.getDefaultMonthlyStatementBusinessType());
        statementInfo.setPeriod(period);
        statementInfo.setHkTempKey(hkTempKey);
        statementInfo.setZhTempKey(zhTempKey);
        statementInfo.setRecordTime(new Date());
        if (downloadInvestMonthlyStatementGraySwitch) {
            // 打开灰度开关，灰度名单内的用户由statement正常生成月结单
            // 删除临时表数据
            tdFundMonthlyOrderTmpMapper.deleteTdFundMonthlyOrderTmp();
            statementInfo.setStatus(MonthlyStatementInfoStatusEnum.PROCESSING.getDbValue());
            statementInfo.setPubConfirmStatus(MonthlyStatementPubConfirmStatusEnum.WAIT_CONFIRM.getDbValue());
        } else {
            statementInfo.setStatus(MonthlyStatementInfoStatusEnum.FINISHED.getDbValue());
            statementInfo.setPubConfirmStatus(MonthlyStatementPubConfirmStatusEnum.CONFIRMED.getDbValue());
        }
        statementInfo.setInitStatus(MonthlyStatementInfoInitStatusEnum.PROCESSING.getDbValue());
        tdMonthlyStatementInfoMapper.insert(statementInfo);
        return statementInfo;
    }

    // 设置美元兑港币汇率
    @Override
    public void setUsdToHkdRate(TdFundMonthlyStatementInfo statementInfo) {
        if (Objects.nonNull(statementInfo.getUsdHkdRate())) {
            // 已有汇率信息，无需重复赋值
            return;
        }

        TdExchangeRateInfoHistory tdExchangeRateInfoHistory = tdExchangeRateInfoHistoryMapper.queryMonthStatementRate(
                RATE_TYPE, statementInfo.getPeriod(), RateSourceEnum.ZA_BANK_CORE.getValue(), CurrencyEnum.USD.getCurrency(), CurrencyEnum.HKD.getCurrency());

        if (Objects.isNull(tdExchangeRateInfoHistory)) {
            throw new RuntimeException("za_bank_fund_trade.td_exchange_rate_info_history is null");
        }

        TdFundMonthlyStatementInfo target = new TdFundMonthlyStatementInfo();
        target.setUsdHkdRate(tdExchangeRateInfoHistory.getRate());
        target.setRateEffectTime(tdExchangeRateInfoHistory.getGmtCreated());
        target.setGmtModified(new Date());

        TdFundMonthlyStatementInfo condition = new TdFundMonthlyStatementInfo();
        condition.setId(statementInfo.getId());
        condition.setInitStatus(statementInfo.getInitStatus());
        int count = tdMonthlyStatementInfoMapper.updateByCondition(target, condition);
        if (count != 1) {
            throw new RuntimeException("更新汇率信息失败, count：" + count);
        }

        // 保存到redis
        String redisKey = StatementRedisCacheKey.RATE_INFO_CACHE_KEY.key(statementInfo.getPeriod());
        exchangeRateInfoService.setExchangeHkdRateToRedis(CurrencyEnum.USD.getCurrency(), redisKey, tdExchangeRateInfoHistory.getRate());
    }


    /**
     * 抽取业务结单数据
     */
    public Map<String, Object> generateDataMap(group.za.bank.statement.domain.entity.TdFundMonthlyStatement docUnGeneratedStatement, group.za.bank.fund.trade.entity.FundMonthlyStatementDataDto tradeStatementDataDto, TdMonthlyStatementOverview monthlyStatementOverview) {
        FundMonthlyStatementDataDto fundMonthlyStatementDataDto = new FundMonthlyStatementDataDto();
        /**
         * 基本信息——baseInfo
         */
        FundMonthlyStatementDataDto.BasicInfoDto basicInfoDto = queryBaseInfo(docUnGeneratedStatement, tradeStatementDataDto, monthlyStatementOverview);
        fundMonthlyStatementDataDto.setBasicInfo(basicInfoDto);
        /**
         * 基金持仓——holdingList
         */
        List<FundMonthlyStatementDataDto.HoldingDto> holdingDtoList
                = queryHoldingList(docUnGeneratedStatement, monthlyStatementOverview.getBankAccountId());
        fundMonthlyStatementDataDto.setHoldingList(holdingDtoList);
        /**
         * 已确认交易——confirmedOrderList
         */
        List<FundMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderList
                = queryConfirmedOrderDtoList(docUnGeneratedStatement, monthlyStatementOverview.getBankAccountId());
        fundMonthlyStatementDataDto.setConfirmedOrderList(confirmedOrderList);
        /**
         * 处理中交易——pendingOrderList
         */
        List<FundMonthlyStatementDataDto.PendingOrderDto> pendingOrderList
                = queryPendingOrderDtoList(docUnGeneratedStatement, monthlyStatementOverview.getBankAccountId());
        fundMonthlyStatementDataDto.setPendingOrderList(pendingOrderList);
        /**
         * 派息——dividendOrderList
         */
        List<FundMonthlyStatementDataDto.DividendOrderDto> dividendOrderList
                = queryDividendOrderDtoList(docUnGeneratedStatement, monthlyStatementOverview.getBankAccountId());
        fundMonthlyStatementDataDto.setDividendOrderList(dividendOrderList);
        /**
         * 存入及提取——depositAndWithdrawalList
         */
        List<FundMonthlyStatementDataDto.DepositAndWithdrawalDto> depositAndWithdrawalList
                = queryDepositAndWithdrawalDtoList(docUnGeneratedStatement, monthlyStatementOverview.getBankAccountId());
        fundMonthlyStatementDataDto.setDepositAndWithdrawalList(depositAndWithdrawalList);


        Map<String, Object> resultMap = JSONObject.parseObject(JsonUtils.toFormatJsonString(fundMonthlyStatementDataDto));
        if (log.isDebugEnabled()) {
            log.debug("开始抽取用户基金结单数据! bankAccountId:{},bankAccountId:{},resultMap:{}"
                    , docUnGeneratedStatement.getBankUserId(), monthlyStatementOverview.getBankAccountId(), JSON.toJSONString(resultMap));
        }

        return resultMap;
    }

    /**
     * 查询基本信息
     *
     * @return
     */
    private FundMonthlyStatementDataDto.BasicInfoDto queryBaseInfo(group.za.bank.statement.domain.entity.TdFundMonthlyStatement fundMonthlyStatement, group.za.bank.fund.trade.entity.FundMonthlyStatementDataDto tradeStatementDataDto, TdMonthlyStatementOverview monthlyStatementOverview) {
        FundMonthlyStatementDataDto.BasicInfoDto basicInfoDto = new FundMonthlyStatementDataDto.BasicInfoDto();
        String period = fundMonthlyStatement.getPeriod();
        Date periodDate = DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT);

        Date monthStartDate = DateUtil.getFirstDayOfMonth(periodDate);
        Date monthEndDate = DateUtil.getLastDayOfMonth(periodDate);

        basicInfoDto.setStatementStartDate(DateUtil.format(monthStartDate, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
        basicInfoDto.setStatementEndDate(DateUtil.format(monthEndDate, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
        basicInfoDto.setAccountNo(monthlyStatementOverview.getClientId());

        basicInfoDto.setTotalMarket(statementMoneyFormat(monthlyStatementOverview.getTotalMarket()));
        // 总市值，保留两位小数，四舍五入
        basicInfoDto.setTotalMarketValue(monthlyStatementOverview.getTotalMarket().setScale(2, RoundingMode.HALF_UP));
        basicInfoDto.setCurrency(CurrencyEnum.HKD.getCurrency());

        basicInfoDto.setStatementDate(DateUtil.format(fundMonthlyStatement.getRecordTime(), StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));

        // 等值期末持仓盈亏
        BigDecimal equalMonthHoldingIncomeValue = activityStatementManager.getEqualHkdMonthHoldingIncomeValue(fundMonthlyStatement.getBankUserId(), monthlyStatementOverview.getBankAccountId(), fundMonthlyStatement.getPeriod(), fundMonthlyStatement.getBusinessType());
        basicInfoDto.setTotalHoldingProfit(equalMonthHoldingIncomeValue);

        basicInfoDto.setCustomerName(tradeStatementDataDto.getBasicInfo().getCustomerName());
        basicInfoDto.setOfficeLocation(tradeStatementDataDto.getBasicInfo().getOfficeLocation());
        basicInfoDto.setStreet(tradeStatementDataDto.getBasicInfo().getStreet());
        basicInfoDto.setArea(tradeStatementDataDto.getBasicInfo().getArea());
        basicInfoDto.setCountry(tradeStatementDataDto.getBasicInfo().getCountry());
        return basicInfoDto;
    }

    /**
     * 查询持仓信息
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.HoldingDto> queryHoldingList(group.za.bank.statement.domain.entity.TdFundMonthlyStatement fundMonthlyStatement, String bankAccountId) {
        String period = fundMonthlyStatement.getPeriod();
        String docLang = fundMonthlyStatement.getDocLang();

        TdMonthlyStatementHoldingSnapshot condition = new TdMonthlyStatementHoldingSnapshot();
        condition.setPeriod(fundMonthlyStatement.getPeriod());
        condition.setBankUserId(fundMonthlyStatement.getBankUserId());
        condition.setBankAccountId(bankAccountId);

        List<TdMonthlyStatementHoldingSnapshot> holdingSnapshotList = tdMonthlyStatementHoldingSnapshotMapper.selectListWithSort(condition, Collections.singletonList(new Order(Direction.ASC, "product_id")));
        if (CollectionUtils.isEmpty(holdingSnapshotList)) {
            return new ArrayList<>();
        }

        List<FundMonthlyStatementDataDto.HoldingDto> holdingDtoList = new ArrayList<>();
        for (TdMonthlyStatementHoldingSnapshot holding : holdingSnapshotList) {
            FundMonthlyStatementDataDto.HoldingDto holdingDto = new FundMonthlyStatementDataDto.HoldingDto();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                holdingDto.setFundName(holding.getProductZhName());
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                holdingDto.setFundName(holding.getProductHkName());
            }

            holdingDto.setFundEngName(holding.getProductEngName());
            //快照表会使用入库
            holdingDto.setFundHkName(holding.getProductHkName());
            holdingDto.setFundZhName(holding.getProductZhName());
            holdingDto.setProductId(holding.getProductId());
            holdingDto.setIsinNo(holding.getIsinNo());

            holdingDto.setCurrency(holding.getCurrency());
            holdingDto.setOpeningBalance(statementShareFormat(holding.getOpeningBalance()));
            holdingDto.setClosingBalance(statementShareFormat(holding.getClosingBalance()));
            holdingDto.setMarketValue(statementMoneyFormat(holding.getMarketValue()));
            // 换汇为港币
            BigDecimal exchangeHkdRate = exchangeRateInfoService.getExchangeHkdRate(holdingDto.getCurrency(), period, fundMonthlyStatement.getBusinessType());
            // 港币市值四舍五入保留两位
            BigDecimal equalHkdMarketValue2Scale = holding.getMarketValue().multiply(exchangeHkdRate).setScale(2, RoundingMode.HALF_UP);
            // 等值港币市值
            String equalHkdMarketValue;
            if (CurrencyEnum.HKD.getCurrency().equals(holdingDto.getCurrency())) {
                // 港币基金的【等值港币市值】需要展示与【市值】相同的值
                equalHkdMarketValue = holdingDto.getMarketValue();
            } else if (BigDecimal.ZERO.compareTo(holding.getMarketValue()) == 0) {
                equalHkdMarketValue = holdingDto.getMarketValue();
            } else {
                // 等值港币市值 = 港币市值四舍五入保留两位且千分位格式化
                equalHkdMarketValue = statementMoneyFormat(equalHkdMarketValue2Scale);
            }
            holdingDto.setEqualHkdMarketValue(equalHkdMarketValue);
            if (Objects.isNull(holding.getReferencePrice())) {
                holdingDto.setReferencePrice(DEFAULT_NO_PRICE);
            } else {
                holdingDto.setReferencePrice(holding.getReferencePrice().compareTo(BigDecimal.ZERO) == 0 ? DEFAULT_NO_PRICE : statementShareFormat(holding.getReferencePrice()));
            }


            holdingDtoList.add(holdingDto);
        }
        return holdingDtoList;
    }


    /**
     * 已确认交易
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.ConfirmedOrderDto> queryConfirmedOrderDtoList(group.za.bank.statement.domain.entity.TdFundMonthlyStatement fundMonthlyStatement, String bankAccountId) {
        List<TdMonthlyStatementOrderSnapshot> orderSnapshotList = tdMonthlyStatementOrderSnapshotMapper.queryMonthlyStatementOrderList(fundMonthlyStatement.getBankUserId(), bankAccountId, fundMonthlyStatement.getPeriod(), MonthStatementModuleTypeEnum.CONFIRMED_MODULE.getValue());
        if (CollectionUtils.isEmpty(orderSnapshotList)) {
            return new ArrayList<>();
        }

        List<FundMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderDtoList = new ArrayList<>();
        for (TdMonthlyStatementOrderSnapshot orderSnapshot : orderSnapshotList) {
            FundMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto = new FundMonthlyStatementDataDto.ConfirmedOrderDto();
            confirmedOrderDto.setCurrency(orderSnapshot.getCurrency());
            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                confirmedOrderDto.setDescription(generateOrderDesc(orderSnapshot.getProductZhName(), fundMonthlyStatement.getDocLang(), orderSnapshot));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                confirmedOrderDto.setDescription(generateOrderDesc(orderSnapshot.getProductHkName(), fundMonthlyStatement.getDocLang(), orderSnapshot));
            }

            confirmedOrderDto.setEngDescription(generateOrderEngDesc(orderSnapshot.getProductEngName(), fundMonthlyStatement.getDocLang(), orderSnapshot));

            Date date = orderSnapshot.getRealConfirmTime();

            BigDecimal fee = orderSnapshot.getFinalFee();

            BigDecimal amount = null;
            BigDecimal units = null;
            BigDecimal unitPrice = null;

            if (FundBusinessTypeEnum.SELL.getValue().equals(orderSnapshot.getBusinessType())) {
                amount = orderSnapshot.getConfirmAmt();
                units = orderSnapshot.getConfirmShare();
                if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(orderSnapshot.getStatus())) {
                    units = orderSnapshot.getApplyShare();
                }
                unitPrice = orderSnapshot.getConfirmNetValue();
            }

            if (FundBusinessTypeEnum.BUY.getValue().equals(orderSnapshot.getBusinessType())) {
                //从收费版本开始：申购订单统一取申购金额
                amount = orderSnapshot.getApplyAmt();

                units = orderSnapshot.getConfirmShare();
                unitPrice = orderSnapshot.getConfirmNetValue();

            }

            confirmedOrderDto.setAmount(statementMoneyFormat(amount));
            confirmedOrderDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
            confirmedOrderDto.setFee(statementMoneyFormat(fee));
            confirmedOrderDto.setNoOfUnits(statementShareFormat(units));
            confirmedOrderDto.setUnitPrice(statementShareFormat(unitPrice));

            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(orderSnapshot);
            statementOrderSnapshotDto.setProductEngName(orderSnapshot.getProductEngName());
            statementOrderSnapshotDto.setProductHkName(orderSnapshot.getProductHkName());
            statementOrderSnapshotDto.setProductZhName(orderSnapshot.getProductZhName());
            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.CONFIRMED_MODULE.getValue());

            confirmedOrderDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);

            confirmedOrderDtoList.add(confirmedOrderDto);
        }

        return confirmedOrderDtoList;
    }

    /**
     * 处理中交易
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.PendingOrderDto> queryPendingOrderDtoList(group.za.bank.statement.domain.entity.TdFundMonthlyStatement fundMonthlyStatement, String bankAccountId) {
        List<TdMonthlyStatementOrderSnapshot> orderSnapshotList = tdMonthlyStatementOrderSnapshotMapper.queryMonthlyStatementOrderList(fundMonthlyStatement.getBankUserId(), bankAccountId, fundMonthlyStatement.getPeriod(), MonthStatementModuleTypeEnum.PENDING_MODULE.getValue());
        if (CollectionUtils.isEmpty(orderSnapshotList)) {
            return new ArrayList<>();
        }

        List<FundMonthlyStatementDataDto.PendingOrderDto> pendingOrderDtoList = new ArrayList<>();
        for (TdMonthlyStatementOrderSnapshot orderSnapshot : orderSnapshotList) {
            FundMonthlyStatementDataDto.PendingOrderDto pendingOrderDto = new FundMonthlyStatementDataDto.PendingOrderDto();
            pendingOrderDto.setCurrency(orderSnapshot.getCurrency());
            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                pendingOrderDto.setDescription(generateOrderDesc(orderSnapshot.getProductZhName(), fundMonthlyStatement.getDocLang(), orderSnapshot));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                pendingOrderDto.setDescription(generateOrderDesc(orderSnapshot.getProductHkName(), fundMonthlyStatement.getDocLang(), orderSnapshot));
            }

            pendingOrderDto.setEngDescription(generateOrderEngDesc(orderSnapshot.getProductEngName(), fundMonthlyStatement.getDocLang(), orderSnapshot));


            Date date = orderSnapshot.getTradeDate();

            BigDecimal amount = null;
            BigDecimal units = null;
            BigDecimal unitPrice = null;
            if (FundBusinessTypeEnum.SELL.getValue().equals(orderSnapshot.getBusinessType())) {
                units = orderSnapshot.getApplyShare();

            }

            if (FundBusinessTypeEnum.BUY.getValue().equals(orderSnapshot.getBusinessType())) {
                amount = orderSnapshot.getApplyAmt();
            }


            pendingOrderDto.setAmount(statementMoneyFormat(amount));
            pendingOrderDto.setFee(statementMoneyFormat(orderSnapshot.getFinalFee()));
            pendingOrderDto.setNoOfUnits(statementShareFormat(units));
            pendingOrderDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
            pendingOrderDto.setUnitPrice(statementShareFormat(unitPrice));


            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(orderSnapshot);
            statementOrderSnapshotDto.setProductEngName(orderSnapshot.getProductEngName());
            statementOrderSnapshotDto.setProductHkName(orderSnapshot.getProductHkName());
            statementOrderSnapshotDto.setProductZhName(orderSnapshot.getProductZhName());
            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.PENDING_MODULE.getValue());

            pendingOrderDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);

            pendingOrderDtoList.add(pendingOrderDto);
        }

        return pendingOrderDtoList;
    }

    /**
     * 派息
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.DividendOrderDto> queryDividendOrderDtoList(group.za.bank.statement.domain.entity.TdFundMonthlyStatement fundMonthlyStatement, String bankAccountId) {
        List<TdMonthlyStatementOrderSnapshot> orderSnapshotList = tdMonthlyStatementOrderSnapshotMapper.queryMonthlyStatementOrderList(fundMonthlyStatement.getBankUserId(), bankAccountId, fundMonthlyStatement.getPeriod(), MonthStatementModuleTypeEnum.DIVIDEND_MODULE.getValue());

        if (CollectionUtils.isEmpty(orderSnapshotList)) {
            return new ArrayList<>();
        }

        List<FundMonthlyStatementDataDto.DividendOrderDto> dividendOrderDtoList = new ArrayList<>();
        for (TdMonthlyStatementOrderSnapshot orderSnapshot : orderSnapshotList) {
            FundMonthlyStatementDataDto.DividendOrderDto dividendOrderDto = new FundMonthlyStatementDataDto.DividendOrderDto();
            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                dividendOrderDto.setDescription(generateOrderDesc(orderSnapshot.getProductZhName()
                        , fundMonthlyStatement.getDocLang(), orderSnapshot));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                dividendOrderDto.setDescription(generateOrderDesc(orderSnapshot.getProductHkName()
                        , fundMonthlyStatement.getDocLang(), orderSnapshot));
            }

            dividendOrderDto.setEngDescription(generateOrderEngDesc(orderSnapshot.getProductEngName()
                    , fundMonthlyStatement.getDocLang(), orderSnapshot));

            Date date = orderSnapshot.getRealDeliveryTime();
            //分红的需要根据数量去判断是现金分红还是单位分红
            FundBusinessTypeDescEnum fundBusinessTypeDescEnum = judgeDividendType(orderSnapshot);
            BigDecimal amount;
            BigDecimal units;
            //现金分红
            if (FundBusinessTypeDescEnum.DIVIDEND_CASH.equals(fundBusinessTypeDescEnum)) {
                amount = orderSnapshot.getConfirmAmt();
                dividendOrderDto.setNoOfUnits(StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
                dividendOrderDto.setCurrency(orderSnapshot.getCurrency());
                dividendOrderDto.setAmount(statementMoneyFormat(amount));
            }
            //单位派息
            if (FundBusinessTypeDescEnum.DIVIDEND_UNIT.equals(fundBusinessTypeDescEnum)) {
                units = orderSnapshot.getConfirmShare();
                dividendOrderDto.setNoOfUnits(statementShareFormat(units));
                dividendOrderDto.setCurrency(StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
                dividendOrderDto.setAmount(StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
            }

            dividendOrderDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));

            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(orderSnapshot);
            statementOrderSnapshotDto.setProductEngName(orderSnapshot.getProductEngName());
            statementOrderSnapshotDto.setProductHkName(orderSnapshot.getProductHkName());
            statementOrderSnapshotDto.setProductZhName(orderSnapshot.getProductZhName());

            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.DIVIDEND_MODULE.getValue());

            dividendOrderDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);

            dividendOrderDtoList.add(dividendOrderDto);
        }

        return dividendOrderDtoList;
    }

    /**
     * 存入及提取
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.DepositAndWithdrawalDto> queryDepositAndWithdrawalDtoList(group.za.bank.statement.domain.entity.TdFundMonthlyStatement fundMonthlyStatement, String bankAccountId) {
        List<TdMonthlyStatementOrderSnapshot> orderSnapshotList = tdMonthlyStatementOrderSnapshotMapper.queryMonthlyStatementOrderList(fundMonthlyStatement.getBankUserId(), bankAccountId, fundMonthlyStatement.getPeriod(), MonthStatementModuleTypeEnum.STORE_TAKE_MODULE.getValue());

        if (CollectionUtils.isEmpty(orderSnapshotList)) {
            return new ArrayList<>();
        }

        List<FundMonthlyStatementDataDto.DepositAndWithdrawalDto> depositAndWithdrawalDtoList = new ArrayList<>();
        for (TdMonthlyStatementOrderSnapshot orderSnapshot : orderSnapshotList) {
            FundMonthlyStatementDataDto.DepositAndWithdrawalDto depositAndWithdrawalDto = new FundMonthlyStatementDataDto.DepositAndWithdrawalDto();
            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                depositAndWithdrawalDto.setDescription(generateOrderDesc(orderSnapshot.getProductZhName(), fundMonthlyStatement.getDocLang(), orderSnapshot));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                depositAndWithdrawalDto.setDescription(generateOrderDesc(orderSnapshot.getProductHkName(), fundMonthlyStatement.getDocLang(), orderSnapshot));
            }

            depositAndWithdrawalDto.setEngDescription(generateOrderEngDesc(orderSnapshot.getProductEngName(), fundMonthlyStatement.getDocLang(), orderSnapshot));


            Date date = orderSnapshot.getRealDeliveryTime();
            BigDecimal fee = orderSnapshot.getFinalFee();

            BigDecimal units = orderSnapshot.getConfirmShare();

            depositAndWithdrawalDto.setCurrency(orderSnapshot.getCurrency());
            depositAndWithdrawalDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
            depositAndWithdrawalDto.setNoOfUnits(statementShareFormat(units));
            depositAndWithdrawalDto.setFee(statementMoneyFormat(fee));

            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(orderSnapshot);
            statementOrderSnapshotDto.setProductEngName(orderSnapshot.getProductEngName());
            statementOrderSnapshotDto.setProductHkName(orderSnapshot.getProductHkName());
            statementOrderSnapshotDto.setProductZhName(orderSnapshot.getProductZhName());

            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.STORE_TAKE_MODULE.getValue());

            depositAndWithdrawalDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);

            depositAndWithdrawalDtoList.add(depositAndWithdrawalDto);
        }

        return depositAndWithdrawalDtoList;
    }

    private FundMonthlyStatementDataDto.StatementOrderSnapshotDto getStatementOrderSnapshotDto(TdMonthlyStatementOrderSnapshot orderSnapshot) {

        FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = new FundMonthlyStatementDataDto.StatementOrderSnapshotDto();
        statementOrderSnapshotDto.setOrderNo(orderSnapshot.getOrderNo());
        statementOrderSnapshotDto.setBusinessType(orderSnapshot.getBusinessType());
        statementOrderSnapshotDto.setProductId(orderSnapshot.getProductId());
        statementOrderSnapshotDto.setIsinNo(orderSnapshot.getIsinNo());
        statementOrderSnapshotDto.setCurrency(orderSnapshot.getCurrency());
        statementOrderSnapshotDto.setApplyTime(orderSnapshot.getApplyTime());
        statementOrderSnapshotDto.setApplyShare(orderSnapshot.getApplyShare());
        statementOrderSnapshotDto.setApplyAmt(orderSnapshot.getApplyAmt());
        statementOrderSnapshotDto.setConfirmAmt(orderSnapshot.getConfirmAmt());
        statementOrderSnapshotDto.setConfirmNetValue(orderSnapshot.getConfirmNetValue());
        statementOrderSnapshotDto.setConfirmNetValueDate(orderSnapshot.getConfirmNetValueDate());
        statementOrderSnapshotDto.setConfirmShare(orderSnapshot.getConfirmShare());
        statementOrderSnapshotDto.setFee(orderSnapshot.getFee());
        statementOrderSnapshotDto.setFeeRate(orderSnapshot.getFeeRate());
        statementOrderSnapshotDto.setFinalFee(orderSnapshot.getFinalFee());
        statementOrderSnapshotDto.setRealConfirmTime(orderSnapshot.getRealConfirmTime());
        statementOrderSnapshotDto.setRealDeliveryTime(orderSnapshot.getRealDeliveryTime());
        statementOrderSnapshotDto.setTradeDate(orderSnapshot.getTradeDate());
        statementOrderSnapshotDto.setStatus(orderSnapshot.getStatus());

        return statementOrderSnapshotDto;
    }

    /**
     * 订单描述 操作+productName
     */
    private String generateOrderDesc(String fundName, String lang, TdMonthlyStatementOrderSnapshot orderSnapshot) {
        String businessType = orderSnapshot.getBusinessType();
        StringBuilder descBuilder = new StringBuilder();
        FundBusinessTypeDescEnum fundBusinessTypeDescEnum = FundBusinessTypeDescEnum.fromValue(businessType);
        //分红的需要根据数量去判断是现金分红还是单位分红
        if (FundBusinessTypeDescEnum.DIVIDEND.equals(fundBusinessTypeDescEnum)) {
            fundBusinessTypeDescEnum = judgeDividendType(orderSnapshot);
        }

        if (fundBusinessTypeDescEnum != null) {
            if (I18nSupportEnum.CN_ZH.getName().equals(lang)) {
                descBuilder.append(fundBusinessTypeDescEnum.getZhMsg());
                if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(orderSnapshot.getStatus())) {
                    descBuilder.append(ORDER_FAIL_DESC_ZH);
                }
                descBuilder.append("    ").append(fundName);
            }
            if (I18nSupportEnum.CN_HK.getName().equals(lang)) {
                descBuilder.append(fundBusinessTypeDescEnum.getHkMsg());
                if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(orderSnapshot.getStatus())) {
                    descBuilder.append(ORDER_FAIL_DESC_HK);
                }
                descBuilder.append("    ").append(fundName);
            }
        }
        return descBuilder.toString();
    }

    /**
     * 订单描述 操作+productName
     *
     * @return
     */
    private String generateOrderEngDesc(String fundName, String lang, TdMonthlyStatementOrderSnapshot orderSnapshot) {
        String businessType = orderSnapshot.getBusinessType();
        FundBusinessTypeDescEnum fundBusinessTypeDescEnum = FundBusinessTypeDescEnum.fromValue(businessType);
        //分红的需要根据数量去判断是现金分红还是单位分红
        if (FundBusinessTypeDescEnum.DIVIDEND.equals(fundBusinessTypeDescEnum)) {
            fundBusinessTypeDescEnum = judgeDividendType(orderSnapshot);
        }
        StringBuilder descBuilder = new StringBuilder();
        descBuilder.append(fundBusinessTypeDescEnum.getEnMsg()).append(" ");
        if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(orderSnapshot.getStatus())) {
            descBuilder.append(ORDER_FAIL_DESC_EN);
        }
        descBuilder.append("    ").append(fundName);
        return descBuilder.toString();
    }

    /**
     * 分红的需要根据数量去判断是现金分红还是单位分红
     *
     * @return
     */
    FundBusinessTypeDescEnum judgeDividendType(TdMonthlyStatementOrderSnapshot orderSnapshot) {
        if (orderSnapshot.getConfirmAmt() != null
                && orderSnapshot.getConfirmAmt().compareTo(BigDecimal.ZERO) > 0) {
            return FundBusinessTypeDescEnum.DIVIDEND_CASH;
        } else {
            return FundBusinessTypeDescEnum.DIVIDEND_UNIT;
        }
    }

    @Override
    public void fundMonthlyStatementMigrationObsKeyFlush(String period) {
        // 成功条数
        int successCount = 0;
        Long latestId = 0L;
        List<group.za.bank.fund.domain.trade.entity.TdFundMonthlyStatement> tradeStatementList = null;
        //分页
        while (true) {
            tradeStatementList = tdFundMonthlyStatementMapper.queryMonthlyStatementList(FundMonthlyStatementDocStatusEnum.FINISHED.getDbValue(), period, latestId, systemConfig.getPageSize());
            if (CollectionUtils.isEmpty(tradeStatementList)) {
                log.warn("fundMonthlyStatementMigrationObsKeyFlush, 没有查到数据! period:{}, latestId:{}", period, latestId);
                break;
            }
            // 用于id分页
            latestId = tradeStatementList.get(tradeStatementList.size() - 1).getId();
            for (TdFundMonthlyStatement tradeStatement : tradeStatementList) {
                try {
                    // 查询statement
                    group.za.bank.statement.domain.entity.TdFundMonthlyStatement statementCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
                    statementCondition.setBankUserId(tradeStatement.getBankUserId());
                    statementCondition.setPeriod(period);
                    statementCondition.setDocLang(tradeStatement.getDocLang());
                    List<group.za.bank.statement.domain.entity.TdFundMonthlyStatement> statementList = tdMonthlyStatementMapper.selectList(statementCondition);
                    if (CollectionUtils.isEmpty(statementList)) {
                        throw new RuntimeException("za_bank_invest_statement.td_fund_monthly_statement is null");
                    }

                    if (StatementConstants.FUND_MIGRATION_FLAG.equals(statementList.get(0).getRemark())) {
                        // 基金历史月结单才要刷obskey
                        this.obskeyFlush(period, tradeStatement);
                        successCount ++;
                    }
                } catch (Exception e) {
                    log.error("fundMonthlyStatementMigrationObsKeyFlush, error, tradeStatement:{}", JSON.toJSONString(tradeStatement), e);
                    throw e;
                }
            }
        }
        log.info("fundMonthlyStatementMigrationObsKeyFlush end, period:{}, successCount:{}", period, successCount);
    }

    /**
     * obskey刷新
     */
    private void obskeyFlush(String period, TdFundMonthlyStatement tradeStatement) {
        // 查询基金月结单概览表
        TdMonthlyStatementOverview condition = new TdMonthlyStatementOverview();
        condition.setPeriod(tradeStatement.getPeriod());
        condition.setBankUserId(tradeStatement.getBankUserId());
        condition.setBankAccountId(tradeStatement.getAccountId());
        TdMonthlyStatementOverview monthlyStatementOverview = tdMonthlyStatementOverviewMapper.selectOne(condition);
        if (Objects.isNull(monthlyStatementOverview)) {
            throw new RuntimeException("za_bank_fund_trade.td_monthly_statement_overview is null");
        }

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement statementCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        statementCondition.setBankUserId(tradeStatement.getBankUserId());
        statementCondition.setPeriod(period);
        statementCondition.setClientId(monthlyStatementOverview.getClientId());
        statementCondition.setDocLang(tradeStatement.getDocLang());
        group.za.bank.statement.domain.entity.TdFundMonthlyStatement statement = tdMonthlyStatementMapper.selectOne(statementCondition);
        if (Objects.isNull(statement)) {
            throw new RuntimeException("za_bank_invest_statement.td_fund_monthly_statement is null");
        }

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement updateStatementCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        updateStatementCondition.setId(statement.getId());

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement updateStatementTarget = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        updateStatementTarget.setDocUrl(tradeStatement.getDocUrl());
        updateStatementTarget.setGmtModified(new Date());
        int updateNumber = tdMonthlyStatementMapper.updateByCondition(updateStatementTarget, updateStatementCondition);
        if (updateNumber != 1) {
            throw new RuntimeException("update TdFundMonthlyStatement.docUrl Failed, statementId:" + statement.getBusinessId());
        }
    }

    /**
     * 更新结单初始化完成状态
     */
    private void updateStatementTotalInfoInitStatus(TdFundMonthlyStatementInfo statementInfo) {
        TdFundMonthlyStatementInfo conditionFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        conditionFundMonthlyStatementInfo.setId(statementInfo.getId());

        TdFundMonthlyStatementInfo newFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        newFundMonthlyStatementInfo.setGmtModified(new Date());


        //统计初始化的结单数量
        TdFundMonthlyStatement tradeTotalCondition = new TdFundMonthlyStatement();
        tradeTotalCondition.setPeriod(statementInfo.getPeriod());
        tradeTotalCondition.setDocStatus(MonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        tradeTotalCondition.setIsDeleted(InvestConstants.N);
        int tradeTotalNumber = tdFundMonthlyStatementMapper.selectCount(tradeTotalCondition) / 2;

        group.za.bank.statement.domain.entity.TdFundMonthlyStatement successCondition = new group.za.bank.statement.domain.entity.TdFundMonthlyStatement();
        successCondition.setPeriod(statementInfo.getPeriod());
        successCondition.setBusinessType(statementInfo.getBusinessType());
        successCondition.setDocStatus(MonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        successCondition.setIsDeleted(InvestConstants.N);
        int statementSuccessNumber = tdMonthlyStatementMapper.selectCount(successCondition) / 2;


        if (tradeTotalNumber == statementSuccessNumber) {
            newFundMonthlyStatementInfo.setInitStatus(MonthlyStatementInfoInitStatusEnum.FINISHED.getDbValue());

        } else {
            log.warn("基金历史月结单, 仍未完全生成完毕! business:{},period:{},tradeTotalNumber:{},statementSuccessNumber:{}"
                    , statementInfo.getBusinessType(), statementInfo.getPeriod(), tradeTotalNumber, statementSuccessNumber);
        }
        newFundMonthlyStatementInfo.setFinishedNumber(statementSuccessNumber);
        newFundMonthlyStatementInfo.setTotalNumber(tradeTotalNumber);
        int updateNumber = tdMonthlyStatementInfoMapper.updateByCondition(newFundMonthlyStatementInfo, conditionFundMonthlyStatementInfo);
        if (updateNumber != 1) {
            log.warn("基金历史月结单, 更新结单初始化状态失败.  business:{},period:{}", statementInfo.getBusinessType(), statementInfo.getPeriod());
            throw new BusinessException(STATEMENT_INFO_STATUS_EXCEPTION);
        } else {
            log.warn("基金历史月结单, 更新结单初始化状态成功.  business:{},period:{}", statementInfo.getBusinessType(), statementInfo.getPeriod());
        }

    }
}