package group.za.bank.statement.service.impl;

import group.za.bank.statement.constants.enums.StatementActionPendingStatusEnum;
import group.za.bank.statement.domain.entity.TdStatementActionPendingData;
import group.za.bank.statement.domain.mapper.TdStatementActionPendingDataMapper;
import group.za.bank.statement.service.StatementActionPendingDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/9 11:23
 * @Description
 */

@Slf4j
@Service
public class StatementActionPendingDataServiceImpl implements StatementActionPendingDataService {

    @Autowired
    private TdStatementActionPendingDataMapper statementActionPendingDataMapper;

    @Override
    public void create(Long statementDataId, String bankUserId, String accountId, String actionId, String actionType, String originalPeriod, String marketCode, boolean uniqueCheck) {
        if (uniqueCheck) {
            TdStatementActionPendingData actionPendingData = selectOne(accountId, actionId);
            if (actionPendingData != null) {
                log.warn("数据已存在,重复插入,accountId={},actionId={}", accountId, actionId);
                return;
            }
        }
        TdStatementActionPendingData record = new TdStatementActionPendingData();
        record.setActionId(actionId);
        record.setActionType(actionType);
        record.setOriginalPeriod(originalPeriod);
        record.setStatementDataId(statementDataId);
        record.setBankUserId(bankUserId);
        record.setAccountId(accountId);
        record.setMarketCode(marketCode);
        record.setVersion(0);
        record.setDataStatus(StatementActionPendingStatusEnum.UN_PROCESSED.getValue());
        statementActionPendingDataMapper.insert(record);
    }

    @Override
    public List<TdStatementActionPendingData> queryPendingList(String marketCode, String accountId, String dataStatus) {
        TdStatementActionPendingData condition = new TdStatementActionPendingData();
        condition.setAccountId(accountId);
        condition.setMarketCode(marketCode);
        condition.setDataStatus(dataStatus);
        return statementActionPendingDataMapper.selectList(condition);
    }

    @Override
    public List<TdStatementActionPendingData> queryPendingListForPeriod(String marketCode, String accountId, String period) {
        TdStatementActionPendingData condition = new TdStatementActionPendingData();
        condition.setAccountId(accountId);
        condition.setPeriod(period);
        condition.setMarketCode(marketCode);
        condition.setDataStatus(StatementActionPendingStatusEnum.PROCESSED.getValue());
        return statementActionPendingDataMapper.selectList(condition);
    }

    @Override
    public int countPendingListForPeriod(String marketCode, String accountId, String period) {
        TdStatementActionPendingData condition = new TdStatementActionPendingData();
        condition.setMarketCode(marketCode);
        condition.setAccountId(accountId);
        condition.setPeriod(period);
        condition.setDataStatus(StatementActionPendingStatusEnum.PROCESSED.getValue());
        return statementActionPendingDataMapper.selectCount(condition);
    }

    @Override
    public void processedActionCapitalData(TdStatementActionPendingData old, String period, Date capitalTime, String remark) {
        TdStatementActionPendingData condition = new TdStatementActionPendingData();
        condition.setId(old.getId());

        TdStatementActionPendingData record = new TdStatementActionPendingData();
        record.setPeriod(period);
        record.setCapitalTime(capitalTime);
        record.setExecuteTime(new Date());
        record.setRemark(remark);
        record.setVersion(old.getVersion() + 1);
        record.setDataStatus(StatementActionPendingStatusEnum.PROCESSED.getValue());
        statementActionPendingDataMapper.updateByCondition(record, condition);
    }

    private TdStatementActionPendingData selectOne(String accountId, String actionId) {
        TdStatementActionPendingData condition = new TdStatementActionPendingData();
        condition.setActionId(actionId);
        condition.setAccountId(accountId);
        return statementActionPendingDataMapper.selectOne(condition);
    }
}
