package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.statement.constants.enums.StatementFileStatusEnum;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.bank.statement.service.TdStockStatementDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * ttl日结单数据检查任务
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Component
@Slf4j
@JobHandler(value = "statementDataDailyCheckJob")
public class StatementDataDailyCheckJob extends IJobHandler {

    @Autowired
    private TdStatementFileRecordService statementFileRecordService;
    @Autowired
    private TdStockStatementDataService stockStatementDataService;

    @Override
    public ReturnT<String> execute(String message) throws Exception {
        log.info("statementDataDailyCheckJob param:{}", message);

        List<String> dateList = new ArrayList<>();
        if(StringUtils.isNotEmpty(message)){
            String[] dateArr = message.split(",");
            dateList.addAll(Arrays.asList(dateArr));
        }else{
            dateList.add(DateUtil.format(new Date(), DateUtil.FORMATDAY));
        }

        for (String statementDateStr : dateList) {
            Date statementDate = DateUtil.parse(statementDateStr, DateUtil.FORMATDAY);
            TdStatementFileRecord statementFileRecord = statementFileRecordService.queryDailyFileRecord(statementDate);
            if(null== statementFileRecord || !StatementFileStatusEnum.SUCCESS.getFileStatus().equals(statementFileRecord.getDataStatus())){
                log.info("【statementDataDailyCheckJob】{}-日结单文件还未解析完成", statementDate);
                continue;
            }
            log.info("statementDataDailyCheckJob statementDateStr:{}, id:{}", statementDateStr, statementFileRecord.getId());

            stockStatementDataService.dailyStatementDataCheck(statementDateStr);
        }

        log.info("statementDataDailyCheckJob end");
        return ReturnT.SUCCESS;
    }
}
