package group.za.bank.statement.service.impl;

import group.za.bank.fund.domain.trade.entity.TdExchangeRateInfo;
import group.za.bank.fund.domain.trade.mapper.TdExchangeRateInfoMapper;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.service.FundMonthlyStatementMigrationService;
import group.za.invest.web.json.JSON;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ExchangeRateInfoServiceImplTest extends BaseTestService {
    @Mock
    TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;
    @Mock
    RedissonClient redissonClient;
    @Mock
    TdExchangeRateInfoMapper tdExchangeRateInfoMapper;
    @Mock
    FundMonthlyStatementMigrationService fundMonthlyStatementMigrationService;
    @Mock
    Logger log;
    @InjectMocks
    ExchangeRateInfoServiceImpl exchangeRateInfoServiceImpl;

    private MockedStatic<JSON> mockedJSON;


    @Test
    public void testInitExchangeRate() throws Exception {
        when(tdMonthlyStatementInfoMapper.queryOne(any())).thenReturn(new TdFundMonthlyStatementInfo());
        List<TdExchangeRateInfo> list = new ArrayList<>();
        TdExchangeRateInfo tdExchangeRateInfo = new TdExchangeRateInfo();
        tdExchangeRateInfo.setSourceCcy("USD");
        tdExchangeRateInfo.setRate(new BigDecimal(7.85639));
        tdExchangeRateInfo.setEffectiveTime(new Date());
        TdExchangeRateInfo tdExchangeRateInfo2 = new TdExchangeRateInfo();
        tdExchangeRateInfo2.setSourceCcy("CNY");
        tdExchangeRateInfo2.setRate(new BigDecimal(7.85639));

        list.add(tdExchangeRateInfo);
        list.add(tdExchangeRateInfo2);

        when(tdExchangeRateInfoMapper.selectList(any())).thenReturn(list);
        when(tdMonthlyStatementInfoMapper.updateByCondition(any(), any()))
                .thenReturn(1);
        exchangeRateInfoServiceImpl.initExchangeRate("202401");
    }

    @Test
    public void testGetExchangeHkdRate() throws Exception {
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setUsdHkdRate(new BigDecimal(1));
        when(tdMonthlyStatementInfoMapper.queryOne(any())).thenReturn(tdFundMonthlyStatementInfo);

        BigDecimal result = exchangeRateInfoServiceImpl.getExchangeHkdRate("USD", "202401", "businessType");
    }

    @Test
    public void testSetExchangeHkdRateToRedis() throws Exception {
        exchangeRateInfoServiceImpl.setExchangeHkdRateToRedis("sourceCcy", "redisKey", new BigDecimal(0));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme