package group.za.bank.statement.service.remote.impl;

import com.alibaba.fastjson.JSON;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.trade.common.constant.enums.StkErrorMsgEnum;
import group.za.bank.sbs.trade.feign.StkActionFeignService;
import group.za.bank.sbs.trade.model.dto.StkBusinessRecordDTO;
import group.za.bank.sbs.trade.model.dto.TaxQueryDTO;
import group.za.bank.sbs.trade.model.req.feign.QueryStatementBusinessRecordReq;
import group.za.bank.sbs.trade.model.req.feign.SettlementDateReq;
import group.za.bank.sbs.trade.model.req.feign.TaxQueryReq;
import group.za.bank.sbs.trade.model.resp.feign.SettlementDateResp;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.TradeRemoteService;
import group.za.bank.statement.service.remote.feign.StkOrderFeign;
import group.za.bank.statement.service.remote.feign.StkTradeCalendarFeign;
import group.za.bank.statement.service.remote.feign.StkTradeTaxFegin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 交易远程服务
 *
 * <AUTHOR>
 * @date 2023/04/03
 **/
@Service
@Slf4j
public class TradeRemoteServiceImpl implements TradeRemoteService {

    @Autowired
    private StkOrderFeign stkOrderFeign;

    @Autowired
    private StkTradeTaxFegin stkTradeTaxFegin;
    @Autowired
    private StkActionFeignService stkActionFeignService;

    @Autowired
    private StkTradeCalendarFeign stkTradeCalendarFeign;


    @Override
    public List<StkBusinessRecordDTO> queryStatementBusinessRecordList(String boOrderNo) {
        QueryStatementBusinessRecordReq req = new QueryStatementBusinessRecordReq();
        req.setBoOrderNo(boOrderNo);
        ResponseData<List<StkBusinessRecordDTO>> listResponseData = stkOrderFeign.queryStatementBusinessRecordList(req);
        if(!listResponseData.judgeSuccess()){
            log.info("queryStatementBusinessRecordList失败:{}", listResponseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return listResponseData.getValue();
    }

    @Override
    public List<TaxQueryDTO> queryTaxList(String accountId, String startDate, String endDate) {
        TaxQueryReq taxQueryReq = new TaxQueryReq();
        taxQueryReq.setAccountId(accountId);
        taxQueryReq.setStartDate(startDate);
        taxQueryReq.setEndDate(endDate);
        ResponseData<List<TaxQueryDTO>> responseData = stkTradeTaxFegin.taxQuery(taxQueryReq);
        if (!responseData.judgeSuccess()) {
            log.warn("查询税费信息失败,req:{}，返回结果:{}", JSON.toJSONString(taxQueryReq), JSON.toJSONString(responseData));
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return responseData.getValue();
    }

    @Override
    public Date getSettlementDate(String marketCode, Date tradeDate, Integer dayDiff) {
        SettlementDateReq req = new SettlementDateReq();
        req.setMarketCode(marketCode);
        req.setTradeDate(DateUtil.format(tradeDate, DateUtil.FORMATDAY));
        // 交易日后两天的日期 是交收日
        req.setDayDiff(dayDiff);
        ResponseData<SettlementDateResp> responseData = stkTradeCalendarFeign.getSettlementDate(req);

        if (null == responseData || !responseData.judgeSuccess() || null == responseData.getValue()) {
            log.error("查询交收日失败");
            throw new BusinessException(StkErrorMsgEnum.RPC_INVOKE_ERR);
        }
        return DateUtil.parse(responseData.getValue().getSettlementDate(),DateUtil.FORMATDAY);
    }


}
