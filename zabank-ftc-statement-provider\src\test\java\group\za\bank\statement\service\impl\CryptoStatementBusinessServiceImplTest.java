package group.za.bank.statement.service.impl;

import cn.hutool.core.lang.Pair;
import group.za.bank.ccs.tradecore.model.entity.CcBusinessRecord;
import group.za.bank.ccs.tradecore.model.entity.CcCryptoInfo;
import group.za.bank.ccs.tradecore.model.entity.CcOrder;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.constants.enums.StatementDirectionEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.dto.CryptoHoldInfoDto;
import group.za.bank.statement.entity.dto.CryptoMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.manager.CryptoTradeCoreManager;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.bank.statement.service.remote.CryptoInfoRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(org.mockito.junit.MockitoJUnitRunner.class)
public class CryptoStatementBusinessServiceImplTest {
    @Mock
    CryptoTradeCoreManager cryptoTradeCoreManager;
    @Mock
    CryptoInfoRemoteService cryptoInfoRemoteService;
    @Mock
    ExchangeRateInfoService exchangeRateInfoService;
    @Mock
    Logger log;
    @InjectMocks
    CryptoStatementBusinessServiceImpl cryptoStatementBusinessServiceImpl;

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJSON;
    private MockedStatic<DateUtil> mockedDateUtil;

    private MockedStatic<MonthlyUtils> mockedMonthlyUtils;

    @Before
    public void setUp() {
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        // Mock JSON
        mockedJSON = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        // Mock DateUtil
        mockedDateUtil = mockStatic(DateUtil.class);
        when(DateUtil.parse(anyString(), anyString())).thenReturn(new Date());
        when(DateUtil.format(any(), any())).thenReturn("2024-04-01");

        mockedMonthlyUtils = mockStatic(MonthlyUtils.class);

    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        if (mockedJSON != null) {
            mockedJSON.close();
        }
        if (mockedDateUtil != null) {
            mockedDateUtil.close();
        }
        if(mockedMonthlyUtils != null){
            mockedMonthlyUtils.close();
        }
    }

    @Test
    public void testGetBusinessType() throws Exception {
        String result = cryptoStatementBusinessServiceImpl.getBusinessType();
    }

    @Test
    public void testIsDataPrepared() throws Exception {
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setPeriod("202404");
        boolean result = cryptoStatementBusinessServiceImpl.isDataPrepared(tdFundMonthlyStatementInfo, true);
    }

    @Test
    public void testGenerateDataMap() throws Exception {
        CcOrder ccOrder = new CcOrder();
        ccOrder.setLastBusinessTime(new Date());
        ccOrder.setFirstDeliveryTime(new Date());
        ccOrder.setDirection(StatementDirectionEnum.SELL.getTradeType());
        when(cryptoTradeCoreManager.cryptoOrderList(any(), any(), any())).thenReturn(Arrays.<CcOrder>asList(ccOrder));

        CcBusinessRecord ccBusinessRecord = new CcBusinessRecord();
        ccBusinessRecord.setDealAmt(new BigDecimal(1));
        ccBusinessRecord.setDealVolume(new BigDecimal(1));
        ccBusinessRecord.setDealPrice(new BigDecimal(1));
        when(cryptoTradeCoreManager.cryptoBusinessRecordList(any())).thenReturn(Arrays.<CcBusinessRecord>asList(ccBusinessRecord));

        CryptoHoldInfoDto cryptoHoldInfoDto = new CryptoHoldInfoDto();
        cryptoHoldInfoDto.setClosePrice(new BigDecimal(1));
        cryptoHoldInfoDto.setCloseVolume(new BigDecimal(1));
        when(cryptoTradeCoreManager.cryptoHoldList(any(), any())).thenReturn(Arrays.<CryptoHoldInfoDto>asList(cryptoHoldInfoDto));
        when(cryptoTradeCoreManager.getCcCryptoInfo(any())).thenReturn(new CcCryptoInfo());
        when(exchangeRateInfoService.getExchangeHkdRate(any(), any(), any())).thenReturn(new BigDecimal(1));
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setPeriod("202404");
        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocLang("CN_zh");
        cryptoStatementBusinessServiceImpl.generateDataMap(tdFundMonthlyStatementInfo, tdFundMonthlyStatement, new TdMonthlyStatementData(), "US");
    }

    //@Test
    public void testParseDataMap2Dto() throws Exception {
        CryptoMonthlyStatementDataDto result = cryptoStatementBusinessServiceImpl.parseDataMap2Dto(new HashMap<String, Object>() {{
            put("String", "businessDataMap");
        }});
    }

    @Test
    public void testIsUserNeedGenerateStatement() throws Exception {
        CcOrder ccOrder = new CcOrder();
        when(cryptoTradeCoreManager.cryptoOrderList(anyString(), anyString(), anyString())).thenReturn(Arrays.<CcOrder>asList(ccOrder));
        when(cryptoTradeCoreManager.cryptoHoldList(anyString(), anyString())).thenReturn(Arrays.<CryptoHoldInfoDto>asList(new CryptoHoldInfoDto()));

        boolean result = cryptoStatementBusinessServiceImpl.isUserNeedGenerateStatement(new TdFundMonthlyStatementInfo(), new UserInvestClientInfoDto(), "US");
    }

    @Test
    public void testIsUserNeedGenerateStatement2() throws Exception {
        CcOrder ccOrder = new CcOrder();
        when(cryptoTradeCoreManager.cryptoOrderList(anyString(), anyString(), anyString())).thenReturn(Arrays.<CcOrder>asList(ccOrder));
        when(cryptoTradeCoreManager.cryptoHoldList(anyString(), anyString())).thenReturn(Arrays.<CryptoHoldInfoDto>asList(new CryptoHoldInfoDto()));

        boolean result = cryptoStatementBusinessServiceImpl.isUserNeedGenerateStatement(new TdFundMonthlyStatementInfo(), "userId", "accountId", null);
    }

    @Test
    public void testGetUserPeriodEndTotalMarketAndProfit() throws Exception {
        when(cryptoTradeCoreManager.cryptoHoldList(anyString(), anyString())).thenReturn(Arrays.<CryptoHoldInfoDto>asList(new CryptoHoldInfoDto()));
        when(exchangeRateInfoService.getExchangeHkdRate(anyString(), anyString(), anyString())).thenReturn(new BigDecimal(0));

        Pair<BigDecimal, BigDecimal> result = cryptoStatementBusinessServiceImpl.getUserPeriodEndTotalMarketAndProfit(new TdFundMonthlyStatementInfo(), new TdFundMonthlyStatement(), CurrencyEnum.AUD, "accountId", "US");
    }

    @Test
    public void testStatementAllProcessFinishHook() throws Exception {
        cryptoStatementBusinessServiceImpl.statementAllProcessFinishHook(new TdFundMonthlyStatementInfo());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme