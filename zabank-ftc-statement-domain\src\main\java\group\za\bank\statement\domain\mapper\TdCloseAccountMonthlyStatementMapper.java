package group.za.bank.statement.domain.mapper;


import group.za.bank.statement.domain.entity.TdCloseAccountMonthlyStatement;
import group.za.invest.mybatis.mapper.template.CrudMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* This mapper interface access the database table td_fund_close_account_monthly_statement
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年12月19日 18:20:53
*/
@Repository
public interface TdCloseAccountMonthlyStatementMapper extends CrudMapper<TdCloseAccountMonthlyStatement> {

    void deleteCloseAccountMonthlyStatement(@Param("period")String period);

    List<TdCloseAccountMonthlyStatement> queryFundCloseAccountMonthlyStatement(@Param("period")String period,
                                                                               @Param("pageSize")int pageSize,
                                                                               @Param("id")long id);
}
