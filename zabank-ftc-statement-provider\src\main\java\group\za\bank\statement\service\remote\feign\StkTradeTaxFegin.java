package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.trade.feign.TaxFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @Date 2023/5/24 14:10
 * @Description
 */
@FeignClient(
        value = "zabank-sbs-trade-service",
        contextId = "stkTradeTaxFegin",
        url = "${sbs.gateway.url}"
)
public interface StkTradeTaxFegin extends TaxFeignService {

}
