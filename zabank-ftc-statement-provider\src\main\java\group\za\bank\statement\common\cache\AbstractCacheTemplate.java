package group.za.bank.statement.common.cache;

import group.za.bank.invest.common.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> guofengbo
 * @date: 2021/8/31 16:17
 * @Description:
 */
@Slf4j
public abstract class AbstractCacheTemplate {

    private static RedissonClient redissonClient;

    public static RedissonClient getRedissonClient() {
        if(Objects.isNull(redissonClient)){
            redissonClient = SpringUtils.getBean(RedissonClient.class);
        }
        return redissonClient;
    }

    /**
     * 查询缓存
     * 
     * @param key
     * @param expire
     * @param cacheLoad
     * @param refresh
     * @param <T>
     * @return
     */
    public static <T> T queryFromCache(String key, long expire, TimeUnit timeUnit, CacheLoad<T> cacheLoad,
        boolean refresh) {
        RBucket<T> bucket = getRedissonClient().getBucket(key);
        if (refresh) {
            T t = cacheLoad.load();
            if (t != null) {
                bucket.set(t, expire, timeUnit);
            }
            return t;
        }

        T result = null;
        try {
            result = bucket.get();
        } catch (Exception e) {
            log.error("查询cache 报错", e);
        }
        if (result != null) {
            return result;
        }
        return queryFromCache(key, expire, timeUnit, cacheLoad, true);
    }

}
