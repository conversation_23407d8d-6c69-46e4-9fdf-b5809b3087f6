package group.za.bank.statement.entity.dto;

import group.za.bank.fund.domain.trade.entity.TdFundHoldingHistory;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 13 14:48
 * @description
 */
@Data
public class FundHistoryHoldingPairDto {


    public FundHistoryHoldingPairDto() {
    }

    public FundHistoryHoldingPairDto(TdFundHoldingHistory firstDto, TdFundHoldingHistory secondDto) {
        this.firstDto = firstDto;
        this.secondDto = secondDto;
    }


    private TdFundHoldingHistory firstDto;
    private TdFundHoldingHistory secondDto;



}
