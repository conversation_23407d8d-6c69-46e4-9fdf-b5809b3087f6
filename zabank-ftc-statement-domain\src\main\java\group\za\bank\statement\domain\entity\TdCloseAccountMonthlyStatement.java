package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
* This class corresponds to the database table td_fund_close_account_monthly_statement
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年12月19日 18:20:53
*/
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_close_account_monthly_statement")
public class TdCloseAccountMonthlyStatement extends AuditIdEntity<Long> {
private static final long serialVersionUID = 1L;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.business_id
        * <p>
        * Database Column Remarks: 业务逻辑id
        * </p>
        */
        @Column(name = "business_id")
        private String businessId;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.period
        * <p>
        * Database Column Remarks: 月结单期数:'yyyyMM'
        * </p>
        */
        @Column(name = "period")
        private String period;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.bank_user_id
        * <p>
        * Database Column Remarks: 银行用户id
        * </p>
        */
        @Column(name = "bank_user_id", nullable = false)
        private String bankUserId;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.bank_account_id
        * <p>
        * Database Column Remarks: 投资账户id
        * </p>
        */
        @Column(name = "client_id")
        private String clientId;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.send_status
        * <p>
        * Database Column Remarks: 发送状态:1-待发送,2-已发送 
        * </p>
        */
        @Column(name = "send_status")
        private Byte sendStatus;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.doc_lang
        * <p>
        * Database Column Remarks: 文档语言
        * </p>
        */
        @Column(name = "doc_lang")
        private String docLang;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.temp_key
        * <p>
        * Database Column Remarks: 模板key
        * </p>
        */
        @Column(name = "temp_key")
        private String tempKey;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.doc_url
        * <p>
        * Database Column Remarks: 文档地址
        * </p>
        */
        @Column(name = "doc_url")
        private String docUrl;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.pub_time
        * <p>
        * Database Column Remarks: 发送成功时间
        * </p>
        */
        @Column(name = "pub_time")
        private Date pubTime;

        /**
        * Database Column Name: td_fund_close_account_monthly_statement.remark
        * <p>
        * Database Column Remarks: 备注
        * </p>
        */
        @Column(name = "remark")
        private String remark;

}