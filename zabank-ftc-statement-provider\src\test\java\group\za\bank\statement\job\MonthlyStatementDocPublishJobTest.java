package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.service.MonthlyStatementService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.Mockito.when;

public class MonthlyStatementDocPublishJobTest extends BaseTestService {
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    SystemConfig systemConfig;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    MonthlyStatementDocPublishJob monthlyStatementDocPublishJob;

    @Test
    public void testExecute() throws Exception {
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("getDefaultMonthlyStatementBusinessTypeResponse");

        ReturnT<String> result = monthlyStatementDocPublishJob.execute("{\"period\":\"202203\",businessType:\"fund,stock\"}");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme