{"targets": [{"name": "holding", "desc": "投資帳戶持倉", "isSkip": "0", "index": 0, "path": "div[class=primary mt0]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundHolding", "type": "fund", "typeName": "assetType", "isArray": "1", "titleIndex": 0, "titleName": "assetTypeTitle", "titlePath": "div[class=table-title-desc]", "index": 0, "path": "table[class=table-title]", "fieldBasePath": "tbody tr", "fieldsName": "nativeRowList", "fields": [{"groupId": "row1", "rowType": 211, "seq": 0, "name": "fundName", "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "1", "ext": {"fundNameOrNot": 1}}, {"groupId": "row1", "rowType": 221, "name": "currency1", "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 0}, {"groupId": "row1", "rowType": 222, "name": "marketValue", "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "seq": 0}, {"name": "fundOpeningBalance", "titlePath": "thead tr th:eq(1)", "path": "td:eq(1)", "isHtml": "1", "skipErr": "1", "seq": 1, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}, {"name": "fundClosingBalance", "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "1", "seq": 2, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}, {"groupId": "row4", "rowType": 10, "name": "referencePrice", "isHtml": "1", "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "seq": 4}, {"groupId": "row4", "rowType": 121, "name": "currency2", "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 3}]}, {"name": "stockHolding", "type": "stock", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titlePath": "div.table-title-desc.mg-top-28", "titleIndex": 0, "index": 0, "fieldBasePath": "tbody tr", "path": "table[class=table-title-stock]", "fieldsName": "nativeRowList", "fields": [{"name": "stockName", "groupId": "stockHoldingGroup1", "rowType": 211, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "seq": 0, "ext": {"stockNameOrNot": 1}}, {"name": "stockCurrency", "groupId": "stockHoldingGroup1", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 0}, {"name": "stockMarketValue", "groupId": "stockHoldingGroup1", "rowType": 222, "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "seq": 0}, {"name": "stockOpeningBalance", "titlePath": "thead tr th:eq(1)", "path": "td:eq(1)", "seq": 1, "isHtml": "1", "valueUnit": "股|股|Share*Shares*Share(s)"}, {"name": "stockClosingBalance", "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "seq": 2, "isHtml": "1", "valueUnit": "股|股|Share*Shares*Share(s)"}, {"name": "stockReferencePrice", "groupId": "stockHoldingGroup2", "rowType": 10, "isHtml": "1", "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "seq": 4}, {"name": "stockCurrency2", "groupId": "stockHoldingGroup2", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 4}, {"name": "marketValueHkd", "groupId": "stockHoldingEpvHkdGroup", "rowType": 10, "isHtml": "1", "titlePath": "thead tr th:eq(6)", "path": "td:eq(6)", "seq": 5, "valueUnit": "@HKD"}]}, {"name": "cryptoHolding", "type": "crypto", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titlePath": "div.table-title-desc.mg-top-28-crypto", "titleIndex": 0, "index": 0, "fieldBasePath": "tbody tr", "path": "table[class=table-title-crypto]", "fieldsName": "nativeRowList", "fields": [{"name": "assetName", "groupId": "row1", "rowType": 211, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "seq": 0, "ext": {"cryptoNameOrNot": 1}}, {"name": "cryptoCurrency", "groupId": "row1", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 0}, {"name": "cryptoMarketValue", "groupId": "row1", "rowType": 222, "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "seq": 0}, {"name": "cryptoOpeningBalance", "titlePath": "thead tr th:eq(1)", "path": "td:eq(1)", "seq": 1, "isHtml": "1"}, {"name": "cryptoClosingBalance", "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "seq": 2, "isHtml": "1"}, {"name": "cryptoReferencePrice", "groupId": "row4", "rowType": 10, "isHtml": "1", "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "seq": 4}, {"name": "cryptoCurrency2", "groupId": "row4", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 4}, {"name": "cryptoMarketValue1", "groupId": "row5", "rowType": 10, "isHtml": "1", "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "seq": 5}, {"name": "cryptoCurrency3", "groupId": "row5", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "seq": 5}]}]}, {"name": "confirmedOrder", "desc": "已確認交易", "isSkip": "0", "index": 0, "path": "div[class=primary]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundConfirmed", "type": "fund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 1, "titlePath": "div[class=table-title-desc]", "path": "table.table-title.table-special", "fieldBasePath": "tbody tr", "index": 0, "fieldsName": "nativeRowList", "fields": [{"name": "fundConDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "fundDescription", "groupId": "row1", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p ", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($this, ' ', ' -')", "ext": {"fundNameOrNot": 1}}, {"name": "fundCurrency", "groupId": "row1", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 1}, {"name": "fundAmount", "groupId": "row1", "rowType": 222, "titlePath": "thead tr th:eq(6)", "path": "td:eq(6)", "isHtml": "0", "seq": 1}, {"name": "fundNoOfUnits", "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "0", "valueUnit": "单位|單位|Unit*Units*Unit(s)", "seq": 2}, {"name": "fundUnitPrice", "groupId": "row3", "rowType": 1, "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 3}, {"name": "fundCurrency2", "groupId": "row3", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 3}, {"name": "fundFee", "groupId": "row4", "rowType": 1, "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "isHtml": "1", "seq": 4}, {"name": "fundCurrency3", "groupId": "row4", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 4}]}, {"name": "stockConfirmed", "type": "stock", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 1, "titlePath": "div.table-title-desc.mg-top-28", "path": "table.table-title-stock.table-special-stock", "fieldBasePath": "> tbody", "index": 0, "fieldsName": "nativeRowList", "fields": [{"name": "nativeRowDetailList", "isArray": "1", "detailFieldsName": "nativeRowDetailList", "titlePath": "thead tr th:eq(0)", "path": "tr:eq(1) td:eq(0)", "isHtml": "0", "seq": 1, "detailFieldBasePath": "table.special-table tbody", "detailFields": [{"name": "stockDate", "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "> tr:eq(0) td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "stockDescription", "rowType": 211, "groupId": "row1", "titlePath": "thead tr th:eq(2)", "path": "> tr:eq(0) td:eq(2) p", "isHtml": "1", "seq": 1, "ext": {"stockNameOrNot": 1}}, {"name": "stockQuantity", "titlePath": "thead tr th:eq(3)", "path": "> tr td:eq(3)", "isHtml": "1", "seq": 2, "valueUnit": "股|股|Share*Shares*Share(s)"}, {"name": "stockPrice", "rowType": 1, "titlePath": "thead tr th:eq(4)", "path": "> tr td:eq(4)", "isHtml": "1", "seq": 3, "valueUnit": "@USD|USD|USD"}, {"name": "stockAmount", "rowType": 1, "groupId": "row1", "titlePath": "thead tr th:eq(5)", "path": "> tr td:eq(5)", "isHtml": "0", "seq": 4, "valueUnit": "@USD|USD|USD"}]}, {"name": "waivedAmount", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 5, "splitter": "=>Waived amount 豁免金额|Waived amount 豁免金额"}, {"name": "secFee", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(1)", "path": "td:eq(1)", "isHtml": "0", "seq": 6, "splitter": "=>SEC Fee 监管费用|SEC Fee 監管費用"}, {"name": "finra<PERSON><PERSON>", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(2)", "path": "td:eq(2)", "isHtml": "0", "seq": 7, "splitter": "=>FINRA Fee交易费用|FINRA Fee交易費用"}, {"name": "commission", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 8, "splitter": "=>Commission佣金|Commission佣金"}, {"name": "settleAmount", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 9, "splitter": "=>Settlement Amount 交收金额|Settlement Amount 交收金額"}]}, {"name": "cryptoConfirmed", "type": "crypto", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 1, "titlePath": "div.table-title-desc.mg-top-28-crypto", "path": "table.table-title-crypto.table-special-crypto", "fieldBasePath": "> tbody", "index": 0, "fieldsName": "nativeRowList", "fields": [{"name": "nativeRowDetailList", "isArray": "1", "detailFieldsName": "nativeRowDetailList", "titlePath": "thead tr th:eq(0)", "path": "tr:eq(1) td:eq(0)", "isHtml": "0", "detailFieldBasePath": "table.special-table-crypto tbody", "detailFields": [{"name": "cryptoDate", "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "> tr:eq(0) td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "cryptoDescription", "rowType": 211, "groupId": "cryptoConfirmedAmount", "titlePath": "thead tr th:eq(2)", "path": "> tr:eq(0) td:eq(2) p", "isHtml": "1", "seq": 1, "ext": {"cryptoNameOrNot": 1}}, {"name": "cryptoQuantity", "titlePath": "thead tr th:eq(4)", "path": "> tr td:eq(4)", "isHtml": "1", "seq": 2}, {"name": "cryptoPriceCurrency", "rowType": 1, "groupId": "cryptoConfirmedPrice", "titlePath": "thead tr th:eq(3)", "path": "> tr td:eq(3)", "isHtml": "1", "seq": 3}, {"name": "cryptoPrice", "rowType": 1, "groupId": "cryptoConfirmedPrice", "titlePath": "thead tr th:eq(5)", "path": "> tr td:eq(5)", "isHtml": "1", "seq": 4}, {"name": "cryptoConfirmedAmountCurrency", "groupId": "cryptoConfirmedAmount", "rowType": 1, "titlePath": "thead tr th:eq(3)", "path": "> tr td:eq(3)", "isHtml": "1", "seq": 5}, {"name": "cryptoAmount", "rowType": 1, "groupId": "cryptoConfirmedAmount", "titlePath": "thead tr th:eq(6)", "path": "> tr td:eq(6)", "isHtml": "0", "seq": 6}]}, {"name": "commission", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 5, "splitter": "=>Commission佣金|Commission佣金"}, {"name": "platformFee", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 6, "splitter": "=>Platform Fee 平台费|Platform Fee 平台費"}, {"name": "settleAmount", "rowType": 1, "dataDetailPath": "tr.bdtop", "index": 0, "titlePath": "td:eq(5)", "path": "td:eq(5)", "isHtml": "0", "seq": 7, "splitter": "=>Settlement Amount 交收金额|Settlement Amount 交收金額"}]}]}, {"name": "pendingOrder", "desc": "基金處理中交易", "isSkip": "0", "index": 0, "path": "div[class=primary mt0-fund]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundPendingOrder", "type": "fund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 0, "titlePath": "div[class=primary mt0-fund]>div[class=bg]", "path": "table.table-title.table-special", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 1, "fields": [{"name": "fundPendingDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "tr td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "fundPendingDescription", "groupId": "row1", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "tr td:eq(1) p", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($ParserUtil.appendAfter($this, ' ', ' -'),'<br> ', ' ', ' -')", "ext": {"fundNameOrNot": 1}}, {"name": "fundPendingCurrency", "groupId": "row1", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "tr td:eq(3)", "isHtml": "0", "seq": 2, "ext": {"unitOrNot": 1}}, {"name": "fundPendingAmount", "groupId": "row1", "rowType": 222, "titlePath": "thead tr th:eq(6)", "path": "tr td:eq(6)", "isHtml": "0", "seq": 1}, {"name": "fundPendingNoOfUnits", "titlePath": "thead tr th:eq(2)", "path": "tr td:eq(2)", "isHtml": "1", "seq": 2, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}, {"name": "fundPendingUnitPrice", "groupId": "row3", "rowType": 1, "titlePath": "thead tr th:eq(4)", "path": "tr td:eq(4)", "isHtml": "1", "seq": 1}, {"name": "fundPendingCurrency2", "groupId": "row3", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "tr td:eq(3)", "isHtml": "0", "seq": 2, "ext": {"unitOrNot": 1}}, {"name": "fundPendingFee", "groupId": "row4", "rowType": 1, "titlePath": "thead tr th:eq(5)", "path": "tr td:eq(5)", "isHtml": "1", "seq": 1}, {"name": "fundPendingCurrency3", "groupId": "row4", "rowType": 121, "titlePath": "thead tr th:eq(3)", "path": "tr td:eq(3)", "isHtml": "0", "seq": 2, "ext": {"unitOrNot": 1}}]}]}, {"name": "dividendOrder", "desc": "派息", "isSkip": "0", "index": 0, "path": "div[class=primary mt0-invest]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundDividendOrder", "type": "fund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 2, "titlePath": "div[class=table-title-desc]", "path": "table.table-title.table-special", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 2, "fields": [{"name": "dividendOrderDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "dividendOrderDescription", "groupId": "row4", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($this, ' ', ' -').replace('Dividend', 'Dividend -')", "ext": {"fundNameOrNot": 1}}, {"name": "dividendOrderNoOfUnits", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "0", "valueUnit": "单位|單位|Unit*Units*Unit(s)", "seq": 2}, {"name": "dividendOrderCurrency", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 3}, {"name": "dividendOrderAmount", "groupId": "row4", "rowType": 222, "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 4}]}, {"name": "stockDividendOrder", "type": "stock", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 2, "titlePath": "div.table-title-desc.mg-top-28", "path": "table.table-title-stock.table-special-stock", "fieldBasePath": "tbody tr", "index": 1, "fieldsName": "nativeRowList", "fields": [{"name": "dividendOrderDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "dividendOrderDescription", "groupId": "stockDividendOrderGroup", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p", "isHtml": "1", "seq": 1, "ext": {"stockNameOrNot": 1}, "splitter": "<=Remark|Remark|Remark"}, {"name": "dividendOrderBonus", "groupId": "stockDividendOrderGroup", "rowType": 212, "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "1", "seq": 2, "ext": {"stockNameOrNot": 1}}, {"name": "dividendOrderQuantity", "groupId": "stockDividendOrderGroup", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 3, "valueUnit": "股|股|Share*Shares*Share(s)"}, {"name": "dividendOrderCurrency", "groupId": "stockDividendOrderGroup", "rowType": 221, "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 4}, {"name": "dividendOrderAmount", "groupId": "stockDividendOrderGroup", "rowType": 222, "titlePath": "thead tr th:eq(5)", "path": "td:eq(5)", "isHtml": "0", "seq": 5}]}]}, {"name": "holdingChange", "desc": "持倉變動", "isSkip": "0", "index": 1, "path": "div[class=primary mt0-invest]>div[class=bg]", "modelName": "nativeModuleList", "models": [{"name": "fundHoldingChange", "type": "fund", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 3, "titlePath": "div[class=table-title-desc]", "path": "table.table-title.table-special", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 3, "fields": [{"name": "fundHoldingChangeDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "fundHoldingChangeDescription", "groupId": "row4", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p", "isHtml": "1", "seq": 1, "exp": "$ParserUtil.appendAfter($ParserUtil.appendAfter($this, ' ', ' -'),'<br> ', ' ', ' -')", "ext": {"fundNameOrNot": 1}}, {"name": "fundHoldingChangeNoOfUnits", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(2)", "path": "td:eq(2)", "isHtml": "0", "seq": 2, "valueUnit": "单位|單位|Unit*Units*Unit(s)"}]}, {"name": "stockHoldingChange", "type": "stock", "typeName": "assetType", "isArray": "1", "titleName": "assetTypeTitle", "titleIndex": 3, "titlePath": "div.table-title-desc.mg-top-28", "path": "table.table-title-stock.table-special-stock", "fieldsName": "nativeRowList", "fieldBasePath": "tbody tr", "index": 2, "fields": [{"name": "stockHoldingChangeDate", "blockType": 1, "rowType": 3, "titlePath": "thead tr th:eq(0)", "path": "td:eq(0)", "isHtml": "0", "seq": 0}, {"name": "stockHoldingChangeDescription", "groupId": "row4", "rowType": 211, "titlePath": "thead tr th:eq(1)", "path": "td:eq(1) p", "isHtml": "1", "seq": 1, "ext": {"stockNameOrNot": 1}}, {"name": "stockHoldingChangeInOut", "groupId": "row4", "rowType": 221, "titlePath": "thead tr th:eq(3)", "path": "td:eq(3)", "isHtml": "0", "seq": 3}, {"name": "stockHoldingChangeNoOfUnits", "groupId": "row4", "rowType": 222, "titlePath": "thead tr th:eq(4)", "path": "td:eq(4)", "isHtml": "0", "seq": 4, "valueUnit": "股|股|Share*Shares*Share(s)"}]}]}]}