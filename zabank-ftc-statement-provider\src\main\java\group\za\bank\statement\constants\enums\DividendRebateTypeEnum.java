package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;

import group.za.bank.invest.basecommon.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> zehua.li
 * @Classname DividendRebateTypeEnum
 * @Description TODO
 * @Date 2024/7/24 16:36
 */
@AllArgsConstructor
@Getter
public enum DividendRebateTypeEnum  implements BaseEnum {
    Additional("1","股息追缴"),
    rebate("2","股息补偿");

    private String value;

    private String msg;


   public static StatementBusinessTypeEnum  convertStatementBusinessTypeEnum(String type){
       if(DividendRebateTypeEnum.Additional.getValue().equals(type)){
           return StatementBusinessTypeEnum.ADDTAX;
       }
       if(DividendRebateTypeEnum.rebate.getValue().equals(type)){
           return StatementBusinessTypeEnum.REBATE;
       }
       throw  new BusinessException( StatementErrorMsgEnum.STATEMENT_SUB_BUSINESS_DATA_WRONG);

    }

}
