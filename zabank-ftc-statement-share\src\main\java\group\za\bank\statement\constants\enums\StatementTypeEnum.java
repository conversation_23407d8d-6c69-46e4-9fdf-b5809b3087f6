package group.za.bank.statement.constants.enums;

/**
 * 结单类型
 *
 * <AUTHOR>
 * @date 2022/04/29
 **/
public enum StatementTypeEnum {
    /**
     * ttl提供月结单数据类型
     * "后期不会使用月结单数据了"
     */
    MONTHLY(2, "月结单"),

    DAILY(1, "日结单"),
    ;

    private Integer type;
    private String desc;

    StatementTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
