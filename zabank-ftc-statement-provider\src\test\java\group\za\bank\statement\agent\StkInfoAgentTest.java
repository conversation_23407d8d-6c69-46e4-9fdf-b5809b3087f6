package group.za.bank.statement.agent;

import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.common.utils.SpringUtils;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.statement.base.BaseTest;
import group.za.bank.statement.base.BaseTestH2Service;
import group.za.bank.statement.service.remote.StkInfoRemoteService;
import group.za.invest.cache.redis.configuration.RedissonAutoConfiguration;

import group.za.invest.json.spring.JsonAutoConfiguration;
import org.junit.Assert;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {StkInfoAgent.class, JacksonAutoConfiguration.class, JsonAutoConfiguration.class,
        RedissonAutoConfiguration.class,   TransactionAutoConfiguration.class, JsonUtils.class, RedissonClient.class, SpringUtils.class})
public class StkInfoAgentTest extends BaseTestH2Service {
    @MockBean
    StkInfoRemoteService stkInfoRemoteService;
    @MockBean
    Logger log;
    @Resource
    StkInfoAgent stkInfoAgent;


    @Test
    public void testGetStkInfo() {
        StockInfoResp exceptResult = new StockInfoResp();
        exceptResult.setStockCode("AAPL");
        when(stkInfoRemoteService.getStockInfo(any())).thenReturn(exceptResult);
        StockInfoResp result = stkInfoAgent.getStkInfo("US", "AAPL");
        Assert.assertEquals(exceptResult.getStockCode(), result.getStockCode());
    }

    @Test
    public void testGetStockNameAbbr() {
        StockInfoResp stockInfoResp = new StockInfoResp();
        stockInfoResp.setStockCode("AAPL");
        stockInfoResp.setStockNameEng("AAPL");
        when(stkInfoRemoteService.getStockInfo(any())).thenReturn(stockInfoResp);

        String result = stkInfoAgent.getStockNameAbbr("US", "AAPL", "US_en");
        Assert.assertEquals("AAPL", result);
    }
}