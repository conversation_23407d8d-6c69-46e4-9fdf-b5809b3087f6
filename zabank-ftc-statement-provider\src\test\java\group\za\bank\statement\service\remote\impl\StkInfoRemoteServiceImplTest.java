package group.za.bank.statement.service.remote.impl;


import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.trade.model.req.feign.StockInfoReq;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.statement.service.remote.feign.StkInfoFeign;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
@RunWith(MockitoJUnitRunner.class)
public class StkInfoRemoteServiceImplTest  {
    @Mock
    StkInfoFeign stkInfoFeign;
    @InjectMocks
    StkInfoRemoteServiceImpl stkInfoRemoteServiceImpl;

    @Test
    public void testGetStockInfo() {
        ResponseData<StockInfoResp> stockInfoRespResponseData = new ResponseData<>();
        stockInfoRespResponseData.setCode("0000");
        stockInfoRespResponseData.setValue(new StockInfoResp());
        StockInfoReq stockInfoReq = new StockInfoReq();

        Mockito.when(stkInfoFeign.getStockInfo(stockInfoReq)).thenReturn(stockInfoRespResponseData);

        StockInfoResp result = stkInfoRemoteServiceImpl.getStockInfo(stockInfoReq);
        Assert.assertEquals(new StockInfoResp(), result);
    }
}