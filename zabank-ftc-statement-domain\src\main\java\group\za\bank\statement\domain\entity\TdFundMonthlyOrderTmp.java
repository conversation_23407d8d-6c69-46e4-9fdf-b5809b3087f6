package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
* This class corresponds to the database table td_fund_monthly_order_tmp
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年11月01日 17:38:43
*/
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_fund_monthly_order_tmp")
public class TdFundMonthlyOrderTmp extends AuditIdEntity<Long> {
private static final long serialVersionUID = 1L;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.order_no
        * <p>
        * Database Column Remarks: 订单id
        * </p>
        */
        @Column(name = "order_no", nullable = false)
        private String orderNo;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.bank_user_id
        * <p>
        * Database Column Remarks: 银行用户id
        * </p>
        */
        @Column(name = "bank_user_id", nullable = false)
        private String bankUserId;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.bank_account_id
        * <p>
        * Database Column Remarks: 银行基金账户
        * </p>
        */
        @Column(name = "bank_account_id", nullable = false)
        private String bankAccountId;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.broker_account_id
        * <p>
        * Database Column Remarks: 上手投资账户（AAIM投资账户）
        * </p>
        */
        @Column(name = "broker_account_id", nullable = false)
        private String brokerAccountId;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.isin_no
        * <p>
        * Database Column Remarks: isin号
        * </p>
        */
        @Column(name = "isin_no", nullable = false)
        private String isinNo;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.currency
        * <p>
        * Database Column Remarks: 结算货币类型
        * </p>
        */
        @Column(name = "currency", nullable = false)
        private String currency;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.product_id
        * <p>
        * Database Column Remarks: 产品Id
        * </p>
        */
        @Column(name = "product_id", nullable = false)
        private String productId;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.product_name
        * <p>
        * Database Column Remarks: 产品名称
        * </p>
        */
        @Column(name = "product_name")
        private String productName;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.broker_order_no
        * <p>
        * Database Column Remarks: 上手订单号
        * </p>
        */
        @Column(name = "broker_order_no")
        private String brokerOrderNo;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.business_type
        * <p>
        * Database Column Remarks: 业务类型(10-基金申购，11-基金赎回，12-基金分红，13-基金转换, 20-存  21-取
        * </p>
        */
        @Column(name = "business_type", nullable = false)
        private String businessType;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.apply_amt
        * <p>
        * Database Column Remarks: 申请金额
        * </p>
        */
        @Column(name = "apply_amt", nullable = false)
        private BigDecimal applyAmt;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.apply_share
        * <p>
        * Database Column Remarks: 申请份额
        * </p>
        */
        @Column(name = "apply_share", nullable = false)
        private BigDecimal applyShare;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.confirm_amt
        * <p>
        * Database Column Remarks: 确认金额
        * </p>
        */
        @Column(name = "confirm_amt")
        private BigDecimal confirmAmt;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.confirm_share
        * <p>
        * Database Column Remarks: 确认份额
        * </p>
        */
        @Column(name = "confirm_share")
        private BigDecimal confirmShare;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.confirm_net_value
        * <p>
        * Database Column Remarks: 成交净值
        * </p>
        */
        @Column(name = "confirm_net_value")
        private BigDecimal confirmNetValue;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.confirm_net_value_date
        * <p>
        * Database Column Remarks: 确认净值日期
        * </p>
        */
        @Column(name = "confirm_net_value_date")
        private Date confirmNetValueDate;


        /**
        * Database Column Name: td_fund_monthly_order_tmp.fee_rate
        * <p>
        * Database Column Remarks: 下单时费率
        * </p>
        */
        @Column(name = "fee_rate", nullable = false)
        private BigDecimal feeRate;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.fee
        * <p>
        * Database Column Remarks: 下单时预估交易费用
        * </p>
        */
        @Column(name = "fee", nullable = false)
        private BigDecimal fee;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.final_fee
        * <p>
        * Database Column Remarks: 最终交易费用
        * </p>
        */
        @Column(name = "final_fee")
        private BigDecimal finalFee;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.trade_date
        * <p>
        * Database Column Remarks: 订单所属交易日
        * </p>
        */
        @Column(name = "trade_date", nullable = false)
        private Date tradeDate;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.confirm_date
        * <p>
        * Database Column Remarks: 申购/赎回确认日期
        * </p>
        */
        @Column(name = "confirm_date")
        private Date confirmDate;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.delivery_date
        * <p>
        * Database Column Remarks: 申购/赎回交收日期
        * </p>
        */
        @Column(name = "delivery_date")
        private Date deliveryDate;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.confirm_date_needed
        * <p>
        * Database Column Remarks: 从发起交易到确认需要几个交易日
        * </p>
        */
        @Column(name = "confirm_date_needed")
        private Integer confirmDateNeeded;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.delivery_date_needed
        * <p>
        * Database Column Remarks: 从发起交易到交收需要几个交易日
        * </p>
        */
        @Column(name = "delivery_date_needed")
        private Integer deliveryDateNeeded;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.real_trade_date
        * <p>
        * Database Column Remarks: 实际交易日
        * </p>
        */
        @Column(name = "real_trade_date")
        private Date realTradeDate;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.real_confirm_time
        * <p>
        * Database Column Remarks: 实际上手申购/赎回确认时间
        * </p>
        */
        @Column(name = "real_confirm_time")
        private Date realConfirmTime;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.real_delivery_time
        * <p>
        * Database Column Remarks: 实际交收时间
        * </p>
        */
        @Column(name = "real_delivery_time")
        private Date realDeliveryTime;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.allfund_pre_delivery_time
        * <p>
        * Database Column Remarks: AllFund预计交收时间
        * </p>
        */
        @Column(name = "allfund_pre_delivery_time")
        private Date allfundPreDeliveryTime;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.status
        * <p>
        * Database Column Remarks: 订单状态：0-初始化，1-待支付，2-已支付，3-支付失败 10-下单处理中，11-已提交，20-订单修改中，30-订单撤销中，31-已撤销，40-下单失败，41-上手确认失败，50-已确认，60-待交收，61-已交收
        * </p>
        */
        @Column(name = "status", escapedName = "`status`", nullable = false)
        private Byte status;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.broker_clear_status
        * <p>
        * Database Column Remarks: 自研柜台状态:NEW 下单成功 未到CUT_OFF日 可改单或取消,WA 下单成功 余额不足 等待处理 PRO 中间状态 已过CUT_OFF日 不能改单或取消 CAN 取消订单  ACK 已提交上手 REJ  已拒绝  FEX  已完成购买 SET 已交收
        * </p>
        */
        @Column(name = "broker_clear_status")
        private String brokerClearStatus;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.activity_flag
        * <p>
        * Database Column Remarks: 活动信息：第0位=1返现券， 第1位=1Coin抵扣
        * </p>
        */
        @Column(name = "activity_flag")
        private String activityFlag;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.apply_fail_reason
        * <p>
        * Database Column Remarks: 下单失败原因
        * </p>
        */
        @Column(name = "apply_fail_reason")
        private String applyFailReason;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.broker_order_status
        * <p>
        * Database Column Remarks: 上手订单状态
        * </p>
        */
        @Column(name = "broker_order_status")
        private String brokerOrderStatus;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.channel
        * <p>
        * Database Column Remarks: 订单下单类型 app:APP下单 manual:手工下单
        * </p>
        */
        @Column(name = "channel")
        private String channel;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.apply_time
        * <p>
        * Database Column Remarks: 申请时间
        * </p>
        */
        @Column(name = "apply_time", nullable = false)
        private Date applyTime;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.update_time
        * <p>
        * Database Column Remarks: 最近更新时间
        * </p>
        */
        @Column(name = "update_time", nullable = false)
        private Date updateTime;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.version
        * <p>
        * Database Column Remarks: 乐观锁版本号
        * </p>
        */
        @Column(name = "version", nullable = false)
        private Integer version;

        /**
        * Database Column Name: td_fund_monthly_order_tmp.remark
        * <p>
        * Database Column Remarks: 备注
        * </p>
        */
        @Column(name = "remark")
        private String remark;

}