package group.za.bank.statement.constants.enums;

import com.google.common.base.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 结单类型
 *
 * <AUTHOR>
 * @date 2022/04/29
 **/
public enum StatementBusinessModuleTypeEnum {
    /**
     * ttl提供月结单数据类型
     * "后期不会使用月结单数据了"
     */

    /**
     * Investment account position 投資帳戶持倉
     */
    FUND_HOLDING_MODULE(StatementSubBusinessTypeEnum.FUND, StatementModuleTypeEnum.HOLDING_MODULE, 3),
    SFUND_HOLDING_MODULE(StatementSubBusinessTypeEnum.SFUND, StatementModuleTypeEnum.HOLDING_MODULE, 3),
    STOCK_HOLDING_MODULE(StatementSubBusinessTypeEnum.STOCK, StatementModuleTypeEnum.HOLDING_MODULE, 1),
    HK_STOCK_HOLDING_MODULE(StatementSubBusinessTypeEnum.HK_STOCK, StatementModuleTypeEnum.HOLDING_MODULE, 2),
    CRYPTO_HOLDING_MODULE(StatementSubBusinessTypeEnum.CRYPTO, StatementModuleTypeEnum.HOLDING_MODULE, 4),
    /**
     * Confirmed Transaction 已確認交易
     */
    FUND_CONFIRMED_ORDER_MODULE(StatementSubBusinessTypeEnum.FUND, StatementModuleTypeEnum.CONFIRMED_ORDER_MODULE, 3),
    SFUND_CONFIRMED_ORDER_MODULE(StatementSubBusinessTypeEnum.SFUND, StatementModuleTypeEnum.CONFIRMED_ORDER_MODULE, 3),
    STOCK_CONFIRMED_ORDER_MODULE(StatementSubBusinessTypeEnum.STOCK, StatementModuleTypeEnum.CONFIRMED_ORDER_MODULE, 1),
    HK_STOCK_CONFIRMED_ORDER_MODULE(StatementSubBusinessTypeEnum.HK_STOCK, StatementModuleTypeEnum.CONFIRMED_ORDER_MODULE, 2),
    CRYPTO_CONFIRMED_ORDER_MODULE(StatementSubBusinessTypeEnum.CRYPTO, StatementModuleTypeEnum.CONFIRMED_ORDER_MODULE,4),
    /**
     * Fund Pending Transaction 基金處理中交易
     */
    FUND_PENDING_ORDER_MODULE(StatementSubBusinessTypeEnum.FUND, StatementModuleTypeEnum.PENDING_ORDER_MODULE, 3),
    SFUND_PENDING_ORDER_MODULE(StatementSubBusinessTypeEnum.SFUND, StatementModuleTypeEnum.PENDING_ORDER_MODULE, 3),

    /**
     * Dividend 派息
     */
    FUND_DIVIDEND_ORDER_MODULE(StatementSubBusinessTypeEnum.FUND, StatementModuleTypeEnum.DIVIDEND_ORDER_MODULE,3),
    SFUND_DIVIDEND_ORDER_MODULE(StatementSubBusinessTypeEnum.SFUND, StatementModuleTypeEnum.DIVIDEND_ORDER_MODULE, 3),
    STOCK_DIVIDEND_ORDER_MODULE(StatementSubBusinessTypeEnum.STOCK, StatementModuleTypeEnum.DIVIDEND_ORDER_MODULE, 1),
    HK_STOCK_DIVIDEND_ORDER_MODULE(StatementSubBusinessTypeEnum.HK_STOCK, StatementModuleTypeEnum.DIVIDEND_ORDER_MODULE, 2),

    /**
     * Deposit & Withdrawal 基金存入及提取
     * Holdings Movement 股票持倉變動
     */
    FUND_HOLDING_CHANGE_MODULE(StatementSubBusinessTypeEnum.FUND, StatementModuleTypeEnum.HOLDING_CHANGE_MODULE, 3),
    SFUND_HOLDING_CHANGE_MODULE(StatementSubBusinessTypeEnum.SFUND, StatementModuleTypeEnum.HOLDING_CHANGE_MODULE, 3),
    STOCK_HOLDING_CHANGE_MODULE(StatementSubBusinessTypeEnum.STOCK, StatementModuleTypeEnum.HOLDING_CHANGE_MODULE, 1),
    HK_STOCK_HOLDING_CHANGE_MODULE(StatementSubBusinessTypeEnum.HK_STOCK, StatementModuleTypeEnum.HOLDING_CHANGE_MODULE, 2),
    ;


    /**
     * 业务类型
     */
    private StatementModuleTypeEnum statementModuleTypeEnum;
    /**
     * 模块类型
     */
    private StatementSubBusinessTypeEnum statementSubBusinessTypeEnum;

    /**
     * 排序字段
     */
    private int sort;


    StatementBusinessModuleTypeEnum(StatementSubBusinessTypeEnum statementSubBusinessTypeEnum, StatementModuleTypeEnum statementModuleTypeEnum, int sort) {
        this.statementModuleTypeEnum = statementModuleTypeEnum;
        this.statementSubBusinessTypeEnum = statementSubBusinessTypeEnum;
        this.sort = sort;
    }


    public StatementModuleTypeEnum getStatementModuleTypeEnum() {
        return statementModuleTypeEnum;
    }

    public StatementSubBusinessTypeEnum getStatementSubBusinessTypeEnum() {
        return statementSubBusinessTypeEnum;
    }

    public int getSort() {
        return sort;
    }

    /**
     * 獲取數據的類型
     * @param subBusinessType
     * @param moduleType
     * @return
     */
    public static List<StatementBusinessModuleTypeEnum> getBySubBusinessTypeAndModuleType(String subBusinessType, String moduleType) {
        if (Strings.isNullOrEmpty(subBusinessType) || Strings.isNullOrEmpty(moduleType)) {
            return Arrays.asList(StatementBusinessModuleTypeEnum.values());
        }
        List<StatementBusinessModuleTypeEnum> statementBusinessModuleTypeEnumList = new ArrayList<>();
        for (StatementBusinessModuleTypeEnum statementBusinessModuleTypeEnum : StatementBusinessModuleTypeEnum.values()) {
            StatementModuleTypeEnum statementModuleTypeEnum = statementBusinessModuleTypeEnum.getStatementModuleTypeEnum();
            StatementSubBusinessTypeEnum statementSubBusinessTypeEnum = statementBusinessModuleTypeEnum.getStatementSubBusinessTypeEnum();
            if (statementModuleTypeEnum.getModuleType().equals(moduleType)
                    && statementSubBusinessTypeEnum.getType().equals(subBusinessType)) {
                statementBusinessModuleTypeEnumList.add(statementBusinessModuleTypeEnum);
            }
        }

        return statementBusinessModuleTypeEnumList;
    }


}
