package group.za.bank.statement.service.impl;

import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 港股
 * <AUTHOR>
 * @date 2025/01/20
 **/
@Slf4j
@Service("stockHkStatementBusinessServiceImpl")
public class StockHkStatementBusinessServiceImpl extends StockStatementBusinessServiceImpl {
    @Override
    public String getBusinessType() {
        return AccountTypeEnum.HK_STOCK.getValue();
    }

}
