package group.za.bank.statement.manager;

import group.za.bank.fund.domain.clear.mapper.ClearDateRecordMapper;
import group.za.bank.fund.domain.trade.entity.TdFundHoldingHistory;
import group.za.bank.fund.domain.trade.mapper.TdFundHoldingHistoryMapper;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.statement.service.ExchangeRateInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/5 15:38
 * @Version 1.0
 **/

@Slf4j
@Component
public class ActivityStatementManager {

    @Resource
    private ClearDateRecordMapper clearDateRecordMapper;
    @Resource
    private ExchangeRateInfoService exchangeRateInfoService;
    @Resource
    private TdFundHoldingHistoryMapper tdFundHoldingHistoryMapper;

    private static final String MONTHLY_STATEMENT_PERIOD_FORMAT = "yyyyMM";

    /**
     * 获取基金等值HKD期末持仓盈亏
     */
    public BigDecimal getEqualHkdMonthHoldingIncomeValue(String bankUserId, String bankAccountId, String lastMonth, String businessType) {
        // 等值HKD期末持仓盈亏
        BigDecimal equalHkdMonthHoldingIncomeValue = BigDecimal.ZERO;

        //查询上个月
        Date monthLatestTradeDate = getLastMonthDate(lastMonth);
        List<TdFundHoldingHistory> lastMonthHoldingHistoryList = tdFundHoldingHistoryMapper.queryAccountHoldingHistoryList(bankUserId, bankAccountId, monthLatestTradeDate);

        if (!CollectionUtils.isEmpty(lastMonthHoldingHistoryList)) {
            Map<String, BigDecimal> monthHoldingIncomeValueMap = new HashMap<>();
            // 根据币种收益累加，持有收益 = 平均成本算法持有收益 + 当前建仓以来累计现金分红
            for (TdFundHoldingHistory holdingHistory : lastMonthHoldingHistoryList) {
                monthHoldingIncomeValueMap.merge(holdingHistory.getCurrency(), holdingHistory.getHoldIncome().add(holdingHistory.getTotalDivAmt()), BigDecimal::add);
            }
            // 换汇
            equalHkdMonthHoldingIncomeValue = monthActivityCcyTotal(monthHoldingIncomeValueMap, CurrencyEnum.HKD.getCurrency(), lastMonth, businessType);
        }

        return equalHkdMonthHoldingIncomeValue.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private BigDecimal monthActivityCcyTotal(Map<String, BigDecimal> ccyTotalMap, String currency, String period, String businessType) {
        BigDecimal ccyTotalValue = BigDecimal.ZERO;
        if (ccyTotalMap.isEmpty()) {
            return ccyTotalValue;
        }
        for (String ccy : ccyTotalMap.keySet()) {
            if (currency.equals(ccy)) {
                ccyTotalValue = ccyTotalValue.add(ccyTotalMap.get(ccy));
            } else {
                // 换汇为港币
                BigDecimal exchangeHkdRate = exchangeRateInfoService.getExchangeHkdRate(ccy, period, businessType);
                // 持有市值 = 持有市值 + （ccy币种的金额*汇率）
                ccyTotalValue = ccyTotalValue.add(ccyTotalMap.get(ccy).multiply(exchangeHkdRate));
            }
        }
        return ccyTotalValue;
    }

    private Date getLastMonthDate(String lastMonth) {
        Date lastMonthDate = DateUtil.parse(lastMonth, MONTHLY_STATEMENT_PERIOD_FORMAT);
        Date monthStartDate = DateUtil.getFirstDayOfMonth(lastMonthDate);
        Date monthEndDate = DateUtil.getLastDayOfMonth(lastMonthDate);
        Date monthLatestTradeDate = clearDateRecordMapper.selectLastClearDate(monthStartDate, monthEndDate);
        return monthLatestTradeDate;
    }
}
