CREATE TABLE IF NOT EXISTS `td_statement_file_record` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`statement_type` int(4) DEFAULT '1' COMMENT '1-月结单，2-日结单',
	`statement_date` varchar(12) DEFAULT NULL COMMENT '结单日期:月结单-yyyyMM，日结单-yyyyMMdd',
	`data_status` int(4) DEFAULT '0' COMMENT '文件解析状态:0-未开始,1-处理中,2-成功，3-失败',
	`creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
	`gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
	`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	`is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除'
) ENGINE = InnoDB;


CREATE TABLE IF NOT EXISTS  `td_stock_statement_data` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`business_id` varchar(64) DEFAULT NULL COMMENT '业务逻辑id',
	`statement_date` VARCHAR(12) DEFAULT NULL COMMENT '结单日期:月结单-yyyyMM，日结单-yyyyMMdd',
	`statement_type` int(4) NOT NULL DEFAULT '1' COMMENT '结单类型:1-日结单，2-月结单',
	`acc_no` varchar(20) DEFAULT NULL COMMENT 'ttl柜台账户',
	`format_type` varchar(20) DEFAULT NULL COMMENT '1-港股成交单,2-美股成交单，3-交易变动明细，4证券资产摘要，5-现金摘要',
	`param1` varchar(50) DEFAULT NULL,
	`param2` varchar(50) DEFAULT NULL,
	`param3` varchar(100) DEFAULT NULL,
	`param4` varchar(50) DEFAULT NULL,
	`param5` varchar(50) DEFAULT NULL,
	`param6` varchar(50) DEFAULT NULL,
	`param7` varchar(500) DEFAULT NULL,
	`param8` varchar(50) DEFAULT NULL,
	`param9` varchar(50) DEFAULT NULL,
	`param10` varchar(200) DEFAULT NULL,
	`param11` varchar(50) DEFAULT NULL,
	`param12` varchar(200) DEFAULT NULL,
	`param13` varchar(50) DEFAULT NULL,
	`param14` varchar(200) DEFAULT NULL,
	`param15` varchar(50) DEFAULT NULL,
	`param16` varchar(200) DEFAULT NULL,
	`param17` varchar(50) DEFAULT NULL,
	`param18` varchar(200) DEFAULT NULL,
	`param19` varchar(50) DEFAULT NULL,
	`param20` varchar(200) DEFAULT NULL,
	`record_time` datetime DEFAULT NULL COMMENT '结单记录生成时间',
	`remark` varchar(100) DEFAULT '' COMMENT '备注',
	`creator` varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
	`gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`modifier` varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
	`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	`is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
	INDEX idx_stdata_bussinessid1(business_id),
	INDEX idx_stdata_date_type2(statement_date, statement_type, format_type)
) ENGINE = InnoDB;



