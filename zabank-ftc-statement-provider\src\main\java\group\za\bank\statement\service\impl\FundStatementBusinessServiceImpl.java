package group.za.bank.statement.service.impl;

import cn.hutool.core.lang.Pair;
import group.za.bank.fund.domain.clear.mapper.ClearDateRecordMapper;
import group.za.bank.fund.domain.trade.entity.TdFundHoldingHistory;
import group.za.bank.fund.domain.trade.entity.TdFundOrder;
import group.za.bank.fund.domain.trade.entity.dto.StatementUserAccountDto;
import group.za.bank.fund.domain.trade.mapper.*;
import group.za.bank.fund.trade.constants.enums.FundBusinessTypeDescEnum;
import group.za.bank.fund.trade.constants.enums.FundBusinessTypeEnum;
import group.za.bank.fund.trade.constants.enums.FundOrderStatusEnum;
import group.za.bank.fund.trade.constants.enums.MonthStatementModuleTypeEnum;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.BankAddress;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.constants.enums.EnumYesOrNo;
import group.za.bank.invest.common.constants.enums.TransactionEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.common.utils.TransactionUtils;
import group.za.bank.market.entity.resp.FundInfoResp;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.domain.entity.*;
import group.za.bank.statement.domain.mapper.TdFundMonthlyOrderTmpMapper;
import group.za.bank.statement.entity.dto.FundMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.HistoryHoldingPairDto;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.manager.ActivityStatementManager;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.remote.MarketRemoteService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.bank.statement.utils.OrderBeanUtils;
import group.za.invest.web.json.JSON;
import group.za.invest.web.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static group.za.bank.statement.utils.StatementNumberFormatUtils.statementMoneyFormat;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.statementShareFormat;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("fundStatementBusinessService")
public class FundStatementBusinessServiceImpl implements StatementBusinessService<FundMonthlyStatementDataDto> {

    /**
     * 月结单期数格式
     */
    private static final String MONTHLY_STATEMENT_PERIOD_FORMAT = "yyyyMM";
    /**
     * 失败的各种语言
     */
    private static final String ORDER_FAIL_DESC_ZH = "失败";
    private static final String ORDER_FAIL_DESC_HK = "失敗";
    private static final String ORDER_FAIL_DESC_EN = "FAILED";
    private static final String DEFAULT_NO_PRICE = "N/A";

    private final Map<String, Boolean> periodAndPreparedStatusMap = new ConcurrentHashMap<>();

    @Autowired
    private SystemConfig systemConfig;
    private final Map<String, FundInfoResp> fundInfoLocalCacheMap = new ConcurrentHashMap<>();

    @Autowired
    private ClearDateRecordMapper clearDateRecordMapper;
    @Autowired
    private TdFundOrderMapper tdFundOrderMapper;
    @Autowired
    private TdFundHoldingHistoryMapper tdFundHoldingHistoryMapper;
    @Autowired
    private MarketRemoteService marketRemoteService;
    @Resource
    private TdFundMonthlyOrderTmpMapper tdFundMonthlyOrderTmpMapper;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private TdMonthlyStatementOverviewMapper tdMonthlyStatementOverviewMapper;
    @Resource
    private TdMonthlyStatementHoldingSnapshotMapper tdMonthlyStatementHoldingSnapshotMapper;
    @Resource
    private ActivityStatementManager activityStatementManager;
    @Resource
    private TdMonthlyStatementOrderSnapshotMapper tdMonthlyStatementOrderSnapshotMapper;
    @Resource
    private ExchangeRateInfoService exchangeRateInfoService;
    @Resource
    private MonthlyStatementManager monthlyStatementManager;

    /**
     * 本期数据是否已经准备完毕
     * 本月最后一天跟本月最后一个日切一致
     */
    @Override
    public boolean isDataPrepared(TdFundMonthlyStatementInfo monthlyStatementInfo, boolean useCache) {
//        String key = monthlyStatementInfo.getPeriod() + monthlyStatementInfo.getBusinessType();
//        if (useCache) {
//            Boolean preparedStatus = periodAndPreparedStatusMap.get(key);
//            if (preparedStatus != null && preparedStatus) {
//                log.info("fundStatementBusinessService.isDataPrepared, 基金相关数据已准备完成, preparedStatus:{}, monthlyStatementInfo:{}", preparedStatus, JSON.toJSONString(monthlyStatementInfo));
//                return preparedStatus;
//            }
//        }

        Date monthDate = DateUtil.parse(monthlyStatementInfo.getPeriod(), systemConfig.getMonthlyStatementPeriodFormat());
        //最后一个交易日
        Date monthLastTradeDate = marketRemoteService.getMonthLatestTradeDate(monthDate);

        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(monthDate));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(monthDate));

        // 判断清算是否完成
        boolean clearCompletion = isClearCompletion(monthLastTradeDate);
        if (!clearCompletion) {
            log.error("fundStatementBusinessService.isDataPrepared 基金清算未完成,请人工查看 monthLastTradeDate:{}", monthLastTradeDate);
            return false;
        }

        // 查询period最后一个清算完成的交易日
        Date monthLastClearDate = clearDateRecordMapper.queryFinalClearDate(monthStartDate, monthEndDate);

        log.info("查询基金交易日和清算日! period:{},tradeDate:{},clearDate:{}"
                , monthlyStatementInfo.getPeriod(), monthLastTradeDate, monthLastClearDate);
        String monthLastTradeDateString = DateUtil.format(monthLastTradeDate, DateUtil.FORMATDAY);
        String monthLastClearDateString = DateUtil.format(monthLastClearDate, DateUtil.FORMATDAY);

        boolean result = monthLastTradeDateString.equals(monthLastClearDateString);
        if (result) {
            // 备份基金订单数据到临时表
            this.backupFundMonthlyOrderTmp(monthStartDate, monthEndDate);
        } else {
            log.error("fundStatementBusinessService.isDataPrepared, 基金数据还未准备完成, 请人工查看, monthLastTradeDate:{}, monthLastClearDate:{}", monthLastTradeDateString, monthLastClearDateString);
        }
//        periodAndPreparedStatusMap.put(key, result);
        return result;
    }

    /**
     * 是否清算完成
     */
    private boolean isClearCompletion(Date clearDate) {
        // 查询清算完成数量
        Integer completionCount = clearDateRecordMapper.selectCompletionClearCount(clearDate);
        if (completionCount == 0) {
            return false;
        }

        // 查询清算记录是否完成
        int count = clearDateRecordMapper.selectUnCompletionClearCount(clearDate);
        return count == 0;
    }

    /**
     * 备份订单临时表数据
     */
    private void backupFundMonthlyOrderTmp(Date monthStartDate, Date monthEndDate) {
        // 是否已存在备份订单
        boolean existsOrder = tdFundMonthlyOrderTmpMapper.existsOrder();
        if (!existsOrder) {
            TransactionUtils.execute(TransactionEnum.STATEMENT.getName(), () -> {
                //备份分红临时订单
                backupDividendsOrder(monthStartDate, monthEndDate);

                //备份申购赎回、调仓临时订单（状态已确认：41、50、60、61）
                backupConfirmedOrder(monthStartDate, monthEndDate);

                //备份申购赎回、调仓临时订单（状态交易中：11、20、30）
                backupPendingOrder(monthStartDate, monthEndDate);
            });
        }
    }

    /**
     * 备份分红订单
     */
    private void backupDividendsOrder(Date monthStartDate, Date monthEndDate) {
        long id = 0L;
        List<TdFundOrder> tdFundOrderList;
        AtomicInteger totalOrderCounter = new AtomicInteger(0);
        while (true) {
            tdFundOrderList = tdFundOrderMapper.queryMonthlyStatementDividendsList(monthStartDate, monthEndDate, systemConfig.getPageSize(), id);

            if (CollectionUtils.isEmpty(tdFundOrderList)) {
                log.info("备份基金分红临时订单查询无数据, pageSize={}，id={}", systemConfig.getPageSize(), id);
                break;
            }

            List<TdFundMonthlyOrderTmp> tdFundMonthlyOrderTmpList = new ArrayList<>();
            tdFundOrderList.forEach(fundOrder -> {
                //复制对象
                TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp = OrderBeanUtils.copyTdFundMonthlyOrderTmp(fundOrder);
                tdFundMonthlyOrderTmpList.add(tdFundMonthlyOrderTmp);
                totalOrderCounter.incrementAndGet();
            });

            id = tdFundOrderList.get(tdFundOrderList.size() - 1).getId();

            tdFundMonthlyOrderTmpMapper.insertBatch(tdFundMonthlyOrderTmpList);
        }

        log.info("备份基金分红临时订单,总条数={}", totalOrderCounter.get());

    }

    /**
     * 备份已确认订单
     */
    private void backupConfirmedOrder(Date monthStartDate, Date monthEndDate) {
        long id = 0L;
        List<TdFundOrder> tdFundOrderList;
        AtomicInteger totalOrderCounter = new AtomicInteger(0);
        while (true) {
            tdFundOrderList = tdFundOrderMapper.queryConfirmedMonthlyStatementList(monthStartDate, monthEndDate, systemConfig.getPageSize(), id);

            if (CollectionUtils.isEmpty(tdFundOrderList)) {
                log.info("备份基金申购赎回、调仓临时订单（状态已确认：41、50、60、61)查询无数据pageSize={}，id={}", systemConfig.getPageSize(), id);
                break;
            }

            List<TdFundMonthlyOrderTmp> tdFundMonthlyOrderTmpList = new ArrayList<>();
            tdFundOrderList.forEach(fundOrder -> {
                //复制对象
                TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp = OrderBeanUtils.copyTdFundMonthlyOrderTmp(fundOrder);
                tdFundMonthlyOrderTmpList.add(tdFundMonthlyOrderTmp);
                totalOrderCounter.incrementAndGet();
            });

            id = tdFundOrderList.get(tdFundOrderList.size() - 1).getId();

            tdFundMonthlyOrderTmpMapper.insertBatch(tdFundMonthlyOrderTmpList);

        }

        log.info("备份基金申购赎回、调仓临时订单（状态已确认：41、50、60、61）结束,总条数={}", totalOrderCounter.get());

    }

    /**
     * 备份在途订单
     */
    protected void backupPendingOrder(Date monthStartDate, Date monthEndDate) {
        long id = 0L;
        List<TdFundOrder> tdFundOrderList;
        AtomicInteger totalOrderCounter = new AtomicInteger(0);
        while (true) {
            tdFundOrderList = tdFundOrderMapper.queryPendingMonthlyStatementList(monthStartDate, monthEndDate, systemConfig.getPageSize(), id);

            if (CollectionUtils.isEmpty(tdFundOrderList)) {
                log.info("备份基金申购赎回、调仓临时订单（状态交易中：11、20、30）查询无数据, pageSize={}，id={}", systemConfig.getPageSize(), id);
                break;
            }

            List<TdFundMonthlyOrderTmp> tdFundMonthlyOrderTmpList = new ArrayList<>();
            tdFundOrderList.forEach(fundOrder -> {
                //复制对象
                TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp = OrderBeanUtils.copyTdFundMonthlyOrderTmp(fundOrder);
                tdFundMonthlyOrderTmpList.add(tdFundMonthlyOrderTmp);
                totalOrderCounter.incrementAndGet();
            });

            id = tdFundOrderList.get(tdFundOrderList.size() - 1).getId();

            tdFundMonthlyOrderTmpMapper.insertBatch(tdFundMonthlyOrderTmpList);

        }

        log.info("备份基金申购赎回、调仓临时订单（状态交易中：11、20、30）结束,总条数={}", totalOrderCounter.get());
    }

    /**
     * 用户期末总市值及总盈亏
     * @return
     */
    @Override
    public Pair<BigDecimal, BigDecimal> getUserPeriodEndTotalMarketAndProfit(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement statementRecord, CurrencyEnum targetCurrency, String accountId, String marketCode) {
        try {
            // 基金HKD总市值
            BigDecimal equalHkdTotalMarket = BigDecimal.ZERO;
            BigDecimal equalHkdTotalHoldingProfit = BigDecimal.ZERO;
            // 从mongo中获取结单数据
            List<TdMonthlyStatementBusinessData> statementBusinessDataList = monthlyStatementManager.listUserStatementBusinessData(statementRecord.getBusinessId());

            if (CollectionUtils.isNotEmpty(statementBusinessDataList)) {
                for (TdMonthlyStatementBusinessData tdMonthlyStatementBusinessData : statementBusinessDataList) {
                    if (tdMonthlyStatementBusinessData.getBusinessType().equals(this.getBusinessType())) {
                        FundMonthlyStatementDataDto dataDto = JSONObject.parse(JSON.toJSONString(tdMonthlyStatementBusinessData.getData()), FundMonthlyStatementDataDto.class);
                        //总市值和总盈亏
                        if(null == dataDto.getBasicInfo().getTotalMarketValue()
                                || null == dataDto.getBasicInfo().getTotalHoldingProfit()){
                            log.error("getUserPeriodEndTotalMarketAndProfit异常，总市值或总盈亏数据不完整， dataDto:{}", JSON.toJSONString(dataDto));
                            throw new BusinessException(StatementErrorMsgEnum.USER_STATEMENT_DATA_NOT_COMPLETE);
                        }
                        equalHkdTotalMarket = dataDto.getBasicInfo().getTotalMarketValue();
                        equalHkdTotalHoldingProfit = dataDto.getBasicInfo().getTotalHoldingProfit();
                        break;
                    }
                }
            }
            return Pair.of(equalHkdTotalMarket, equalHkdTotalHoldingProfit);
        } catch (Exception e) {
            log.error("getUserPeriodEndTotalMarket error, statementRecord:{}", JSON.toJSONString(statementRecord), e);
            throw e;
        }
    }

    /**
     * 判断用户是否需要生成结单
     */
    @Override
    public boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , UserInvestClientInfoDto userInvestClientInfoDto, String marketCode) {

        String bankUserId = userInvestClientInfoDto.getBankUserId();
        String bankAccountId = userInvestClientInfoDto.getBankAccountId();

        return isUserNeedGenerateStatement(tdFundMonthlyStatementInfo, bankUserId, bankAccountId, marketCode);
    }


    /**
     * 判断当前用户当月是否需要生成月结单
     * 在按月會計期的任何時間，該客戶的帳戶不是零結餘，或有任何交易，就要發月結單
     */
    @Override
    public boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, String bankUserId, String bankAccountId, String marketCode) {
        String period = tdFundMonthlyStatementInfo.getPeriod();
        Date periodDate = DateUtil.parse(period, systemConfig.getMonthlyStatementPeriodFormat());
        Date periodStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(periodDate));
        Date periodEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(periodDate));


        List<StatementUserAccountDto> hasHoldingFundAccountList
                = tdFundHoldingHistoryMapper.queryMonthlyStatementFundAccountList(periodStartDate, periodEndDate
                , bankUserId, bankAccountId);

        int statementFundAccount = tdFundMonthlyOrderTmpMapper.countMonthlyStatementFundAccount(bankUserId, bankAccountId);

        //没有数据的用户
        if (CollectionUtils.isEmpty(hasHoldingFundAccountList) && statementFundAccount == 0) {
            return false;
        }

        return true;
    }

    /**
     * 查区间最后的交易日 返回的日期时分秒都置为0
     */
    private Date queryMonthLatestTradeDate(Date monthStartDate, Date monthEndDate) {
        return clearDateRecordMapper.selectLastClearDate(monthStartDate, monthEndDate);
    }


    /**
     * 抽取业务结单数据
     *
     * @param tdFundMonthlyStatementInfo
     * @param statementData
     * @return
     */
    @Override
    public Map<String, Object> generateDataMap(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement docUnGeneratedStatement, TdMonthlyStatementData statementData, String marketCode) {
        // 银行账户id
        String bankAccountId = statementData.getAccountId();
        FundMonthlyStatementDataDto fundMonthlyStatementDataDto = new FundMonthlyStatementDataDto();
        /**
         * 基本信息——baseInfo
         */
        FundMonthlyStatementDataDto.BasicInfoDto basicInfoDto = queryBaseInfo(docUnGeneratedStatement, bankAccountId, tdFundMonthlyStatementInfo.getBusinessType());
        fundMonthlyStatementDataDto.setBasicInfo(basicInfoDto);
        /**
         * 基金持仓——holdingList
         */
        List<FundMonthlyStatementDataDto.HoldingDto> holdingDtoList
                = queryHoldingList(docUnGeneratedStatement, fundInfoLocalCacheMap, bankAccountId, basicInfoDto);
        fundMonthlyStatementDataDto.setHoldingList(holdingDtoList);
        /**
         * 已确认交易——confirmedOrderList
         */
        List<FundMonthlyStatementDataDto.ConfirmedOrderDto> confirmedOrderList
                = queryConfirmedOrderDtoList(docUnGeneratedStatement, fundInfoLocalCacheMap, bankAccountId);
        fundMonthlyStatementDataDto.setConfirmedOrderList(confirmedOrderList);
        /**
         * 处理中交易——pendingOrderList
         */
        List<FundMonthlyStatementDataDto.PendingOrderDto> pendingOrderList
                = queryPendingOrderDtoList(docUnGeneratedStatement, fundInfoLocalCacheMap, bankAccountId);
        fundMonthlyStatementDataDto.setPendingOrderList(pendingOrderList);
        /**
         * 派息——dividendOrderList
         */
        List<FundMonthlyStatementDataDto.DividendOrderDto> dividendOrderList
                = queryDividendOrderDtoList(docUnGeneratedStatement, fundInfoLocalCacheMap, bankAccountId);
        fundMonthlyStatementDataDto.setDividendOrderList(dividendOrderList);
        /**
         * 存入及提取——depositAndWithdrawalList
         */
        List<FundMonthlyStatementDataDto.DepositAndWithdrawalDto> depositAndWithdrawalList
                = queryDepositAndWithdrawalDtoList(docUnGeneratedStatement, fundInfoLocalCacheMap, bankAccountId);
        fundMonthlyStatementDataDto.setDepositAndWithdrawalList(depositAndWithdrawalList);

        if (CollectionUtils.isEmpty(holdingDtoList)
                && CollectionUtils.isEmpty(confirmedOrderList)
                && CollectionUtils.isEmpty(pendingOrderList)
                && CollectionUtils.isEmpty(dividendOrderList)
                && CollectionUtils.isEmpty(depositAndWithdrawalList)) {
            throw new RuntimeException("该用户没有基金结单数据, 人工排查 docUnGeneratedStatement: " + JSON.toJSONString(docUnGeneratedStatement));
        }

        Map<String, Object> resultMap = JSONObject.parseObject(JsonUtils.toFormatJsonString(fundMonthlyStatementDataDto));
        if (log.isDebugEnabled()) {
            log.debug("开始抽取用户基金结单数据! bankAccountId:{},accountId:{},resultMap:{}"
                    , docUnGeneratedStatement.getBankUserId(), statementData.getAccountId(), resultMap);
        }

        return resultMap;
    }

    /**
     * 查询基本信息
     *
     * @return
     */
    private FundMonthlyStatementDataDto.BasicInfoDto queryBaseInfo(TdFundMonthlyStatement fundMonthlyStatement, String bankAccountId, String businessType) {
        FundMonthlyStatementDataDto.BasicInfoDto basicInfoDto = new FundMonthlyStatementDataDto.BasicInfoDto();
        String bankUserId = fundMonthlyStatement.getBankUserId();
        String period = fundMonthlyStatement.getPeriod();
        Date periodDate = DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT);

        Date monthStartDate = DateUtil.getFirstDayOfMonth(periodDate);
        Date monthEndDate = DateUtil.getLastDayOfMonth(periodDate);

        basicInfoDto.setStatementStartDate(DateUtil.format(monthStartDate, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
        basicInfoDto.setStatementEndDate(DateUtil.format(monthEndDate, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
        basicInfoDto.setAccountNo(fundMonthlyStatement.getClientId());

        // 总市值默认值为0，如果期末有持仓数据，则在queryHoldingList方法内设置等值HKD总市值
        basicInfoDto.setTotalMarket(statementMoneyFormat(BigDecimal.ZERO));
        basicInfoDto.setTotalMarketValue(BigDecimal.ZERO);
        basicInfoDto.setCurrency(CurrencyEnum.HKD.getCurrency());

        basicInfoDto.setStatementDate(DateUtil.format(fundMonthlyStatement.getRecordTime()
                , StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));

        AddressInfoResp addressInfoResp = userRemoteService.queryContactAddressInfo(bankUserId);

        boolean isUserMcv = userRemoteService.isUserMcv(bankUserId);


        String name = addressInfoResp.getEngName() + " " + addressInfoResp.getZhName();
        basicInfoDto.setCustomerName(name);


        conversionAddress(basicInfoDto, addressInfoResp, isUserMcv);

        // 等值期末持仓盈亏
        BigDecimal equalMonthHoldingIncomeValue = activityStatementManager.getEqualHkdMonthHoldingIncomeValue(fundMonthlyStatement.getBankUserId(), bankAccountId, fundMonthlyStatement.getPeriod(), fundMonthlyStatement.getBusinessType());
        basicInfoDto.setTotalHoldingProfit(equalMonthHoldingIncomeValue);
        return basicInfoDto;
    }

    /**
     * 查询持仓信息
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.HoldingDto> queryHoldingList(TdFundMonthlyStatement fundMonthlyStatement
            , Map<String, FundInfoResp> fundInfoLocalCacheMap, String bankAccountId, FundMonthlyStatementDataDto.BasicInfoDto basicInfoDto) {
        String period = fundMonthlyStatement.getPeriod();
        String docLang = fundMonthlyStatement.getDocLang();


        Date periodDate = DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT);
        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(periodDate));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(periodDate));

        Date lastPeriodDate = DateUtil.getLastDayOfLastMonth(periodDate);
        Date lastMonthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(lastPeriodDate));
        Date lastMonthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(lastPeriodDate));

        //本月的最后一个交易日 1231
        Date monthLatestTradeDate = queryMonthLatestTradeDate(monthStartDate, monthEndDate);
        //上个月的最后一个交易日 1130
        Date lastMonthLatestTradeDate = queryMonthLatestTradeDate(lastMonthStartDate, lastMonthEndDate);


        //期初
        List<TdFundHoldingHistory> lastMonthLatestFundHoldingList
                = tdFundHoldingHistoryMapper.queryFundAccountMonthlyLatestHoldingList(bankAccountId, lastMonthLatestTradeDate);

        //期末
        List<TdFundHoldingHistory> currentMonthLatestFundHoldingList
                = tdFundHoldingHistoryMapper.queryFundAccountMonthlyLatestHoldingList(bankAccountId, monthLatestTradeDate);

        //期初期末都没有数据直接返回
        if (CollectionUtils.isEmpty(currentMonthLatestFundHoldingList)
                && CollectionUtils.isEmpty(lastMonthLatestFundHoldingList)) {
            return new ArrayList<>();
        }

        Map<String, HistoryHoldingPairDto> productIdAndOpenCloseHoldingPairMap = new LinkedHashMap<>();
        //填充期初的
        lastMonthLatestFundHoldingList.forEach(tdFundHoldingHistory -> {
            String productKey = tdFundHoldingHistory.getProductId();
            HistoryHoldingPairDto historyHoldingPairDto = new HistoryHoldingPairDto(tdFundHoldingHistory, null);
            productIdAndOpenCloseHoldingPairMap.put(productKey, historyHoldingPairDto);
        });
        //填充期末的
        currentMonthLatestFundHoldingList.forEach(tdFundHoldingHistory -> {
            String productKey = tdFundHoldingHistory.getProductId();
            HistoryHoldingPairDto historyHoldingPairDto = productIdAndOpenCloseHoldingPairMap.get(productKey);
            if (historyHoldingPairDto == null) {
                historyHoldingPairDto = new HistoryHoldingPairDto(null, tdFundHoldingHistory);
                productIdAndOpenCloseHoldingPairMap.put(productKey, historyHoldingPairDto);
            } else {
                historyHoldingPairDto.setSecondDto(tdFundHoldingHistory);
            }

        });

        //基金信息
        List<String> productIdList = productIdAndOpenCloseHoldingPairMap.keySet().stream()
                .collect(Collectors.toList());
        Map<String, FundInfoResp> productIdAndFundInfoRespMap = appendFundInfoToLocalCache(productIdList, fundInfoLocalCacheMap);

        List<FundMonthlyStatementDataDto.HoldingDto> holdingDtoList = new ArrayList<>();
        // 等值hkd总市值
        BigDecimal equalHkdTotalMarketValue = BigDecimal.ZERO;
        for (Map.Entry<String, HistoryHoldingPairDto> entry : productIdAndOpenCloseHoldingPairMap.entrySet()) {
            FundMonthlyStatementDataDto.HoldingDto holdingDto = new FundMonthlyStatementDataDto.HoldingDto();
            String productId = entry.getKey();


            FundInfoResp fundInfoResp = productIdAndFundInfoRespMap.get(productId);
            if (fundInfoResp == null) {
                throw new RuntimeException("queryHoldingList, 基金月结单查找不到基金基本信息, productId：" + productId);
            }
            // 份额精度
            Integer qtyDecimal = fundInfoResp.getQtyDecimal();
            // 净值精度
            Integer navDecimal = fundInfoResp.getNavDecimal();

            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                holdingDto.setFundName(fundInfoResp.getCnFundName());
            }

            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                holdingDto.setFundName(fundInfoResp.getHkFundName());
            }

            holdingDto.setFundEngName(fundInfoResp.getEnFundName());
            //快照表会使用入库
            holdingDto.setFundHkName(fundInfoResp.getHkFundName());
            holdingDto.setFundZhName(fundInfoResp.getCnFundName());
            holdingDto.setProductId(fundInfoResp.getProductId());
            holdingDto.setIsinNo(fundInfoResp.getIsinNo());

            HistoryHoldingPairDto historyHoldingPairDto = entry.getValue();
            TdFundHoldingHistory openHoldingHistory = historyHoldingPairDto.getFirstDto();
            TdFundHoldingHistory closeHoldingHistory = historyHoldingPairDto.getSecondDto();

            BigDecimal marketValue = BigDecimal.ZERO;
            BigDecimal referencePrice = null;

            BigDecimal openingBalance = null;
            if (openHoldingHistory != null) {
                openingBalance = openHoldingHistory.getHoldQty();
                holdingDto.setCurrency(openHoldingHistory.getCurrency());
            } else {
                openingBalance = BigDecimal.ZERO;
            }
            holdingDto.setOpeningBalance(statementShareFormat(openingBalance, qtyDecimal));


            BigDecimal closingBalance = null;
            if (closeHoldingHistory != null) {
                closingBalance = closeHoldingHistory.getHoldQty();
                holdingDto.setCurrency(closeHoldingHistory.getCurrency());
                marketValue = closeHoldingHistory.getMarketValue();
                referencePrice = closeHoldingHistory.getNetValue();
            } else {
                closingBalance = BigDecimal.ZERO;
            }

            holdingDto.setClosingBalance(statementShareFormat(closingBalance, qtyDecimal));
            holdingDto.setMarketValue(statementMoneyFormat(marketValue));
            // 换汇为港币
            BigDecimal exchangeHkdRate = exchangeRateInfoService.getExchangeHkdRate(holdingDto.getCurrency(), period, fundMonthlyStatement.getBusinessType());
            // 港币市值四舍五入保留两位
            BigDecimal equalHkdMarketValue2Scale = marketValue.multiply(exchangeHkdRate).setScale(2, RoundingMode.HALF_UP);
            // 等值hkd总市值 = 等值hkd总市值 + 港币市值
            equalHkdTotalMarketValue = equalHkdTotalMarketValue.add(equalHkdMarketValue2Scale);
            // 等值港币市值
            String equalHkdMarketValue;
            if (CurrencyEnum.HKD.getCurrency().equals(holdingDto.getCurrency())) {
                // 港币基金的【等值港币市值】需要展示与【市值】相同的值
                equalHkdMarketValue = holdingDto.getMarketValue();
            } else if (BigDecimal.ZERO.compareTo(marketValue) == 0) {
                equalHkdMarketValue = holdingDto.getMarketValue();
            } else {
                // 等值港币市值 = 港币市值四舍五入保留两位且千分位格式化
                equalHkdMarketValue = statementMoneyFormat(equalHkdMarketValue2Scale);
            }
            holdingDto.setEqualHkdMarketValue(equalHkdMarketValue);
            holdingDto.setReferencePrice(referencePrice == null ? DEFAULT_NO_PRICE : statementShareFormat(referencePrice, navDecimal));


            boolean isClosingZero = false;
            boolean isOpeningZero = false;
            if (closingBalance == null || closingBalance.compareTo(BigDecimal.ZERO) == 0) {
                isClosingZero = true;
            }
            if (openingBalance == null || openingBalance.compareTo(BigDecimal.ZERO) == 0) {
                isOpeningZero = true;
            }

            if (isClosingZero && isOpeningZero) {
                log.info("期初、期末结余均为0,月结单持仓不显示. fundMonthlyStatementId:{}", fundMonthlyStatement.getBusinessId());
                continue;
            } else {
                log.info("期初、期末结余不为0，增加持仓记录. openingBalance:{},closingBalance:{}", openingBalance, closingBalance);
            }
            holdingDtoList.add(holdingDto);
        }
        // 用户等值hkd总市值
        basicInfoDto.setTotalMarket(statementMoneyFormat(equalHkdTotalMarketValue));
        basicInfoDto.setTotalMarketValue(equalHkdTotalMarketValue);
        return holdingDtoList;
    }

    /**
     * 已确认交易
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.ConfirmedOrderDto> queryConfirmedOrderDtoList(TdFundMonthlyStatement fundMonthlyStatement
            , Map<String, FundInfoResp> fundInfoLocalCacheMap, String bankAccountId) {
        String period = fundMonthlyStatement.getPeriod();
        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));

        List<TdFundMonthlyOrderTmp> fundOrderList
                = tdFundMonthlyOrderTmpMapper.queryFundAccountMonthlyStatementConfirmedOrderList(monthStartDate, monthEndDate, fundMonthlyStatement.getBankUserId(), bankAccountId);

        if (CollectionUtils.isEmpty(fundOrderList)) {
            return new ArrayList<>();
        }
        List<String> productIdList = fundOrderList.stream()
                .map(tdFundMonthlyOrderTmp -> tdFundMonthlyOrderTmp.getProductId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, FundInfoResp> productIdAndFundInfoRespMap = appendFundInfoToLocalCache(productIdList, fundInfoLocalCacheMap);


        return fundOrderList.stream().map(tdFundMonthlyOrderTmp -> {
            FundMonthlyStatementDataDto.ConfirmedOrderDto confirmedOrderDto = new FundMonthlyStatementDataDto.ConfirmedOrderDto();
            confirmedOrderDto.setCurrency(tdFundMonthlyOrderTmp.getCurrency());
            String productId = tdFundMonthlyOrderTmp.getProductId();
            FundInfoResp fundInfoResp = productIdAndFundInfoRespMap.get(productId);
            if (fundInfoResp == null) {
                throw new RuntimeException("queryConfirmedOrderDtoList, 基金月结单查找不到基金基本信息, productId:" + productId);
            }
            // 份额精度
            Integer qtyDecimal = fundInfoResp.getQtyDecimal();
            // 净值精度
            Integer navDecimal = fundInfoResp.getNavDecimal();

            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                confirmedOrderDto.setDescription(generateOrderDesc(fundInfoResp.getCnFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundMonthlyOrderTmp));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                confirmedOrderDto.setDescription(generateOrderDesc(fundInfoResp.getHkFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundMonthlyOrderTmp));
            }

            confirmedOrderDto.setEngDescription(generateOrderEngDesc(fundInfoResp.getEnFundName()
                    , fundMonthlyStatement.getDocLang(), tdFundMonthlyOrderTmp));

            Date date = tdFundMonthlyOrderTmp.getRealConfirmTime();

            BigDecimal fee = tdFundMonthlyOrderTmp.getFinalFee();

            BigDecimal amount = null;
            BigDecimal units = null;
            BigDecimal unitPrice = null;

            if (FundBusinessTypeEnum.SELL.getValue().equals(tdFundMonthlyOrderTmp.getBusinessType())) {
                amount = tdFundMonthlyOrderTmp.getConfirmAmt();
                units = tdFundMonthlyOrderTmp.getConfirmShare();
                if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(tdFundMonthlyOrderTmp.getStatus())) {
                    units = tdFundMonthlyOrderTmp.getApplyShare();
                }
                unitPrice = tdFundMonthlyOrderTmp.getConfirmNetValue();
            }

            if (FundBusinessTypeEnum.BUY.getValue().equals(tdFundMonthlyOrderTmp.getBusinessType())) {
                //从收费版本开始：申购订单统一取申购金额
                amount = tdFundMonthlyOrderTmp.getApplyAmt();

                units = tdFundMonthlyOrderTmp.getConfirmShare();
                unitPrice = tdFundMonthlyOrderTmp.getConfirmNetValue();

            }

            confirmedOrderDto.setAmount(statementMoneyFormat(amount));
            confirmedOrderDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
            confirmedOrderDto.setFee(statementMoneyFormat(fee));
            confirmedOrderDto.setNoOfUnits(statementShareFormat(units, qtyDecimal));
            confirmedOrderDto.setUnitPrice(statementShareFormat(unitPrice, navDecimal));

            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(tdFundMonthlyOrderTmp);
            statementOrderSnapshotDto.setProductEngName(fundInfoResp.getEnFundName());
            statementOrderSnapshotDto.setProductHkName(fundInfoResp.getHkFundName());
            statementOrderSnapshotDto.setProductZhName(fundInfoResp.getCnFundName());
            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.CONFIRMED_MODULE.getValue());

            confirmedOrderDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);

            return confirmedOrderDto;
        }).collect(Collectors.toList());
    }

    /**
     * 处理中交易
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.PendingOrderDto> queryPendingOrderDtoList(TdFundMonthlyStatement fundMonthlyStatement
            , Map<String, FundInfoResp> fundInfoLocalCacheMap, String bankAccountId) {
        String period = fundMonthlyStatement.getPeriod();
        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));

        List<TdFundMonthlyOrderTmp> fundOrderList
                = tdFundMonthlyOrderTmpMapper.queryFundAccountMonthlyStatementPendingOrderList(monthStartDate, monthEndDate, fundMonthlyStatement.getBankUserId(), bankAccountId);
        if (CollectionUtils.isEmpty(fundOrderList)) {
            return new ArrayList<>();
        }
        List<String> productIdList = fundOrderList.stream()
                .map(tdFundMonthlyOrderTmp -> tdFundMonthlyOrderTmp.getProductId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, FundInfoResp> productIdAndFundInfoRespMap = appendFundInfoToLocalCache(productIdList, fundInfoLocalCacheMap);

        return fundOrderList.stream().map(tdFundOrderTmp -> {
            FundMonthlyStatementDataDto.PendingOrderDto pendingOrderDto = new FundMonthlyStatementDataDto.PendingOrderDto();
            pendingOrderDto.setCurrency(tdFundOrderTmp.getCurrency());

            String productId = tdFundOrderTmp.getProductId();
            FundInfoResp fundInfoResp = productIdAndFundInfoRespMap.get(productId);
            if (fundInfoResp == null) {
                throw new RuntimeException("queryPendingOrderDtoList, 基金月结单查找不到基金基本信息, productId:" + productId);
            }
            // 份额精度
            Integer qtyDecimal = fundInfoResp.getQtyDecimal();

            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                pendingOrderDto.setDescription(generateOrderDesc(fundInfoResp.getCnFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                pendingOrderDto.setDescription(generateOrderDesc(fundInfoResp.getHkFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));
            }

            pendingOrderDto.setEngDescription(generateOrderEngDesc(fundInfoResp.getEnFundName()
                    , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));


            Date date = tdFundOrderTmp.getTradeDate();

            BigDecimal amount = null;
            BigDecimal fee = null;
            BigDecimal units = null;
            BigDecimal unitPrice = null;
            fee = tdFundOrderTmp.getFinalFee();
            if (FundBusinessTypeEnum.SELL.getValue().equals(tdFundOrderTmp.getBusinessType())) {
                units = tdFundOrderTmp.getApplyShare();

            }

            if (FundBusinessTypeEnum.BUY.getValue().equals(tdFundOrderTmp.getBusinessType())) {
                amount = tdFundOrderTmp.getApplyAmt();
            }


            pendingOrderDto.setAmount(statementMoneyFormat(amount));
            pendingOrderDto.setFee(statementMoneyFormat(fee));
            pendingOrderDto.setNoOfUnits(statementShareFormat(units, qtyDecimal));
            pendingOrderDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
            pendingOrderDto.setUnitPrice(statementShareFormat(unitPrice));


            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(tdFundOrderTmp);
            statementOrderSnapshotDto.setProductEngName(fundInfoResp.getEnFundName());
            statementOrderSnapshotDto.setProductHkName(fundInfoResp.getHkFundName());
            statementOrderSnapshotDto.setProductZhName(fundInfoResp.getCnFundName());
            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.PENDING_MODULE.getValue());

            pendingOrderDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);
            return pendingOrderDto;
        }).collect(Collectors.toList());
    }

    /**
     * 派息
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.DividendOrderDto> queryDividendOrderDtoList(TdFundMonthlyStatement fundMonthlyStatement
            , Map<String, FundInfoResp> fundInfoLocalCacheMap, String bankAccountId) {
        String period = fundMonthlyStatement.getPeriod();
        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));

        List<TdFundMonthlyOrderTmp> fundOrderList
                = tdFundMonthlyOrderTmpMapper.queryFundAccountMonthlyStatementDividendOrderList(monthStartDate, monthEndDate,
                fundMonthlyStatement.getBankUserId(), bankAccountId);
        if (CollectionUtils.isEmpty(fundOrderList)) {
            return new ArrayList<>();
        }
        List<String> productIdList = fundOrderList.stream()
                .map(tdFundMonthlyOrderTmp -> tdFundMonthlyOrderTmp.getProductId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, FundInfoResp> productIdAndFundInfoRespMap = appendFundInfoToLocalCache(productIdList, fundInfoLocalCacheMap);

        return fundOrderList.stream().map(tdFundOrderTmp -> {
            FundMonthlyStatementDataDto.DividendOrderDto dividendOrderDto = new FundMonthlyStatementDataDto.DividendOrderDto();
            String productId = tdFundOrderTmp.getProductId();
            FundInfoResp fundInfoResp = productIdAndFundInfoRespMap.get(productId);
            if (fundInfoResp == null) {
                throw new RuntimeException("queryDividendOrderDtoList, 基金月结单查找不到基金基本信息, productId:" + productId);
            }
            // 份额精度
            Integer qtyDecimal = fundInfoResp.getQtyDecimal();

            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                dividendOrderDto.setDescription(generateOrderDesc(fundInfoResp.getCnFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                dividendOrderDto.setDescription(generateOrderDesc(fundInfoResp.getHkFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));
            }

            dividendOrderDto.setEngDescription(generateOrderEngDesc(fundInfoResp.getEnFundName()
                    , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));

            Date date = tdFundOrderTmp.getRealDeliveryTime();
            //分红的需要根据数量去判断是现金分红还是单位分红
            FundBusinessTypeDescEnum fundBusinessTypeDescEnum = judgeDividendType(tdFundOrderTmp);
            BigDecimal amount;
            BigDecimal units;
            //现金分红
            if (FundBusinessTypeDescEnum.DIVIDEND_CASH.equals(fundBusinessTypeDescEnum)) {
                amount = tdFundOrderTmp.getConfirmAmt();
                dividendOrderDto.setNoOfUnits(StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
                dividendOrderDto.setCurrency(tdFundOrderTmp.getCurrency());
                dividendOrderDto.setAmount(statementMoneyFormat(amount));
            }
            //单位派息
            if (FundBusinessTypeDescEnum.DIVIDEND_UNIT.equals(fundBusinessTypeDescEnum)) {
                units = tdFundOrderTmp.getConfirmShare();
                dividendOrderDto.setNoOfUnits(statementShareFormat(units, qtyDecimal));
                dividendOrderDto.setCurrency(StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
                dividendOrderDto.setAmount(StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER);
            }

            dividendOrderDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));

            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(tdFundOrderTmp);
            statementOrderSnapshotDto.setProductEngName(fundInfoResp.getEnFundName());
            statementOrderSnapshotDto.setProductHkName(fundInfoResp.getHkFundName());
            statementOrderSnapshotDto.setProductZhName(fundInfoResp.getCnFundName());

            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.DIVIDEND_MODULE.getValue());

            dividendOrderDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);
            return dividendOrderDto;
        }).collect(Collectors.toList());
    }

    /**
     * 存入及提取
     *
     * @param fundMonthlyStatement
     * @return
     */
    private List<FundMonthlyStatementDataDto.DepositAndWithdrawalDto> queryDepositAndWithdrawalDtoList(TdFundMonthlyStatement fundMonthlyStatement, Map<String, FundInfoResp> fundInfoLocalCacheMap, String bankAccountId) {


        String period = fundMonthlyStatement.getPeriod();
        Date monthStartDate = DateUtil.startOfDay(DateUtil.getFirstDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));
        Date monthEndDate = DateUtil.endOfDay(DateUtil.getLastDayOfMonth(DateUtil.parse(period, MONTHLY_STATEMENT_PERIOD_FORMAT)));
        List<TdFundMonthlyOrderTmp> fundOrderList
                = tdFundMonthlyOrderTmpMapper.queryFundAccountMonthlyStatementDepositAndWithdrawalList(monthStartDate, monthEndDate,
                fundMonthlyStatement.getBankUserId(), bankAccountId);
        if (CollectionUtils.isEmpty(fundOrderList)) {
            return new ArrayList<>();
        }
        List<String> productIdList = fundOrderList.stream()
                .map(tdFundMonthlyOrderTmp -> tdFundMonthlyOrderTmp.getProductId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, FundInfoResp> productIdAndFundInfoRespMap = appendFundInfoToLocalCache(productIdList, fundInfoLocalCacheMap);

        return fundOrderList.stream().map(tdFundOrderTmp -> {
            FundMonthlyStatementDataDto.DepositAndWithdrawalDto depositAndWithdrawalDto
                    = new FundMonthlyStatementDataDto.DepositAndWithdrawalDto();

            String productId = tdFundOrderTmp.getProductId();
            FundInfoResp fundInfoResp = productIdAndFundInfoRespMap.get(productId);
            if (fundInfoResp == null) {
                throw new RuntimeException("queryDepositAndWithdrawalDtoList, 基金月结单查找不到基金基本信息, productId:" + productId);
            }
            // 份额精度
            Integer qtyDecimal = fundInfoResp.getQtyDecimal();

            String docLang = fundMonthlyStatement.getDocLang();
            if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
                depositAndWithdrawalDto.setDescription(generateOrderDesc(fundInfoResp.getCnFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));
            }
            if (I18nSupportEnum.CN_HK.getName().equals(docLang)) {
                depositAndWithdrawalDto.setDescription(generateOrderDesc(fundInfoResp.getHkFundName()
                        , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));
            }

            depositAndWithdrawalDto.setEngDescription(generateOrderEngDesc(fundInfoResp.getEnFundName()
                    , fundMonthlyStatement.getDocLang(), tdFundOrderTmp));


            Date date = tdFundOrderTmp.getRealDeliveryTime();
            BigDecimal fee = tdFundOrderTmp.getFinalFee();

            BigDecimal units = tdFundOrderTmp.getConfirmShare();

            depositAndWithdrawalDto.setCurrency(tdFundOrderTmp.getCurrency());
            depositAndWithdrawalDto.setDate(DateUtil.format(date, StatementConstants.MONTHLY_STATEMENT_DATE_FORMAT, Locale.ENGLISH));
            depositAndWithdrawalDto.setNoOfUnits(statementShareFormat(units, qtyDecimal));
            depositAndWithdrawalDto.setFee(statementMoneyFormat(fee));

            //新增
            FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = getStatementOrderSnapshotDto(tdFundOrderTmp);
            statementOrderSnapshotDto.setProductEngName(fundInfoResp.getEnFundName());
            statementOrderSnapshotDto.setProductHkName(fundInfoResp.getHkFundName());
            statementOrderSnapshotDto.setProductZhName(fundInfoResp.getCnFundName());

            statementOrderSnapshotDto.setBizType(MonthStatementModuleTypeEnum.STORE_TAKE_MODULE.getValue());

            depositAndWithdrawalDto.setStatementOrderSnapshotDto(statementOrderSnapshotDto);
            return depositAndWithdrawalDto;
        }).collect(Collectors.toList());
    }

    private FundMonthlyStatementDataDto.StatementOrderSnapshotDto getStatementOrderSnapshotDto(TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp) {

        FundMonthlyStatementDataDto.StatementOrderSnapshotDto statementOrderSnapshotDto = new FundMonthlyStatementDataDto.StatementOrderSnapshotDto();
        statementOrderSnapshotDto.setOrderNo(tdFundMonthlyOrderTmp.getOrderNo());
        statementOrderSnapshotDto.setBusinessType(tdFundMonthlyOrderTmp.getBusinessType());
        statementOrderSnapshotDto.setProductId(tdFundMonthlyOrderTmp.getProductId());
        statementOrderSnapshotDto.setIsinNo(tdFundMonthlyOrderTmp.getIsinNo());
        statementOrderSnapshotDto.setCurrency(tdFundMonthlyOrderTmp.getCurrency());
        statementOrderSnapshotDto.setApplyTime(tdFundMonthlyOrderTmp.getApplyTime());
        statementOrderSnapshotDto.setApplyShare(tdFundMonthlyOrderTmp.getApplyShare());
        statementOrderSnapshotDto.setApplyAmt(tdFundMonthlyOrderTmp.getApplyAmt());
        statementOrderSnapshotDto.setConfirmAmt(tdFundMonthlyOrderTmp.getConfirmAmt());
        statementOrderSnapshotDto.setConfirmNetValue(tdFundMonthlyOrderTmp.getConfirmNetValue());
        statementOrderSnapshotDto.setConfirmNetValueDate(tdFundMonthlyOrderTmp.getConfirmNetValueDate());
        statementOrderSnapshotDto.setConfirmShare(tdFundMonthlyOrderTmp.getConfirmShare());
        statementOrderSnapshotDto.setFee(tdFundMonthlyOrderTmp.getFee());
        statementOrderSnapshotDto.setFeeRate(tdFundMonthlyOrderTmp.getFeeRate());
        statementOrderSnapshotDto.setFinalFee(tdFundMonthlyOrderTmp.getFinalFee());
        statementOrderSnapshotDto.setRealConfirmTime(tdFundMonthlyOrderTmp.getRealConfirmTime());
        statementOrderSnapshotDto.setRealDeliveryTime(tdFundMonthlyOrderTmp.getRealDeliveryTime());
        statementOrderSnapshotDto.setTradeDate(tdFundMonthlyOrderTmp.getTradeDate());
        statementOrderSnapshotDto.setStatus(tdFundMonthlyOrderTmp.getStatus());

        return statementOrderSnapshotDto;
    }

    /**
     * 订单描述 操作+productName
     *
     * @param tdFundMonthlyOrderTmp
     * @return
     */
    private String generateOrderEngDesc(String fundName, String lang, TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp) {
        String businessType = tdFundMonthlyOrderTmp.getBusinessType();
        FundBusinessTypeDescEnum fundBusinessTypeDescEnum = FundBusinessTypeDescEnum.fromValue(businessType);
        //分红的需要根据数量去判断是现金分红还是单位分红
        if (FundBusinessTypeDescEnum.DIVIDEND.equals(fundBusinessTypeDescEnum)) {
            fundBusinessTypeDescEnum = judgeDividendType(tdFundMonthlyOrderTmp);
        }
        StringBuilder descBuilder = new StringBuilder();
        descBuilder.append(fundBusinessTypeDescEnum.getEnMsg()).append(" ");
        if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(tdFundMonthlyOrderTmp.getStatus())) {
            descBuilder.append(ORDER_FAIL_DESC_EN);
        }
        descBuilder.append("    ").append(fundName);
        return descBuilder.toString();
    }

    /**
     * 订单描述 操作+productName
     *
     * @param tdFundMonthlyOrderTmp
     * @return
     */
    private String generateOrderDesc(String fundName, String lang, TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp) {
        String businessType = tdFundMonthlyOrderTmp.getBusinessType();
        StringBuilder descBuilder = new StringBuilder();
        FundBusinessTypeDescEnum fundBusinessTypeDescEnum = FundBusinessTypeDescEnum.fromValue(businessType);
        //分红的需要根据数量去判断是现金分红还是单位分红
        if (FundBusinessTypeDescEnum.DIVIDEND.equals(fundBusinessTypeDescEnum)) {
            fundBusinessTypeDescEnum = judgeDividendType(tdFundMonthlyOrderTmp);
        }

        if (fundBusinessTypeDescEnum != null) {
            if (I18nSupportEnum.CN_ZH.getName().equals(lang)) {
                descBuilder.append(fundBusinessTypeDescEnum.getZhMsg());
                if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(tdFundMonthlyOrderTmp.getStatus())) {
                    descBuilder.append(ORDER_FAIL_DESC_ZH);
                }
                descBuilder.append("    ").append(fundName);
            }
            if (I18nSupportEnum.CN_HK.getName().equals(lang)) {
                descBuilder.append(fundBusinessTypeDescEnum.getHkMsg());
                if (FundOrderStatusEnum.BROKER_FAILED.getDbValue().equals(tdFundMonthlyOrderTmp.getStatus())) {
                    descBuilder.append(ORDER_FAIL_DESC_HK);
                }
                descBuilder.append("    ").append(fundName);
            }
        }
        return descBuilder.toString();
    }

    /**
     * 分红的需要根据数量去判断是现金分红还是单位分红
     *
     * @param tdFundMonthlyOrderTmp
     * @return
     */
    FundBusinessTypeDescEnum judgeDividendType(TdFundMonthlyOrderTmp tdFundMonthlyOrderTmp) {
        if (tdFundMonthlyOrderTmp.getConfirmAmt() != null
                && tdFundMonthlyOrderTmp.getConfirmAmt().compareTo(BigDecimal.ZERO) > 0) {
            return FundBusinessTypeDescEnum.DIVIDEND_CASH;
        } else {
            return FundBusinessTypeDescEnum.DIVIDEND_UNIT;
        }
    }

    /**
     * 追加没有缓存到的基金信息到缓存里
     *
     * @param productIdList
     * @param fundInfoLocalCacheMap
     */
    protected Map<String, FundInfoResp> appendFundInfoToLocalCache(List<String> productIdList, Map<String, FundInfoResp> fundInfoLocalCacheMap) {
        List<String> noCacheProductIdList = new ArrayList<>();
        productIdList.forEach(productId -> {
            FundInfoResp cacheFundInfo = fundInfoLocalCacheMap.get(productId);
            if (cacheFundInfo == null) {
                noCacheProductIdList.add(productId);
            }
        });
        if (noCacheProductIdList.size() > 0) {
            Map<String, FundInfoResp> productIdAndFundInfoRespMap = marketRemoteService.queryFundInfo(noCacheProductIdList);
            fundInfoLocalCacheMap.putAll(productIdAndFundInfoRespMap);
        }
        return fundInfoLocalCacheMap;
    }


    /**
     * 参数：isMcv
     * 通讯地址转化为
     * 第一栏
     * 第二栏
     * 第三栏
     * 第四栏
     *
     * @param addressInfoResp
     * @return
     */
    void conversionAddress(FundMonthlyStatementDataDto.BasicInfoDto basicInfoDto, AddressInfoResp addressInfoResp, Boolean isMcv) {
        log.info("源basicInfoDto={}", JSON.toJSONString(basicInfoDto));
        BankAddress currentAddress = addressInfoResp.getCurrentAddress();
        if (currentAddress == null) {
            log.error("生成月结单失败，用户通讯地址为null! bankUserId:{}", addressInfoResp.getBankUserId());
            throw new RuntimeException("用户通讯地址为null");
        }

        if (isMcv && (EnumYesOrNo.NO.getValue().equals(currentAddress.getEnglishAddressFlag()))) {
            basicInfoDto.setOfficeLocation(addressInfoResp.getCountry());
            basicInfoDto.setStreet(addressInfoResp.getArea());
            basicInfoDto.setArea(addressInfoResp.getOfficeLocation());
            basicInfoDto.setCountry("");
        } else {
            basicInfoDto.setOfficeLocation(addressInfoResp.getOfficeLocation());
            basicInfoDto.setStreet(addressInfoResp.getStreet());
            basicInfoDto.setArea(addressInfoResp.getArea());
            basicInfoDto.setCountry(addressInfoResp.getCountry());
        }
        log.info("转化basicInfoDto={}", JSON.toJSONString(addressInfoResp));
    }

    private BigDecimal computeTotalMarket(String period, String currency, Map<String, BigDecimal> totalMarketMap, String businessType) {
        BigDecimal marketValue = BigDecimal.ZERO;
        if (totalMarketMap.isEmpty()) {
            // 如果为空
            return marketValue;
        }
        for (String ccy : totalMarketMap.keySet()) {
            if (currency.equals(ccy)) {
                marketValue = marketValue.add(totalMarketMap.get(ccy));
            } else {
                // 换汇为港币
                BigDecimal exchangeHkdRate = exchangeRateInfoService.getExchangeHkdRate(ccy, period, businessType);
                // 持有市值 = 持有市值 + （ccy币种的金额*汇率）
                marketValue = marketValue.add(totalMarketMap.get(ccy).multiply(exchangeHkdRate));
            }
        }
        return marketValue;

    }

    /**
     * 将map数据解析成对应的data
     *
     * @param businessDataMap
     * @return
     */
    @Override
    public FundMonthlyStatementDataDto parseDataMap2Dto(Map<String, Object> businessDataMap) {
        if (businessDataMap == null) {
            return null;
        }
        return JSONObject.parse(JsonUtils.toJsonString(businessDataMap), FundMonthlyStatementDataDto.class);
    }


    /**
     * 本期月结单全部处理完毕，包括推送。
     * 留个口子，给业务服务清除缓存等信息
     *
     * @param tdFundMonthlyStatementInfo
     */
    @Override
    public void statementAllProcessFinishHook(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo) {
        fundInfoLocalCacheMap.clear();
        periodAndPreparedStatusMap.remove(tdFundMonthlyStatementInfo.getPeriod());
    }


    @Override
    public String getBusinessType() {
        return AccountTypeEnum.FUND.getValue();
    }
}
