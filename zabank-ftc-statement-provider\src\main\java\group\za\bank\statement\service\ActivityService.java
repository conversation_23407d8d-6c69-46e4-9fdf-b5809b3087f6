package group.za.bank.statement.service;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;

/**
 * <AUTHOR>
 * @createTime 06 14:30
 * @description
 */
public interface ActivityService {


    /**
     * 月结单activity发送
     * @param monthlyStatementInfo
     * @param monthlyStatement
     * @param baseMonthlyStatementDataDto
     */
    void monthlyStatementActivityPub(TdFundMonthlyStatementInfo monthlyStatementInfo, TdFundMonthlyStatement monthlyStatement, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto);

}
