package group.za.bank.statement.manager;

import group.za.bank.fund.domain.clear.mapper.ClearDateRecordMapper;
import group.za.bank.fund.domain.trade.mapper.TdFundHoldingHistoryMapper;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.ExchangeRateInfoService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.math.BigDecimal;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

public class ActivityStatementManagerTest extends BaseTestService {
    @Mock
    ClearDateRecordMapper clearDateRecordMapper;
    @Mock
    ExchangeRateInfoService exchangeRateInfoService;
    @Mock
    TdFundHoldingHistoryMapper tdFundHoldingHistoryMapper;
    @Mock
    Logger log;
    @InjectMocks
    ActivityStatementManager activityStatementManager;


    @Test
    public void testGetEqualHkdMonthHoldingIncomeValue() throws Exception {
        BigDecimal result = activityStatementManager.getEqualHkdMonthHoldingIncomeValue("bankUserId", "bankAccountId", "202402", "businessType");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme