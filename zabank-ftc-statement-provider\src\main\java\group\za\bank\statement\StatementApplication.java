package group.za.bank.statement;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import group.za.invest.cache.redis.annotation.EnableRedissonClient;
import group.za.invest.web.spring.InvestBootApplication;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;


@Slf4j
@ComponentScan(value = {"group.za.bank","com.obs.services","com.zatech.bank.act"})
@EnableFeignClients(value = {"group.za.bank", "group.za.invest"})
@EnableApolloConfig
@EnableEurekaClient
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableRedissonClient

@MapperScan(value = "com.zatech.bank.act.repository.dao",sqlSessionFactoryRef = "statementSqlSessionFactory")
public class StatementApplication{



    public static void main(String[] args) {
        InvestBootApplication.run(log, StatementApplication.class, args);
    }







}
