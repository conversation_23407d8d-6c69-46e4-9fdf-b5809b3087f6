package group.za.bank.statement.service.remote.impl;


import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.trade.model.req.feign.StockInfoReq;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.StkInfoRemoteService;
import group.za.bank.statement.service.remote.feign.StkInfoFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 股票基本信息远程接口
 *
 * <AUTHOR>
 * @date 2022/06/23
 **/
@Service
@Slf4j
public class StkInfoRemoteServiceImpl implements StkInfoRemoteService {

    @Autowired
    StkInfoFeign stkInfoFeign;

    @Override
    public StockInfoResp getStockInfo(StockInfoReq stockInfoReq) {
        ResponseData<StockInfoResp> stockInfo = stkInfoFeign.getStockInfo(stockInfoReq);
        if (!stockInfo.judgeSuccess()) {
            log.info("getStockInfo fail,stockInfoReq:{},msg:{}", stockInfoReq, stockInfo.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.STOCK_INFO_QUERY_ERROR);
        }
        return stockInfo.getValue();
    }
}
