package group.za.bank.statement.entity.dto;

import group.za.bank.statement.service.impl.StockStatementBusinessServiceImpl;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 14 16:59
 * @description
 */
@Data
public class StockStatementTradeDateInfo {
    private String period;

    //本月核心交易日历
    private List<String> coreTradeDateList;
    //下月核心交易日历
    private List<String> nextPeriodCoreTradeDateList;

    private Map<String, MarketStatementTradeDateInfo> marketCodeAndTradeDateInfoMap;

    @Data
    public static class MarketStatementTradeDateInfo{
        private String marketCode;


        private String startTradeDate;

        private String endTradeDate;

        //指定市场当月首日对应核心日历的日期
        private String periodStartCoreTradeDate;
        //指定市场当月最后一个交易日对应核心日历的日期
        private String periodEndCoreTradeDate;

        //本月当前市场交易日历
        private List<String> tradeDateList;
    }
}
