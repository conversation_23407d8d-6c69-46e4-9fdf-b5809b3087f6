package group.za.bank.statement.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import group.za.invest.json.serializer.CustomDateDeserializer;
import group.za.invest.json.serializer.CustomDateSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description 结单native显示里边每个模块里边每个订单模块
 */
@Data
public class NativeBlockDto {

    /**
     * 订单交易日期
     */
    @JsonSerialize(using = CustomDateSerializer.class)
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date tradeDate;

    /**
     * 标题行，app需要样式处理
     */
    NativeRowDto nativeTitle;

    /**
     * 成交明细
     */
    private List<List<NativeRowDto>> nativeRowDetailList;

    /**
     * 一个块代表一个订单
     * 一个订单里边一个属性组成一行
     */
    private List<NativeRowDto> nativeRowList;




}
