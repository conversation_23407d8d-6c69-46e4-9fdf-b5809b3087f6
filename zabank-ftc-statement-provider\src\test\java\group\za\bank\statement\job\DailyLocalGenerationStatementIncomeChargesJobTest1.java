package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

public class DailyLocalGenerationStatementIncomeChargesJobTest1 {
    @Mock
    TradeCalendarRemoteService tradeCalendarRemoteService;
    @Mock
    TdStockStatementDataService stockStatementDataService;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    DailyLocalGenerationStatementIncomeChargesJob dailyLocalGenerationStatementIncomeChargesJob;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testExecute() throws Exception {
        when(tradeCalendarRemoteService.isTradeDay(anyString(), any())).thenReturn(true);

        ReturnT<String> result = dailyLocalGenerationStatementIncomeChargesJob.execute("2024-01-01");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme