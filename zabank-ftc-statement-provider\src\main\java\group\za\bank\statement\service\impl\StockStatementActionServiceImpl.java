package group.za.bank.statement.service.impl;

import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.bankfront.common.enums.OperateTypeEnum;
import group.za.bank.sbs.bankfront.model.dto.StatementBusinessCommonDTO;
import group.za.bank.sbs.business.model.req.TransferDetailReq;
import group.za.bank.sbs.business.model.resp.TransferDetailResp;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.trade.common.constant.enums.ActionTypeEnum;
import group.za.bank.sbs.trade.common.constant.enums.FeeNameEnum;
import group.za.bank.sbs.trade.common.constant.enums.HkCorporationActionRecycleTypeEnum;
import group.za.bank.sbs.trade.common.constant.enums.TransferTypeEnum;
import group.za.bank.sbs.trade.mapper.StkActionDetailSubMapper;
import group.za.bank.sbs.trade.model.dto.StkActionDetailSubDto;
import group.za.bank.sbs.trade.model.entity.StkActionPlan;
import group.za.bank.sbs.trade.model.entity.StkActionRecycleDetail;
import group.za.bank.sbs.tradedata.model.req.ActionInfoQueryReq;
import group.za.bank.sbs.tradedata.model.resp.ActionInfoResp;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.TdStatementActionPendingData;
import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.bank.statement.entity.dto.ActionCapitalInfoDTO;
import group.za.bank.statement.entity.dto.StatementFeeDto;
import group.za.bank.statement.entity.dto.StatementTradeChangeDetailDto;
import group.za.bank.statement.entity.dto.TaxQueryDTO;
import group.za.bank.statement.manager.StockActionManager;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.StatementActionPendingDataService;
import group.za.bank.statement.service.StockStatementActionService;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.BusinessRemoteService;
import group.za.bank.statement.service.remote.TradeRemoteService;
import group.za.bank.statement.service.remote.TradedataRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/28 11:34
 * @Description
 */
@Slf4j
@Service
public class StockStatementActionServiceImpl implements StockStatementActionService {

    @Autowired
    private TradeRemoteService tradeRemoteService;
    @Autowired
    private TradedataRemoteService tradedataRemoteService;
    @Autowired
    private StatementActionPendingDataService statementActionPendingDataService;
    @Autowired
    private TdStockStatementDataService stockStatementDataService;
    @Autowired
    private BusinessRemoteService businessRemoteService;
    @Autowired
    private StkActionDetailSubMapper stkActionDetailSubMapper;

    @Autowired
    private StockTradeManager stockTradeManager;
    @Autowired
    private StockActionManager stockActionManager;
    @Autowired
    private SystemConfig systemConfig;

    @Override
    public List<StatementTradeChangeDetailDto> getTaxTradeChangeDetailData(String marketCode, String accountId, String period) {
        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;

        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);
        List<StatementTradeChangeDetailDto> extraTradeChangeDetailDtos = new ArrayList<>();
        //查询税费（ADR和金融税）
        List<TaxQueryDTO> taxQueryDTOs = stockActionManager.taxQuery(accountId, startDate, endDate, marketCode);
        if (!CollectionUtils.isEmpty(taxQueryDTOs)) {
            taxQueryDTOs.forEach(tax -> {
                StatementTradeChangeDetailDto tradeChangeDetailDto = new StatementTradeChangeDetailDto();
                tradeChangeDetailDto.setAmount(tax.getAmt().toPlainString());
                String businessType = null;
                if (TaxFeeTypeEnum.ADR.getValue().equals(tax.getType())) {
                    businessType = StatementBusinessTypeEnum.ADR.getBusinessType();
                } else if (TaxFeeTypeEnum.FTT.getValue().equals(tax.getType())) {
                    businessType = StatementBusinessTypeEnum.FTT.getBusinessType();
                }
                tradeChangeDetailDto.setBusinessType(businessType);
                tradeChangeDetailDto.setStockCode(tax.getStockCode());
                tradeChangeDetailDto.setCurrency(tax.getCurrency());
                tradeChangeDetailDto.setTradeDate(DateUtil.format(tax.getDebitSuccessDate(), DateUtil.FORMAT_SHORT));
                tradeChangeDetailDto.setSourceStockCode(tax.getStockCode());
                tradeChangeDetailDto.setTtlMarketCode(ttlMarketCode);
                extraTradeChangeDetailDtos.add(tradeChangeDetailDto);
            });
        }

        return extraTradeChangeDetailDtos;
    }

    @Override
    public List<StatementTradeChangeDetailDto> filterAndAttachActionCapitalForPeriod(String marketCode, String period, String bankUserId, String accountId,
                                                                                     List<StatementTradeChangeDetailDto> tradeChangeDetailDtoList) {

        //查询已经处理的月结单期数(用于当期月结单多次生成)
        List<TdStatementActionPendingData> processedActionPendingDatas = statementActionPendingDataService.queryPendingListForPeriod(marketCode, accountId, period);
        processedActionPendingDatas.forEach(actionPendingData -> {
            TdStockStatementData stockStatementData =
                    stockStatementDataService.findByPrimaryKey(actionPendingData.getStatementDataId()).getValue();
            if (stockStatementData != null) {
                addOrReplaceActionCapitalDto(tradeChangeDetailDtoList, stockStatementData,
                        actionPendingData.getActionId(), actionPendingData.getCapitalTime());
            }
        });

        Set<String> processedReferenceNos = getActionPendingDataReferenceNos(processedActionPendingDatas);

        //过滤已经处理过的月结单公司行动ID, 避免重复远程查询公司行动资金入账
        Set<String> queryStatementReferenceNos = tradeChangeDetailDtoList.stream()
                //过滤非公司行动资金类型数据
                .filter(changeDetailDto -> StatementBusinessTypeEnum.getActionCapitalBusinessList().contains(changeDetailDto.getBusinessType()))
                .map(StatementTradeChangeDetailDto::getReferenceNo)
                .filter(referenceNo -> !processedReferenceNos.contains(referenceNo))
                .collect(Collectors.toSet());

        //待处理的月结单公司行动记录
        List<TdStatementActionPendingData> statementActionPendingDatas = statementActionPendingDataService.queryPendingList(marketCode, accountId,
                StatementActionPendingStatusEnum.UN_PROCESSED.getValue());
        Set<String> pendingReferenceNos = getActionPendingDataReferenceNos(statementActionPendingDatas);
        queryStatementReferenceNos.addAll(pendingReferenceNos);

        if (queryStatementReferenceNos.isEmpty()) {
            return tradeChangeDetailDtoList;
        }

        //查询公司行动资金入账时间
        List<ActionCapitalInfoDTO> actionCapitalInfoDTOS = stockTradeManager.queryActionCapitalInfo(accountId, queryStatementReferenceNos);
        //公司资金入账结果转为Map
        Map<String, ActionCapitalInfoDTO> actionCapitalMap = actionCapitalInfoDTOS.stream()
                .collect(Collectors.toMap(ActionCapitalInfoDTO::getReferenceNo, Function.identity()));

        //当前月结单资金入账时间为空公司行动的编号
        Set<String> emptyActionCapitalReferenceNos = new HashSet<>();

        //处理当前月结单公司行动资金入账的数据
        tradeChangeDetailDtoList.stream()
                //过滤已处理过的数据
                .filter(changeDetailDto -> !processedReferenceNos.contains(changeDetailDto.getReferenceNo()))
                //过滤非公司行动资金类型数据
                .filter(changeDetailDto -> StatementBusinessTypeEnum.getActionCapitalBusinessList().contains(changeDetailDto.getBusinessType()))
                .forEach(changeDetailDto -> {
                    ActionCapitalInfoDTO capitalInfoDTO = actionCapitalMap.get(changeDetailDto.getReferenceNo());
                    if (capitalInfoDTO == null) {
                        log.error("该用户{}没有公司行动入账记录,bankUserId={},actionId:{}", period, bankUserId, changeDetailDto.getReferenceNo());
                        statementActionPendingDataService.create(changeDetailDto.getStatementId(),
                                bankUserId, accountId, changeDetailDto.getReferenceNo(), changeDetailDto.getBusinessType(), period, changeDetailDto.getTtlMarketCode(), true);
                        emptyActionCapitalReferenceNos.add(changeDetailDto.getReferenceNo());
                        return;
                    }

                    //如果获取不到资金入账时间或者资金入账时间不是当前结单月份,则剔除掉该数据，并记录到月结单公司行动待处理数据中,等待下个月处理
                    if (capitalInfoDTO.getCapitalTime() == null
                            || !period.equals(DateUtil.format(capitalInfoDTO.getCapitalTime(), StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT))) {
                        log.warn("资金入账时间或者资金入账时间不是当前结单月份,bankUserId={},actionId:{}", bankUserId, changeDetailDto.getReferenceNo());
                        statementActionPendingDataService.create(changeDetailDto.getStatementId(),
                                bankUserId, accountId, changeDetailDto.getReferenceNo(), changeDetailDto.getBusinessType(), period, changeDetailDto.getTtlMarketCode(), true);
                        emptyActionCapitalReferenceNos.add(changeDetailDto.getReferenceNo());
                        return;
                    }
                    //获取到资金入账替换为实际资金入账时间
                    changeDetailDto.setTradeDate(DateUtil.format(capitalInfoDTO.getCapitalTime(), DateUtil.FORMAT_SHORT));
                });

        //处理之前月结单公司行动资金尚未入账的数据
        statementActionPendingDatas.forEach(actionPendingData -> {
            ActionCapitalInfoDTO capitalInfoDTO = actionCapitalMap.get(actionPendingData.getActionId());
            if (capitalInfoDTO == null) {
                return;
            }
            //处理资金入账成功的公司行动数据
            if (capitalInfoDTO.getCapitalTime() != null) {
                String capitalPeriod = DateUtil.format(capitalInfoDTO.getCapitalTime(),
                        StatementConstants.MONTHLY_STATEMENT_PERIOD_FORMAT);

                //如果资金入账时间月份和当前期数月份不相等,则跳过
                if (!capitalPeriod.equals(period)) {
                    return;
                }

                //如果资金入账时间小于公司行动之前所属于的月结单期数,证明已经处理过,则跳过并更新为已处理
                if (capitalPeriod.compareTo(actionPendingData.getOriginalPeriod()) < 0) {
                    statementActionPendingDataService.processedActionCapitalData(actionPendingData, capitalPeriod,
                            capitalInfoDTO.getCapitalTime(),
                            "资金入账时间小于当前月结单期数,直接修改为已处理");
                    return;
                }


                TdStockStatementData stockStatementData =
                        stockStatementDataService.findByPrimaryKey(actionPendingData.getStatementDataId()).getValue();
                if (stockStatementData != null) {
                    addOrReplaceActionCapitalDto(tradeChangeDetailDtoList, stockStatementData, actionPendingData.getActionId(), capitalInfoDTO.getCapitalTime());
                    //把之前月结单公司行动待处理数据更新为已处理
                    statementActionPendingDataService.processedActionCapitalData(actionPendingData, period,
                            capitalInfoDTO.getCapitalTime(), null);
                }
            }
        });

        //剔除资金入账时间为空的记录
        List<StatementTradeChangeDetailDto> targetStatementTradeChangeDetailDtos = tradeChangeDetailDtoList.stream()
                .filter(detail -> !emptyActionCapitalReferenceNos.contains(detail.getReferenceNo())
                        || !StatementBusinessTypeEnum.getActionCapitalBusinessList().contains(detail.getBusinessType()))
                .collect(Collectors.toList());
        return targetStatementTradeChangeDetailDtos;
    }

    @Override
    public void reBuildActionBusinessTypeData(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos) {
        //过滤拆合股类型去查询本地公司行动供股数据，判断出换股类型，并更新数据
        statementTradeChangeDetailDtos.stream()
                .filter(t -> StatementBusinessTypeEnum.SSP.getBusinessType().equals(t.getBusinessType())
                        || StatementBusinessTypeEnum.SCS.getBusinessType().equals(t.getBusinessType()))
                .forEach(t -> {
                    ActionInfoResp actionInfoResp = queryActionInfo(t.getReferenceNo());
                    if (ActionTypeEnum.SHARE_SWAP.getValue().equals(actionInfoResp.getActivityType())) {
                        t.setBusinessType(StatementBusinessTypeEnum.SCH.getBusinessType());
                        log.info("reBuildActionBusinessTypeData更新为换股类型，actionId:{}", t.getReferenceNo());
                    }
                });
    }

    @Override
    public List<StatementTradeChangeDetailDto> getTransferFeeTradeChangeDetailData(String bankUserId, String accountId, String period) {
        List<StatementTradeChangeDetailDto> transferFeeDetailData = new ArrayList<>();

        List<TransferDetailResp> transferDetailList = queryTransferDetailList(accountId, period);
        transferDetailList.forEach(t -> {
            StatementTradeChangeDetailDto tradeChangeDetailDto = new StatementTradeChangeDetailDto();
            StatementBusinessTypeEnum businessTypeEnum = TransferTypeEnum.TRANSFER_IN.getTransferType().equals(t.getTransferType())
                    ? StatementBusinessTypeEnum.SDP : StatementBusinessTypeEnum.SWD;
            tradeChangeDetailDto.setBusinessType(businessTypeEnum.getBusinessType());
            tradeChangeDetailDto.setAmount(t.getDebitAmount().toPlainString());
            tradeChangeDetailDto.setStockCode(t.getStockCode());
            tradeChangeDetailDto.setCurrency(t.getDebitCurrency());
            tradeChangeDetailDto.setTradeDate(DateUtil.format(t.getDebitDate(), DateUtil.FORMAT_SHORT));
            tradeChangeDetailDto.setSourceStockCode(t.getStockCode());
            tradeChangeDetailDto.setTtlMarketCode(MarketCodeEnum.US.getTtlValue());

            transferFeeDetailData.add(tradeChangeDetailDto);
        });
        return transferFeeDetailData;
    }

    @Override
    public List<TransferDetailResp> queryTransferDetailList(String accountId, String period) {
        return businessRemoteService.queryTransferDetailList(buildTransferDetailReq(accountId, period));
    }

    @Override
    public List<StatementTradeChangeDetailDto> getDividendPressAndRebateDataByAccountId(String marketCode, String accountId, String period) {
        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;

        String ttlMarketCode = MarketCodeEnum.getTtlValueByValue(marketCode);
        List<StkActionDetailSubDto> list = stkActionDetailSubMapper.queryDividendPressAndRebateByAccountId(marketCode, accountId, startDate, endDate);
        List<StatementTradeChangeDetailDto> result = list.stream().map(actionDetailSub -> {
            StatementTradeChangeDetailDto dto = new StatementTradeChangeDetailDto();
            StatementBusinessTypeEnum statementBusinessTypeEnum = DividendRebateTypeEnum.convertStatementBusinessTypeEnum(actionDetailSub.getBusinessType());
            dto.setBusinessType(statementBusinessTypeEnum.getBusinessType());
            dto.setAmount(actionDetailSub.getBusinessAmount().toPlainString());
            dto.setStockCode(actionDetailSub.getStockCode());
            dto.setCurrency(CurrencyEnum.USD.getCurrency());
            dto.setTradeDate(DateUtil.format(actionDetailSub.getBusinessTime(), DateUtil.FORMAT_SHORT));
            dto.setSourceStockCode(actionDetailSub.getStockCode());
            dto.setTtlMarketCode(ttlMarketCode);
            return dto;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<StatementTradeChangeDetailDto> getBusinessCapitalData(String marketCode, String accountId, String period) {
        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;
        String ttlValueByValue = MarketCodeEnum.getTtlValueByValue(marketCode);

        List<StatementBusinessCommonDTO> statementBusinessCapitalDTOS = stockTradeManager.queryCapitalListByStatement(accountId, startDate, endDate);
        return statementBusinessCapitalDTOS.stream().map(capitalDTO -> {
            StatementTradeChangeDetailDto dto = new StatementTradeChangeDetailDto();
            StatementBusinessTypeEnum statementBusinessTypeEnum = OperateTypeEnum.CREDIT.getValue().equals(capitalDTO.getOperationType())
                    ? StatementBusinessTypeEnum.CAPITAL_IN : StatementBusinessTypeEnum.CAPITAL_OUT;
            dto.setBusinessType(statementBusinessTypeEnum.getBusinessType());
            dto.setAmount(capitalDTO.getBusinessAmount().toPlainString());
            dto.setStockCode(capitalDTO.getStockCode());
            dto.setCurrency(capitalDTO.getCurrency());
            dto.setTradeDate(DateUtil.format(capitalDTO.getBusinessTime(), DateUtil.FORMAT_SHORT));
            dto.setSourceStockCode(capitalDTO.getStockCode());
            dto.setTtlMarketCode(ttlValueByValue);

            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 从缓存取公司行动基本信息数据
     *
     * @param actionId
     * @return
     */
    private ActionInfoResp queryActionInfo(String actionId) {
        String calendarKey = StatementRedisCacheKey.ACTION_INFO_CACHE_KEY.key(actionId);
        return AbstractCacheTemplate.queryFromCache(calendarKey, 1, TimeUnit.DAYS, () -> {
            ActionInfoQueryReq actionInfoQueryReq = new ActionInfoQueryReq();
            actionInfoQueryReq.setActionId(actionId);
            return tradedataRemoteService.actionInfoQuery(actionInfoQueryReq);
        }, false);
    }

    private Set<String> getActionPendingDataReferenceNos(List<TdStatementActionPendingData> processedActionPendingDatas) {
        return processedActionPendingDatas.stream()
                .map(pendingData -> pendingData.getActionId())
                .collect(Collectors.toSet());
    }

    /**
     * 添加或者更新公司行动交易变动明细数据
     *
     * @param actionChangeDetailDtoList
     * @param stockStatementData
     * @param referenceNo
     * @param capitalTime
     */
    private void addOrReplaceActionCapitalDto(List<StatementTradeChangeDetailDto> actionChangeDetailDtoList,
                                              TdStockStatementData stockStatementData, String referenceNo, Date capitalTime) {
        String tradeDate = DateUtil.format(capitalTime, DateUtil.FORMAT_SHORT);

        for (StatementTradeChangeDetailDto changeDetailDto : actionChangeDetailDtoList) {
            //如果公司行动编号和当期当前月结单公司行动编号一致，则直接替换
            if (changeDetailDto.getReferenceNo().equals(referenceNo)) {
                changeDetailDto.setTradeDate(tradeDate);
                return;
            }
        }

        //添加加到当期月结单公司行动数据中
        StatementTradeChangeDetailDto tradeChangeDetailDto = new StatementTradeChangeDetailDto();
        stockStatementDataService.buildTradeChangeDetailDto(tradeChangeDetailDto, stockStatementData);
        tradeChangeDetailDto.setTradeDate(tradeDate);
        actionChangeDetailDtoList.add(tradeChangeDetailDto);
    }

    /**
     * 构建转仓费用记录查询参数
     *
     * @param accountId
     * @param period
     * @return
     */
    private TransferDetailReq buildTransferDetailReq(String accountId, String period) {
        TransferDetailReq transferDetailReq = new TransferDetailReq();
        transferDetailReq.setAccountId(accountId);

        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;
        transferDetailReq.setStartDate(DateUtil.stringToDate(startDate));
        transferDetailReq.setEndDate(DateUtil.stringToDate(endDate));

        return transferDetailReq;
    }

    @Override
    public List<StatementTradeChangeDetailDto> getBusinessStockMovementData(String marketCode, String accountId, String period) {
        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;
        String ttlValueByValue = MarketCodeEnum.getTtlValueByValue(marketCode);

        List<StatementBusinessCommonDTO> statementBusinessCapitalDTOS = stockTradeManager.queryStockListByStatement(accountId, startDate, endDate);
        return statementBusinessCapitalDTOS.stream().map(capitalDTO -> {
            StatementTradeChangeDetailDto dto = new StatementTradeChangeDetailDto();
            //如果是转出，则取相反数
            BigDecimal businessQty = capitalDTO.getBusinessQty();
            if (OperateTypeEnum.DEBIT.getValue().equals(capitalDTO.getOperationType())) {
                businessQty = businessQty.negate();
            }
            dto.setBusinessType(StatementBusinessTypeEnum.STOCK_MOVEMENT.getBusinessType());
            dto.setQty(businessQty.toPlainString());
            dto.setStockCode(capitalDTO.getStockCode());
            dto.setCurrency(capitalDTO.getCurrency());
            dto.setTradeDate(DateUtil.format(capitalDTO.getBusinessTime(), DateUtil.FORMAT_SHORT));
            dto.setSourceStockCode(capitalDTO.getStockCode());
            //默认美股
            dto.setTtlMarketCode(ttlValueByValue);
            dto.setBusinessId(capitalDTO.getBusinessId());

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void splitHkCashAndFee(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos) {
        log.info("splitHkCashAndFee start...");
        for (int i = 0; i < statementTradeChangeDetailDtos.size(); ) {
            log.info("【splitHkCashAndFee】processing index: {}", i);
            StatementTradeChangeDetailDto changeDetailDto = statementTradeChangeDetailDtos.get(i);

            // 只处理符合条件的业务类型
            if (!StatementBusinessTypeEnum.getHkActionFeeBusinessList().contains(changeDetailDto.getBusinessType())) {
                // 不符合条件，继续下一个元素
                log.info("【splitHkCashAndFee】skipping non-target business type: {}", changeDetailDto.getBusinessType());
                i++;
                continue;
            }

            // 根据业务类型进行拆分处理
            StatementBusinessTypeEnum businessTypeEnum = StatementBusinessTypeEnum.getByBusinessType(changeDetailDto.getBusinessType());
            switch (businessTypeEnum) {
                case CDV:
                    handleHkDividendAndFee(statementTradeChangeDetailDtos, i, changeDetailDto, FeeNameEnum.DIVIDEND_COLLECTION_FEE, StatementBusinessTypeEnum.CDV_F);
                    break;
                case ESB:
                    handleHkDividendAndFee(statementTradeChangeDetailDtos, i, changeDetailDto, FeeNameEnum.CORPORATE_ACTION_FEE, StatementBusinessTypeEnum.ESB_F);
                    break;
                case TPA:
                    // 派息手续费数据，作为费用处理
                    fillHkBuyBackAndFee(statementTradeChangeDetailDtos, i, changeDetailDto);
                    break;
                default:
                    // 对于不符合处理条件的类型，可以选择跳过
                    log.info("【splitHkCashAndFee】skipping unhandled business type: {}", changeDetailDto.getBusinessType());
                    break;
            }
            i++;
        }
        log.info("splitHkCashAndFee end...");
    }

    private void handleHkDividendAndFee(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos, int index,
                                        StatementTradeChangeDetailDto changeDetailDto,
                                        FeeNameEnum feeNameEnum,
                                        StatementBusinessTypeEnum statementBusinessTypeEnum) {
        // 读取费用
        BigDecimal fee = BigDecimal.ZERO;
        Map<String, StatementFeeDto> feeMap = changeDetailDto.getFeeMap();
        for (Map.Entry<String, StatementFeeDto> entry : feeMap.entrySet()) {
            if (entry.getKey().startsWith(feeNameEnum.getName())) {
                fee = new BigDecimal(entry.getValue().getFee());
                break;
            }
        }

        // 如果没有费用，则无需拆分，直接跳过
        if (fee.compareTo(BigDecimal.ZERO) == 0) {
            log.info("【handleHkDividendAndFee】skipping without fee: {}", changeDetailDto.getBusinessType());
            return;
        }

        // 计算派发总金额，并更新原元素
        BigDecimal grossAmount = new BigDecimal(changeDetailDto.getAmount()).add(fee.abs());
        changeDetailDto.setAmount(grossAmount.toPlainString());

        // 复制一条数据，作为费用数据，并添加到源列表中
        StatementTradeChangeDetailDto feeDto = new StatementTradeChangeDetailDto();
        BeanUtils.copyProperties(changeDetailDto, feeDto);
        feeDto.setBusinessType(statementBusinessTypeEnum.getBusinessType());
        feeDto.setAmount(fee.toPlainString());

        // 添加到源列表的特定位置（可以选择添加到当前元素之后）
        statementTradeChangeDetailDtos.add(index + 1, feeDto);

        log.info("【handleHkDividendAndFee】split fee: {}", changeDetailDto.getBusinessType());
    }


    /**
     * 拆分港股回购数据
     *
     * @param statementTradeChangeDetailDtos
     * @param index
     * @param changeDetailDto
     */
    private void fillHkBuyBackAndFee(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos, int index,
                                     StatementTradeChangeDetailDto changeDetailDto) {
        //读取费用
        BigDecimal stampDuty = BigDecimal.ZERO;
        BigDecimal cashCollectionCharge = BigDecimal.ZERO;
        Map<String, StatementFeeDto> feeMap = changeDetailDto.getFeeMap();
        for (Map.Entry<String, StatementFeeDto> entry : feeMap.entrySet()) {
            String key = entry.getKey();
            StatementFeeDto entryValue = entry.getValue();
            if (key.startsWith(FeeNameEnum.STAMP_DUTY.getName())) {
                stampDuty = new BigDecimal(entryValue.getFee());
            }
            if (key.startsWith(FeeNameEnum.CASH_COLLECTION_CHARGE.getName())) {
                cashCollectionCharge = new BigDecimal(entryValue.getFee());
            }
        }
        BigDecimal feeSum = stampDuty.abs().add(cashCollectionCharge.abs());
        // 如果没有费用，则无需拆分，直接跳过
        if (feeSum.compareTo(BigDecimal.ZERO) == 0) {
            log.info("【fillHkBuyBackAndFee】skipping without fee: {}", changeDetailDto.getBusinessType());
            return;
        }
        //计算派发净金额
        BigDecimal netAmount = new BigDecimal(changeDetailDto.getAmount()).subtract(feeSum);
        //派息数据把金额更改为净金额
        changeDetailDto.setAmount(netAmount.toPlainString());

        int newIndex = index;
        //复制一条数据，做为费用数据
        if (stampDuty.abs().compareTo(BigDecimal.ZERO) > 0) {
            StatementTradeChangeDetailDto stampFeeDto = new StatementTradeChangeDetailDto();
            BeanUtils.copyProperties(changeDetailDto, stampFeeDto);
            stampFeeDto.setBusinessType(StatementBusinessTypeEnum.TPA_F1.getBusinessType());
            stampFeeDto.setAmount(stampDuty.toPlainString());

            // 添加到源列表的特定位置（可以选择添加到当前元素之后）
            newIndex = newIndex + 1;
            statementTradeChangeDetailDtos.add(newIndex, stampFeeDto);
            log.info("【fillHkBuyBackAndFee】split COSD fee: {}", changeDetailDto.getBusinessType());
        }

        //复制一条数据，做为费用数据
        if (cashCollectionCharge.abs().compareTo(BigDecimal.ZERO) > 0) {
            StatementTradeChangeDetailDto feeDto = new StatementTradeChangeDetailDto();
            BeanUtils.copyProperties(changeDetailDto, feeDto);
            feeDto.setBusinessType(StatementBusinessTypeEnum.TPA_F2.getBusinessType());
            feeDto.setAmount(cashCollectionCharge.toPlainString());

            // 添加到源列表的特定位置（可以选择添加到当前元素之后）
            newIndex = newIndex + 1;
            statementTradeChangeDetailDtos.add(newIndex, feeDto);
            log.info("【fillHkBuyBackAndFee】split COCC fee: {}", changeDetailDto.getBusinessType());
        }
    }

    @Override
    public void dealHkRightsDividendData(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos, boolean isDividend) {
        log.info("dealHkRightsDividendData start isDividend:{}",isDividend);
        // 使用Iterator进行安全移除
        Iterator<StatementTradeChangeDetailDto> iterator = statementTradeChangeDetailDtos.iterator();
        while (iterator.hasNext()) {
            StatementTradeChangeDetailDto tradeChangeDetailDto = iterator.next();

            // 检查业务类型是否在港股供股业务列表中
            if (!StatementBusinessTypeEnum.getHkActionRightsBusinessList().contains(tradeChangeDetailDto.getBusinessType())) {
                // 如果不在列表中，跳过当前元素
                continue;
            }

            // 移除供股派发的数据
            if (StatementBusinessTypeEnum.EAL.getBusinessType().equals(tradeChangeDetailDto.getBusinessType())) {
                if(isDividend){
                    if (null == tradeChangeDetailDto.getAmount() && null != tradeChangeDetailDto.getQty()) {
                        // 直接在原始列表上移除元素
                        iterator.remove();
                        continue;
                    }
                    //重新赋值供股退款的数据
                    if (null == tradeChangeDetailDto.getQty() && null != tradeChangeDetailDto.getAmount()) {
                        tradeChangeDetailDto.setBusinessType(StatementBusinessTypeEnum.EAL_R.getBusinessType());
                    }
                }else{
                    //移除供股退款的数据
                    if (null == tradeChangeDetailDto.getQty() && null != tradeChangeDetailDto.getAmount()) {
                        // 直接在原始列表上移除元素
                        iterator.remove();
                        continue;
                    }
                }
            }

            // 处理剩余元素
            String referenceNo = tradeChangeDetailDto.getReferenceNo();
            String accNo = tradeChangeDetailDto.getAccNo();

            StkActionPlan stkActionPlan = stockTradeManager.queryActionPlanBySubscriptionId(accNo, referenceNo);

            //识别出涡轮牛熊回收数据
            if (ActionTypeEnum.WARRANT_CBBC.getValue().equals(stkActionPlan.getActivityType())) {
                tradeChangeDetailDto.setBusinessType(StatementBusinessTypeEnum.CBBCED.getBusinessType());
            }
            tradeChangeDetailDto.setRemark(stkActionPlan.getRemark());

        }
        log.info("dealHkRightsDividendData end");
    }


    @Override
    public String buildHkActionBusinessDesc(StatementTradeChangeDetailDto changeDetailDto, StatementBusinessTypeEnum statementBusinessTypeEnum, String docLang) {
        // 不属于特定业务类型，则走默认拼接逻辑
        if (!StatementBusinessTypeEnum.getHkActionRebildEventTypeBusinessList().contains(statementBusinessTypeEnum.getBusinessType())) {
            return buildDefaultBusinessDesc(statementBusinessTypeEnum, docLang);
        }

        String remark = changeDetailDto.getRemark();
        StatementActionEventTypeEnum eventTypeEnum = buildHkDividendRightsEventTypeEnum(remark, statementBusinessTypeEnum);

        if (eventTypeEnum == null) {
            return buildDefaultBusinessDesc(statementBusinessTypeEnum, docLang);
        }

        StringBuilder description = new StringBuilder();

        if (StatementBusinessTypeEnum.ESB_F.getBusinessType().equals(statementBusinessTypeEnum.getBusinessType())) {
            String businessTypeStart = getLocalizedTypeName(eventTypeEnum, docLang);
            String businessTypeEnd = getLocalizedBusinessTypeDesc(StatementBusinessTypeEnum.ESB_F, docLang);
            description.append(eventTypeEnum.getTypeNameEn())
                    .append("-")
                    .append(StatementBusinessTypeEnum.ESB_F.getDescEn())
                    .append(" ")
                    .append(businessTypeStart)
                    .append("-")
                    .append(businessTypeEnd);
        } else {
            String localizedTypeName = getLocalizedTypeName(eventTypeEnum, docLang);
            description.append(eventTypeEnum.getTypeNameEn())
                    .append(" ")
                    .append(localizedTypeName);
        }

        return description.toString();
    }

    @Override
    public String buildHkActionRecycleBusinessDesc(StatementTradeChangeDetailDto changeDetailDto, StatementBusinessTypeEnum statementBusinessTypeEnum, String docLang) {
        StatementActionEventTypeEnum eventTypeEnum = changeDetailDto.getEventTypeEnum();
        if (changeDetailDto.getEventTypeEnum() == null) {
            return buildDefaultBusinessDesc(statementBusinessTypeEnum, docLang);
        }
        String localizedTypeName = getLocalizedTypeName(eventTypeEnum, docLang);

        String description = eventTypeEnum.getTypeNameEn() +
                " " +
                localizedTypeName;

        return description;
    }

    /**
     * 构建港股供股事件类型枚举
     * @param remark
     * @param statementBusinessTypeEnum
     * @return
     */
    private StatementActionEventTypeEnum buildHkDividendRightsEventTypeEnum(String remark, StatementBusinessTypeEnum statementBusinessTypeEnum) {
        // 将remark转换为大写，以便进行前缀匹配
        String upperRemark = remark == null ? "" : remark.toUpperCase();

        String rightsIssuePrefix = systemConfig.getActionRightsIssueTypeRemark().toUpperCase();
        String openOfferPrefix = systemConfig.getActionOpenOfferTypeRemark().toUpperCase();
        String preferentialOfferPrefix = systemConfig.getActionPreferentialOfferTypeRemark().toUpperCase();
        String equityWarrantPrefix = systemConfig.getActionEquityWarrantTypeRemark().toUpperCase();

        // 根据业务类型返回默认的枚举类型或基于remark前缀匹配的枚举类型
        switch (statementBusinessTypeEnum) {
            case ESB:
            case ESB_F:
                // 创建前缀到枚举类型的映射
                Map<String, StatementActionEventTypeEnum> rightsSubcriptionEventTypeMap = new HashMap<>();
                rightsSubcriptionEventTypeMap.put(rightsIssuePrefix, StatementActionEventTypeEnum.RIGHTS_SUBSCRIPTION);
                rightsSubcriptionEventTypeMap.put(openOfferPrefix, StatementActionEventTypeEnum.OPEN_OFFER_SUBSCRIPTION);
                rightsSubcriptionEventTypeMap.put(preferentialOfferPrefix, StatementActionEventTypeEnum.PREFERENTIAL_OFFER_SUBSCRIPTION);
                rightsSubcriptionEventTypeMap.put(equityWarrantPrefix, StatementActionEventTypeEnum.EQUITY_WARRANT_SUBSCRIPTION);

                return upperRemark.isEmpty() ? StatementActionEventTypeEnum.RIGHTS_SUBSCRIPTION : getEventTypeFromPrefix(rightsSubcriptionEventTypeMap, upperRemark);
            case EAL_R:
                // 创建前缀到枚举类型的映射
                Map<String, StatementActionEventTypeEnum> rightsRefundEventTypeMap = new HashMap<>();
                rightsRefundEventTypeMap.put(rightsIssuePrefix, StatementActionEventTypeEnum.RIGHTS_ISSUE_REFUNDS);
                rightsRefundEventTypeMap.put(openOfferPrefix, StatementActionEventTypeEnum.OPEN_OFFER_REFUNDS);
                rightsRefundEventTypeMap.put(preferentialOfferPrefix, StatementActionEventTypeEnum.PREFERENTIAL_OFFER_REFUNDS);
                rightsRefundEventTypeMap.put(equityWarrantPrefix, StatementActionEventTypeEnum.EQUITY_WARRANT_REFUNDS);

                return upperRemark.isEmpty() ? StatementActionEventTypeEnum.RIGHTS_ISSUE_REFUNDS : getEventTypeFromPrefix(rightsRefundEventTypeMap, upperRemark);
            case EAL:
                // 创建前缀到枚举类型的映射
                Map<String, StatementActionEventTypeEnum> rightsAllotmentEventTypeMap = new HashMap<>();
                rightsAllotmentEventTypeMap.put(rightsIssuePrefix, StatementActionEventTypeEnum.RIGHTS_ISSUE_ALLOTMENT);
                rightsAllotmentEventTypeMap.put(openOfferPrefix, StatementActionEventTypeEnum.OPEN_OFFER_ALLOTMENT);
                rightsAllotmentEventTypeMap.put(preferentialOfferPrefix, StatementActionEventTypeEnum.PREFERENTIAL_OFFER_ALLOTMENT);
                rightsAllotmentEventTypeMap.put(equityWarrantPrefix, StatementActionEventTypeEnum.EQUITY_WARRANT_ALLOTMENT);

                return upperRemark.isEmpty() ? StatementActionEventTypeEnum.RIGHTS_ISSUE_ALLOTMENT : getEventTypeFromPrefix(rightsAllotmentEventTypeMap, upperRemark);
            case CBBCED:
            case CBBCE:
                Map<String, StatementActionEventTypeEnum> exerciseEventTypeMap = new HashMap<>();
                exerciseEventTypeMap.put(systemConfig.getActionDerivativeWarrantExercisedTypeRemark().toUpperCase(), StatementActionEventTypeEnum.DERIVATIVE_WARRANT_EXERCISED);
                exerciseEventTypeMap.put(systemConfig.getActionCbbcExercisedTypeRemark().toUpperCase(), StatementActionEventTypeEnum.CBBC_EXERCISED);

                return upperRemark.isEmpty() ? StatementActionEventTypeEnum.DERIVATIVE_WARRANT_EXERCISED : getEventTypeFromPrefix(exerciseEventTypeMap, upperRemark);
            case BRT:
            case BWT:
                // 创建前缀到枚举类型的映射
                Map<String, StatementActionEventTypeEnum> rightsIssueEventTypeMap = new HashMap<>();
                rightsIssueEventTypeMap.put(rightsIssuePrefix, StatementActionEventTypeEnum.RIGHTS_ISSUE);
                rightsIssueEventTypeMap.put(openOfferPrefix, StatementActionEventTypeEnum.OPEN_OFFER);
                rightsIssueEventTypeMap.put(preferentialOfferPrefix, StatementActionEventTypeEnum.PREFERENTIAL_OFFER);
                rightsIssueEventTypeMap.put(equityWarrantPrefix, StatementActionEventTypeEnum.EQUITY_WARRANT);

                return upperRemark.isEmpty() ? StatementActionEventTypeEnum.RIGHTS_ISSUE : getEventTypeFromPrefix(rightsIssueEventTypeMap, upperRemark);
            default:
                return null;
        }
    }

    // 根据前缀从映射中获取枚举类型，如果未找到则返回null
    private StatementActionEventTypeEnum getEventTypeFromPrefix(Map<String, StatementActionEventTypeEnum> prefixToEventTypeMap, String upperRemark) {
        for (Map.Entry<String, StatementActionEventTypeEnum> entry : prefixToEventTypeMap.entrySet()) {
            if (upperRemark.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    private String buildDefaultBusinessDesc(StatementBusinessTypeEnum statementBusinessTypeEnum, String docLang) {
        String localizedDesc = I18nSupportEnum.CN_ZH.getName().equals(docLang) ? statementBusinessTypeEnum.getDesc() : statementBusinessTypeEnum.getDescHk();
        return statementBusinessTypeEnum.getDescEn() + " " + localizedDesc;
    }

    private String getLocalizedTypeName(StatementActionEventTypeEnum eventTypeEnum, String docLang) {
        return I18nSupportEnum.CN_ZH.getName().equals(docLang) ? eventTypeEnum.getTypeNameCn() : eventTypeEnum.getTypeNameHk();
    }

    private String getLocalizedBusinessTypeDesc(StatementBusinessTypeEnum businessTypeEnum, String docLang) {
        return I18nSupportEnum.CN_ZH.getName().equals(docLang) ? businessTypeEnum.getDesc() : businessTypeEnum.getDescHk();
    }

    @Override
    public List<StatementTradeChangeDetailDto> getActionRecycleData(String marketCode, String accountId, String period) {
        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;

        List<StkActionRecycleDetail> stkActionRecycleDetails = stockActionManager.queryActionRecycleDetailList(accountId, startDate, endDate);
        return stkActionRecycleDetails.stream().map(stkActionRecycleDetail -> {
            StatementTradeChangeDetailDto dto = new StatementTradeChangeDetailDto();
            dto.setBusinessType(StatementBusinessTypeEnum.CBBCFR.getBusinessType());
            dto.setQty(stkActionRecycleDetail.getHoldQty().toPlainString());
            dto.setStockCode(stkActionRecycleDetail.getStockCode());
            dto.setCurrency(CurrencyEnum.HKD.getCurrency());
            dto.setTradeDate(DateUtil.format(stkActionRecycleDetail.getBusinessTime(), DateUtil.FORMAT_SHORT));
            dto.setSourceStockCode(stkActionRecycleDetail.getStockCode());
            dto.setTtlMarketCode(MarketCodeEnum.HK.getTtlValue());
            dto.setBusinessId(stkActionRecycleDetail.getBusinessId());

            StatementActionEventTypeEnum actionRecycleEventTypeEnum = getActionRecycleEventTypeEnum(stkActionRecycleDetail.getCorporateType());
            dto.setEventTypeEnum(actionRecycleEventTypeEnum);

            return dto;
        }).collect(Collectors.toList());
    }

    private StatementActionEventTypeEnum getActionRecycleEventTypeEnum(String type){
        HkCorporationActionRecycleTypeEnum actionRecycleTypeEnum = BaseEnum.getEnumByType(type, HkCorporationActionRecycleTypeEnum.values());
        switch (actionRecycleTypeEnum) {
            case CBBC:
                return StatementActionEventTypeEnum.CBBC_FORCED_REDEMPTION;
            case EQUITY_WARRANT:
                return StatementActionEventTypeEnum.EQUITY_WARRANT_EXPIRATION;
            case RIGHT:
                return StatementActionEventTypeEnum.RIGHTS_ISSUE_LAPSED;
            case OPEN_OFFER:
                return StatementActionEventTypeEnum.OPEN_OFFER_LAPSED;
            case PREFERENTIAL_OFFER:
                return StatementActionEventTypeEnum.PREFERENTIAL_OFFER_LAPSED;
            case DERIVATIVE_WARRANT:
                return StatementActionEventTypeEnum.DERIVATIVE_WARRANT_EXPIRATION;
            default:
                return null;
        }
    }
}
