#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 532676608 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3828), pid=11292, tid=48736
#
# JRE version:  (21.0.5+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+8-b631.28, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 

Host: Intel(R) Core(TM) i5-10505 CPU @ 3.20GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Sat May 31 21:01:22 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 0.085607 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000138254f5c20):  JavaThread "Unknown thread" [_thread_in_vm, id=48736, stack(0x0000001faff00000,0x0000001fb0000000) (1024K)]

Stack: [0x0000001faff00000,0x0000001fb0000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e52b9]
V  [jvm.dll+0x8c3633]
V  [jvm.dll+0x8c5b8e]
V  [jvm.dll+0x8c6273]
V  [jvm.dll+0x288f46]
V  [jvm.dll+0x6e1a35]
V  [jvm.dll+0x6d5e3a]
V  [jvm.dll+0x3634bb]
V  [jvm.dll+0x36b086]
V  [jvm.dll+0x3bd346]
V  [jvm.dll+0x3bd618]
V  [jvm.dll+0x335c2c]
V  [jvm.dll+0x33691b]
V  [jvm.dll+0x88aa89]
V  [jvm.dll+0x3ca518]
V  [jvm.dll+0x873b98]
V  [jvm.dll+0x45eede]
V  [jvm.dll+0x460bc1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffe03d1a148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000138277b28b0 WorkerThread "GC Thread#0"                     [id=55776, stack(0x0000001fb0000000,0x0000001fb0100000) (1024K)]
  0x00000138277c5730 ConcurrentGCThread "G1 Main Marker"            [id=42756, stack(0x0000001fb0100000,0x0000001fb0200000) (1024K)]
  0x00000138277c8cf0 WorkerThread "G1 Conc#0"                       [id=39472, stack(0x0000001fb0200000,0x0000001fb0300000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe03408ce7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe03d8eb30] Heap_lock - owner thread: 0x00000138254f5c20

Heap address: 0x0000000604400000, size: 8124 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001383bbd0000,0x000001383cbb0000] _byte_map_base: 0x0000013838bae000

Marking Bits: (CMBitMap*) 0x00000138277b2ec0
 Bits: [0x000001383cbb0000, 0x0000013844aa0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.067 Loaded shared library C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7f6660000 - 0x00007ff7f666a000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.exe
0x00007ffeb6430000 - 0x00007ffeb6647000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffeb5260000 - 0x00007ffeb5324000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffeb3c30000 - 0x00007ffeb4003000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffeb3a90000 - 0x00007ffeb3ba1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe060d0000 - 0x00007ffe060e8000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jli.dll
0x00007ffeb59d0000 - 0x00007ffeb5b81000 	C:\WINDOWS\System32\USER32.dll
0x00007ffeb3a60000 - 0x00007ffeb3a86000 	C:\WINDOWS\System32\win32u.dll
0x00007ffeb5400000 - 0x00007ffeb5429000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffeb35b0000 - 0x00007ffeb36d2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe9e530000 - 0x00007ffe9e7cb000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffeb58b0000 - 0x00007ffeb5957000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffeb3820000 - 0x00007ffeb38ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe065d0000 - 0x00007ffe065eb000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\VCRUNTIME140.dll
0x00007ffeb5220000 - 0x00007ffeb5251000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe065f0000 - 0x00007ffe065fc000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\vcruntime140_1.dll
0x00007ffea1df0000 - 0x00007ffea1e7d000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\msvcp140.dll
0x00007ffe030c0000 - 0x00007ffe03e81000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\server\jvm.dll
0x00007ffeb5160000 - 0x00007ffeb5211000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffeb4350000 - 0x00007ffeb43f7000 	C:\WINDOWS\System32\sechost.dll
0x00007ffeb38c0000 - 0x00007ffeb38e8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffeb4ee0000 - 0x00007ffeb4ff4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffeb5330000 - 0x00007ffeb53a1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffeb2d90000 - 0x00007ffeb2ddd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe9ed50000 - 0x00007ffe9ed84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffeae5b0000 - 0x00007ffeae5ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffeb2d70000 - 0x00007ffeb2d83000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffeb2410000 - 0x00007ffeb2428000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe065c0000 - 0x00007ffe065ca000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\jimage.dll
0x00007ffea0520000 - 0x00007ffea0752000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffeb5b90000 - 0x00007ffeb5f23000 	C:\WINDOWS\System32\combase.dll
0x00007ffeb40a0000 - 0x00007ffeb4177000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe8a060000 - 0x00007ffe8a092000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffeb3bb0000 - 0x00007ffeb3c2b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe06480000 - 0x00007ffe064a0000 	C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\software2\ideaIU-2024.3.1.1.win\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;C:\software2\ideaIU-2024.3.1.1.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://gitlab.in.za': 
java_class_path (initial): C:/software2/ideaIU-2024.3.1.1.win/plugins/vcs-git/lib/git4idea-rt.jar;C:/software2/ideaIU-2024.3.1.1.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8518631424                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8518631424                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/software/Git/mingw64/libexec/git-core;C:/software/Git/mingw64/libexec/git-core;C:\software\Git\mingw64\bin;C:\software\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\software\Git\cmd;C:\Program Files\dotnet\;C:\software2\xshell\;C:\Program Files\nodejs\;C:\software2\python3\Scripts\;C:\software2\python3\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\software2\apache-maven-3.5.4\bin;C:\software2\protoc-21.5-win64\bin;C:\software2\python\Scripts;C:\software2\go\bin;C:\software2\windsurf\Windsurf\bin;C:\software2\Microsoft VS Code\bin
USERNAME=yingfeng.fu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12680K (0% of 33259564K total physical memory with 1901972K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 2 days 7:15 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0x100, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201

Memory: 4k page, system-wide physical 32480M (1857M free)
TotalPageFile size 93174M (AvailPageFile size 20M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 64M, peak: 572M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+8-b631.28) for windows-amd64 JRE (21.0.5+8-b631.28), built on 2024-11-23 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
