package group.za.bank.statement.entity.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @createTime 26 15:36
 * @description
 */
@Data
public class UserMonthlyStatementResp implements Serializable {

    /**
     * 年份
     */
    private String year;
    /**
     * 结单id
     */
    private String businessId;
    /**
     * 结单地址
     */
    private String docUrl;
    /**
     * 结单期数
     */
    private String period;
    /**
     * 结单语言
     */
    private String docLang;

    /**
     * 月结单状态，0-无月结的，1-已初始化
     */
    private String statementStatus;

    /**
     * 月结单状态生成日期
     */
    private String generateDate;


}
