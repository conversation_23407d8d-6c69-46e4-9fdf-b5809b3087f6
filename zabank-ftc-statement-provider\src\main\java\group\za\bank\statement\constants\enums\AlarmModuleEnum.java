package group.za.bank.statement.constants.enums;

import group.za.bank.invest.common.constants.enums.AlarmModule;
import lombok.AllArgsConstructor;

/**
 * 告警枚举类
 *
 * <AUTHOR>
 * @date 2022/05/26
 **/
@AllArgsConstructor
public enum AlarmModuleEnum implements AlarmModule {
    STATEMENT_FILE_DOWNLOAD_FAIL("statement_file_download_fail", "股票月结单文件下载失败"),
    STATEMENT_FILE_PARSE_FAIL("statement_file_parse_fail", "股票月结单文件解析失败");


    private final String value;
    private final String msg;

    @Override
    public String getValue() {
        return null;
    }

    @Override
    public String getMsg() {
        return null;
    }
}
