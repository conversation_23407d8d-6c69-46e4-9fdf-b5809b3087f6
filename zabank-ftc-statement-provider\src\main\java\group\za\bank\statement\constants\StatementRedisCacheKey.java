package group.za.bank.statement.constants;

import group.za.invest.cache.redis.base.RedisCacheKeyBuilder;
import lombok.Getter;


/**
 * <AUTHOR>
 * @Date  2021/11/18 14:51
 * @Description 交易redis前缀常量
 */
@Getter
public enum StatementRedisCacheKey implements RedisCacheKeyBuilder {


    // 月结单初始化
    MONTHLY_STATEMENT_INIT("fts:stm:monthlyStatementInit"),


    // 月结单推送
    MONTHLY_STATEMENT_PUB("fts:stm:monthlyStatementPub"),



    // 月结单业务初始化
    MONTHLY_STATEMENT_BUSINESS_INIT_STATUS("fts:stm:monthlyStatementBusinessInitStatus"),

    // 月结单业务数据准备状态
    MONTHLY_STATEMENT_BUSINESS_DATA_PREPARE_STATUS("fts:stm:monthlyStatementBusinessDataPrepareStatus"),

    // 无月结单用户
    MONTHLY_STATEMENT_NO_STATEMENT_USER("fts:stm:monthlyStatementNoStatementUser"),
    /**
     * 用户结单初始化
     */
    USER_MONTHLY_STATEMENT_INIT("fts:stm:userMonthlyStatementInit"),

    /**
     * 用户结单初始化(单个重新初始化)
     */
    USER_MONTHLY_STATEMENT_REBULID_INIT("fts:stm:userMonthlyStatementRebuildInit"),
    /**
     * 用户结单生成通知
     */
    USER_MONTHLY_STATEMENT_GENERATE("fts:stm:userMonthlyStatementGenerate"),


    /**
     * 用户结单推送
     */
    USER_MONTHLY_STATEMENT_PUB("fts:stm:userMonthlyStatementPub"),

    /**
     * 股票基本信息key
     */
    STK_INFO_CACHE_KEY("fts:stm:stk:info"),
    /**
     * 实时汇率信息key
     */
    RATE_INFO_CACHE_KEY("fts:stm:rate:info"),
    /**
     * 历史汇率信息key
     */
    RATE_INFO_HIS_CACHE_KEY("fts:stm:rate:info:his"),

    /**
     * 最新历史汇率信息key
     */
    RATE_INFO_HIS_LAST_CACHE_KEY("fts:stm:rate:info:his:last"),

    /**
     * 某月最后一个交易日
     */
    TRADECALENDAR_MONTH_LAST_TRADEDATE_CACHE_KEY("fts:stm:tradecalendar:month:last:tradedate"),
    /**
     * 某月最后一个交易日
     */
    TRADECALENDAR_MONTH_FIRST_TRADEDATE_CACHE_KEY("fts:stm:tradecalendar:month:first:tradedate"),


    /**
     * 月结单股票所用交易日历
     */
    STOCK_MONTHLY_STATEMENT_CALENDAR_KEY("fts:stm:stock:calendar"),

    /**
     * 公司行动基本信息
     */
    ACTION_INFO_CACHE_KEY("fts:stm:stock:action:info"),

    /**
     * 虚拟货币币对信息
     */
    CRYPTO_INFO_CACHE_KEY("fts:stm:crypto:info"),

    /**
     * 公司行动小数股信息列表查询
     */
    FRACTION_ACTION_INFO_LIST_QUERY("fts:stm:fraction_action_info_list_query"),
    /**
     * 公司行动小数股信息列表缓存
     */
    FRACTION_ACTION_INFO_LIST_CACHE("fts:stm:fraction_action_info_list_cache"),

    /**
     * 公司行动基本信息缓存
     */
    ACTION_INFO_CACHE("action_info_cache"),

    ;

    StatementRedisCacheKey(String prefix) {
        this.prefix = prefix;
    }


    private String prefix;
}
