package group.za.bank.statement.service;


import group.za.bank.sbs.business.model.resp.TransferDetailResp;
import group.za.bank.statement.constants.enums.StatementBusinessTypeEnum;
import group.za.bank.statement.entity.dto.StatementTradeChangeDetailDto;

import java.util.List;

/**
 * 股票月结单公司行动业务处理接口
 */
public interface StockStatementActionService {

    /**
     * 获取税费变动明细（ADR和FTT金融税）
     * @param accountId
     * @param period
     * @return
     */
    List<StatementTradeChangeDetailDto> getTaxTradeChangeDetailData(String marketCode, String accountId, String period);

    /**
     * 处理公司行动资金入账变动明细
     * @param period
     * @param bankUserId
     * @param accountId
     * @param tradeChangeDetailDtoList
     * @return
     */
    List<StatementTradeChangeDetailDto> filterAndAttachActionCapitalForPeriod(String marketCode, String period, String bankUserId, String accountId,
                                                                List<StatementTradeChangeDetailDto> tradeChangeDetailDtoList);

    /**
     * 重新构建换股类型
     * @param statementTradeChangeDetailDtos
     */
    void reBuildActionBusinessTypeData(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos);

    /**
     * 查询
     * @param bankUserId
     * @param accountId
     * @param period
     * @return
     */
    List<StatementTradeChangeDetailDto> getTransferFeeTradeChangeDetailData(String bankUserId, String accountId, String period);

    /**
     * 查询转仓明细列表
     * @param accountId
     * @param period
     * @return
     */
    List<TransferDetailResp> queryTransferDetailList(String accountId, String period);

    /**
     * 查询股息追缴和补偿数据
     * @param accountId
     * @param period
     * @return
     */

    List<StatementTradeChangeDetailDto> getDividendPressAndRebateDataByAccountId(String marketCode, String accountId, String period);


    /**
     * 查询资金转账数据
     * @param accountId
     * @param period
     * @return
     */
    List<StatementTradeChangeDetailDto> getBusinessCapitalData(String marketCode, String accountId, String period);


    /**
     * 查询业务股票变动数据
     * @param accountId
     * @param period
     * @return
     */
    List<StatementTradeChangeDetailDto> getBusinessStockMovementData(String marketCode, String accountId, String period);

    /**
     *  拆分港币派息和费用
     *
     * @param statementTradeChangeDetailDtos
     * @return
     */
    void splitHkCashAndFee(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos);

    /**
     * 处理港股供股数据
     * @param statementTradeChangeDetailDtos
     */
    void dealHkRightsDividendData(List<StatementTradeChangeDetailDto> statementTradeChangeDetailDtos, boolean isDividend);

    String buildHkActionBusinessDesc(StatementTradeChangeDetailDto changeDetailDto, StatementBusinessTypeEnum statementBusinessTypeEnum, String docLang);

    String buildHkActionRecycleBusinessDesc(StatementTradeChangeDetailDto changeDetailDto, StatementBusinessTypeEnum statementBusinessTypeEnum, String docLang);
    /**
     * 处理港股回收数据
     * @param marketCode
     * @param accountId
     * @param period
     * @return
     */
    List<StatementTradeChangeDetailDto> getActionRecycleData(String marketCode, String accountId, String period);

}
