package group.za.bank.statement.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ali oss配置类
 *
 * <AUTHOR>
 * @date 2023/08/07
 **/
@Data
@Component
@ConfigurationProperties(prefix = "statement.ali.oss")
public class AliOSSProperties {

    /**
     * endpoint
     */
    public String endPoint;
    /**
     * bucketName
     */
    public String bucketName;
    /**
     * accessKeyId
     */
    public String accessKeyId;
    /**
     * accessKeySecret
     */
    public String accessKeySecret;
    /**
     *  文件前缀，相当于目录
     */
    public String prefix;
}
