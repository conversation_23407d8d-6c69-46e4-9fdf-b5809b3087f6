<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdFundMonthlyStatement">
        <id column="id" jdbcType="BIGINT"
            property="id"/>
        <result column="creator" jdbcType="VARCHAR"
                property="creator"/>
        <result column="gmt_created" jdbcType="TIMESTAMP"
                property="gmtCreated"/>
        <result column="modifier" jdbcType="VARCHAR"
                property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP"
                property="gmtModified"/>
        <result column="is_deleted" jdbcType="CHAR"
                property="isDeleted"/>
        <result column="business_id" jdbcType="VARCHAR"
                property="businessId"/>
        <result column="client_id" jdbcType="VARCHAR"
                property="clientId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="period" jdbcType="CHAR"
                property="period"/>
        <result column="business_type" jdbcType="CHAR"
                property="businessType"/>
        <result column="bank_user_id" jdbcType="VARCHAR"
                property="bankUserId"/>
        <result column="doc_status" jdbcType="TINYINT"
                property="docStatus"/>
        <result column="pub_status" jdbcType="TINYINT"
                property="pubStatus"/>
        <result column="doc_lang" jdbcType="VARCHAR"
                property="docLang"/>
        <result column="temp_key" jdbcType="VARCHAR"
                property="tempKey"/>
        <result column="html_url" jdbcType="VARCHAR"
                property="htmlUrl"/>
        <result column="doc_url" jdbcType="VARCHAR"
                property="docUrl"/>
        <result column="pub_time" jdbcType="TIMESTAMP"
                property="pubTime"/>
        <result column="doc_time" jdbcType="TIMESTAMP"
                property="docTime"/>
        <result column="record_time" jdbcType="TIMESTAMP"
                property="recordTime"/>
        <result column="remark" jdbcType="VARCHAR"
                property="remark"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_id, period,business_type, bank_user_id,client_id, channel, doc_status, pub_status,
        temp_key,doc_lang,doc_url,html_url, pub_time, doc_time,
        record_time, remark, creator, gmt_created,
        modifier, gmt_modified, is_deleted
    </sql>

    <!-- 自定义通用SQL查询条件 -->
    <sql id="Where_Extra_Condition">
    </sql>

    <!--未推送的结单记录-->
    <select id="queryDocUnPublishedStatementList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        td_fund_monthly_statement
        WHERE business_id IN
        (SELECT
        business_id
        FROM
        `td_fund_monthly_statement`
        WHERE is_deleted = 'N'
        AND doc_status = 2
        <if test="latestId != null">
            <![CDATA[ AND id > #{latestId} ]]>
        </if>
        AND period = #{period}
        GROUP BY period,
        bank_user_id, client_id
        <![CDATA[ HAVING MAX(pub_status) < 2 AND COUNT(*)>1 ) ]]>
        ORDER BY id
        <if test="pageSize != null">
            <![CDATA[ LIMIT #{pageSize} ]]>
        </if>
    </select>


    <!--未完成的结单记录-->
    <select id="countDocUnFinishedStatement" resultType="int">
        SELECT
         count(1)
        FROM
        `td_fund_monthly_statement`
        WHERE is_deleted='N'
        <![CDATA[ AND doc_status  <> 2  ]]>
        AND period = #{period}
        AND business_type = #{business}
    </select>

    <!--未生成的结单记录-->
    <select id="queryDocUnGeneratedStatementList"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_statement`
        WHERE is_deleted='N'
        AND doc_status = 1
        AND period = #{period}
        <if test="latestId != null">
            <![CDATA[ AND id > #{latestId} ]]>
        </if>
        ORDER BY id
        <if test="pageSize != null">
            <![CDATA[ LIMIT #{pageSize} ]]>
        </if>
    </select>

    <!--已初始化未通知生成-->
    <select id="queryDocUnNotifyStatementList"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_statement`
        WHERE is_deleted='N'
        AND doc_status = 3
        AND period = #{period}
        <if test="latestId != null">
            <![CDATA[ AND id > #{latestId} ]]>
        </if>
        ORDER BY id
        <if test="pageSize != null">
            <![CDATA[ LIMIT #{pageSize} ]]>
        </if>
    </select>


    <!--用户结单记录-->
    <select id="queryUserFundMonthlyStatementList"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `td_fund_monthly_statement`
        WHERE is_deleted='N'
        AND bank_user_id = #{bankUserId}
        AND client_id = #{clientId}
        <if test="period != null">
            <![CDATA[ AND period = #{period} ]]>
        </if>
        <if test="docLang != null">
            <![CDATA[ AND doc_lang = #{docLang} ]]>
        </if>
        <if test="year != null">
            <![CDATA[ AND period LIKE CONCAT(#{year},'%') ]]>
        </if>
        ORDER BY period DESC
    </select>

    <select id="countPublishSuccessAccountNumber"  resultType="int">
        SELECT
            COUNT(1)
        FROM
            (SELECT
                 client_id
             FROM
                 `td_fund_monthly_statement`
             WHERE period =  #{period}
               AND doc_status = 2 and pub_status = 2
             GROUP BY client_id) t
    </select>

    <select id="publishSuccessAccountNumberRemark"  resultType="group.za.bank.statement.domain.entity.dto.RemarkDto">
        SELECT
            remark, count(1) num
        FROM
            `td_fund_monthly_statement`
        WHERE
            period = #{period}
          AND doc_status = 2
          AND pub_status = 2
          AND remark is not null
        GROUP BY remark
    </select>

    <!--用户结单记录-->
    <select id="queryPubMonthlyStatement" resultMap="BaseResultMap">
        SELECT
        t1.*
        FROM
        `td_fund_monthly_statement` t1
        WHERE t1.is_deleted='N'
        AND t1.client_id = #{clientId}
        AND t1.doc_status = 2
        <if test="docLang != null">
            <![CDATA[ AND t1.doc_lang = #{docLang} ]]>
        </if>
        AND t1.period in
            (
            SELECT
            DISTINCT t2.period
            FROM
            `td_fund_monthly_statement` t2
            WHERE t2.is_deleted='N'
            AND t2.client_id = #{clientId}
            AND t2.pub_status = 2
            AND t2.doc_status = 2
            AND t2.period in
            <foreach collection="periods" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        ORDER BY t1.period ASC
    </select>

    <select id="queryStatementByPeriodAndClientId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        td_fund_monthly_statement
        WHERE
        period = #{period}
        AND doc_status = '2'
        AND is_deleted = 'N'
        AND client_id in
        <foreach collection="clientIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND id > #{id}
        order by id
        limit #{pageSize}
    </select>
</mapper>
