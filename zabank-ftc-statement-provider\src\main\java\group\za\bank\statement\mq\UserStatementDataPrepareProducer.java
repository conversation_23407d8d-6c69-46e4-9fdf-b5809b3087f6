package group.za.bank.statement.mq;

import group.za.bank.invest.common.utils.IdWorker;
import group.za.bank.statement.entity.UserMonthlyStatementDataPrepareDto;
import group.za.invest.rabbitmq.base.BaseRabbitMqProducer;
import group.za.invest.rabbitmq.entity.BaseMqMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.stereotype.Component;

import static group.za.bank.statement.constants.MqConstants.USER_MONTHLY_STATEMENT_DATA_PREPARE_EXCHANGE;
import static group.za.bank.statement.constants.MqConstants.USER_MONTHLY_STATEMENT_DATA_PREPARE_ROUTE_KEY;

/**
 * */
@Component
@Slf4j
public class UserStatementDataPrepareProducer extends BaseRabbitMqProducer<UserMonthlyStatementDataPrepareDto> {

    @Override
    public void send(BaseMqMsg<UserMonthlyStatementDataPrepareDto> msg) {
        // 发送MQ
        rabbitTemplate.convertAndSend(USER_MONTHLY_STATEMENT_DATA_PREPARE_EXCHANGE
                , USER_MONTHLY_STATEMENT_DATA_PREPARE_ROUTE_KEY,
                msg, message -> {
                    message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    return message;
                }, new CorrelationData(IdWorker.idworker.nextIdStr()));
    }




    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        if (ack) {
            log.info("消息发送到exchange成功,{}", correlationData);
        } else {
            log.info("消息发送到exchange失败,原因: {}", cause);
        }
    }
}
