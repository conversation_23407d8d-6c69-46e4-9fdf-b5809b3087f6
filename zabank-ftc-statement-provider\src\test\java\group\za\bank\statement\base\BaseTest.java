package group.za.bank.statement.base;

import com.zatech.bank.act.BusinessMessageProducer;
import com.zatech.bank.act.repository.dao.ActivityMessageDao;
import group.za.bank.statement.common.config.SystemConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import redis.embedded.RedisServer;

import java.util.Map;

@Slf4j
@MapperScan(basePackages = {"group.za.bank..*.mapper"})
@RunWith(SpringRunner.class)
@WebAppConfiguration
@ActiveProfiles("test")
@Ignore
public class BaseTest {

    protected MockMvc mockMvc;

    @Autowired
    protected WebApplicationContext webApplicationContext;

    protected static WebApplicationContext APPLICATION_CONTEXT;



    @MockBean
    protected BusinessMessageProducer businessMessageProducer;

    @MockBean
    protected ActivityMessageDao activityMessageDao;

    protected static RedisServer redisServer;

    @MockBean
    protected SystemConfig systemConfig;



    static {
        System.setProperty("env", "test");
    }

    @BeforeClass
    public static void initRedis() {

        System.out.println("init redis server...");
        if (redisServer == null) {
            redisServer = RedisServer.builder().port(6379).setting("maxmemory 128M") // maxheap 128M
                    .setting("requirepass sbstrade").build();
            redisServer.start();
        }
    }

    @AfterClass
    public static void closeRedis() {
        System.out.println("close redis server");
        if (redisServer != null) {
            redisServer.stop();
            redisServer = null;
        }
    }

    @Before
    public void setUp() throws Exception {
        // 初始化测试用例类中由Mockito的注解标注的所有模拟对象
        MockitoAnnotations.initMocks(this);

        APPLICATION_CONTEXT = webApplicationContext;

        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }




    public MvcResult testPut(String url, String requestStr) {
        try {
            MvcResult mvcResult = mockMvc
                    .perform(MockMvcRequestBuilders.post(url).contentType(MediaType.APPLICATION_JSON).content(requestStr))
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk())
                    // .andExpect(jsonPath("$.code",is("0000")))
                    .andReturn();

            return mvcResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public MvcResult testPut(String url, String requestStr, Map<String, String> headMap) {
        try {

            HttpHeaders heads = new HttpHeaders();
            if (headMap != null) {
                for (String key : headMap.keySet()) {
                    String value = headMap.get(key);
                    heads.add(key, value);
                }
            }
            MvcResult mvcResult = mockMvc
                    .perform(MockMvcRequestBuilders.post(url).contentType(MediaType.APPLICATION_JSON).content(requestStr)
                            .headers(heads))
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk())
                    // .andExpect(jsonPath("$.code",is("0000")))
                    .andReturn();

            return mvcResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
