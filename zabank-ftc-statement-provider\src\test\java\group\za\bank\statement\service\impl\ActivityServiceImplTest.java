package group.za.bank.statement.service.impl;

import com.zatech.act.share.dto.request.business.ActivityBusinessDTO;
import com.zatech.bank.act.BusinessMessageProducer;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ActivityServiceImplTest {

    @InjectMocks
    private ActivityServiceImpl activityService;

    @Mock
    private BusinessMessageProducer businessMessageProducer;

    private static final String TEST_USER_ID = "1458074522581205504";
    private static final String TEST_ACCOUNT_ID = "A00002105";
    private static final String TEST_PERIOD = "202206";
    private static final String TEST_BUSINESS_TYPE = "fund,stock";

    @Test
    public void testMonthlyStatementActivityPub() {
        // Given
        doNothing().when(businessMessageProducer).sendBusinessMessageDirectly(any(ActivityBusinessDTO.class), anyBoolean());
        
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setBusinessType(TEST_BUSINESS_TYPE);

        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setDocLang(I18nSupportEnum.CN_HK.getName());
        statement.setPeriod(TEST_PERIOD);
        statement.setBankUserId(TEST_USER_ID);
        statement.setBusinessId("TEST_BUSINESS_ID");

        BaseMonthlyStatementDataDto dataDto = new BaseMonthlyStatementDataDto();
        dataDto.setAccountNo(TEST_ACCOUNT_ID);
        dataDto.setTotalAmountValue(new BigDecimal("1000.00"));
        dataDto.setTotalHoldingProfit(new BigDecimal("100.00"));

        // When
        activityService.monthlyStatementActivityPub(statementInfo, statement, dataDto);

        // Then
        verify(businessMessageProducer, times(1)).sendBusinessMessageDirectly(any(ActivityBusinessDTO.class), anyBoolean());
    }
}
