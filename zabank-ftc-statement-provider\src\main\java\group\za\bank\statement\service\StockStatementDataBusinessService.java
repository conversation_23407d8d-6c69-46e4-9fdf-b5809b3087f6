package group.za.bank.statement.service;

import group.za.bank.sbs.trade.model.dto.CompanyActionDetailInfoDTO;
import group.za.bank.sbs.trade.model.dto.CompanyActionExerciseDetailDTO;
import group.za.bank.statement.domain.entity.TdStockStatementData;

import java.util.Date;
import java.util.List;

/**
 * 股票结单数据业务处理类
 *
 * <AUTHOR>
 * @date 2024/04/25
 **/
public interface StockStatementDataBusinessService {

    /**
     * 自动构建订单数据
     * @param businessId
     * @param statementDate
     * @param tradeDate
     */
    List<TdStockStatementData> localGenerationOrderData(String businessId, String statementDate, Date tradeDate);

    /**
     * 自动构建交易变动数据
     * @param businessId
     * @param statementDate
     * @param tradeDate
     */
    List<TdStockStatementData> localGenerationTradeChangeData(String businessId, String statementDate, Date tradeDate);

    /**
     * 自动构建持仓数据
     * @param businessId
     * @param statementDate
     * @param tradeDate
     */
    List<TdStockStatementData> localGenerationHoldingData(String businessId, String statementDate, Date tradeDate);

    /**
     * 构建派息数据
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param dividendList
     * @return
     */
    List<TdStockStatementData> generationTradeChangeDividend(String businessId, String statementDate,
                                                             Date tradeDate, List<CompanyActionDetailInfoDTO> dividendList);

    /**
     * 构建小数股相关数据
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param fractionList
     * @return
     */
    List<TdStockStatementData> generationTradeChangeFraction(String businessId, String statementDate,
                                                             Date tradeDate, List<CompanyActionDetailInfoDTO> fractionList);

    /**
     * 构建exercise数据
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param exerciseDetailList
     * @return
     */
    List<TdStockStatementData> generationTradeChangeExercise(String businessId, String statementDate,
                                                             Date tradeDate, List<CompanyActionExerciseDetailDTO> exerciseDetailList);
}
