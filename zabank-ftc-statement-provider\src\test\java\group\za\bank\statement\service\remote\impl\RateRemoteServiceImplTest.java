package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.bankfront.model.resp.QueryRateListResp;
import group.za.bank.sbs.bankfront.model.resp.QueryRateResp;
import group.za.bank.sbs.trade.model.resp.feign.StkRateInfoHisResp;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.remote.feign.HistoryRateFeign;
import group.za.bank.statement.service.remote.feign.RateFeign;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class RateRemoteServiceImplTest extends BaseTestService {
    @Mock
    RateFeign rateFeign;
    @Mock
    HistoryRateFeign historyRateFeign;
    @Mock
    Logger log;
    @InjectMocks
    RateRemoteServiceImpl rateRemoteServiceImpl;


    @Test
    public void testQueryRate() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        QueryRateListResp queryRateListResp = new QueryRateListResp();
        queryRateListResp.setQueryRateRespList(Arrays.asList(new QueryRateResp()));
        resp.setValue(queryRateListResp);
        when(rateFeign.getRateList(any())).thenReturn(resp);
        QueryRateResp result = rateRemoteServiceImpl.queryRate("sourceCcy", "targetCcy");
    }

    @Test
    public void testQueryRateHis() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new StkRateInfoHisResp());
        when(historyRateFeign.getRateInfoHis(any())).thenReturn(resp);
        StkRateInfoHisResp result = rateRemoteServiceImpl.queryRateHis(null);
    }

    @Test
    public void testGetLatestHisRateInfo() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        resp.setValue(new StkRateInfoHisResp());
        when(historyRateFeign.getLatestHisRateInfo(any())).thenReturn(resp);
        StkRateInfoHisResp result = rateRemoteServiceImpl.getLatestHisRateInfo(null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme