package group.za.bank.statement.domain.entity;

import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 29 14:01
 * @description
 */
@Data
@Document(collection="td_monthly_statement_html_parsed_data")
@CompoundIndexes({
        @CompoundIndex(name = "idx_userId_clientId", def = "{'userId': 1, 'clientId': 1}")
})
public class TdMonthlyStatementHtmlParsedData {

    @Field("id")
    private String id;

    @Field("statementInfoId")
    private String statementInfoId;

    @Field("period")
    private String period;

    @Field("business")
    private String business;

    @Field("statementId")
    private String statementId;

    @Field("userId")
    private String userId;

    @Field("clientId")
    private String clientId;


    @Field("appLang")
    private String appLang;


    @Field("data")
    private String data;


    /**
     * 记录时间
     */
    @Field("recordDate")
    private Date recordDate;

}
