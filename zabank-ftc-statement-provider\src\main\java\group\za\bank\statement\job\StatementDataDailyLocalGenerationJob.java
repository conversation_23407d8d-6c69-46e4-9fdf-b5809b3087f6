package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.trade.model.resp.feign.TradeDateDiffResp;
import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.entity.req.StatementDataParseReq;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import group.za.invest.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * ttl日结单数据检查任务
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Component
@Slf4j
@JobHandler(value = "statementDataDailyLocalGenerationJob")
public class StatementDataDailyLocalGenerationJob extends IJobHandler {

    @Autowired
    private TdStatementFileRecordService statementFileRecordService;
    @Autowired
    private TdStockStatementDataService stockStatementDataService;

    @Autowired
    private TradeCalendarRemoteService tradeCalendarRemoteService;

    @Override
    public ReturnT<String> execute(String message) throws Exception {
        log.info("【statementDataDailyAutoBuildJob】 param:{}", message);

        if(StringUtils.isEmpty(message)){
            log.info("参数不能为空");
            return ReturnT.FAIL;
        }
        StatementDataParseReq req = JsonUtils.toJavaObject(message, StatementDataParseReq.class);

        //校验文件解析记录
        TdStatementFileRecord statementFileRecord = statementFileRecordService.checkFileRecord(StatementTypeEnum.DAILY.getType(), req.getStatementDate(), req.getRefresh(), null);

        //查询交易日
        TradeDateDiffResp tradeDateDiffResp = tradeCalendarRemoteService.tradeDateDiff(MarketCodeEnum.US.getValue(), req.getStatementDate(), -1);
        Date tradeDate = group.za.invest.common.utils.DateUtil.parse(tradeDateDiffResp.getTradeDate(), DateUtil.FORMATDAY);

        //自动构建结单数据
        stockStatementDataService.localGenerationStatementFileData(tradeDate, req.getStatementDate(), statementFileRecord);

        log.info("【statementDataDailyAutoBuildJob】 end");
        return ReturnT.SUCCESS;
    }
}
