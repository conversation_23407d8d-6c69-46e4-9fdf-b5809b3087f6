package group.za.bank.statement.utils;

import group.za.bank.fund.domain.trade.entity.TdFundOrder;
import group.za.bank.statement.domain.entity.TdFundMonthlyOrderTmp;
import org.junit.Test;

public class OrderBeanUtilsTest {

    @Test
    public void testCopyTdFundMonthlyOrderTmp() throws Exception {
        TdFundMonthlyOrderTmp result = OrderBeanUtils.copyTdFundMonthlyOrderTmp(new TdFundOrder());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme