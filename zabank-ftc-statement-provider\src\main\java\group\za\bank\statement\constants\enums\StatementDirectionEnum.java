package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.exception.BusinessException;
import lombok.Getter;

/**
 * 方向枚举，出账入账
 * <AUTHOR>
 * @date 2022/05/11
 **/
@Getter
public enum StatementDirectionEnum {
    /**
     * 结单买卖方向
     */
    BUY("BUY", "BUY 买入", "BUY 買入"),
    SELL("SELL", "SELL 卖出", "SELL 賣出"),
    ;

    private String tradeType;
    private String desc;
    private String descHk;

    StatementDirectionEnum(String tradeType, String desc, String descHk) {
        this.tradeType = tradeType;
        this.desc = desc;
        this.descHk = descHk;
    }

    public static StatementDirectionEnum getByTradeType(String tradeType){
        for(StatementDirectionEnum statementDirectionEnum : StatementDirectionEnum.values()){
            if(statementDirectionEnum.tradeType.equals(tradeType)){
                return statementDirectionEnum;
            }
        }
        throw new BusinessException(StatementErrorMsgEnum.FILE_TRADE_TYPE_ERROR);
    }
}
