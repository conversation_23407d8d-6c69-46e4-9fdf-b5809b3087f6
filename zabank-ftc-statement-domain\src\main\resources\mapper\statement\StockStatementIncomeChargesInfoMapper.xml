<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.StockStatementIncomeChargesInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.StockStatementIncomeChargesInfo">
        <id column="id" jdbcType="BIGINT"
            property="id"/>
        <result column="creator" jdbcType="VARCHAR"
                property="creator"/>
        <result column="gmt_created" jdbcType="TIMESTAMP"
                property="gmtCreated"/>
        <result column="modifier" jdbcType="VARCHAR"
                property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP"
                property="gmtModified"/>
        <result column="is_deleted" jdbcType="CHAR"
                property="isDeleted"/>
        <result column="biz_id" jdbcType="VARCHAR"
                property="bizId"/>
        <result column="parse_id" jdbcType="VARCHAR"
                property="parseId"/>
        <result column="user_id" jdbcType="VARCHAR"
                property="userId"/>
        <result column="stock_code" jdbcType="VARCHAR"
                property="stockCode"/>
        <result column="market_code" jdbcType="VARCHAR"
                property="marketCode"/>
        <result column="account_id" jdbcType="VARCHAR"
                property="accountId"/>
        <result column="statement_date" jdbcType="DATE"
                property="statementDate"/>

        <result column="stock_name_en" jdbcType="VARCHAR"
                property="stockNameEn"/>
        <result column="stock_name_hk" jdbcType="VARCHAR"
                property="stockNameHk"/>
        <result column="stock_name_zh" jdbcType="VARCHAR"
                property="stockNameZh"/>
        <result column="remark" jdbcType="VARCHAR"
                property="remark"/>
        <result column="bonus_qty" jdbcType="DECIMAL"
                property="bonusQty"/>
        <result column="currency" jdbcType="VARCHAR"
                property="currency"/>
        <result column="amount" jdbcType="DECIMAL"
                property="amount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_id, parse_id, user_id, account_id, statement_date, description_zh, description_hk, bonus_share, bonus_qty, currency, amount, creator, gmt_created, modifier, gmt_modified, is_deleted
    </sql>

    <!-- 自定义通用SQL查询条件 -->
    <sql id="Where_Extra_Condition">
    </sql>
</mapper>
