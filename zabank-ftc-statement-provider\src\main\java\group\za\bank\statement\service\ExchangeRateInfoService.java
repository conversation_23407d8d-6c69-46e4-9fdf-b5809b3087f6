package group.za.bank.statement.service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/3/15
 * @Description 换汇业务
 * @Version v1.0
 */
public interface ExchangeRateInfoService {

    /**
     * 初始化汇率
     */
    void initExchangeRate(String period);

    /**
     * 获取兑换HKD的汇率
     */
    BigDecimal getExchangeHkdRate(String sourceCcy, String period, String businessType);

    /**
     * 设置汇率到redis
     */
    void setExchangeHkdRateToRedis(String sourceCcy, String redisKey, BigDecimal rate);
}