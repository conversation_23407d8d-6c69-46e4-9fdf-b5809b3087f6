package group.za.bank.statement.service.impl;

import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.constants.enums.StatementFileStatusEnum;
import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.domain.mapper.TdStatementFileRecordMapper;
import group.za.bank.statement.domain.mapper.TdStockStatementDataMapper;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.invest.common.utils.DateUtil;
import group.za.invest.common.utils.IdWorker;
import group.za.invest.mybatis.repository.service.impl.BaseCrudServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* This service implement access the database table td_statement_file_record
* <p>
* Database Table Remarks:
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 10:39:39
*/
@Service
@Slf4j
public class TdStatementFileRecordServiceImpl extends BaseCrudServiceImpl<TdStatementFileRecordMapper, TdStatementFileRecord> implements TdStatementFileRecordService {

    @Autowired
    private TdStatementFileRecordMapper statementFileRecordMapper;

    @Autowired
    private TdStockStatementDataMapper stockStatementDataMapper;



    /**
     * 查询月结单文件解析状态
     *
     * @param startDate yyyyMMdd
     * @param endDate   yyyyMMdd
     * @return
     */
    @Override
    public List<TdStatementFileRecord> queryDailyFileRecords(String startDate, String endDate) {
        return statementFileRecordMapper.queryDailyFileParseRecords(startDate,endDate);
    }


    /**
     * 查询月结单文件解析状态
     *
     * @param statementDate yyyyMMdd
     * @return
     */
    @Override
    public List<TdStatementFileRecord> queryMonthlyFileRecords(String statementDate) {
        return statementFileRecordMapper.queryMonthlyFileParseRecords(statementDate);
    }

    /**
     * @param statementType
     * @param statementDateString  yyyyMMdd
     * @param refresh
     * @param fileLastModifiedTime
     * @return
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager",rollbackFor = Exception.class)
    public TdStatementFileRecord checkFileRecord(Integer statementType, String statementDateString, Boolean refresh, Date fileLastModifiedTime) {
        Date statementDate = DateUtil.parse(statementDateString, DateUtil.FORMATDAY);
        TdStatementFileRecord condition = new TdStatementFileRecord();
        condition.setStatementDate(statementDate);
        condition.setStatementType(statementType);

        TdStatementFileRecord tdStatementFileRecord = statementFileRecordMapper.selectOne(condition);

        if (tdStatementFileRecord == null) {
            // 首次解析，则新增文件解析记录
            tdStatementFileRecord = new TdStatementFileRecord();
            tdStatementFileRecord.setId(IdWorker.idworker.nextId());
            tdStatementFileRecord.setStatementType(statementType);
            tdStatementFileRecord.setStatementDate(statementDate);
            tdStatementFileRecord.setFileLastModified(fileLastModifiedTime);
            tdStatementFileRecord.setDataStatus(StatementFileStatusEnum.ING.getFileStatus());

            int insert = statementFileRecordMapper.insert(tdStatementFileRecord);
            if (insert < 1) {
                log.error("保存文件记录错误, 类型：{}, 日期：{}", statementType, statementDate);
                throw new BusinessException(StatementErrorMsgEnum.FILE_RECORD_ERROR);
            }
        } else {
            // 检查是否需要重新解析（基于文件最后修改时间或强制刷新）
            boolean needsReparse = (refresh ||
                    (fileLastModifiedTime.compareTo(tdStatementFileRecord.getFileLastModified()) > 0) ||
                    StatementFileStatusEnum.FAIL.getFileStatus().equals(tdStatementFileRecord.getDataStatus()));

            if (!needsReparse && !StatementFileStatusEnum.ING.getFileStatus().equals(tdStatementFileRecord.getDataStatus())) {
                // 如果不需要重新解析且不在处理中，则抛出异常
                log.info("文件已解析完成或在处理中，status:{}", tdStatementFileRecord.getDataStatus());
                throw new BusinessException(StatementErrorMsgEnum.FILE_PARSE_REPEATED);
            }

            // 如果需要重新解析，则更新状态并删除原数据
            if (needsReparse) {
                tdStatementFileRecord.setDataStatus(StatementFileStatusEnum.ING.getFileStatus());
                tdStatementFileRecord.setFileLastModified(fileLastModifiedTime);
                statementFileRecordMapper.updateByPrimaryKey(tdStatementFileRecord);

                int delete = stockStatementDataMapper.deleteByBusinessId(tdStatementFileRecord.getId().toString());
                log.info("重新解析文件，删除原数据{}条", delete);
            }
        }

        return tdStatementFileRecord;
    }

    @Override
    public TdStatementFileRecord queryDailyFileRecord(Date statementDate) {
        TdStatementFileRecord condition = new TdStatementFileRecord();
        condition.setStatementType(StatementTypeEnum.DAILY.getType());
        condition.setStatementDate(statementDate);
        return statementFileRecordMapper.selectOne(condition);
    }
}
