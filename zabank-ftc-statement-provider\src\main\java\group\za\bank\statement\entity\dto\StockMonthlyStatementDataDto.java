package group.za.bank.statement.entity.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 25 11:45
 * @description
 */
@Data
public class StockMonthlyStatementDataDto {



    /**
     * 基本信息——baseInfo
     */
    private BasicInfoDto basicInfoDto;

    /**
     * 股票持仓——holdingList
     */
    private List<HoldingDto> holdingList = new ArrayList<>();

    /**
     * 已确认交易——confirmedOrderList
     */

    private List<ConfirmedOrderDto> confirmedOrderList = new ArrayList<>();


    /**
     * 派息——dividendOrderList
     */
    private List<DividendOrderDto> dividendOrderList = new ArrayList<>();

    /**
     * 持仓变动——holdingChangeList
     */
    private List<HoldingChangeDto> holdingChangeList = new ArrayList<>();


    @Getter
    @Setter
    @ToString
    public static class BasicInfoDto {
        //币种
        private String currency;
        //总市值，已保留两位小数并四舍五入
        private BigDecimal totalMarketValue;

        //持仓盈亏
        private BigDecimal holdingProfit;
    }


    @Getter
    @Setter
    @ToString
    public static class HoldingDto  extends StockBasicInfoDto {
        private String stockCode;
        private String stockName;
        private String stockEngName;

        //币种
        private String currency;
        //期初结余
        private String openingBalance;

        //期终结余
        private String closingBalance;

        //参考价格
        private String referencePrice;

        //市值
        private String marketValue;

        //港币市值
        private String hkdMarketValue;

    }

    @Getter
    @Setter
    @ToString
    public static class ConfirmedOrderDto  extends StockBasicInfoDto{
        private String stockCode;
        private String stockName;
        private String stockEngName;

        /** 买卖方向 */
        private String direction;
        /**
         * 交易日
         */
        private String tradeDate;
        /**
         * 交收日
         */
        private String clearDate;
        //说明
        private String description;
        private String engDescription;

        //币种
        private String currency;

        //单位数目
        private String qty;

        //单位价格
        private String avgPrice;

        //佣金
        private String commission;

        //证监会规费
        private String secFee;

        //交易费
        private String finraFee;

        //豁免金额
        private String waivedAmount;


        //交收金额
        private String amount;

        //印花税
        private String stampDuty;

        //证监会交徵费
        private String sfcTransactionLevy;

        //会财局交易徵费
        private String afrcTransactionLevy;

        //交收费
        private String hkexClearingFee;

        //交易费
        private String hkexTradingFee;

        //成交明细
        private List<OrderDetailDto> details;
    }



    @Getter
    @Setter
    @ToString
    public static class DividendOrderDto  extends StockBasicInfoDto{
        private String stockCode;
        private String stockName;
        private String stockEngName;

        //出账或入账
        private String direction;

        //@JsonFormat(pattern = "yyyy/MM/dd")
        private String tradeDate;
        //说明
        private String description;
        private String engDescription;


        //单位数目
        private String qty;
        //币种
        private String currency;
        //金额
        private String amount;

        //备注
        private String remark;

    }


    @Getter
    @Setter
    @ToString
    public static class HoldingChangeDto extends StockBasicInfoDto {
        private String stockCode;
        private String stockName;
        private String stockEngName;

        //出账或入账
        private String direction;

        private String tradeDate;
        //说明
        private String description;
        private String engDescription;
        //单位数目
        private String qty;

        //备注
        private String remark;

        //参考编号
        private String referenceNo;
    }



    @Getter
    @Setter
    @ToString
    public static class StockBasicInfoDto {
        private String exchangeCode;
        private String stockCode;
        private String stockName;
        private String stockEngName;

    }

    @Getter
    @Setter
    @ToString
    public static class OrderDetailDto{

        /** 序号 **/
        private String seq;

        /** 成交数量 **/
        private String transQty;

        /** 成交价 **/
        private String transPrice;

        /** 成交金额 **/
        private String transAmount;
    }

}
