package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import java.util.Date;
import java.math.BigDecimal;

import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * This class corresponds to the database table stock_statement_income_charges_info
 * <p>
 * Database Table Remarks: 
 * </p>
 *
 * <AUTHOR>
 * @since 2024年08月26日 15:39:59
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "stock_statement_income_charges_info")
public class StockStatementIncomeChargesInfo extends AuditIdEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Name: stock_statement_income_charges_info.biz_id
     * <p>
     * Database Column Remarks: 业务主键
     * </p>
     */
    @Column(name = "biz_id", nullable = false)
    private String bizId;

    /**
     * Database Column Name: stock_statement_income_charges_info.parse_id
     * <p>
     * Database Column Remarks: 解析记录表关联ID
     * </p>
     */
    @Column(name = "parse_id", nullable = false)
    private String parseId;

    /**
     * Database Column Name: stock_statement_income_charges_info.user_id
     * <p>
     * Database Column Remarks: 用户id
     * </p>
     */
    @Column(name = "user_id", nullable = false)
    private String userId;
    @Column(name = "stock_code", nullable = false)
    private String stockCode;
    @Column(name = "market_code", nullable = false)
    private String marketCode;

    /**
     * Database Column Name: stock_statement_income_charges_info.account_id
     * <p>
     * Database Column Remarks: 资金账号
     * </p>
     */
    @Column(name = "account_id", nullable = false)
    private String accountId;

    @Column(name = "business_type")
    private String businessType;

    /**
     * Database Column Name: stock_statement_income_charges_info.statement_date
     * <p>
     * Database Column Remarks: 结单日期
     * </p>
     */
    @Column(name = "statement_date", nullable = false)
    private Date statementDate;


    @Column(name = "stock_name_en")
    private String stockNameEn;

    /**
     * Database Column Name: stock_statement_income_charges_info.bonus_share
     * <p>
     * Database Column Remarks: 红股名称
     * </p>
     */
    @Column(name = "stock_name_hk")
    private String stockNameHk;
    @Column(name = "stock_name_zh")
    private String stockNameZh;
    @Column(name = "remark")
    private String remark;

    /**
     * Database Column Name: stock_statement_income_charges_info.bonus_qty
     * <p>
     * Database Column Remarks: 红股数量
     * </p>
     */
    @Column(name = "bonus_qty")
    private BigDecimal bonusQty;

    /**
     * Database Column Name: stock_statement_income_charges_info.currency
     * <p>
     * Database Column Remarks: 币种
     * </p>
     */
    @Column(name = "currency")
    private String currency;

    /**
     * Database Column Name: stock_statement_income_charges_info.amount
     * <p>
     * Database Column Remarks: 金额
     * </p>
     */
    @Column(name = "amount")
    private BigDecimal amount;


}