alter table za_bank_invest_statement.td_fund_monthly_statement drop index uk_client_business_period_doc;
alter table za_bank_invest_statement.td_fund_monthly_statement add index idx_client_business_period(business_type, period,client_id);

CREATE TABLE za_bank_invest_statement.`td_statement_action_pending_data`
(
    `id`                bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `action_id`         varchar(32) NOT NULL COMMENT '公司行动ID',
    `bank_user_id`      varchar(32) NOT NULL COMMENT '用户ID',
    `statement_data_id` bigint(20)  NOT NULL COMMENT '结单数据ID(用于关联原始结单数据)',
    `action_type`       varchar(4)  NOT NULL COMMENT '公司行动类型',
    `capital_time`      datetime             DEFAULT NULL COMMENT '资金处理成功时间',
    `original_period`   char(6)              DEFAULT NULL COMMENT '原始月结单期数:yyyyMM',
    `period`            char(6)              DEFAULT NULL COMMENT '所属月结单期数:yyyyMM',
    `data_status`       varchar(4)           DEFAULT '0' COMMENT '数据状态 0-未处理 1-已处理',
    `execute_time`      datetime             DEFAULT NULL COMMENT '处理时间',
    `remark`            varchar(500)         DEFAULT '' COMMENT '备注',
    `version`           int(10)     NOT NULL DEFAULT '0' COMMENT '版本控制',
    `creator`           varchar(32) NOT NULL DEFAULT 'system' COMMENT '创建者',
    `gmt_created`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`          varchar(32) NOT NULL DEFAULT 'system' COMMENT '修改者',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`        char(1)     NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`bank_user_id`, `action_id`) USING BTREE,
    KEY `idx_statement_data_id` (`statement_data_id`)
) ENGINE = InnoDB COMMENT ='月结单公司行动待处理数据';