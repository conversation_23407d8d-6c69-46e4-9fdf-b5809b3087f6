package group.za.bank.statement.service.impl;

import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.domain.mapper.TdStatementFileRecordMapper;
import group.za.bank.statement.domain.mapper.TdStockStatementDataMapper;
import group.za.invest.mybatis.table.TableInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TdStatementFileRecordServiceImplTest  {
    @Mock
    TdStatementFileRecordMapper statementFileRecordMapper;
    @Mock
    TdStockStatementDataMapper stockStatementDataMapper;
    @Mock
    Logger log;

    @Mock
    TableInfo tableInfo;
    @Mock
    Map<String, String> fieldColumns;

    @InjectMocks
    TdStatementFileRecordServiceImpl tdStatementFileRecordServiceImpl;


    @Test
    public void testQueryMonthlyFileStatus() {
         tdStatementFileRecordServiceImpl.queryDailyFileRecords("********","********");
    }

    @Test
    public void testCheckFileRecord() {
        when(statementFileRecordMapper.insert(any())).thenReturn(1);
        tdStatementFileRecordServiceImpl.checkFileRecord(StatementTypeEnum.MONTHLY.getType(), "202205", Boolean.TRUE, null);
    }
}