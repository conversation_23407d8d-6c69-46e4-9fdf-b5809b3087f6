package group.za.bank.statement.common.config;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    private static final String PREFIX_STATEMENT_EXECUTOR = "statement-executor-";

    /**
     * 通用的线程池
     */
    @Bean(name = "statementExecutor")
    public ThreadPoolTaskExecutor statementExecutor() {
        return initStatementExecutor(5, 5, 2048, PREFIX_STATEMENT_EXECUTOR);
    }

    /**
     * 数据检查专用线程池线程池
     */
    @Bean(name = "statementCheckExecutor")
    public ThreadPoolTaskExecutor statementCheckExecutor() {
        return initStatementExecutor(3, 3, 1000, PREFIX_STATEMENT_EXECUTOR);
    }



    /**
     * 初始化执行器
     *
     * @param corePoolSize
     * @param maxPoolSize
     * @param queueCapacity
     * @param namePrefix
     */
    private ThreadPoolTaskExecutor initStatementExecutor(int corePoolSize, int maxPoolSize, int queueCapacity, String namePrefix) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);

        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(namePrefix);

        // 线程号传递
        executor.setTaskDecorator(runnable -> {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                try {
                    runnable.run();
                } catch (RejectedExecutionException e){
                    log.warn("线程池已满,当前任务被拒绝",e);
                }finally {
                    // 只清理本线程池中线程的数据
                    if (Thread.currentThread().getName().startsWith(namePrefix)) {
                        MDC.clear();
                    }
                }
            };
        });
        return executor;
    }



}
