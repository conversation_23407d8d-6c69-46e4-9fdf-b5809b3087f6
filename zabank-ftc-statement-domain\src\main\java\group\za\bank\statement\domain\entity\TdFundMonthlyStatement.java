package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * This class corresponds to the database table td_fund_monthly_statement
 * <p>
 * Database Table Remarks:
 * </p>
 *
 * <AUTHOR>
 * @since 2021年08月24日 11:36:52
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_fund_monthly_statement")
public class TdFundMonthlyStatement extends AuditIdEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Name: td_fund_monthly_statement.business_id
     * <p>
     * Database Column Remarks: 业务逻辑id
     * </p>
     */
    @Column(name = "business_id")
    private String businessId;


    /**
     * Database Column Name: td_fund_monthly_statement.business
     * <p>
     * Database Column Remarks: 业务类型
     * </p>
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * Database Column Name: td_fund_monthly_statement.period
     * <p>
     * Database Column Remarks: 月结单期数:'yyyyMM'
     * </p>
     */
    @Column(name = "period")
    private String period;

    /**
     * Database Column Name: td_fund_monthly_statement.bank_user_id
     * <p>
     * Database Column Remarks: 银行用户id
     * </p>
     */
    @Column(name = "bank_user_id", nullable = false)
    private String bankUserId;

    /**
     * Database Column Name: td_fund_monthly_statement.client_id
     * <p>
     * Database Column Remarks: clientId
     * </p>
     */
    @Column(name = "client_id")
    private String clientId;

    /**
     * Database Column Name: td_fund_monthly_statement.channel
     * <p>
     * Database Column Remarks: 通道：normal-普通 southBound-南向通
     * </p>
     */
    @Column(name = "channel", nullable = false)
    private String channel;

    /**
     * Database Column Name: td_fund_monthly_statement.doc_status
     * <p>
     * Database Column Remarks: 月结单文档状态:0:-初始化,1-待生成,2-已生成,3-等待通知文件生成 执行顺序：0312
     * </p>
     */
    @Column(name = "doc_status")
    private Byte docStatus;

    /**
     * Database Column Name: td_fund_monthly_statement.pub_status
     * <p>
     * Database Column Remarks: 月结单发送状态:0-待发送,1-已发送
     * </p>
     */
    @Column(name = "pub_status")
    private Byte pubStatus;


    /**
     * Database Column Name: td_fund_monthly_statement.zh_temp_key
     * <p>
     * Database Column Remarks: 文档语言
     * </p>
     */
    @Column(name = "doc_lang")
    private String docLang;

    /**
     * Database Column Name: td_fund_monthly_statement.zh_temp_key
     * <p>
     * Database Column Remarks: 中文模板key
     * </p>
     */
    @Column(name = "temp_key")
    private String tempKey;


    /**
     * Database Column Name: td_fund_monthly_statement.html_url
     * <p>
     * Database Column Remarks: html文档地址
     * </p>
     */
    @Column(name = "html_url")
    private String htmlUrl;

    /**
     * Database Column Name: td_fund_monthly_statement.zh_doc_url
     * <p>
     * Database Column Remarks: 文档地址
     * </p>
     */
    @Column(name = "doc_url")
    private String docUrl;


    /**
     * Database Column Name: td_fund_monthly_statement.pub_time
     * <p>
     * Database Column Remarks: 发送成功时间
     * </p>
     */
    @Column(name = "pub_time")
    private Date pubTime;

    /**
     * Database Column Name: td_fund_monthly_statement.doc_time
     * <p>
     * Database Column Remarks: 文档生成时间
     * </p>
     */
    @Column(name = "doc_time")
    private Date docTime;

    /**
     * Database Column Name: td_fund_monthly_statement.record_time
     * <p>
     * Database Column Remarks: 结单记录生成时间
     * </p>
     */
    @Column(name = "record_time")
    private Date recordTime;

    /**
     * Database Column Name: td_fund_monthly_statement.remark
     * <p>
     * Database Column Remarks: 备注
     * </p>
     */
    @Column(name = "remark")
    private String remark;

}