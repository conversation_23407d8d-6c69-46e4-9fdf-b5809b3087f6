package group.za.bank.statement.common.constants;

/**
 * <AUTHOR>
 * @Date  2021/11/29 15:29
 * @Description Console服务常量
 */
public class StatementConstants {
    /** 基金的系统ID */
    public static final String SYSTEM_ID = "FTC";

    /**
     * 服务名
     */
    public static final String SERVER_NAME = "zabank-ftc-statement-service";


    /** 解析文件分隔符 */
    public final static String STATEMENT_FILE_SPLIT = "|";

    /** 批量解析文件数量 */
    public final static Integer FILE_BATCH_SIZE = 500;

    /** 简体日期格式 **/
    public static final String DATE_FORMAT_CN = "yyyy/MM/dd";

    /** 繁体日期格式 **/
    public static final String DATE_FORMAT_HK = "dd/MM/yyyy";


    /** native日期格式 **/
    public static final String NATIVE_DATE_FORMAT = "yyyy/MM/dd";

    /**
     * 结单中空值的占位符
     */
    public static final String STATEMENT_NULL_VALUE_PLACEHOLDER = "--";

    /**
     * 月结单DATE格式
     */
    public static final String MONTHLY_STATEMENT_DATE_FORMAT = "dd MMM yyyy";

    /**
     * 月结单期数格式
     */
    public static final String MONTHLY_STATEMENT_PERIOD_FORMAT = "yyyyMM";

    /** 0字符串 **/
    public static final String ZERO_DEFAULT = "0";

    /**
     * 公司行动小数股订单编号前缀
     */
    public static final String ACTION_FRACTION_ORDER_PREFIX = "fraction";
    /**
     * 公司行动小数股订单编号
     * fraction_{actionId}_{userId}_{seq}
     */
    public static final String ACTION_FRACTION_ORDER_NO = ACTION_FRACTION_ORDER_PREFIX + "_%s_%s_%s";

    public static final String DAY_END_TIME = "235959";

    /**
     * html空格符
     */
    public static final String HTML_NBSP_FLAG = "&nbsp;";

    /**
     * 斜杠
     */
    public static final String SLASH_FLAG = "/";

    /**
     * 双短横杠
     */
    public static final String DOUBLE_SHORT_BAR_FLAG = "--";

    /**
     * 基金月结单迁移标识
     */
    public static final String FUND_MIGRATION_FLAG="fundMigration";

    /**
     * 下划线分隔符
     */
    public final static String UNDERSCORE_SEPARATOR = "_";

    /** 换行符 **/
    public static final String SHIFT_LINE = " </br>";

    /** 数字货币assetid拼接规则 **/
    public static final String CRYPTO_ASSET_ID_FORMAT = "%sHKD.HKY";

    /** 数字货币assetid拼接规则 **/
    public static final String CRYPTO_ASSET_ID_USD_FORMAT = "%sUSD.HKY";
    /**
     * 数字货币资产备份表月份字段日期格式
     */
    public static final String CRYPTO_ASSET_BACKUP_MONTH_FORMAT = "yyyy-MM";

    /**
     * html文件解析股票持仓分组id的前缀
     */
    public final static String HTML_PARSE_STOCK_HOLDING_GROUP_ID_PREFIX = "stockHoldingGroup";

    /**
     * 股票持仓等值港币市值groupId
     */
    public final static String HTML_PARSE_STOCK_HOLDING_EQV_HKD_GROUP = "stockHoldingEpvHkdGroup";

    /**
     * html文件解析股票收益及费用摘要分组id的前缀
     */
    public final static String HTML_PARSE_STOCK_DIVIDEND_ORDER_GROUP_ID_PREFIX = "stockDividendOrderGroup";

    /**
     * HTML文件解析数字货币订单币种名称
     */
    public final static String HTML_PARSE_CRYPTO_ORDER_CURRENCY_NAME = "cryptoPriceCurrency";

    /**
     * HTML文件解析数字货币订单币种前缀
     */
    public final static String HTML_PARSE_CRYPTO_ORDER_CURRENCY_PREFIX = "Currency";

    /**
     * HTML文件解析数字货币订单币种前缀
     */
    public final static int HTML_PARSE_CRYPTO_ORDER_CURRENCY_LENGTH = 14;

    /**
     * 公司行动小数股ID列表缓存过期时间 (s)
     */
    public final static long ACTION_FRACTION_ID_LIST_CACHE_TIMEOUT = 3;

    /**
     * 公司行动基本数据缓存过期时间 (s)
     */
    public final static long ACTION_INFO_CACHE_TIMEOUT = 3600 * 6;

    /**
     * 费用为空的时候的默认值
     */
    public static final String DEFAULT_FEE = "0.00";


    public static final String STATEMENT_FILE_CHECK_SETTLED_QTY_COLUMN = "tSettled";

    public static final String STATEMENT_FILE_CHECK_HOLD_QTY_COLUMN = "holdQty";

    public static final String STATEMENT_FILE_CHECK_COST_PRICE_COLUMN = "costPrice";

}
