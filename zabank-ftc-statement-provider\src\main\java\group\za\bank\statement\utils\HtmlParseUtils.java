package group.za.bank.statement.utils;

/**
 * html解析工具类
 *
 * <AUTHOR>
 * @Date 2023/3/22 11:06
 * @Description
 */
public class HtmlParseUtils {

    /**
     * 在分割符后追加字符串
     *
     * @param source
     * @param separator
     * @param appendStr
     * @return
     */
    public static String appendAfter(String source, String separator, String appendStr) {
        if (source == null) {
            return null;
        }
        int index = source.indexOf(separator);
        if (index <= -1) {
            return source;
        }
        String target = source.substring(0, index);
        return source.replace(target, target + appendStr);
    }

    /**
     * 在两次分割符后追加字符串
     *
     * @param source
     * @param separator1
     * @param separator2
     * @param appendStr
     * @return
     */
    public static String appendAfter(String source, String separator1, String separator2, String appendStr) {
        if (source == null) {
            return null;
        }

        int sepIndex1 = source.indexOf(separator1);
        if (sepIndex1 <= -1) {
            return source;
        }
        String sep1AfterStr = source.substring(source.indexOf(separator1) + separator1.length());
        int sepIndex2 = sep1AfterStr.indexOf(separator2);
        if (sepIndex2 <= -1) {
            return sep1AfterStr;
        }
        String target = sep1AfterStr.substring(0, sepIndex2);
        return source.replace(target, target + appendStr);
    }
}
