package group.za.bank.statement.base;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.SoftAssertions;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import redis.embedded.RedisServer;

@RunWith(SpringRunner.class)
@Slf4j
@ActiveProfiles("test")
@MapperScan(basePackages = {"group.za.bank..*.mapper"})
@Ignore
public class BaseTestH2Service {
    private static RedisServer redisServer = null;

    static {
        System.setProperty("env", "test");

    }

    protected MockMvc mockMvc;
    protected SoftAssertions softAssertions;


    @Autowired
    WebApplicationContext webApplicationContext;


    @BeforeClass
    public static void initRedisServer() throws InterruptedException {
        System.out.println("init redis server...");
        if (redisServer == null) {
            redisServer = RedisServer.builder().port(6379).setting("maxmemory 128M") // maxheap 128M
                    .setting("requirepass sbstrade").build();
            redisServer.start();
        }
    }

    @AfterClass
    public static void close() {
        System.out.println("close redis server");
        if (redisServer != null) {
            redisServer.stop();
            redisServer = null;
        }
    }

    @Before
    public void setUp() throws Exception {
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        softAssertions = new SoftAssertions();
        // 初始化测试用例类中由Mockito的注解标注的所有模拟对象
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // 解决mybatisplus 报错   按工程的实体类自行填写
        /*TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), TdStockStatementData.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), TdStatementFileRecord.class);

        Field global_config = GlobalConfigUtils.class.getDeclaredField("GLOBAL_CONFIG");
        global_config.setAccessible(true);
        Map<String, GlobalConfig> GLOBAL_CONFIG =
                (Map<String, GlobalConfig>) global_config.get(GlobalConfigUtils.class);
        for (String s : GLOBAL_CONFIG.keySet()) {
            GLOBAL_CONFIG.get(s).setSqlSessionFactory(sqlSessionFactory);
        }*/

    }

}