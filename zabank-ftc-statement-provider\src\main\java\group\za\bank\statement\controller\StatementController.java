package group.za.bank.statement.controller;


import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.common.utils.ResponseUtil;
import group.za.bank.invest.common.utils.UserKeyAccessReportUtil;
import group.za.bank.invest.common.utils.UserUtil;
import group.za.bank.statement.constants.enums.StatementUserKeyAccessTypeEnum;
import group.za.bank.statement.entity.req.AppUserMonthlyStatementListReq;
import group.za.bank.statement.entity.req.NativeDetailReq;
import group.za.bank.statement.entity.req.NativeSummaryReq;
import group.za.bank.statement.entity.resp.NativeDetailResp;
import group.za.bank.statement.entity.resp.NativeSummaryListResp;
import group.za.bank.statement.entity.resp.UserMonthlyStatementListResp;
import group.za.bank.statement.manager.CryptoTradeCoreManager;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.UserStatementNativeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 结单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@RestController
@Slf4j
public class StatementController {

    @Autowired
    MonthlyStatementService monthlyStatementService;

    @Autowired
    UserStatementNativeService userStatementNativeService;

    @Autowired
    private CryptoTradeCoreManager cryptoTradeCoreManager;


    /**
     * APP结单列表
     * @param req
     * @return
     */
    @RequestMapping(value = "/statement/normal/monthly/list", method = RequestMethod.POST)
    public ResponseData<UserMonthlyStatementListResp> userMonthlyStatementList(@RequestBody @Valid AppUserMonthlyStatementListReq req) {
        I18nSupportEnum langEnum = UserUtil.getLanguage();
        UserMonthlyStatementListResp userMonthlyStatementListResp
                = monthlyStatementService.queryUserMonthlyStatement(langEnum,req);
        return ResponseUtil.success(userMonthlyStatementListResp);
    }


    /**
     * 用户月结单native总结信息
     * @param req
     * @return
     */
    @RequestMapping(value = "/statement/normal/monthly/nativeSummary", method = RequestMethod.POST)
    public ResponseData<NativeSummaryListResp> nativeSummary(@RequestBody @Valid NativeSummaryReq req) {
        I18nSupportEnum langEnum = UserUtil.getLanguage();
        NativeSummaryListResp resp = userStatementNativeService.queryUserMonthlyStatementNativeSummaryData(langEnum,req);
        return ResponseUtil.success(resp);
    }


    /**
     * 用户月结单native详情信息
     * @param req
     * @return
     */
    @RequestMapping(value = "/statement/normal/monthly/nativeDetail", method = RequestMethod.POST)
    public ResponseData<NativeDetailResp> nativeDetail(@RequestBody @Valid NativeDetailReq req) {
        I18nSupportEnum langEnum = UserUtil.getLanguage();
        NativeDetailResp resp = userStatementNativeService.queryUserMonthlyStatementNativeDetail(langEnum,req);
        UserKeyAccessReportUtil.tracking(StatementUserKeyAccessTypeEnum.VIEW_INVEST_MONTH_STATEMENT, req);
        return ResponseUtil.success(resp);
    }

}

