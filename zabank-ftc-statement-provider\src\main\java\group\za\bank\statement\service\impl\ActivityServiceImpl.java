package group.za.bank.statement.service.impl;

import com.zatech.act.share.constant.*;
import com.zatech.act.share.dto.request.business.ActivityBusinessDTO;
import com.zatech.act.share.dto.request.business.area.AdditionalAreaDTO;
import com.zatech.act.share.dto.request.business.area.CommonAreaDTO;
import com.zatech.act.share.dto.request.business.area.TableAreaDTO;
import com.zatech.bank.act.BusinessMessageProducer;
import group.za.bank.fund.trade.constants.TradeConstants;
import group.za.bank.invest.common.utils.IdWorker;
import group.za.bank.statement.constants.StatementActivityConstants;
import group.za.bank.statement.constants.enums.ChannelEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import group.za.bank.statement.service.ActivityService;
import group.za.bank.statement.utils.StatementNumberFormatUtils;
import group.za.bank.statement.utils.StockStatementNumberFormatUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static group.za.bank.statement.common.constants.StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;

/**
 * <AUTHOR>
 * @createTime 06 14:34
 * @description
 */
@Service
@Slf4j
public class ActivityServiceImpl implements ActivityService {

    @Resource
    BusinessMessageProducer businessMessageProducer;

    /**
     * 月结单activity发送
     *
     * @param monthlyStatementInfo
     * @param monthlyStatement
     * @param baseMonthlyStatementDataDto
     */
    @Override
    @Transactional(transactionManager = "statementTransactionManager", rollbackFor = Exception.class)
    public void monthlyStatementActivityPub(TdFundMonthlyStatementInfo monthlyStatementInfo, TdFundMonthlyStatement monthlyStatement, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto) {
        try {
            log.info("monthlyStatementActivityPub act start,statementId:{}", monthlyStatement.getBusinessId());
            String period = monthlyStatementInfo.getPeriod();
            String bankUserId = monthlyStatement.getBankUserId();
            ActivityBusinessDTO activityBusinessDTO = buildActivityBusinessDTO(bankUserId, period, baseMonthlyStatementDataDto, monthlyStatement);
            // 构建自定义标签数据，支持南向通账户activity
            this.setCommonAreaProductCode(activityBusinessDTO, monthlyStatement);
            // 调用组件上报
            businessMessageProducer.sendBusinessMessageDirectly(activityBusinessDTO, true);
        } catch (Exception e) {
            log.error("monthlyStatementActivityPub error", e);
            throw new RuntimeException("上报Activity异常", e);
        }
    }

    private void setCommonAreaProductCode(ActivityBusinessDTO activityBusinessDTO, TdFundMonthlyStatement monthlyStatement) {
        if (ChannelEnum.SOUTH_BOUND.getValue().equals(monthlyStatement.getChannel())){
            // 如果是南向基金账户, 则添加南向自定义标签，方便activity区分
            CommonAreaDTO commonArea = activityBusinessDTO.getCommonArea();
            commonArea.setProductCode(StatementActivityConstants.SOUTH_BOUND_PRODUCT_CODE);
        }
    }

    ActivityBusinessDTO buildActivityBusinessDTO(String bankUserId, String month, BaseMonthlyStatementDataDto baseMonthlyStatementDataDto, TdFundMonthlyStatement monthlyStatement) {
        ActivityBusinessDTO activityBusinessDTO = new ActivityBusinessDTO();

        // 上报的系统ID
        String systemId = TradeConstants.SYSTEM_ID;
        String serialNumber = bankUserId + "_" + IdWorker.idworker.nextIdStr();

        // 公共区域
        CommonAreaDTO commonAreaDTO = new CommonAreaDTO();

        //系统ID
        commonAreaDTO.setSerialSystemId(systemId);

        //流水号
        commonAreaDTO.setSerialNumber(serialNumber);

        commonAreaDTO.setEffectiveTimestamp(System.currentTimeMillis());//生效时间

        // 上报消息ID
        commonAreaDTO.setMessageId(IdWorker.idworker.nextIdStr());
        // 上报系统ID
        commonAreaDTO.setSystemId(systemId);
        // 上报消息类型
        commonAreaDTO.setMessageType(MessageTypeEnum.BEHAVIOUR);
        // 上报消息业务大类
        commonAreaDTO.setBussinessCategory(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_BUSINESS_CATEGORY);
        // 上报消息业务子类
        commonAreaDTO.setBussinessSubCategory(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_BUSINESS_SUB_CATEGORY_SEND);
        // 南向通
        if (monthlyStatement.getChannel().equals(ChannelEnum.SOUTH_BOUND.getValue())) {
            // 上报消息业务大类
            commonAreaDTO.setBussinessCategory(StatementActivityConstants.SOUTH_INVEST_MONTHLY_STATEMENT_BUSINESS_CATEGORY);
            // 上报消息业务子类
            commonAreaDTO.setBussinessSubCategory(StatementActivityConstants.SOUTH_INVEST_MONTHLY_STATEMENT_BUSINESS_SUB_CATEGORY_SEND);
        }
        // 上报时间戳-毫秒时间戳
        commonAreaDTO.setReportTimestamp(System.currentTimeMillis());
        // 业务时间戳-毫秒时间戳
        commonAreaDTO.setBussinessTimestamp(System.currentTimeMillis());
        // 事件类型
        commonAreaDTO.setEventType(EventTypeEnum.MASTER);
        // 客户号
        commonAreaDTO.setCustomerIdentity(bankUserId);
        // 客户类型
        commonAreaDTO.setCustomerType(CustomerTypeEnum.USER);
        // 设置公共域对象
        activityBusinessDTO.setCommonArea(commonAreaDTO);


        //设置附加区对象
        String customMonth = month.substring(month.length() - 2, month.length());
        AdditionalAreaDTO investAdditionalAreaDTO = new AdditionalAreaDTO();
        //需要配置国际化// 填的month
        investAdditionalAreaDTO.setItemCode(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_MONTH);
        //填的 month01-month12
        investAdditionalAreaDTO.setItemValue(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_MONTH + customMonth);
        investAdditionalAreaDTO.setItemValueType(ItemValueTypeEnum.CODE);

        //附加区--上报月结单期数占位符 ItemValueType类型：value字段存放的值
        AdditionalAreaDTO periodAdditionalAreaDTO = new AdditionalAreaDTO();
        periodAdditionalAreaDTO.setItemCode(StatementActivityConstants.INVEST_STATEMENT_PERIOD);
        periodAdditionalAreaDTO.setItemValue(month);
        periodAdditionalAreaDTO.setItemValueType(ItemValueTypeEnum.VALUE);

        activityBusinessDTO.setAdditionalArea(Arrays.asList(investAdditionalAreaDTO, periodAdditionalAreaDTO));

        //table区
        activityBusinessDTO.setTableArea(handleStatementTableAreaDTO(baseMonthlyStatementDataDto));

        return activityBusinessDTO;
    }

    /**
     * 处理月结单activity :期末总市值（等值HKD)、期末持仓盈亏（等值HKD）
     */
    public List<TableAreaDTO> handleStatementTableAreaDTO(BaseMonthlyStatementDataDto baseMonthlyStatementDataDto) {
        List<TableAreaDTO> tableAreaDTOList = new ArrayList<>();
        /**
         * 期末总市值（等值HKD)
         */
        TableAreaDTO monthTotalMarket = new TableAreaDTO();
        monthTotalMarket.setItemCode(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_TOTAL_MARKET);
        monthTotalMarket.setItemType(ItemTypeEnum.DETAIL);
        String totalMarketValue = StockStatementNumberFormatUtils
                .statementMoneyFormatOfRoundDown(baseMonthlyStatementDataDto.getTotalAmountValue(), STATEMENT_NULL_VALUE_PLACEHOLDER);
        monthTotalMarket.setItemValue(totalMarketValue);
        monthTotalMarket.setValueType(ValueTypeEnum.STRING);
        monthTotalMarket.setGroupIdentity(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_GROUP);
        /**
         * 期末持仓盈亏（等值HKD）
         */
        TableAreaDTO monthHoldingIncome = new TableAreaDTO();
        monthHoldingIncome.setItemCode(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_TOTAL_HOLDING_PROFIT);
        monthHoldingIncome.setItemType(ItemTypeEnum.DETAIL);

        //数据库：22.01 当大于0 app显示 +22.01
        String itemValue = null;
        BigDecimal totalHoldingProfit = baseMonthlyStatementDataDto.getTotalHoldingProfit();
        if (baseMonthlyStatementDataDto.getTotalHoldingProfit().compareTo(BigDecimal.ZERO) > 0) {
            itemValue = "+" + StatementNumberFormatUtils.statementMoneyFormat(totalHoldingProfit);
        } else {
            itemValue = StatementNumberFormatUtils.statementMoneyFormat(totalHoldingProfit);
        }
        monthHoldingIncome.setItemValue(itemValue);
        monthHoldingIncome.setValueType(ValueTypeEnum.STRING);
        monthHoldingIncome.setGroupIdentity(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_GROUP);


        /**
         * description 说明描述
         */
        TableAreaDTO description = new TableAreaDTO();
        description.setItemCode(StatementActivityConstants.INVEST_MONTHLY_STATEMENT_DESCRIPTION);
        description.setItemType(ItemTypeEnum.DETAIL);

        tableAreaDTOList.add(monthTotalMarket);
        tableAreaDTOList.add(monthHoldingIncome);
        tableAreaDTOList.add(description);
        return tableAreaDTOList;
    }

}
