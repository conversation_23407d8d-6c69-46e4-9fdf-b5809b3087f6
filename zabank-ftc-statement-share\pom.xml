<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">



    <parent>
        <groupId>group.za.bank</groupId>
        <artifactId>zabank-ftc-statement-service</artifactId>
        <!-- 不需要更改此版本 -->
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zabank-ftc-statement-share</artifactId>
    <version>${zabank.ftc.statement.share.version}</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <zainvest.common.version>1.7.1</zainvest.common.version>
        <zainvest.usercore.share.version>1.8.1</zainvest.usercore.share.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>group.za.bank</groupId>
            <artifactId>zabank-invest-common</artifactId>
            <version>${zabank.invest.common.version}</version>
        </dependency>
    </dependencies>


</project>