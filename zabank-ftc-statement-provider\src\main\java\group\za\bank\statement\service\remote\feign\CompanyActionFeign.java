package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.tradedata.fegin.CompanyActionFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * tradedata服务公司行动远程接口
 * <AUTHOR>
 * @date 2023/06/09
 **/
@FeignClient(
        value = "zabank-sbs-tradedata-service",
        contextId = "companyActionFeign",
        url = "${sbs.tradedata.gateway.url}"
)
public interface CompanyActionFeign extends CompanyActionFeignService {
}
