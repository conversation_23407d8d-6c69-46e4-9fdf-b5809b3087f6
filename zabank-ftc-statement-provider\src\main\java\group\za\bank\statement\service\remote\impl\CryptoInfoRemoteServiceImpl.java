package group.za.bank.statement.service.remote.impl;


import group.za.bank.ccs.tradecore.fegin.CryptoInfoFeignService;
import group.za.bank.ccs.tradecore.model.req.CryptoInfoReq;
import group.za.bank.ccs.tradecore.model.resp.CryptoInfoResp;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.CryptoInfoRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 交易币对基础信息
 *
 * <AUTHOR>
 * @date 2023/08/03
 **/
@Slf4j
@Service
public class CryptoInfoRemoteServiceImpl implements CryptoInfoRemoteService {

    @Autowired
    private CryptoInfoFeignService cryptoInfoFeignService;

    @Override
    public CryptoInfoResp queryCryptoInfo(String exchangeCode, String assetType) {
        CryptoInfoReq cryptoInfoReq = new CryptoInfoReq();
        cryptoInfoReq.setExchangeCode(exchangeCode);
        cryptoInfoReq.setAssetType(assetType);
        ResponseData<CryptoInfoResp> responseData = cryptoInfoFeignService.queryCryptoInfo(cryptoInfoReq);
        if (!responseData.judgeSuccess()) {
            log.error("queryCryptoInfo error,code:{},msg:{}", responseData.getCode(), responseData.getMsg());
            throw new BusinessException(responseData.getCode(), responseData.getMsg());
        }

        if (Objects.isNull(responseData.getValue())) {
            log.error("queryCryptoInfo error,data is null");
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return responseData.getValue();
    }
}
