package group.za.bank.statement.service.impl;

import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.domain.entity.TdStatementActionPendingData;
import group.za.bank.statement.domain.mapper.TdStatementActionPendingDataMapper;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

public class StatementActionPendingDataServiceImplTest extends BaseTestService {
    @Mock
    TdStatementActionPendingDataMapper statementActionPendingDataMapper;
    @Mock
    Logger log;
    @InjectMocks
    StatementActionPendingDataServiceImpl statementActionPendingDataServiceImpl;


    @Test
    public void testCreate() throws Exception {
        statementActionPendingDataServiceImpl.create(Long.valueOf(1), "bankUserId", "accountId", "actionId", "actionType", "originalPeriod", "USEX", true);
    }

    @Test
    public void testQueryPendingList() throws Exception {
        List<TdStatementActionPendingData> result = statementActionPendingDataServiceImpl.queryPendingList("US","accountId", "dataStatus");
    }

    @Test
    public void testQueryPendingListForPeriod() throws Exception {
        List<TdStatementActionPendingData> result = statementActionPendingDataServiceImpl.queryPendingListForPeriod("US","accountId", "202401");
    }

    @Test
    public void testCountPendingListForPeriod() throws Exception {
        int result = statementActionPendingDataServiceImpl.countPendingListForPeriod("US", "accountId", "202401");
    }

    @Test
    public void testProcessedActionCapitalData() throws Exception {
        TdStatementActionPendingData tdStatementActionPendingData = new TdStatementActionPendingData();
        tdStatementActionPendingData.setVersion(1);
        statementActionPendingDataServiceImpl.processedActionCapitalData(tdStatementActionPendingData, "202401", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 26).getTime(), "remark");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme