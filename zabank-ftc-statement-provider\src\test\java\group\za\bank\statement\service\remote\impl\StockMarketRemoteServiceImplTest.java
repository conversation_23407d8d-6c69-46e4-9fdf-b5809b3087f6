package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.quotasup.share.service.entity.resp.DayKlineRespDTO;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.remote.feign.QuotaKlineFeign;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class StockMarketRemoteServiceImplTest extends BaseTestService {
    @Mock
    QuotaKlineFeign quotaKlineFeign;
    @Mock
    Logger log;
    @InjectMocks
    StockMarketRemoteServiceImpl stockMarketRemoteServiceImpl;


    @Test
    public void testQueryStockKline() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        when(quotaKlineFeign.queryDayKline(any())).thenReturn(responseData);
        Map<String, DayKlineRespDTO> result = stockMarketRemoteServiceImpl.queryStockKline(new GregorianCalendar(2024, Calendar.DECEMBER, 11, 10, 26).getTime(), Arrays.<String>asList("AAPL"));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme