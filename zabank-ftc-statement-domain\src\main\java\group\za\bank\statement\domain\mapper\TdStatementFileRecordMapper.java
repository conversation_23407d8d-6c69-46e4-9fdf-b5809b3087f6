package group.za.bank.statement.domain.mapper;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import group.za.invest.mybatis.mapper.template.CrudMapper;

import java.util.List;

/**
* This mapper interface access the database table td_statement_file_record
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 10:39:39
*/
@Repository
public interface TdStatementFileRecordMapper extends CrudMapper<TdStatementFileRecord> {


    /**
     * 查询日期范围内日结单解析记录
     * @return
     */
    List<TdStatementFileRecord> queryDailyFileParseRecords(@Param("startDate") String startDate,@Param("endDate") String endDate  );



    /**
     * 查询日期范围内日结单解析记录
     * @return
     */
    List<TdStatementFileRecord> queryMonthlyFileParseRecords(@Param("statementDate") String statementDate );


}
