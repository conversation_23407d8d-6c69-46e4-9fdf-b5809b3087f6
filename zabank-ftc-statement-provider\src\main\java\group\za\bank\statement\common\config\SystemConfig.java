package group.za.bank.statement.common.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 系统配置
 * @Date 2020/10/28 20:41
 * @Version v1.0
 **/
@Component
@Getter
@Setter
@EnableApolloConfig
public class SystemConfig {


    @Value("${monthly.statement.defaultMonthlyStatementBusinessType}")
    private String defaultMonthlyStatementBusinessType;

    /**
     * 中文模板key
     */
    @Value("${monthly.statement.doc.tempPath}")
    private String tempPath;

    /**
     * 中文模板key
     */
    @Value("${monthly.statement.doc.zhTempKey}")
    private String zhTempKey;
    /**
     * 繁体模板key
     */
    @Value("${monthly.statement.doc.hkTempKey}")
    private String hkTempKey;

    /**
     * 南向通中文模板key
     */
    @Value("${monthly.statement.doc.nxtZhTempKey}")
    private String nxtZhTempKey;
    /**
     * 南向通繁体模板key
     */
    @Value("${monthly.statement.doc.nxtHkTempKey}")
    private String nxtHkTempKey;


    /**
     * 确认可以推送前检查所有结单状态必须是已生成才可以推送
     */
    @Value("${monthly.statement.checkAllStatementDocGenerateStatusBeforePubConfirm}")
    private boolean checkAllStatementDocGenerateStatusBeforePubConfirm;

    /**
     * 推送前是否需要确认
     */
    @Value("${monthly.statement.checkConfirmStatusBeforePub}")
    private boolean checkConfirmStatusBeforePub;

    /**
     * 是否缓存无需生成结单用户的列表
     */
    @Value("${monthly.statement.cacheNoStatementUserSet:true}")
    private boolean cacheNoStatementUserSet;

    /**
     * 查询分页大小
     */
    @Value("${monthly.statement.pageSize}")
    private Integer pageSize ;

    /**
     * 月结单期数格式
     */
    @Value("${monthly.statement.monthlyStatementPeriodFormat}")
    private  String monthlyStatementPeriodFormat ;
    /**
     * 月结单大title格式
     */
    @Value("${monthly.statement.monthlyStatementDateFormat}")
    private  String monthlyStatementDateFormat ;
    /**
     * 月结单表格格式
     */
    @Value("${monthly.statement.monthlyStatementShortDateFormat}")
    private  String monthlyStatementShortDateFormat ;


    @Value("${monthly.statement.businessSubTypeSeparator}")
    public  String businessSubTypeSeparator ;



    /**
     * 企业微信告警接收人列表
     */
    @Value("${system.alarm.wechat.userList}")
    private List<String> wechatAlarmUserList;

    /**
     * 邮件告警接收人列表
     */
    @Value("${system.alarm.email.userList}")
    private List<String> emailAlarmUserList;


    /** 月结单FTP */
    @Value("${ttl.file.sftp.host}")
    private String sftpHost;
    @Value("${ttl.file.sftp.port}")
    private int sftpPort;
    @Value("${ttl.file.sftp.username}")
    private String username;
    @Value("${ttl.file.sftp.password}")
    private String password;
    @Value("${ttl.file.sftp.dir}")
    private String dir;

    @Value("${ttl.file.localPath}")
    private String localPath;


    /**
     * 日结单文件解析的类型，多个类型用英文逗号分隔
     */
    @Value("${monthly.statement.dailyFile.parse.formatTypes}")
    private String dailyStatementFileParseFormatTypes;

    /**
     * 月结单文件解析的类型，多个类型用英文逗号分隔
     */
    @Value("${monthly.statement.monthlyFile.parse.formatTypes}")
    private String monthlyStatementFileParseFormatTypes;





    /**
     * ttl文件中成交订单对应的费用id
     */

    /**
     * 证监会 sec
     */
    @Value("${monthly.statement.stock.secFeeKey}")
    private String secFeeKey;

    /**
     * 交易费
     */
    @Value("${monthly.statement.stock.finraFeeKey}")
    private String finraFeeKey;


    /**
     * 费用缺失的时候是否生成结单
     */
    @Value("${monthly.statement.stock.generateStatementWhenFeeNull}")
    private Boolean generateStatementWhenFeeNull;



    /**
     * native中页的条数
     */
    @Value("${monthly.statement.nativePageSize:2}")
    private Integer nativePageSize;

    /**
     * 股票数据校验开关(默认true校验)
     */
    @Value("${monthly.statement.stock.check:true}")
    private Boolean stockCheck;

    /**
     * house账户列表
     */
    @Value("${monthly.statement.house.account.list}")
    private List<String> houseAccountList;

    /**
     * 月结单native汇总月份数配置
     */
    @Value("${monthly.statement.native.summary.month.size:36}")
    private Integer nativeSummaryMonthSize;

    /**
     * 结单文件下载是否使用oss
     */
    @Value("${monthly.statement.file.ali.oss.selector:true}")
    private boolean statementFileAliOssSelector;

    /**
     * 销户月结单邮件接收人
     */
    @Value("${close.monthly.statement.emailList}")
    private List<String> statementEmailList;

    /**
     * 股票结单数据检查自动插入，默认false
     */
    @Value("${stock.statement.data.check.auto.insert:false}")
    private boolean stockStatementDataCheckAutoInsert;

    /**
     * 结单公司行动小数股IB卖出校验开关
     */
    @Value("${statement.company.fraction.ib.sell.check:true}")
    private boolean statementCompanyFractionIbSellCheck;


//    /**
//     * 印花税
//     */
//    @Value("${monthly.statement.stock.fee.stampDuty:Stamp Duty}")
//    private String stampDuty;
//
//    @Value("${monthly.statement.stock.fee.sfcTransactionLevy:Transaction Levy}")
//    private String sfcTransactionLevy;
//
//    @Value("${monthly.statement.stock.fee.afrcTransactionLevy:Clearing Fee}")
//    private String afrcTransactionLevy;
//
//    @Value("${monthly.statement.stock.fee.hkexTradingFee:Trading Fee}")
//    private String hkexClearingFee;
//
//    @Value("${monthly.statement.stock.fee.hkexTradingFee:Trading Fee}")
//    private String hkexTradingFee;

    /**
     * 是否允许日结单文件为空，默认为不允许，为空的时候不处理
     */
    @Value("${monthly.statement.daily.file.null.able:false}")
    private Boolean dailyStatementFileNullAble;

    @Value("${action.rights.issue.type.remark:RIGHTS}")
    private String actionRightsIssueTypeRemark;

    @Value("${action.open.offer.type.remark:OPEN OFFER}")
    private String actionOpenOfferTypeRemark;

    @Value("${action.preferential.offer.type.remark:PREFERENTIAL OFFER}")
    private String actionPreferentialOfferTypeRemark;

    @Value("${action.equity.warrant.type.remark:EQUITY WARRANT}")
    private String actionEquityWarrantTypeRemark;

    @Value("${action.derivative.warrant.exercised.type.remark:DERIVATIVE WARRANT EXERCISED}")
    private String actionDerivativeWarrantExercisedTypeRemark;

    @Value("${action.cbbc.exercised.type.remark:CBBC EXERCISED}")
    private String actionCbbcExercisedTypeRemark;

}
