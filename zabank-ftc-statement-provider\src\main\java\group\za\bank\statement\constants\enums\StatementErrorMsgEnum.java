package group.za.bank.statement.constants.enums;


import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.constants.enums.ResourceHandler;

/**
 * 结单状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/22 14:00
 */
public enum StatementErrorMsgEnum implements ResourceHandler {

    /**
     * 月结单异常代码
     */
    SUCCESS("0000", "成功", "Success", "成功"),
    REMOTE_ERROR(CommonErrorMsgEnum.RPC_INVOKE_ERR),


    DUPLICATE_OPERATION("1001", "请勿重复操作", "please do not repeat", "請勿重複操作"),
    STATEMENT_EXISTED("1002", "结单已存在！", "statement existed！", "结单已存在！"),
    STATEMENT_BUSINESS_DATA_SERVICE_NOT_FOUND("1003", "找不到结单业务数据服务service！", "statement business data service not found！", "找不到结单业务数据服务service！"),
    STATEMENT_DATA_STATUS_EXCEPTION("1004", "结单数据状态异常！", "statement data status exception！", "结单数据状态异常！"),
    STATEMENT_RECORD_STATUS_EXCEPTION("1005", "结单记录状态异常！", "statement record status exception！", "结单记录状态异常！"),
    STATEMENT_INFO_STATUS_EXCEPTION("1006", "结单汇总状态异常！", "statement info status exception！", "结单汇总状态异常！"),
    STATEMENT_ALREADY_CONFIRM_PUB("1007", "结单已经确认可以推送！", "statement already confirm pub！", "结单已经确认可以推送！"),
    STATEMENT_NOT_CONFIRM_PUB("1008", "结单未确认可以推送！", "statement not confirm pub！", "结单未确认可以推送！"),
    STATEMENT_INFO_NOT_FOUND("1009", "结单不存在！", "statement info not found！", "结单不存在！"),
    STATEMENT_ALREADY_PUB_CONFIRMED("1010", "结单已确认可以推送！", "statement already pub confirmed！", "结单已确认可以推送！"),
    STATEMENT_CAN_NOT_GET_USER_HABITANCY_INFO("1011", "查找不到用户基本信息!" , "The user habitancy info cannot be found !", "查找不到用户基本信息!"),
    STATEMENT_DOC_GENERATE_NOT_COMPLETE("1012", "部分结单文件未生成完毕!"   , "Statement doc generate not complete!", "部分结单文件未生成完毕!"),
    STATEMENT_SUB_BUSINESS_DATA_IS_EMPTY("1013", "结单子业务数据为空!" , "Statement sub business data is empty!", "结单子业务数据为空!"),
    STATEMENT_SUB_BUSINESS_DATA_WRONG("1014", "结单子业务数据错误!" , "Statement sub business data wrong!", "结单子业务数据错误!"),




    /**
     * 文件解析错误 2XXX
     */
    FILE_RECORD_ERROR("2000", "保存文件记录错误", "save file record error", "保存文件記錄錯誤"),
    FILE_PARSE_ERROR("2001", "结单文件解析错误", "statement file parse error", "結單文件解析錯誤"),
    FILE_DOWNLOAD_ERROR("2002", "结单文件下载失败", "statement file download fail", "結單文件下載失敗"),
    FILE_PARSE_REPEATED("2003", "结单文件解析重复", "statement file parse repeated", "结单文件解析重复"),
    FILE_TRADE_TYPE_ERROR("2004", "结单文件交易类型错误", "statement file trade type error", "结单文件交易类型错误"),
    FILE_HTML_PARSE_ERROR("2005", "HTML文件解析错误", "statement file html parse error", "HTML文件解析錯誤"),


    /**
     * 汇率 3XXX
     */
    RATE_QUERY_ERROR("3001", "获取汇率失败", "query rate fail", "獲取匯率失敗"),


    /**
     * 用户数据 4XXX
     */
    USER_INVEST_ACCOUNT_NOT_FOUND("4015", "找不到用户投资账户!" , "User invest account not found!", "找不到用户投资账户!"),
    USER_STATEMENT_DATA_NOT_COMPLETE("4020", "用户结单数据不完整!", "User statement data not complete!", "用户结单数据不完整!"),

    /**
     * 股票数据 5XXX
     */
    STOCK_INFO_QUERY_ERROR("5001", "获取股票基本信息失败", "query stock info fail", "獲取股票基本信息失敗"),
    STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND("5002", "股票月结单缺少Fec fee", "STOCK_CONFIRM_ORDER_FEC_FEE_NOT_FOUND", "股票月结单缺少Fec fee"),
    STOCK_CONFIRM_ORDER_FINRA_FEE_NOT_FOUND("5003", "股票月结单缺少Finra fee", "STOCK_CONFIRM_ORDER_FINRA_FEE_NOT_FOUND", "股票月结单缺少Finra fee"),
    GET_STOCK_KLINE_FAIL("5005", "获取股票k线失败", "Failed to get stock line", "获取股票k线失败"),
    STOCK_CONFIRM_ORDER_QTY_CAN_NOT_BE_NULL("5006", "股票月结单已确认订单QTY不能为null", "STOCK_CONFIRM_ORDER_QTY_CAN_NOT_BE_NULL", "股票月结单已确认订单QTY不能为null"),
    STOCK_CONFIRM_ORDER_AMOUNT_CAN_NOT_BE_NULL("5007", "股票月结单已确认订单AMOUNT不能为null", "STOCK_CONFIRM_ORDER_AMOUNT_CAN_NOT_BE_NULL", "股票月结单已确认订单AMOUNT不能为null"),
    STOCK_CONFIRM_ORDER_AVG_PRICE_CAN_NOT_BE_NULL("5008", "股票月结单已确认订单AVG PRICE不能为null", "STOCK_CONFIRM_ORDER_AVG_PRICE_CAN_NOT_BE_NULL", "股票月结单已确认订单AVG PRICE不能为null"),
    STOCK_HISTORY_HOLDING_DATA_NULL("5009", "股票历史持仓数据为空", "股票历史持仓数据为空", "股票历史持仓数据为空"),

    STOCK_STATEMENT_AUTO_BUILD_ERROR("5100", "自动生成股票结单数据异常", "自动生成股票结单数据异常", "自动生成股票结单数据异常"),
    STOCK_STATEMENT_ORDER_NO_NULL("5101", "自动生成股票结单订单编号为空", "自动生成股票结单订单编号为空", "自动生成股票结单订单编号为空"),



    /**
     * 基金数据 6XXX
     */
    FUND_STATEMENT_CAN_NOT_GET_FUND_BASE_INFO("6016", "查找不到基金{0}基本信息!"  , "The fund  {0} info cannot be found !", "查找不到基金{0}基本信息!"),
    GET_FUND_INFO_FAIL("6017", "获取基金信息失败", "Failed to get fund information", "未能取得基金資料"),
    FUND_BUSINESS_TYPE_NOT_FOUND("6018", "基金业务类型不存在!", "Fund business type not found!", "基金业务类型不存在"),
    GET_FUND_MONTH_LAST_TRADE_DATE_ERROR("6019", "查询基金月份最后一个交易日错误!", "Get fund month last trade date error!", "查询基金月份最后一个交易日错误"),




    ;


    private String code;
    private String zhMsg;
    private String enMsg;
    private String hkMsg;

    StatementErrorMsgEnum(String code, String zhMsg, String enMsg, String hkMsg) {
        this.code = code;
        this.zhMsg = zhMsg;
        this.enMsg = enMsg;
        this.hkMsg = hkMsg;
    }

    StatementErrorMsgEnum(CommonErrorMsgEnum commonErrorMsgEnum) {
        this.code = commonErrorMsgEnum.getCode();
        this.zhMsg = commonErrorMsgEnum.getZhMsg();
        this.enMsg = commonErrorMsgEnum.getEnMsg();
        this.hkMsg = commonErrorMsgEnum.getHkMsg();
    }

    @Override
    public String getCode() {
        return getPrefix() + this.code;
    }

    @Override
    public String getPrefix() {
        return "OPS0";
    }

    @Override
    public String getZhMsg() {
        return zhMsg;
    }

    @Override
    public String getEnMsg() {
        return enMsg;
    }

    @Override
    public String getHkMsg() {
        return hkMsg;
    }

}
