package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.quotasup.share.service.entity.req.DayKlineReqDTO;
import group.za.bank.sbs.quotasup.share.service.entity.resp.DayKlineRespDTO;
import group.za.bank.statement.service.remote.StockMarketRemoteService;
import group.za.bank.statement.service.remote.feign.QuotaKlineFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created on 2020/11/20 13:57.
 *
 */
@Slf4j
@Service
public class StockMarketRemoteServiceImpl implements StockMarketRemoteService {

    @Resource
    private QuotaKlineFeign quotaKlineFeign;


    /**
     * 批量查询多个股票收盘价
     *
     * @param assetIdList
     */
    @Override
    public Map<String, DayKlineRespDTO> queryStockKline(Date tradeDate,List<String> assetIdList) {
        if(CollectionUtils.isEmpty(assetIdList)){
            return new HashMap<>(0);
        }
        DayKlineReqDTO req = new DayKlineReqDTO();
        req.setAssetIdList(assetIdList);
        req.setQuoteDate(DateUtil.format(tradeDate,DateUtil.FORMATDAY));
        req.setCandleMode(0);

        ResponseData<List<DayKlineRespDTO>> response = quotaKlineFeign.queryDayKline(req);
        if(log.isDebugEnabled()){
            log.debug("rpc queryStockKline! req:{},resp:{}",req,response);
        }
        if (!response.judgeSuccess()) {
            throw new BusinessException(response.getCode(), response.getMsg());
        }
        List<DayKlineRespDTO> responseData = response.getValue();

        if (responseData==null || CollectionUtils.isEmpty(responseData)) {
            log.error("queryStockKline is null,assetIdList:{}", assetIdList);
            return new HashMap<>(0);
        }

        return responseData.stream().collect(Collectors.toMap(DayKlineRespDTO::getAssetId, dayKlineRespDTO -> dayKlineRespDTO));
    }
}
