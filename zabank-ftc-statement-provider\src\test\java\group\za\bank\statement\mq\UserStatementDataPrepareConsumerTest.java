package group.za.bank.statement.mq;

import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.MonthlyStatementService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.springframework.beans.factory.BeanFactory;

public class UserStatementDataPrepareConsumerTest extends BaseTestService {
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    Logger log;
    @Mock
    BeanFactory beanFactory;
    @InjectMocks
    UserStatementDataPrepareConsumer userStatementDataPrepareConsumer;

    @Test
    public void testConfigPrefix() throws Exception {
        String result = userStatementDataPrepareConsumer.configPrefix();
    }

}

