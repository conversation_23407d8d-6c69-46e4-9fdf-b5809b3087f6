package group.za.bank.statement.utils;

import com.google.common.base.Strings;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;



/**
 * <AUTHOR>
 * @createTime 21 18:27
 * @description
 */
public class StockStatementNumberFormatUtils {






    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementQtyFormat(String number, String emptyValue) {
        if (Strings.isNullOrEmpty(number)) {
            return emptyValue;
        } else {
            return statementQtyFormat(new BigDecimal(number), emptyValue);
        }
    }


    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementQtyFormat(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0");
        //数量不能为负值
        number = number.abs();
        return decimalFormat.format(number);
    }

    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementQtyFractionFormat(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.00000000##");
        //数量不能为负值
        number = number.abs();
        return decimalFormat.format(number);
    }

    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementShareFormat(String number, String emptyValue) {
        if (Strings.isNullOrEmpty(number)) {
            return emptyValue;
        } else {
            return statementShareFormat(new BigDecimal(number), emptyValue);
        }
    }
    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementShareFormat(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.0000##");
        //份额不能为负值
        number = number.abs();
        return decimalFormat.format(number);
    }



    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementMoneyFormat(String number, String emptyValue) {
        if (Strings.isNullOrEmpty(number)) {
            return emptyValue;
        } else {
            return statementMoneyFormat(new BigDecimal(number), emptyValue);
        }
    }


    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementMoneyFormatOfRoundDown(String number, String emptyValue) {
        if (Strings.isNullOrEmpty(number)) {
            return emptyValue;
        } else {
            return statementMoneyFormatOfRoundDown(new BigDecimal(number), emptyValue);
        }
    }
    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementMoneyFormat(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.00");
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
        return decimalFormat.format(number);
    }

    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementMoneyFormatOfRoundDown(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.00");
        decimalFormat.setRoundingMode(RoundingMode.DOWN);
        return decimalFormat.format(number);
    }

    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementPriceFormat(String number, String emptyValue) {
        if (Strings.isNullOrEmpty(number)) {
            return emptyValue;
        } else {
            return statementPriceFormat(new BigDecimal(number), emptyValue);
        }
    }
    /**
     * 月结单表格中数字的格式化:千分位
     *
     * @return
     */
    public static String statementPriceFormat(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.0000");
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
        return decimalFormat.format(number);
    }

    /**
     * 千分位，保留4位rounddown
     * @param number
     * @param emptyValue
     * @return
     */
    public static String statement4RoundDownFormat(BigDecimal number, String emptyValue) {
        if (number == null) {
            return emptyValue;
        }
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.0000");
        decimalFormat.setRoundingMode(RoundingMode.DOWN);
        return decimalFormat.format(number);
    }

    /**
     * 格式化为千分为符并去除末尾的0
     * @param number
     * @return
     */
    public static String formatCommaStyleStripTrailingZeros(BigDecimal number, String emptyValue){
        if (number == null) {
            return emptyValue;
        }

        // 设置格式化规则
        DecimalFormat decimalFormat = new DecimalFormat("#,###.###");

        // 格式化BigDecimal，并去除小数位末尾的0
        // 设置最大小数位数
        decimalFormat.setMaximumFractionDigits(32);
        // 设置最小小数位数
        decimalFormat.setMinimumFractionDigits(0);

        return decimalFormat.format(number);
    }
}
