package group.za.bank.statement.domain.mapper;

import group.za.bank.statement.domain.entity.TdFundMonthlyOrderTmp;
import group.za.invest.mybatis.mapper.template.CrudMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * This mapper interface access the database table td_fund_monthly_order_tmp
 * <p>
 * Database Table Remarks:
 * </p>
 *
 * <AUTHOR>
 * @since 2022年11月01日 17:52:10
 */
@Repository
public interface TdFundMonthlyOrderTmpMapper extends CrudMapper<TdFundMonthlyOrderTmp> {

    /**
     * 查询用户在临时表是否有订单
     * 分红类型：61状态数据 （用实际交收时间）
     * 申购赎回调仓类型：交易中（11，20，30状态数据 用户交易日期间查询） 已确认（41，50，60，61状态数据 用实际确认时间）
     *
     * @return
     */
    int countMonthlyStatementFundAccount(@Param("bankUserId") String bankUserId, @Param("bankAccountId") String bankAccountId);


    /**
     * 月结单查询指定月份指定用户的确认订单
     *
     * @param monthStartDate
     * @param monthEndDate
     * @return
     */
    List<TdFundMonthlyOrderTmp> queryFundAccountMonthlyStatementConfirmedOrderList(@Param("monthStartDate") Date monthStartDate,
                                                                                   @Param("monthEndDate") Date monthEndDate,
                                                                                   @Param("bankUserId") String bankUserId,
                                                                                   @Param("bankAccountId") String bankAccountId);

    /**
     * 月结单查询指定月份指定用户的交易中订单
     *
     * @param monthStartDate
     * @param monthEndDate
     * @return
     */
    List<TdFundMonthlyOrderTmp> queryFundAccountMonthlyStatementPendingOrderList(@Param("monthStartDate") Date monthStartDate,
                                                                                 @Param("monthEndDate") Date monthEndDate,
                                                                                 @Param("bankUserId") String bankUserId,
                                                                                 @Param("bankAccountId") String bankAccountId);

    /**
     * 月结单查询指定月份指定用户的派息订单
     *
     * @param monthStartDate
     * @param monthEndDate
     * @return
     */
    List<TdFundMonthlyOrderTmp> queryFundAccountMonthlyStatementDividendOrderList(@Param("monthStartDate") Date monthStartDate,
                                                                                  @Param("monthEndDate") Date monthEndDate,
                                                                                  @Param("bankUserId") String bankUserId,
                                                                                  @Param("bankAccountId") String bankAccountId);

    /**
     * 月结单查询指定月份指定用户的调仓订单
     *
     * @param monthStartDate
     * @param monthEndDate
     * @return
     */
    List<TdFundMonthlyOrderTmp> queryFundAccountMonthlyStatementDepositAndWithdrawalList(@Param("monthStartDate") Date monthStartDate,
                                                                                         @Param("monthEndDate") Date monthEndDate,
                                                                                         @Param("bankUserId") String bankUserId,
                                                                                         @Param("bankAccountId") String bankAccountId);

    /**
     * 物理删除临时表数据
     */
    int deleteTdFundMonthlyOrderTmp();

    /**
     * 是否存在数据
     */
    boolean existsOrder();

    /**
     * 查询用户申购订单
     */
    List<TdFundMonthlyOrderTmp> queryUserBuyOrder(@Param("bankUserId") String bankUserId, @Param("bankAccountId") String bankAccountId);

}
