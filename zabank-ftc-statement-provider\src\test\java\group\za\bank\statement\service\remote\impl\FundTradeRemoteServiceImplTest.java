package group.za.bank.statement.service.remote.impl;

import group.za.bank.fund.trade.entity.remote.resp.InvestMonthlyStatementSwitchResp;
import group.za.bank.fund.trade.feign.TradeFeignService;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.base.BaseTestService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.Mockito.when;


public class FundTradeRemoteServiceImplTest extends BaseTestService {
    @Mock
    TradeFeignService tradeFeignService;
    @Mock
    Logger log;
    @InjectMocks
    FundTradeRemoteServiceImpl fundTradeRemoteServiceImpl;


    @Test
    public void testQueryInvestMonthlyStatementSwitch() throws Exception {
        InvestMonthlyStatementSwitchResp mockResult = new InvestMonthlyStatementSwitchResp();
        mockResult.setDownloadInvestMonthlyStatementSwitch(true);
        ResponseData<InvestMonthlyStatementSwitchResp> objectResponseData = new ResponseData<>();
        objectResponseData.setValue(mockResult);
        objectResponseData.setCode("0000");
        when(tradeFeignService.queryInvestMonthlyStatementSwitch()).thenReturn(objectResponseData);
        fundTradeRemoteServiceImpl.queryInvestMonthlyStatementSwitch();
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme