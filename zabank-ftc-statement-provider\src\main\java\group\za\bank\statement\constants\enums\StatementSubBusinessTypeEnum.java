package group.za.bank.statement.constants.enums;

/**
 * 结单类型
 *
 * <AUTHOR>
 * @date 2022/04/29
 **/
public enum StatementSubBusinessTypeEnum {
    /**
     */
    FUND("fund", "基金","基金","FUND"),
    SFUND("sfund", "基金","基金","FUND"),
    STOCK("stock", "美股","美股","US STOCK"),
    HK_STOCK("hkStock", "港股","港股","HK STOCK"),
    CRYPTO("crypto", "虚拟资产","虛擬資產","Virtual Asset"),
    ;

    private String type;
    private String titleZh;
    private String titleHk;
    private String titleEn;


    StatementSubBusinessTypeEnum(String type, String titleZh, String titleHk, String titleEn) {
        this.type = type;
        this.titleZh = titleZh;
        this.titleHk = titleHk;
        this.titleEn = titleEn;
    }


    public String getType() {
        return type;
    }

    public String getTitleZh() {
        return titleZh;
    }

    public String getTitleHk() {
        return titleHk;
    }

    public String getTitleEn() {
        return titleEn;
    }
}
