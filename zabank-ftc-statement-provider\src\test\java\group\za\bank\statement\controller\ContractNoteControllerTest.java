package group.za.bank.statement.controller;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.entity.req.ContractNoteInfoReq;
import group.za.bank.statement.entity.resp.ContractNoteInfoResp;
import group.za.bank.statement.service.ContractNoteService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

public class ContractNoteControllerTest {
    @Mock
    ContractNoteService contractNoteService;
    @Mock
    Logger log;
    @InjectMocks
    ContractNoteController contractNoteController;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testContractInfo() throws Exception {
        when(contractNoteService.contractInfoQuery(any())).thenReturn(new ContractNoteInfoResp());

        ResponseData<ContractNoteInfoResp> result = contractNoteController.contractInfo(new ContractNoteInfoReq());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme