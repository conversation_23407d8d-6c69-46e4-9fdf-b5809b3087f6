package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.trade.feign.StkActionFeignService;
import group.za.bank.sbs.trade.model.dto.StkBusinessRecordDTO;
import group.za.bank.sbs.trade.model.dto.TaxQueryDTO;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.remote.feign.StkOrderFeign;
import group.za.bank.statement.service.remote.feign.StkTradeTaxFegin;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class TradeRemoteServiceImplTest extends BaseTestService {
    @Mock
    StkOrderFeign stkOrderFeign;
    @Mock
    StkTradeTaxFegin stkTradeTaxFegin;
    @Mock
    StkActionFeignService stkActionFeignService;
    @Mock
    Logger log;
    @InjectMocks
    TradeRemoteServiceImpl tradeRemoteServiceImpl;


    @Test
    public void testQueryStatementBusinessRecordList() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        when(stkOrderFeign.queryStatementBusinessRecordList(any())).thenReturn(resp);
        List<StkBusinessRecordDTO> result = tradeRemoteServiceImpl.queryStatementBusinessRecordList("boOrderNo");
    }

    @Test
    public void testQueryTaxList() throws Exception {
        ResponseData resp = new ResponseData();
        resp.setCode("0000");
        when(stkTradeTaxFegin.taxQuery(any())).thenReturn(resp);
        List<TaxQueryDTO> result = tradeRemoteServiceImpl.queryTaxList("accountId", "startDate", "endDate");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme