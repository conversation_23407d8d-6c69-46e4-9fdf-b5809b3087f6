package group.za.bank.statement.entity.dto;

import lombok.Data;
import lombok.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * <AUTHOR>
 * @ClassName MultipartFile
 * @Description TODO
 * @date 2021/8/31 15:51
 * @Version 1.0
 */
@Data
public class ByteArrayMultipartFile implements MultipartFile {
    private final String name;
    private final String originalFilename;
    private final String contentType;
    @NonNull
    private final byte[] bytes;

    public boolean isEmpty() {
        return this.bytes.length == 0;
    }

    public long getSize() {
        return (long)this.bytes.length;
    }

    public InputStream getInputStream() {
        return new ByteArrayInputStream(this.bytes);
    }

    public void transferTo(File destination) throws IOException {
        FileOutputStream outputStream = null;

        try {
            outputStream = new FileOutputStream(destination);
            outputStream.write(this.bytes);
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }

        }

    }

    public ByteArrayMultipartFile(String name, String originalFilename, String contentType, @NonNull byte[] bytes) {
        if (bytes == null) {
            throw new NullPointerException("bytes is marked @NonNull but is null");
        } else {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.bytes = bytes;
        }
    }

    public ByteArrayMultipartFile(String name, @Nullable String originalFilename, @Nullable String contentType, InputStream contentStream) throws IOException {
        this(name, originalFilename, contentType, FileCopyUtils.copyToByteArray(contentStream));
    }

    public String getName() {
        return this.name;
    }

    public String getOriginalFilename() {
        return this.originalFilename;
    }

    public String getContentType() {
        return this.contentType;
    }

    @NonNull
    public byte[] getBytes() {
        return this.bytes;
    }
}
