package group.za.bank.statement.manager;

import group.za.bank.sbs.bankfront.mapper.CounterFileInfoExtendMapper;
import group.za.bank.sbs.bankfront.mapper.CrmBatchAssetDetailExtendMapper;
import group.za.bank.sbs.bankfront.model.dto.StatementBusinessCommonDTO;
import group.za.bank.sbs.counterfront.module.entity.order.resp.QueryOrderIdByContractIdListResp;
import group.za.bank.sbs.trade.mapper.*;
import group.za.bank.sbs.trade.model.dto.*;
import group.za.bank.sbs.trade.model.entity.StkContractNote;
import group.za.bank.sbs.trade.model.entity.StkHoldingHistory;
import group.za.bank.sbs.trade.model.entity.StkOrder;
import group.za.bank.sbs.trade.model.entity.TaskResult;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.entity.convert.EntityConvert;
import group.za.bank.statement.service.remote.CounterFrontRemoteService;
import group.za.bank.statement.service.remote.TradeRemoteService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

public class StockTradeManagerTest extends BaseTestService {
    @Mock
    StkBusinessRecordExtendMapper stkBusinessRecordExtendMapper;
    @Mock
    CounterFileInfoExtendMapper counterFileInfoExtendMapper;
    @Mock
    TradeRemoteService tradeRemoteService;
    @Mock
    EntityConvert entityConvert;
    @Mock
    StkContractNoteExtendMapper stkContractNoteExtendMapper;
    @Mock
    StkHoldingHistoryExtendMapper stkHoldingHistoryExtendMapper;
    @Mock
    StkOrderExtendMapper stkOrderExtendMapper;
    @Mock
    StkActionActivityExtendMapper stkActionActivityExtendMapper;
    @Mock
    StkActionDetailExtendMapper stkActionDetailExtendMapper;
    @Mock
    TaskResultExtendMapper taskResultExtendMapper;
    @Mock
    CounterFrontRemoteService counterFrontRemoteService;
    @Mock
    CrmBatchAssetDetailExtendMapper batchAssetDetailExtendMapper;
    @Mock
    Logger log;
    @InjectMocks
    StockTradeManager stockTradeManager;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testQueryStkBusinessRecord() throws Exception {
        StkBusinessRecordDTO stkBusinessRecordDTO = new StkBusinessRecordDTO();
        when(tradeRemoteService.queryStatementBusinessRecordList(anyString())).thenReturn(Arrays.<StkBusinessRecordDTO>asList(stkBusinessRecordDTO));

        List<StkBusinessRecordDTO> result = stockTradeManager.queryStkBusinessRecord("referenceNo");
    }

    @Test
    public void testQueryStkContractNoteInfo() throws Exception {
        StkContractNote result = stockTradeManager.queryStkContractNoteInfo("orderNo", "userId", "docLang", "orderType", "activityType");
    }

    @Test
    public void testQueryHoldingHistoryByTradeDate() throws Exception {
        StkHoldingHistory result = stockTradeManager.queryHoldingHistoryByTradeDate("accountId", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime(), "stockCode");
    }

    @Test
    public void testQueryHoldingHistoryByStatementCheck() throws Exception {
        StkHoldingHistory result = stockTradeManager.queryHoldingHistoryByStatementCheck("accountId", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime(), "stockCode");
    }

    @Test
    public void testQueryOrderListByDate() throws Exception {
        List<StkOrder> result = stockTradeManager.queryOrderListByDate(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime());
    }

    @Test
    public void testQueryActionCapitalSuccessList() throws Exception {
        List<CompanyActionCapitalInfoDTO> result = stockTradeManager.queryActionCapitalSuccessList(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime());
    }

    @Test
    public void testQueryActionBookCloseData() throws Exception {
        List<CompanyActionDetailInfoDTO> result = stockTradeManager.queryActionBookCloseData(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime());
    }

    @Test
    public void testQueryTaskResultList() throws Exception {
        List<TaskResult> result = stockTradeManager.queryTaskResultList(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime());
    }

    @Test
    public void testQueryOrderInByBoNo() throws Exception {
        QueryOrderIdByContractIdListResp queryOrderIdByContractIdListResp = new QueryOrderIdByContractIdListResp();
        when(counterFrontRemoteService.queryOrderIdByContractId(anyString())).thenReturn(queryOrderIdByContractIdListResp);

        StkOrder result = stockTradeManager.queryOrderInByBoNo("boNo");
    }

    @Test
    public void testQueryActionExerciseData() throws Exception {
        List<CompanyActionExerciseDetailDTO> result = stockTradeManager.queryActionExerciseData(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime());
    }

    @Test
    public void testQueryOrderListByStatement() throws Exception {
        List<OrderInfoStatementDTO> result = stockTradeManager.queryOrderListByStatement(new GregorianCalendar(2024, Calendar.DECEMBER, 9, 19, 45).getTime());
    }

    @Test
    public void testQueryStatementBusinessCapital() throws Exception {
        List<StatementBusinessCommonDTO> result = stockTradeManager.queryCapitalListByStatement("accountId", "startDate", "endDate");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme