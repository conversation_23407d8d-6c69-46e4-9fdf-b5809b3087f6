<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdMonthlyStatementDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdMonthlyStatementData">
        <id column="id" jdbcType="BIGINT"
            property="id"/>

        <result column="creator" jdbcType="VARCHAR"
                property="creator"/>
        <result column="gmt_created" jdbcType="TIMESTAMP"
                property="gmtCreated"/>
        <result column="modifier" jdbcType="VARCHAR"
                property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP"
                property="gmtModified"/>
        <result column="is_deleted" jdbcType="CHAR"
                property="isDeleted"/>
        <result column="business_id" jdbcType="VARCHAR"
                property="businessId"/>
        <result column="statement_id" jdbcType="VARCHAR"
                property="statementId"/>
        <result column="business_type" jdbcType="VARCHAR"
                property="businessType"/>
        <result column="record_time" jdbcType="TIMESTAMP"
                property="recordTime"/>
        <result column="remark" jdbcType="VARCHAR"
                property="remark"/>

    </resultMap>





</mapper>
