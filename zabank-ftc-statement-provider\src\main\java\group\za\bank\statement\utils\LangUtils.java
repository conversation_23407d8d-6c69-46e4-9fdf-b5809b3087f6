package group.za.bank.statement.utils;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;

/**
 * <AUTHOR>
 * @createTime 13 11:32
 * @description
 */
public class LangUtils {

    /**
     * 简英繁转成
     * 简英、
     * 简繁
     *
     * @param lang
     * @return
     */
    public static String selectLang(String lang) {
        if (I18nSupportEnum.CN_ZH.getName().equals(lang)) {
            return I18nSupportEnum.CN_ZH.getName();
        }
        return I18nSupportEnum.CN_HK.getName();
    }




    /**
     * 简英繁转成
     * 简英、
     * 简繁
     *
     * @param docLang
     * @return
     */
    public static I18nSupportEnum changeDocLang2Normal(String docLang) {
        if (I18nSupportEnum.CN_ZH.getName().equals(docLang)) {
            return I18nSupportEnum.CN_ZH;
        }
        return I18nSupportEnum.CN_HK;
    }


}
