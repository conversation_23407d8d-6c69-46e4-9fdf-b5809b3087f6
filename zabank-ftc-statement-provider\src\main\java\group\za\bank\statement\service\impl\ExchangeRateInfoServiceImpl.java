package group.za.bank.statement.service.impl;

import group.za.bank.fund.domain.trade.entity.TdExchangeRateInfo;
import group.za.bank.fund.domain.trade.mapper.TdExchangeRateInfoMapper;
import group.za.bank.fund.trade.constants.enums.RateSourceEnum;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/3/15
 * @Description 换汇服务
 * @Version v1.0
 */
@Service
@Slf4j
public class ExchangeRateInfoServiceImpl implements ExchangeRateInfoService {

    @Resource
    private TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private TdExchangeRateInfoMapper tdExchangeRateInfoMapper;

    @Override
    public void initExchangeRate(String period) {
        List<TdExchangeRateInfo> exchangeHkdRateList = null;
        TdFundMonthlyStatementInfo statementInfo = null;
        try {
            statementInfo = tdMonthlyStatementInfoMapper.queryOne(period);
            if (Objects.isNull(statementInfo)) {
                throw new RuntimeException("TdFundMonthlyStatementInfo is null");
            }
            if (!Objects.isNull(statementInfo.getUsdHkdRate())) {
                log.info("initExchangeRate, 已完成汇率初始化, tdFundMonthlyStatementInfo:{}", JSON.toJSONString(statementInfo));
                return;
            }

            // 获取兑换港币的所有汇率信息
            TdExchangeRateInfo rateInfoCondition = new TdExchangeRateInfo();
            rateInfoCondition.setRateSource(RateSourceEnum.ZA_BANK_CORE.getValue());
            rateInfoCondition.setTargetCcy(CurrencyEnum.HKD.getCurrency());
            exchangeHkdRateList = tdExchangeRateInfoMapper.selectList(rateInfoCondition);

            BigDecimal cnyHkdRate = null;
            BigDecimal usdHkdRate = null;
            Date rateEffectTime = null;
            for (TdExchangeRateInfo rateInfo : exchangeHkdRateList) {
                if (Objects.isNull(rateEffectTime)) {
                    rateEffectTime = rateInfo.getEffectiveTime();
                }
                if (rateInfo.getSourceCcy().equals(CurrencyEnum.USD.getCurrency())) {
                    usdHkdRate = rateInfo.getRate();
                }
                if (rateInfo.getSourceCcy().equals(CurrencyEnum.CNY.getCurrency())) {
                    cnyHkdRate = rateInfo.getRate();
                }
            }

            if (Objects.isNull(cnyHkdRate) || Objects.isNull(usdHkdRate) || Objects.isNull(rateEffectTime)) {
                throw new RuntimeException("字段cnyHkdRate或usdHkdRate或rateEffectTime的汇率信息为空");
            }

            TdFundMonthlyStatementInfo target = new TdFundMonthlyStatementInfo();
            target.setCnyHkdRate(cnyHkdRate);
            target.setUsdHkdRate(usdHkdRate);
            target.setRateEffectTime(rateEffectTime);
            target.setGmtModified(new Date());

            TdFundMonthlyStatementInfo condition = new TdFundMonthlyStatementInfo();
            condition.setId(statementInfo.getId());
            condition.setInitStatus(statementInfo.getInitStatus());
            int count = tdMonthlyStatementInfoMapper.updateByCondition(target, condition);
            if (count != 1) {
                throw new RuntimeException("更新汇率信息失败, count：" + count);
            }

            // 保存到redis
            String redisKey = StatementRedisCacheKey.RATE_INFO_CACHE_KEY.key(period);
            this.setExchangeHkdRateToRedis(CurrencyEnum.CNY.getCurrency(), redisKey, cnyHkdRate);
            this.setExchangeHkdRateToRedis(CurrencyEnum.USD.getCurrency(), redisKey, usdHkdRate);

        } catch (Exception e) {
            log.error("initExchangeRate error, period:{}, statementInfo:{}, exchangeHkdRateList:{}",
                    period, JSON.toJSONString(statementInfo), JSON.toJSONString(exchangeHkdRateList), e);
            throw e;
        }
    }


    /**
     * 获取兑换HKD的汇率
     */
    @Override
    public BigDecimal getExchangeHkdRate(String sourceCcy, String period, String businessType) {
        // HKD和sourceCcy一致，则直接返回1
        if (CurrencyEnum.HKD.getCurrency().equals(sourceCcy)) {
            return BigDecimal.ONE;
        }
        String redisKey = StatementRedisCacheKey.RATE_INFO_CACHE_KEY.key(period);
        BigDecimal rate;
        // 先从redis中获取数据
        try {
            RBucket<Map<String, BigDecimal>> bucket = redissonClient.getBucket(redisKey);
            Map<String, BigDecimal> rateMap = bucket.get();
            if (Objects.nonNull(rateMap) && rateMap.size() > 0) {
                rate = rateMap.get(sourceCcy);
                if (Objects.nonNull(rate)) {
                    return rate;
                }
            }
        } catch (Exception e) {
            log.error("exchangeHkdRate error, sourceCcy:{}, period:{}, businessType:{}", sourceCcy, period, businessType, e);
        }
        CurrencyEnum currencyEnum = CurrencyEnum.getByCurrencyId(sourceCcy);
        // redis没有，再从DB取
        TdFundMonthlyStatementInfo statementInfo = null;
        try {
            statementInfo = tdMonthlyStatementInfoMapper.queryOne(period);
            if (Objects.isNull(statementInfo)) {
                throw new RuntimeException("TdFundMonthlyStatementInfo is null");
            }

            switch (currencyEnum) {
                case USD:
                    BigDecimal usdHkdRate = statementInfo.getUsdHkdRate();
                    if (Objects.isNull(usdHkdRate)) {
                        throw new RuntimeException("字段usdHkdRate的汇率信息为空");
                    }
                    this.setExchangeHkdRateToRedis(CurrencyEnum.USD.getCurrency(), redisKey, usdHkdRate);
                    return usdHkdRate;
                case CNY:
                    BigDecimal cnyHkdRate = statementInfo.getCnyHkdRate();
                    if (Objects.isNull(cnyHkdRate)) {
                        throw new RuntimeException("字段cnyHkdRate的汇率信息为空");
                    }
                    this.setExchangeHkdRateToRedis(CurrencyEnum.CNY.getCurrency(), redisKey, cnyHkdRate);
                    return cnyHkdRate;
                default:
                    throw new RuntimeException("不支持该币种的换汇");
            }
        } catch (Exception e) {
            log.error("exchangeHkdRate error, sourceCcy:{}, period:{}, businessType:{}, tdFundMonthlyStatementInfo:{}", sourceCcy, period, businessType, JSON.toJSONString(statementInfo), e);
            throw e;
        }
    }

    /**
     * 将数据放到redis中
     */
    @Override
    public void setExchangeHkdRateToRedis(String sourceCcy, String redisKey, BigDecimal rate) {
        try {
            RBucket<Map<String, BigDecimal>> bucket = redissonClient.getBucket(redisKey);
            Map<String, BigDecimal> rateMap = bucket.get();
            if (Objects.isNull(rateMap)) {
                rateMap = new HashMap<>(16);
            }
            rateMap.put(sourceCcy, rate);
            // redis过期时间10天
            bucket.set(rateMap, 10, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("setExchangeHkdRateToRedis error, sourceCCy:{}, redisKey:{}", sourceCcy, redisKey, e);
        }
    }

}