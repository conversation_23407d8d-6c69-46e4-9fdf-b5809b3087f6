package group.za.bank.statement.service.impl;

import group.za.bank.sbs.bankfront.model.dto.StatementBusinessCommonDTO;
import group.za.bank.sbs.business.model.resp.TransferDetailResp;
import group.za.bank.sbs.trade.mapper.StkActionDetailSubMapper;
import group.za.bank.sbs.trade.model.dto.TaxQueryDTO;
import group.za.bank.sbs.tradedata.model.dto.ActionCapitalInfoDTO;
import group.za.bank.sbs.tradedata.model.resp.ActionCapitalInfoResp;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.domain.entity.TdStatementActionPendingData;
import group.za.bank.statement.entity.dto.StatementTradeChangeDetailDto;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.StatementActionPendingDataService;
import group.za.bank.statement.service.TdStockStatementDataService;
import group.za.bank.statement.service.remote.BusinessRemoteService;
import group.za.bank.statement.service.remote.TradeRemoteService;
import group.za.bank.statement.service.remote.TradedataRemoteService;
import group.za.invest.core.dto.Result;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.*;

public class StockStatementActionServiceImplTest extends BaseTestService {
    @Mock
    TradeRemoteService tradeRemoteService;
    @Mock
    TradedataRemoteService tradedataRemoteService;
    @Mock
    StatementActionPendingDataService statementActionPendingDataService;
    @Mock
    TdStockStatementDataService stockStatementDataService;
    @Mock
    BusinessRemoteService businessRemoteService;
    @Mock
    StkActionDetailSubMapper stkActionDetailSubMapper;
    @Mock
    StockTradeManager stockTradeManager;
    @Mock
    Logger log;
    @InjectMocks
    StockStatementActionServiceImpl stockStatementActionServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetTaxTradeChangeDetailData() throws Exception {
        TaxQueryDTO taxQueryDTO = new TaxQueryDTO();
        taxQueryDTO.setAmt(new BigDecimal("1"));
        when(tradeRemoteService.queryTaxList(anyString(), anyString(), anyString())).thenReturn(Arrays.<TaxQueryDTO>asList(taxQueryDTO));

        List<StatementTradeChangeDetailDto> result = stockStatementActionServiceImpl.getTaxTradeChangeDetailData("US", "accountId", "202401");
    }

    @Test
    public void testFilterAndAttachActionCapitalForPeriod() throws Exception {
        when(stockStatementDataService.findByPrimaryKey(anyLong())).thenReturn(new Result<>());
        TdStatementActionPendingData tdStatementActionPendingData1 = new TdStatementActionPendingData();
        tdStatementActionPendingData1.setStatementDataId(1L);
        tdStatementActionPendingData1.setActionId("1111");
        when(statementActionPendingDataService.queryPendingList(anyString(), anyString(), anyString())).thenReturn(Arrays.<TdStatementActionPendingData>asList(tdStatementActionPendingData1));
        TdStatementActionPendingData tdStatementActionPendingData = new TdStatementActionPendingData();
        tdStatementActionPendingData.setStatementDataId(1L);
        tdStatementActionPendingData.setActionId("1111");
        when(statementActionPendingDataService.queryPendingListForPeriod(anyString(), anyString(), anyString())).thenReturn(Arrays.<TdStatementActionPendingData>asList(tdStatementActionPendingData));
        ActionCapitalInfoResp actionCapitalInfoResp = new ActionCapitalInfoResp();
        ActionCapitalInfoDTO actionCapitalInfoDTO = new ActionCapitalInfoDTO();
        actionCapitalInfoDTO.setReferenceNo("1111");
        actionCapitalInfoDTO.setCapitalTime(new Date());
        actionCapitalInfoResp.setDetailList(Arrays.asList(actionCapitalInfoDTO));
        when(tradedataRemoteService.actionCapitalInfoQuery(any())).thenReturn(actionCapitalInfoResp);
        StatementTradeChangeDetailDto statementTradeChangeDetailDto = new StatementTradeChangeDetailDto();
        statementTradeChangeDetailDto.setBusinessType("CDV");
        List<StatementTradeChangeDetailDto> result = stockStatementActionServiceImpl.filterAndAttachActionCapitalForPeriod("", "202401", "bankUserId", "accountId", Arrays.<StatementTradeChangeDetailDto>asList(statementTradeChangeDetailDto));
    }

    @Test
    public void testReBuildActionBusinessTypeData() throws Exception {

        stockStatementActionServiceImpl.reBuildActionBusinessTypeData(Arrays.<StatementTradeChangeDetailDto>asList(new StatementTradeChangeDetailDto()));
    }

    @Test
    public void testGetTransferFeeTradeChangeDetailData() throws Exception {
        TransferDetailResp transferDetailResp = new TransferDetailResp();
        transferDetailResp.setDebitAmount(new BigDecimal("1"));
        when(businessRemoteService.queryTransferDetailList(any())).thenReturn(Arrays.<TransferDetailResp>asList(transferDetailResp));

        List<StatementTradeChangeDetailDto> result = stockStatementActionServiceImpl.getTransferFeeTradeChangeDetailData("bankUserId", "accountId", "202401");
    }

    @Test
    public void testQueryTransferDetailList() throws Exception {
        TransferDetailResp transferDetailResp = new TransferDetailResp();
        when(businessRemoteService.queryTransferDetailList(any())).thenReturn(Arrays.<TransferDetailResp>asList(transferDetailResp));

        List<TransferDetailResp> result = stockStatementActionServiceImpl.queryTransferDetailList("accountId", "202401");
    }

    @Test
    public void testGetDividendPressAndRebateDataByAccountId() throws Exception {
        List<StatementTradeChangeDetailDto> result = stockStatementActionServiceImpl.getDividendPressAndRebateDataByAccountId("US","accountId", "202401");
    }

    @Test
    public void testGetBusinessCapitalData() throws Exception {
        StatementBusinessCommonDTO statementBusinessCapitalDTO = new StatementBusinessCommonDTO();
        statementBusinessCapitalDTO.setBusinessAmount(new BigDecimal("1"));
        when(stockTradeManager.queryCapitalListByStatement( anyString(), anyString(), anyString())).thenReturn(Arrays.<StatementBusinessCommonDTO>asList(statementBusinessCapitalDTO));

        List<StatementTradeChangeDetailDto> result = stockStatementActionServiceImpl.getBusinessCapitalData("US","accountId", "202401");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme