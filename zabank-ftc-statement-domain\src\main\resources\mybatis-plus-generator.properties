## \u4F5C\u8005
author=system
## \u751F\u6210\u540E\u662F\u5426\u6253\u5F00\u8D44\u6E90\u7BA1\u7406\u5668
open=false
## \u662F\u5426\u8986\u76D6\u539F\u6765\u7684\u6587\u4EF6
override=true
## \u5F00\u542FSwagger2\u6A21\u5F0F
swagger2=false
## \u662F\u5426\u751F\u6210controller
controllerGen=false
## \u662F\u5426\u751F\u6210controller
serviceGen=false

## \u9A71\u52A8\u8FDE\u63A5\u7684URL
dataSource.url=**************************************************************************************
## \u9A71\u52A8\u540D\u79F0
dataSource.driverName=com.mysql.jdbc.Driver
## \u6570\u636E\u5E93\u8FDE\u63A5\u7528\u6237\u540D
dataSource.username=za_bank_ftc_dev
dataSource.password=J7IiJVcggW^RNISx
## \u6570\u636E\u5E93\u8FDE\u63A5\u5BC6\u7801\uFF0C\u5BC6\u7801\u4E0D\u9700\u8981push\u5230git\u4E0A\uFF0C\u672C\u5730\u4F7F\u7528\u65F6\u624D\u914D\u7F6E

## \u7236\u5305\u540D\u3002\u5982\u679C\u4E3A\u7A7A\uFF0C\u5C06\u4E0B\u9762\u5B50\u5305\u540D\u5FC5\u987B\u5199\u5168\u90E8\uFF0C \u5426\u5219\u5C31\u53EA\u9700\u5199\u5B50\u5305\u540D
package.parent=group.za.bank.statement.domain
## Entity\u5305\u540D
package.entity=entity
## service\u5305\u540D
package.service=service
## Service Impl\u5305\u540D\u5305\u540D
package.serviceImpl=service.impl
## mapper\u5305\u540D\u5305\u540D
package.mapper=mapper
## Controller\u5305\u540D\u5305\u540D\u5305\u540D
package.controller=web


## \u7B56\u7565\u914D\u7F6E\u9879
## \u6570\u636E\u5E93\u8868\u6620\u5C04\u5230\u5B9E\u4F53\u7684\u547D\u540D\u7B56\u7565
strategy.naming=underline_to_camel
## \u6570\u636E\u5E93\u5B57\u6BB5\u6620\u5C04\u5230\u5B9E\u4F53\u7684\u547D\u540D\u7B56\u7565
strategy.fieldNaming=underline_to_camel
## \u5305\u542B\u7684\u8868\uFF0C\u4E0E\u6392\u9664\u7684\u8868\u4E8C\u9009\u4E00\u914D\u7F6E
strategy.include=td_fund_monthly_statement_info
## \u6392\u9664\u7684\u8868\uFF0C\u4E0E\u5305\u542B\u7684\u8868\u4E8C\u9009\u4E00\u914D\u7F6E
#strategy.exclude=