monthly.statement.doc.hkTempKey = hk-zabank_invest_monthly_hk_20250310_v1.html
monthly.statement.doc.zhTempKey = hk-zabank_invest_monthly_zh_20250310_v1.html

monthly.statement.defaultMonthlyStatementBusinessType = fund,stock,crypto,sfund,hkStock

statement.company.fraction.ib.sell.check = true

zainvest.sharding.group.sbsIpo-group.beanNamePrefix = sbsIpo
zainvest.sharding.group.sbsIpo-group.mybatis.mapper-locations[0] = classpath*:mapper/*.xml
zainvest.sharding.group.sbsIpo-group.mybatis.mapper-scanner.base-package = group.za.bank.sbs.ipo.mapper
zainvest.sharding.group.sbsIpo-group.databases[0].ip =
zainvest.sharding.group.sbsIpo-group.databases[0].port = 3303
zainvest.sharding.group.sbsIpo-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.sbsIpo-group.databases[0].userName =
zainvest.sharding.group.sbsIpo-group.databases[0].password =
zainvest.sharding.group.sbsIpo-group.databases[0].dbName = zabank_sbs_ipo