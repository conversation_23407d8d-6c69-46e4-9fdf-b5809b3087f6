package group.za.bank.statement.service.remote;

import group.za.bank.sbs.counterfront.module.entity.order.resp.QueryOrderIdByContractIdListResp;

/**
 * 柜台网关远程接口
 *
 * <AUTHOR>
 * @date 2024/04/25
 **/
public interface CounterFrontRemoteService {

    /**
     * 根据BO订单号查询FO订单号
     * @param contractId
     * @return
     */
    QueryOrderIdByContractIdListResp queryOrderIdByContractId(String contractId);
}
