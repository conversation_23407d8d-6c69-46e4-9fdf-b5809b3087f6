<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdCloseAccountMonthlyStatementMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdCloseAccountMonthlyStatement">
                        <id column="id" jdbcType="BIGINT"
                            property="id"/>
                        <result column="creator" jdbcType="VARCHAR"
                                property="creator"/>
                        <result column="gmt_created" jdbcType="TIMESTAMP"
                                property="gmtCreated"/>
                        <result column="modifier" jdbcType="VARCHAR"
                                property="modifier"/>
                        <result column="gmt_modified" jdbcType="TIMESTAMP"
                                property="gmtModified"/>
                        <result column="is_deleted" jdbcType="CHAR"
                                property="isDeleted"/>
                        <result column="business_id" jdbcType="VARCHAR"
                                property="businessId"/>
                        <result column="period" jdbcType="CHAR"
                                property="period"/>
                        <result column="bank_user_id" jdbcType="VARCHAR"
                                property="bankUserId"/>
                        <result column="client_id" jdbcType="VARCHAR"
                                property="clientId"/>
                        <result column="send_status" jdbcType="TINYINT"
                                property="sendStatus"/>
                        <result column="doc_lang" jdbcType="VARCHAR"
                                property="docLang"/>
                        <result column="temp_key" jdbcType="VARCHAR"
                                property="tempKey"/>
                        <result column="doc_url" jdbcType="VARCHAR"
                                property="docUrl"/>
                        <result column="pub_time" jdbcType="TIMESTAMP"
                                property="pubTime"/>
                        <result column="remark" jdbcType="VARCHAR"
                                property="remark"/>
            </resultMap>

            <!-- 通用查询结果列 -->
            <sql id="Base_Column_List">
                id, business_id, period, bank_user_id, client_id, send_status, doc_lang, temp_key, doc_url, pub_time, remark, creator, gmt_created, modifier, gmt_modified, is_deleted
            </sql>

            <!-- 自定义通用SQL查询条件 -->
            <sql id="Where_Extra_Condition">
            </sql>


    <!--物理按期数删除数据-->
    <delete id="deleteCloseAccountMonthlyStatement">
        DELETE FROM
        td_close_account_monthly_statement
        WHERE
        period = #{period}
    </delete>

    <select id="queryFundCloseAccountMonthlyStatement" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        td_close_account_monthly_statement
        WHERE
        period = #{period}
        AND is_deleted = 'N'
        AND id > #{id}
        order by id
        limit #{pageSize}
    </select>


    </mapper>
