package group.za.bank.statement.service.impl;

import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/12/9
 * @Description 南向通基金实现类
 * @Version v1.0
 */
@Slf4j
@Service("sfundStatementBusinessService")
public class SFundStatementBusinessServiceImpl extends FundStatementBusinessServiceImpl {

    @Override
    public String getBusinessType() {
        return AccountTypeEnum.S_FUND.getValue();
    }
}
