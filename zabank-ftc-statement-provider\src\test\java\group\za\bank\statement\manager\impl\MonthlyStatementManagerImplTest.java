package group.za.bank.statement.manager.impl;

import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.domain.entity.*;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementDataMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.domain.mapper.TdStockStatementDataMapper;
import group.za.bank.statement.domain.repository.TdMonthlyStatementDataRepository;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MonthlyStatementManagerImplTest {
    @Mock
    TdMonthlyStatementDataMapper tdMonthlyStatementDataMapper;
    @Mock
    TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;
    @Mock
    TdMonthlyStatementMapper tdMonthlyStatementMapper;
    @Mock
    TdMonthlyStatementDataRepository tdMonthlyStatementDataRepository;
    @Mock
    SystemConfig systemConfig;
    @Mock
    TdStockStatementDataMapper stockStatementDataMapper;
    @Mock
    Logger log;
    @InjectMocks
    MonthlyStatementManagerImpl monthlyStatementManagerImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetPubConfirmedFundStatementInfo() throws Exception {
        monthlyStatementManagerImpl.getPubConfirmedFundStatementInfo("202401");
    }

    @Test
    public void testCreateStatement() throws Exception {
        monthlyStatementManagerImpl.createStatement("business", "202401", "hkTempKey", "zhTempKey", "", "");
    }

    @Test(expected = BusinessException.class)
    public void testStatementInitFinish() throws Exception {
        monthlyStatementManagerImpl.statementInitFinish("business", "202401");
    }

    @Test(expected = BusinessException.class)
    public void testStatementPubConfirm() throws Exception {
        monthlyStatementManagerImpl.statementPubConfirm("202401", "operatorId");
    }

    @Test
    public void testGetStatementRecord() throws Exception {
        TdFundMonthlyStatement result = monthlyStatementManagerImpl.getStatementRecord("statementId");
    }

    @Test
    public void testGetStatementRecordByDocLang() throws Exception {
        TdFundMonthlyStatement result = monthlyStatementManagerImpl.getStatementRecordByDocLang("202401", "business", "clientId", "docLang");
    }

    @Test
    public void testListUserStatementRecords() throws Exception {
        List<TdFundMonthlyStatement> result = monthlyStatementManagerImpl.listUserStatementRecords("202401", "clientId");
    }

    @Test
    public void testCreateUserMonthlyStatementRecords() throws Exception {
        TdFundMonthlyStatement result = monthlyStatementManagerImpl.createUserMonthlyStatementRecords("docLang", new TdFundMonthlyStatementInfo(), new UserInvestClientInfoDto(), true, new Date());
    }

    @Test(expected = BusinessException.class)
    public void testFinishUserDataPrepare() throws Exception {
        monthlyStatementManagerImpl.finishUserDataPrepare(new TdMonthlyStatementData(), true);
    }

    @Test(expected = BusinessException.class)
    public void testUserStatementInitFinish() throws Exception {
        monthlyStatementManagerImpl.userStatementInitFinish("statementId");
    }

    @Test(expected = BusinessException.class)
    public void testStatementDocNotified() throws Exception {
        monthlyStatementManagerImpl.statementDocNotified("statementId");
    }

    @Test(expected = BusinessException.class)
    public void testUserStatementDocGenerated() throws Exception {
        monthlyStatementManagerImpl.userStatementDocGenerated("businessId", "pdfPath","");
    }

    @Test(expected = BusinessException.class)
    public void testUserStatementPubFinished() throws Exception {
        monthlyStatementManagerImpl.userStatementPubFinished("statementId", "1");
    }

    @Test
    public void testGetStatement() throws Exception {
        TdFundMonthlyStatementInfo result = monthlyStatementManagerImpl.getStatement("202401");
    }

    @Test
    public void testListUserStatementDataRecord() throws Exception {
        List<TdMonthlyStatementData> result = monthlyStatementManagerImpl.listUserStatementDataRecord("statementId");
    }

    @Test
    public void testCreateUserStatementBusinessData() throws Exception {
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod("202201");
        statementInfo.setBusinessType("fund,stock");

       /* List<TdMonthlyStatementData> result = monthlyStatementManagerImpl.createUserStatementBusinessData(
                statementInfo, new TdFundMonthlyStatement(), new HashMap<String, UserInvestClientInfoDto>() {{
            put("String", new UserInvestClientInfoDto());
        }});*/
    }

    @Test
    public void testIsAllStatementDocGenerated() throws Exception {
        when(tdMonthlyStatementMapper.countDocUnFinishedStatement(anyString(), anyString())).thenReturn(0);

        boolean result = monthlyStatementManagerImpl.isAllStatementDocGenerated("business", "202401");
    }

    @Test
    public void testSaveUserStatementBusinessData() throws Exception {
        monthlyStatementManagerImpl.saveUserStatementBusinessData("statementId", "fund,stock", new HashMap<String, Object>() {{
            put("String", "dataMap");
        }});
    }

    @Test
    public void testListUserStatementBusinessData() throws Exception {
        when(tdMonthlyStatementDataRepository.findByStatementId(anyString())).thenReturn(Arrays.<TdMonthlyStatementBusinessData>asList(new TdMonthlyStatementBusinessData()));

        List<TdMonthlyStatementBusinessData> result = monthlyStatementManagerImpl.listUserStatementBusinessData("statementId");
    }

    @Test
    public void testInsertBatchStatementData() throws Exception {
        monthlyStatementManagerImpl.insertBatchStatementData(Arrays.<TdStockStatementData>asList(new TdStockStatementData()));
    }

    @Test
    public void testUpdateUserStatementBusinessData() throws Exception {

    }

    @Test
    public void testGetUserStatementBusinessData() throws Exception {
        when(tdMonthlyStatementDataRepository.getByStatementIdAndBusinessType(anyString(), anyString())).thenReturn(new TdMonthlyStatementBusinessData());

        TdMonthlyStatementBusinessData result = monthlyStatementManagerImpl.getUserStatementBusinessData("statementId", "fund,stock");
    }

    @Test
    public void testDeleteUserStatementBusinessData() throws Exception {
        monthlyStatementManagerImpl.deleteUserStatementBusinessData("statementId", "fund,stock");
    }

    @Test
    public void testQueryUserInvestMonthlyStatementList() throws Exception {
        List<TdFundMonthlyStatement> result = monthlyStatementManagerImpl.queryUserInvestMonthlyStatementList("bankUserId", "clientId", "2024", "202401");
    }
}

