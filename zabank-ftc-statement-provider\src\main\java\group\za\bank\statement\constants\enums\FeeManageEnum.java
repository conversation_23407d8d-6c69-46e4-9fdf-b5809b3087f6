package group.za.bank.statement.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 港股成交单据字段枚举
 * <AUTHOR>
 * @date 2022/04/27
 **/
@AllArgsConstructor
@Getter
public enum FeeManageEnum {

    /** 經紀佣金 **/
    COMMISSION("","COMMISSION",FeeChargeType.SHARE.getValue(),"Commission Fee","經紀佣金",new BigDecimal("0.0048"),new BigDecimal("1.88"),BigDecimal.valueOf(Long.MAX_VALUE),RoundingMode.UP,2),
    /** 交收費 **/
    SETTLE_OR_CCASS_FEE("","SETTLE_OR_CCASS_FEE",FeeChargeType.SHARE.getValue(),"Clearing Fee","交收費",new BigDecimal("0.003"),new BigDecimal("0.01"),BigDecimal.valueOf(Long.MAX_VALUE),RoundingMode.UP,2),
    /** 證監會費 **/
    SECURITIES_MANAGEMENT_FEE("","SECURITIES_MANAGEMENT_FEE",FeeChargeType.AMOUNT.getValue(),"SEC Fee","證監會費",new BigDecimal("0.000029"),new BigDecimal("0.01"),new BigDecimal("5.95"),RoundingMode.UP,2),
    /** 交易活動費 **/
    TRADING_FEE("","TRADING_FEE",FeeChargeType.SHARE.getValue(),"Trading Activity Fee","交易活動費",new BigDecimal("0.00013"),new BigDecimal("0.01"),new BigDecimal("6.49"),RoundingMode.UP,2),
    ;

    private String feeId;
    private String type;
    private String chargeType;
    private String name;
    private String feeChineseName;
    private BigDecimal feeRate;
    private BigDecimal minimumPerTransaction;
    private BigDecimal maximumPerTransaction;
    private RoundingMode roundingMode;
    private int scale;

}
