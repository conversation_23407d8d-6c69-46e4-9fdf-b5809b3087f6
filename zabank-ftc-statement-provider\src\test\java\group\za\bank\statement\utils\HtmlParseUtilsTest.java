package group.za.bank.statement.utils;

import org.junit.Test;

public class HtmlParseUtilsTest {

    @Test
    public void testAppendAfter() throws Exception {
        String result = HtmlParseUtils.appendAfter("source", "separator", "appendStr");
    }

    @Test
    public void testAppendAfter2() throws Exception {
        String result = HtmlParseUtils.appendAfter("source", "separator1", "separator2", "appendStr");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme