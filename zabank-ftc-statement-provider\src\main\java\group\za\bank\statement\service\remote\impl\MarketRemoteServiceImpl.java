package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.ResponseValidator;
import group.za.bank.market.entity.req.FundProductIdListReq;
import group.za.bank.market.entity.req.MarketTradeDayReq;
import group.za.bank.market.entity.resp.FundInfoResp;
import group.za.bank.market.entity.resp.TradeDayResp;
import group.za.bank.market.feign.MarketFeignService;
import group.za.bank.statement.service.remote.MarketRemoteService;
import group.za.invest.web.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.GET_FUND_INFO_FAIL;
import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.GET_FUND_MONTH_LAST_TRADE_DATE_ERROR;

/**
 * Created on 2020/11/20 13:57.
 *
 */
@Slf4j
@Service
public class MarketRemoteServiceImpl implements MarketRemoteService {

    @Resource
    private MarketFeignService marketFeignService;




    /**
     * @param productIdList
     * @return group.za.invest.market.entity.resp.FundTradeInfoResp
     * @Description 批量获取基金基本信息
     * @Date 2020/10/13 16:18
     * @Param [productId]
     */
    @Override
    public Map<String, FundInfoResp> queryFundInfo(List<String> productIdList) {
        FundProductIdListReq req = new FundProductIdListReq();
        req.setProductIdList(productIdList);
        ResponseData<List<FundInfoResp>> response = marketFeignService.getFundInfoByProductIdList(req);
        if(log.isDebugEnabled()){
            log.debug("rpc queryFundInfo! req:{},resp:{}",req,response);
        }
        if (!response.judgeSuccess()) {
            throw new BusinessException(response.getCode(), response.getMsg());
        }
        List<FundInfoResp> responseData = response.getValue();
        if (CollectionUtils.isEmpty(responseData)) {
            throw new BusinessException(GET_FUND_INFO_FAIL);
        }
        return responseData.stream().collect(Collectors.toMap(FundInfoResp::getProductId, fundInfoResp -> fundInfoResp));
    }


    /**
     * 指定月份的最后一个交易日
     * /inner/market/tradeDaysBeforeDate
     * @param monthDate
     * @return
     */
    @Override
    public Date getMonthLatestTradeDate(Date monthDate) {
        MarketTradeDayReq req = new MarketTradeDayReq();
        ResponseData<List<TradeDayResp>> response = null;
        try {
            Date nextMonthDate = DateUtil.addMonth(monthDate, 1);
            Date nextMonthFirstDate = DateUtil.getFirstDayOfMonth(nextMonthDate);

            req.setNowDate(nextMonthFirstDate);
            req.setSize(1);
            response = marketFeignService.marketTradeDaysBeforeDate(req);
            log.info("getMonthLatestTradeDate, 入参:{}, 出参:{}", JSON.toJSONString(req), JSON.toJSONString(response));
            ResponseValidator.checkRemoteResponseValue(response);
            List<TradeDayResp> tradeDayRespList = response.getValue();
            if (CollectionUtils.isEmpty(tradeDayRespList)) {
                throw new BusinessException(GET_FUND_MONTH_LAST_TRADE_DATE_ERROR);
            }
            return tradeDayRespList.get(0).getTradeDay();
        } catch (Exception e) {
            log.error("getMonthLatestTradeDate error, 入参:{}, 出参:{}", JSON.toJSONString(req), JSON.toJSONString(response), e);
            throw e;
        }
    }
}
