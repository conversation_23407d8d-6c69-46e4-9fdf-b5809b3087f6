package group.za.bank.statement.agent;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.sbs.trade.model.req.feign.StockInfoReq;
import group.za.bank.sbs.trade.model.resp.feign.StockInfoResp;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.service.remote.StkInfoRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 股票信息缓存
 * <AUTHOR>
 * @date 2022/05/11
 **/
@Slf4j
@Component("stkInfoAgent")
public class StkInfoAgent {

    /** 缓存8小时 */
    private final static Long TIMEOUT = 60 * 60 * 8L;

    @Autowired
    private StkInfoRemoteService stkInfoRemoteService;


    public StockInfoResp getStkInfo(String marketCode, String stockCode){
        String redisKey = StatementRedisCacheKey.STK_INFO_CACHE_KEY.key(marketCode, stockCode);

        StockInfoReq stockInfoReq = new StockInfoReq();
        stockInfoReq.setStockCode(stockCode);
        stockInfoReq.setMarketCode(marketCode);

        return AbstractCacheTemplate.queryFromCache(redisKey, TIMEOUT, TimeUnit.SECONDS,
                ()-> stkInfoRemoteService.getStockInfo(stockInfoReq), false);
    }

    public String getStockNameAbbr(String marketCode, String stockCode, String userLang) {
        log.info("getStockNameAbbr start,marketCode:{},stockCode:{},lang:{}", marketCode, stockCode, userLang);
        String stockName = "";
        StockInfoResp stkInfo = this.getStkInfo(marketCode, stockCode);
        if(null == stkInfo){
            log.info("getStockNameAbbr stkInfo is null");
            return stockName;
        }

        return getStockNameAbbr(stkInfo, userLang);
    }

    /**
     * 如果stockName为空则默认取英文进行兜底展示
     * @param marketCode
     * @param stockCode
     * @param userLang
     * @return
     */
    public String getStockNameAbbrDefaultEn(String marketCode, String stockCode, String userLang) {
        String stockNameAbbr = getStockNameAbbr(marketCode, stockCode, userLang);
        if(StringUtils.isEmpty(stockNameAbbr)){
            return getStockNameAbbr(marketCode, stockCode, I18nSupportEnum.US_EN.getName());
        }
        return stockNameAbbr;
    }

    /**
     * 根据用户语言获取对应的stockName(如果为空不做英文兜底)
     * @param stkInfo
     * @param userLang
     * @return
     */
    public String getStockNameAbbr(StockInfoResp stkInfo, String userLang) {
        String stockName = "";
        if ((I18nSupportEnum.CN_ZH.getName().equals(userLang) || I18nSupportEnum.CN_ZH.getNameOfBank().equals(userLang))) {
            stockName = stkInfo.getStockNameCnAbbr();
            if(StringUtils.isEmpty(stockName)){
                stockName = StringUtils.defaultIfBlank(stkInfo.getStockNameCn(), "");
            }
        }

        if ((I18nSupportEnum.CN_HK.getName().equals(userLang) || I18nSupportEnum.CN_HK.getNameOfBank().equals(userLang))) {
            stockName = stkInfo.getStockNameHkAbbr();
            if(StringUtils.isEmpty(stockName)){
                stockName = StringUtils.defaultIfBlank(stkInfo.getStockNameHk(), "");
            }
        }

        if ((I18nSupportEnum.US_EN.getName().equals(userLang) || I18nSupportEnum.US_EN.getNameOfBank().equals(userLang))) {
            stockName = stkInfo.getStockNameEngAbbr();
            if(StringUtils.isEmpty(stockName)){
                stockName = StringUtils.defaultIfBlank(stkInfo.getStockNameEng(),"");
            }
        }

        log.info("getStockNameAbbr end,stockName:{}", stockName);
        return stockName;
    }

    public String getIpoStockNameAbbr(String stockNameCn, String stockNameEn, String stockNameHk, String userLang) {
        String stockName = "";
        if ((I18nSupportEnum.CN_ZH.getName().equals(userLang) || I18nSupportEnum.CN_ZH.getNameOfBank().equals(userLang))) {
            stockName = stockNameCn;
        }

        if ((I18nSupportEnum.CN_HK.getName().equals(userLang) || I18nSupportEnum.CN_HK.getNameOfBank().equals(userLang))) {
            stockName = stockNameHk;
        }

        if ((I18nSupportEnum.US_EN.getName().equals(userLang) || I18nSupportEnum.US_EN.getNameOfBank().equals(userLang))) {
            stockName = stockNameEn;
        }

        log.info("getIpoStockNameAbbr end,stockName:{}", stockName);
        return stockName;
    }
}
