package group.za.bank.statement.entity.dto;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 21 11:21
 * @description
 */
@Data
public class UserStatementRecordDto {


    private String statementId;

    private String docLang;

    private String clientId;
    /**
     * 结单期数
     */
    private String period;

    /**
     * 结单业务
     */
    private String business;

    /**
     * 用户结单记录
     */
    private TdFundMonthlyStatement statementRecord;


    /**
     * 用户结单数据记录
     */
    private List<TdMonthlyStatementData> monthlyStatementDataList;





}
