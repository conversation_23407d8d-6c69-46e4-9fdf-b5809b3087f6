package group.za.bank.statement.domain.entity;

import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 29 14:01
 * @description
 */
@Data
@Document(collection="td_monthly_statement_data")
public class TdMonthlyStatementBusinessData {

    @Field("id")
    private String id;

    @Field("statementId")
    @Indexed
    private String statementId;

    @Field("businessType")
    private String businessType;

    @Field("data")
    private Map<String,Object> data;

    /**
     * 记录时间
     */
    @Field("recordDate")
    private Date recordDate;

}
