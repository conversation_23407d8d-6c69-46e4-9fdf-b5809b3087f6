package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.statement.service.FundMonthlyStatementMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/7/13
 * @Description 月结单基金迁移job
 * 执行参数: 202005,202212
 * @Version v1.0
 */
@Slf4j
@JobHandler(value = "fundMonthlyStatementMigrationJob")
@Component
public class FundMonthlyStatementMigrationJob extends IJobHandler {

    @Resource
    private FundMonthlyStatementMigrationService fundMonthlyStatementMigrationService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        StopWatch watch = StopWatch.createStarted();
        log.info("fundMonthlyStatementMigrationJob start param:{}", param);

        if (StringUtils.isEmpty(param)) {
            log.warn("fundMonthlyStatementMigrationJob end param is null");
            return ReturnT.SUCCESS;
        }

        for (String period : param.split(",")) {
            try {
                fundMonthlyStatementMigrationService.fundMonthlyStatementMigration(period);
            } catch (Exception e) {
                log.error("fundMonthlyStatementMigrationJob error, period:{}", period, e);
            }
        }

        log.info("fundMonthlyStatementMigrationJob end param:{}, cost:{}ms", param, watch.getTime());
        return ReturnT.SUCCESS;
    }
}