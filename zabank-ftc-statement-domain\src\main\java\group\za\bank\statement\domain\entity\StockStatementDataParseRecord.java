package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import java.util.Date;

import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * This class corresponds to the database table stock_statement_data_parse_record
 * <p>
 * Database Table Remarks: 
 * </p>
 *
 * <AUTHOR>
 * @since 2024年08月26日 15:39:59
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "stock_statement_data_parse_record")
public class StockStatementDataParseRecord extends AuditIdEntity<Integer> {
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Name: stock_statement_data_parse_record.trade_date
     * <p>
     * Database Column Remarks: 交易日期
     * </p>
     */
    @Column(name = "trade_date", nullable = false)
    private Date tradeDate;

    /**
     * Database Column Name: stock_statement_data_parse_record.data_type
     * <p>
     * Database Column Remarks: 数据类型
     * </p>
     */
    @Column(name = "data_type", nullable = false)
    private String dataType;

    @Column(name = "parse_id", nullable = false)
    private String parseId;

    /**
     * Database Column Name: stock_statement_data_parse_record.data_status
     * <p>
     * Database Column Remarks: 解析状态:0-未开始,1-处理中,2-成功，3-失败
     * </p>
     */
    @Column(name = "data_status")
    private Integer dataStatus;

}