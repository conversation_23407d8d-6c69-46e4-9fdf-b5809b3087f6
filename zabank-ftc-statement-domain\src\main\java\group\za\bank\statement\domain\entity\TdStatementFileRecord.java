package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
* This class corresponds to the database table td_statement_file_record
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 14:37:24
*/
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_statement_file_record")
public class TdStatementFileRecord extends AuditIdEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
    * Database Column Name: td_statement_file_record.statement_type
    * <p>
    * Database Column Remarks: 1-月结单，2-日结单
    * </p>
    */
    @Column(name = "statement_type")
    private Integer statementType;

    /**
    * Database Column Name: td_statement_file_record.statement_date
    * <p>
    * Database Column Remarks: 结单日期:月结单-yyyyMM，日结单-yyyyMMdd
    * </p>
    */
    @Column(name = "statement_date")
    private Date statementDate;

    @Column(name = "file_last_modified")
    private Date fileLastModified;

    /**
    * Database Column Name: td_statement_file_record.data_status
    * <p>
    * Database Column Remarks: 文件解析状态:0-未开始,1-处理中,2-成功，3-失败
    * </p>
    */
    @Column(name = "data_status")
    private Integer dataStatus;

}