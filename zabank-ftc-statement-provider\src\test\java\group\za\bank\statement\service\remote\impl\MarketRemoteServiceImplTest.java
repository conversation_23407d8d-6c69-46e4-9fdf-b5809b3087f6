package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.market.entity.resp.FundInfoResp;
import group.za.bank.market.entity.resp.TradeDayResp;
import group.za.bank.market.feign.MarketFeignService;
import group.za.bank.statement.base.BaseTestService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class MarketRemoteServiceImplTest extends BaseTestService {
    @Mock
    MarketFeignService marketFeignService;
    @Mock
    Logger log;
    @InjectMocks
    MarketRemoteServiceImpl marketRemoteServiceImpl;


    @Test
    public void testQueryFundInfo() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        FundInfoResp fundInfoResp = new FundInfoResp();
        fundInfoResp.setProductId("String");
        responseData.setValue(Arrays.asList(fundInfoResp));
        when(marketFeignService.getFundInfoByProductIdList(any())).thenReturn(responseData);
        Map<String, FundInfoResp> result = marketRemoteServiceImpl.queryFundInfo(Arrays.<String>asList("String"));
    }

    @Test
    public void testGetMonthLatestTradeDate() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        TradeDayResp fundInfoResp = new TradeDayResp();
        fundInfoResp.setTradeDay(new Date());
        responseData.setValue(Arrays.asList(fundInfoResp));
        when(marketFeignService.marketTradeDaysBeforeDate(any())).thenReturn(responseData);
        Date result = marketRemoteServiceImpl.getMonthLatestTradeDate(new GregorianCalendar(2024, Calendar.DECEMBER, 11, 17, 4).getTime());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme