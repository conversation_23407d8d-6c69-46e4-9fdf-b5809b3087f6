package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/25
 * @Description 月结单信息
 * @Version v1.0
 */
@Data
public class MonthlyStatementDto {
    /**
     * 业务逻辑id
     */
    private String businessId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 月结单期数:'yyyyMM'
     */
    private String period;

    /**
     * 银行用户id
     */
    private String bankUserId;

    /**
     * clientId
     */
    private String clientId;

    /**
     * 月结单文档状态:0:-初始化,1-待生成,2-已生成,3-等待通知文件生成 执行顺序：0312
     */
    private Byte docStatus;

    /**
     * 月结单发送状态:0-待发送,1-已发送
     */
    private Byte pubStatus;

    /**
     * 文档语言
     */
    private String docLang;

    /**
     * 中文模板key
     */
    private String tempKey;

    /**
     * html文档地址
     */
    private String htmlUrl;

    /**
     * 文档地址
     */
    private String docUrl;

    /**
     * 发送成功时间
     */
    private Date pubTime;

    /**
     * 文档生成时间
     */
    private Date docTime;

    /**
     * 结单记录生成时间
     */
    private Date recordTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 总市值（基金+股票）
     */
    private BigDecimal totalMarketValue;

    /**
     * 币种
     */
    private String currency;

}
