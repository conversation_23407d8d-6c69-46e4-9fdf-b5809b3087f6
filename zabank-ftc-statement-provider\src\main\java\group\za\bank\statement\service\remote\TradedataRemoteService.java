package group.za.bank.statement.service.remote;

import group.za.bank.sbs.tradedata.model.req.ActionCapitalInfoQueryReq;
import group.za.bank.sbs.tradedata.model.req.ActionFractionOrderQueryReq;
import group.za.bank.sbs.tradedata.model.req.ActionInfoQueryReq;
import group.za.bank.sbs.tradedata.model.resp.ActionCapitalInfoResp;
import group.za.bank.sbs.tradedata.model.resp.ActionFractionOrderResp;
import group.za.bank.sbs.tradedata.model.resp.ActionInfoResp;

/**
 * tradedata服务远程接口
 *
 * <AUTHOR>
 * @date 2023/06/09
 **/
public interface TradedataRemoteService {

    /**
     * 公司行动小数股订单查询
     */
    ActionFractionOrderResp actionFractionOrderQuery(ActionFractionOrderQueryReq req);

    /**
     * 公司行动资金入账信息查询
     * @param req
     * @return
     */
    ActionCapitalInfoResp actionCapitalInfoQuery(ActionCapitalInfoQueryReq req);

    /**
     * 公司行动基本信息查询
     * @param req
     * @return
     */
    ActionInfoResp actionInfoQuery(ActionInfoQueryReq req);
}
