<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.StockStatementDataParseRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.StockStatementDataParseRecord">
        <id column="id" jdbcType="INTEGER"
            property="id"/>
        <result column="creator" jdbcType="VARCHAR"
                property="creator"/>
        <result column="gmt_created" jdbcType="TIMESTAMP"
                property="gmtCreated"/>
        <result column="modifier" jdbcType="VARCHAR"
                property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP"
                property="gmtModified"/>
        <result column="is_deleted" jdbcType="CHAR"
                property="isDeleted"/>
        <result column="trade_date" jdbcType="DATE"
                property="tradeDate"/>
        <result column="data_type" jdbcType="VARCHAR"
                property="dataType"/>
        <result column="parse_id" jdbcType="VARCHAR"
                property="parseId"/>
        <result column="data_status" jdbcType="INTEGER"
                property="dataStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, trade_date,parse_id, data_type, data_status, creator, gmt_created, modifier, gmt_modified, is_deleted
    </sql>

    <!-- 自定义通用SQL查询条件 -->
    <sql id="Where_Extra_Condition">
    </sql>
</mapper>
