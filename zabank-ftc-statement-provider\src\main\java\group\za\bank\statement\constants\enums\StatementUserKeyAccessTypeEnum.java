package group.za.bank.statement.constants.enums;

import group.za.bank.invest.common.constants.enums.UserKeyAccessTypeEnum;

/**
 * <AUTHOR>
 * @Date 2023/8/11
 * @Description 用户关键访问类型枚举
 * @Version v1.0
 */
public enum StatementUserKeyAccessTypeEnum implements UserKeyAccessTypeEnum {

    VIEW_INVEST_MONTH_STATEMENT("view_invest_month_statement", "查看投资月结单(股票和基金)"),
    ;
    private String value;
    private String msg;

    private StatementUserKeyAccessTypeEnum(String value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }
}