package group.za.bank.statement.domain.mapper;

import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.invest.mybatis.mapper.template.CrudMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

    /**
* This mapper interface access the database table td_fund_monthly_statement
* <p>
    * Database Table Remarks: 
    * </p>
*
* <AUTHOR>
* @since 2021年08月24日 11:36:52
*/
@Repository
public interface TdMonthlyStatementInfoMapper extends CrudMapper<TdFundMonthlyStatementInfo> {

    TdFundMonthlyStatementInfo queryOne(@Param("period") String period);

}
