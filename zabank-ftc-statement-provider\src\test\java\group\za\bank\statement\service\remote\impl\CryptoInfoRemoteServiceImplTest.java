package group.za.bank.statement.service.remote.impl;

import group.za.bank.ccs.tradecore.fegin.CryptoInfoFeignService;
import group.za.bank.ccs.tradecore.model.resp.CryptoInfoResp;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class CryptoInfoRemoteServiceImplTest extends BaseTestService {
    @Mock
    CryptoInfoFeignService cryptoInfoFeignService;
    @Mock
    Logger log;
    @InjectMocks
    CryptoInfoRemoteServiceImpl cryptoInfoRemoteServiceImpl;

    @Test
    public void testQueryCryptoInfo() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        responseData.setValue(new CryptoInfoResp());
        when(cryptoInfoFeignService.queryCryptoInfo(any())).thenReturn(responseData);
        CryptoInfoResp result = cryptoInfoRemoteServiceImpl.queryCryptoInfo("exchangeCode", "assetType");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme