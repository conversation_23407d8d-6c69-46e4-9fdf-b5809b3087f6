package group.za.bank.statement.service.impl;

import cn.hutool.core.lang.Pair;
import group.za.bank.fund.domain.clear.mapper.ClearDateRecordMapper;
import group.za.bank.fund.domain.trade.mapper.*;
import group.za.bank.invest.account.entity.dto.BankAddress;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.domain.account.mapper.UserInvestAccountMapper;
import group.za.bank.invest.pub.feign.MessageFeignService;
import group.za.bank.market.entity.resp.FundInfoResp;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.domain.mapper.TdFundMonthlyOrderTmpMapper;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.manager.ActivityStatementManager;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.remote.MarketRemoteService;
import group.za.bank.statement.service.remote.PubRemoteService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FundStatementBusinessServiceImplTest  {
    @Mock
    SystemConfig systemConfig;
    @Mock
    Map<String, FundInfoResp> fundInfoLocalCacheMap;
    @Mock
    TdFundCustAssetHistoryMapper tdFundCustAssetHistoryMapper;
    @Mock
    ClearDateRecordMapper clearDateRecordMapper;
    @Mock
    UserInvestAccountMapper userInvestAccountMapper;
    @Mock
    TdFundMonthlyStatementMapper tdFundMonthlyStatementMapper;
    @Mock
    TdFundMonthlyStatementInfoMapper tdFundMonthlyStatementInfoMapper;
    @Mock
    MessageFeignService messageFeignService;
    @Mock
    UserRemoteService userRemoteService;
    @Mock
    PubRemoteService pubRemoteService;
    @Mock
    TdFundOrderMapper tdFundOrderMapper;
    @Mock
    TdFundHoldingHistoryMapper tdFundHoldingHistoryMapper;
    @Mock
    MarketRemoteService marketRemoteService;

    @Mock
    MonthlyStatementManager monthlyStatementManager;

    @Mock
    TdFundMonthlyOrderTmpMapper tdFundMonthlyOrderTmpMapper;

    @Mock
    ActivityStatementManager activityStatementManager;

    @InjectMocks
    @Autowired
    FundStatementBusinessServiceImpl fundStatementBusinessServiceImpl;

    private final static String ACCOUNT_ID ="A00002105";
    private final static String USER_ID ="1458074522581205504";
    private final static String BUSINESS_TYPE ="fund";
    private static final String BUSINESS = "fund,stock";
    private static final String PERIOD = "202206";

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJson;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        mockedJson = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");


    }

    @After
    public void close(){
        mockedJsonUtils.close();
        mockedJson.close();
    }

    @Test
    public void testGetBusinessType() throws Exception {
        String result = fundStatementBusinessServiceImpl.getBusinessType();
        Assert.assertEquals(BUSINESS_TYPE, result);
    }

    @Test
    public void testIsDataPrepared() throws Exception {
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setPeriod(PERIOD);
        fundStatementBusinessServiceImpl.isDataPrepared(tdFundMonthlyStatementInfo,true);
        fundStatementBusinessServiceImpl.isDataPrepared(tdFundMonthlyStatementInfo,false);
    }

    @Test
    public void testGetUserPeriodEndTotalMarket() throws Exception {
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setPeriod(PERIOD);
        tdFundMonthlyStatementInfo.setBusinessType(BUSINESS);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocLang(I18nSupportEnum.CN_HK.getName());
        tdFundMonthlyStatement.setPeriod(PERIOD);

        TdMonthlyStatementBusinessData tdMonthlyStatementData = new TdMonthlyStatementBusinessData();
        tdMonthlyStatementData.setBusinessType("stock");
        when(monthlyStatementManager.listUserStatementBusinessData(any())).thenReturn(Arrays.asList(tdMonthlyStatementData));
        Pair<BigDecimal, BigDecimal> userPeriodEndTotalMarket = fundStatementBusinessServiceImpl
                .getUserPeriodEndTotalMarketAndProfit(tdFundMonthlyStatementInfo, tdFundMonthlyStatement, CurrencyEnum.HKD, ACCOUNT_ID, "US");
        Assert.assertEquals(new BigDecimal(0), userPeriodEndTotalMarket.getKey());
    }

    @Test
    public void testIsUserNeedGenerateStatement() throws Exception {
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setPeriod(PERIOD);
        tdFundMonthlyStatementInfo.setBusinessType(BUSINESS);

        UserInvestClientInfoDto userInvestClientInfoDto = new UserInvestClientInfoDto();
        userInvestClientInfoDto.setBankUserId(USER_ID);
        userInvestClientInfoDto.setBankUserId(ACCOUNT_ID);

        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        when(tdFundMonthlyOrderTmpMapper.countMonthlyStatementFundAccount(any(), any())).thenReturn(0);

        boolean result = fundStatementBusinessServiceImpl.isUserNeedGenerateStatement(tdFundMonthlyStatementInfo, userInvestClientInfoDto, "US");

        Assert.assertEquals(false, result);
    }

    @Test
    public void testGenerateDataMap() throws Exception {
        Map<String,FundInfoResp> fundInfoRespMap = new HashMap<>();
        when(marketRemoteService.queryFundInfo(any())).thenReturn(fundInfoRespMap);
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setPeriod(PERIOD);
        tdFundMonthlyStatementInfo.setBusinessType(BUSINESS);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocLang(I18nSupportEnum.CN_HK.getName());
        tdFundMonthlyStatement.setPeriod(PERIOD);

        TdMonthlyStatementData tdMonthlyStatementData = new TdMonthlyStatementData();

        AddressInfoResp addressInfoResp = new AddressInfoResp();
        addressInfoResp.setCurrentAddress(new BankAddress());
        when(userRemoteService.queryContactAddressInfo(any())).thenReturn(addressInfoResp);

        when(activityStatementManager.getEqualHkdMonthHoldingIncomeValue(any(), any(), any(), any())).thenReturn(new BigDecimal(0));

        Map<String, Object> result = fundStatementBusinessServiceImpl.generateDataMap(tdFundMonthlyStatementInfo,tdFundMonthlyStatement,tdMonthlyStatementData, "US");

    }

    @Test
    public void testAppendFundInfoToLocalCache() throws Exception {
        Map<String,FundInfoResp> fundInfoRespMap = new HashMap<>();
        fundInfoRespMap.put("0000001",new FundInfoResp());
        when(marketRemoteService.queryFundInfo(any())).thenReturn(fundInfoRespMap);

        Map<String, FundInfoResp> result = fundStatementBusinessServiceImpl
                .appendFundInfoToLocalCache(Arrays.<String>asList("0000001"), fundInfoRespMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(result));
    }

    @Test
    public void testStatementAllProcessFinish() throws Exception {
    }
}

