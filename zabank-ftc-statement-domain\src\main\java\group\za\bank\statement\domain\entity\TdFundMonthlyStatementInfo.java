package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.math.BigDecimal;

/**
 * This class corresponds to the database table td_fund_monthly_statement_info
 * <p>
 * Database Table Remarks:
 * </p>
 *
 * <AUTHOR>
 * @since 2021年08月25日 20:23:33
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_fund_monthly_statement_info")
public class TdFundMonthlyStatementInfo extends AuditIdEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Name: td_fund_monthly_statement_info.business_id
     * <p>
     * Database Column Remarks: 业务逻辑id
     * </p>
     */
    @Column(name = "business_id")
    private String businessId;

    /**
     * Database Column Name: td_fund_monthly_statement_info.period
     * <p>
     * Database Column Remarks: 月结单期数:'yyyyMM'
     * </p>
     */
    @Column(name = "period")
    private String period;

    /**
     * Database Column Name: td_fund_monthly_statement_info.business_type
     * <p>
     * Database Column Remarks: 业务类型
     * </p>
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * Database Column Name: td_fund_monthly_statement_info.init_status
     * <p>
     * Database Column Remarks: 初始化状态:1-处理中,2-已完成
     * </p>
     */
    @Column(name = "init_status")
    private Byte initStatus;

    /**
     * Database Column Name: td_fund_monthly_statement_info.pub_confirm_status
     * <p>
     * Database Column Remarks: 月结单发送确认状态:0-未确认，1-已确认
     * </p>
     */
    @Column(name = "pub_confirm_status")
    private Byte pubConfirmStatus;

    /**
     * Database Column Name: td_fund_monthly_statement_info.status
     * <p>
     * Database Column Remarks: 处理状态:1-处理中,2-已完成
     * </p>
     */
    @Column(name = "status", escapedName = "`status`")
    private Byte status;

    /**
     * Database Column Name: td_fund_monthly_statement_info.confirm_operator_id
     * <p>
     * Database Column Remarks: 确认发送人id
     * </p>
     */
    @Column(name = "confirm_operator_id")
    private String confirmOperatorId;

    /**
     * Database Column Name: td_fund_monthly_statement_info.confirm_operator_name
     * <p>
     * Database Column Remarks: 确认发送人名字
     * </p>
     */
    @Column(name = "confirm_operator_name")
    private String confirmOperatorName;

    /**
     * Database Column Name: td_fund_monthly_statement_info.hk_temp_key
     * <p>
     * Database Column Remarks: 繁体模板key
     * </p>
     */
    @Column(name = "hk_temp_key")
    private String hkTempKey;

    /**
     * Database Column Name: td_fund_monthly_statement_info.zh_temp_key
     * <p>
     * Database Column Remarks: 中文模板key
     * </p>
     */
    @Column(name = "zh_temp_key")
    private String zhTempKey;

    /**
     * 南向通简体模板
     */
    @Column(name = "nxt_zh_temp_key")
    private String nxtZhTempKey;

    /**
     * 南向通繁体模板
     */
    @Column(name = "nxt_hk_temp_key")
    private String nxtHkTempKey;

    /**
     * Database Column Name: td_fund_monthly_statement_info.record_time
     * <p>
     * Database Column Remarks: 结单记录生成时间
     * </p>
     */
    @Column(name = "record_time")
    private Date recordTime;

    /**
     * Database Column Name: td_fund_monthly_statement_info.total_number
     * <p>
     * Database Column Remarks: 结单数量
     * </p>
     */
    @Column(name = "total_number")
    private Integer totalNumber;

    /**
     * Database Column Name: td_fund_monthly_statement_info.total_client_number
     * <p>
     * Database Column Remarks: 当期账户数量
     * </p>
     */
    @Column(name = "total_client_number")
    private Integer totalClientNumber;

    /**
     * Database Column Name: td_fund_monthly_statement_info.finished_number
     * <p>
     * Database Column Remarks: 完成数量
     * </p>
     */
    @Column(name = "finished_number")
    private Integer finishedNumber;

    /**
     * Database Column Name: td_fund_monthly_statement_info.usd_hkd_rate
     * <p>
     * Database Column Remarks: usd换hkd汇率
     * </p>
     */
    @Column(name = "usd_hkd_rate")
    private BigDecimal usdHkdRate;

    /**
     * Database Column Name: td_fund_monthly_statement_info.cny_hkd_rate
     * <p>
     * Database Column Remarks: cny换hkd汇率
     * </p>
     */
    @Column(name = "cny_hkd_rate")
    private BigDecimal cnyHkdRate;

    /**
     * Database Column Name: td_fund_monthly_statement_info.rate_record_date
     * <p>
     * Database Column Remarks: 汇率生效时间
     * </p>
     */
    @Column(name = "rate_effect_time")
    private Date rateEffectTime;

    /**
     * Database Column Name: td_fund_monthly_statement_info.remark
     * <p>
     * Database Column Remarks: 备注
     * </p>
     */
    @Column(name = "remark")
    private String remark;

}