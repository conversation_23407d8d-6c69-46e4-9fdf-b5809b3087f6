package group.za.bank.statement.service;


import cn.hutool.core.lang.Pair;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @createTime 25 10:24
 * @description 结单业务逻辑
 * 子类实现类的beanName必须用businessType开头
 */
public interface StatementBusinessService<T> {

    String getBusinessType();


    /**
     * 本期数据是否已经准备完毕
     *
     * @param tdFundMonthlyStatementInfo
     * @return
     */
    boolean isDataPrepared(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo,boolean useCache);

    /**
     * 抽取业务结单数据
     *
     * @param statementData
     * @return
     */
    Map<String, Object> generateDataMap(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement docUnGeneratedStatement, TdMonthlyStatementData statementData, String marketCode );


    /**
     * 将map数据解析成对应的data
     * @param businessDataMap
     * @return
     */
    T parseDataMap2Dto(Map<String, Object> businessDataMap);


    /**
     * 判断用户本期该业务是否需要生成月结单
     *
     * @param tdFundMonthlyStatementInfo
     * @param userInvestClientInfoDto
     * @return
     */
    boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo,UserInvestClientInfoDto userInvestClientInfoDto, String marketCode);



    /**
     * 判断用户本期该业务是否需要生成月结单
     *
     * @param tdFundMonthlyStatementInfo
     * @return
     */
    boolean isUserNeedGenerateStatement(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo,String userId,String accountId, String marketCode);
    /**
     * 用户期末总市值及总盈亏
     *
     * @param tdFundMonthlyStatementInfo
     * @param targetCurrency
     * @return
     */
    Pair<BigDecimal, BigDecimal> getUserPeriodEndTotalMarketAndProfit(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement statementRecord, CurrencyEnum targetCurrency, String accountId, String marketCode);


    /**
     * 本期月结单全部处理完毕，包括推送。
     * 留个口子，给业务服务清除缓存等信息
     * 业务处理完之后业务执行的操作
     * @param tdFundMonthlyStatementInfo
     */
    void statementAllProcessFinishHook(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo);





}
