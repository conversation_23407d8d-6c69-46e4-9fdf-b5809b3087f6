package group.za.bank.statement.service.impl;

import com.google.common.collect.Lists;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.BankAddress;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.EnumYesOrNo;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.FundMonthlyStatementDataDto;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StatementBasicInfoServiceImplTest {

    @InjectMocks
    private StatementBasicInfoServiceImpl statementBasicInfoService;

    @Mock
    private SystemConfig systemConfig;

    @Mock
    private UserRemoteService userRemoteService;

    @Mock
    private MonthlyStatementService monthlyStatementService;

    @Mock
    private MonthlyStatementManager monthlyStatementManager;

    @Mock
    private StatementBusinessService statementBusinessService;

    private static final String TEST_PERIOD = "202311";
    private static final String TEST_BANK_USER_ID = "testUser";
    private static final String TEST_CLIENT_ID = "testClient";
    private static final String TEST_ACCOUNT_ID = "testAccount";

    private MockedStatic<JSON> mockedJSON;
    private MockedStatic<JsonUtils> mockedJsonUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        doReturn(",").when(systemConfig).getBusinessSubTypeSeparator();

        // Mock JSON
        mockedJSON = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");
    }

    @After
    public void after(){
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        mockedJSON.close();
    }

    @Test
    public void testGenerateDataDto_NormalCase() {
        // Prepare test data
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setBusinessType(AccountTypeEnum.FUND.getValue() + "," + AccountTypeEnum.STOCK.getValue());

        TdFundMonthlyStatement statementRecord = new TdFundMonthlyStatement();
        statementRecord.setBankUserId(TEST_BANK_USER_ID);
        statementRecord.setClientId(TEST_CLIENT_ID);
        statementRecord.setRecordTime(new Date());

        TdMonthlyStatementData fundData = new TdMonthlyStatementData();
        fundData.setBusinessType(AccountTypeEnum.FUND.getValue());
        fundData.setAccountId(TEST_ACCOUNT_ID);

        TdMonthlyStatementData stockData = new TdMonthlyStatementData();
        stockData.setBusinessType(AccountTypeEnum.STOCK.getValue());
        stockData.setAccountId(TEST_ACCOUNT_ID);

        List<TdMonthlyStatementData> dataList = Lists.newArrayList(fundData, stockData);

        // Mock dependencies
        when(monthlyStatementService.getStatementBusinessService(anyString())).thenReturn(statementBusinessService);
        when(statementBusinessService.getUserPeriodEndTotalMarketAndProfit(any(), any(), any(), anyString(), any()))
                .thenReturn(new cn.hutool.core.lang.Pair<>(BigDecimal.TEN, BigDecimal.ONE));
        when(monthlyStatementService.showBusinessTitle(anyString(), anyString(), anyString(), any())).thenReturn(true);

        AddressInfoResp addressInfoResp = new AddressInfoResp();
        addressInfoResp.setEngName("John");
        addressInfoResp.setZhName("约翰");
        BankAddress bankAddress = new BankAddress();
        bankAddress.setEnglishAddressFlag(EnumYesOrNo.YES.getValue());
        addressInfoResp.setCurrentAddress(bankAddress);
        addressInfoResp.setOfficeLocation("Office Location");
        addressInfoResp.setStreet("Street");
        addressInfoResp.setArea("Area");
        addressInfoResp.setCountry("Country");

        when(userRemoteService.queryContactAddressInfo(anyString())).thenReturn(addressInfoResp);
        when(userRemoteService.isUserMcv(anyString())).thenReturn(false);

        // Execute test
        BaseMonthlyStatementDataDto result = statementBasicInfoService.generateDataDto(statementInfo, statementRecord, dataList);

        // Verify results
        assertNotNull(result);
        assertEquals(TEST_CLIENT_ID, result.getClientId());
        assertEquals("John 约翰", result.getCustomerName().trim());
        assertEquals("Office Location", result.getOfficeLocation());
        assertEquals("Street", result.getStreet());
        assertEquals("Area", result.getArea());
        assertEquals("Country", result.getCountry());
        assertTrue(result.isFund());
        assertTrue(result.isStock());
        assertEquals(new BigDecimal("20"), result.getTotalAmountValue());
    }

    @Test
    public void testGenerateDataDto_HistoricalFundStatement() {
        // Prepare test data
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setBusinessType(AccountTypeEnum.FUND.getValue());

        TdFundMonthlyStatement statementRecord = new TdFundMonthlyStatement();
        statementRecord.setBankUserId(TEST_BANK_USER_ID);
        statementRecord.setClientId(TEST_CLIENT_ID);
        statementRecord.setRecordTime(new Date());
        statementRecord.setRemark(StatementConstants.FUND_MIGRATION_FLAG);
        statementRecord.setBusinessId("testBusinessId");

        TdMonthlyStatementData fundData = new TdMonthlyStatementData();
        fundData.setBusinessType(AccountTypeEnum.FUND.getValue());
        fundData.setAccountId(TEST_ACCOUNT_ID);

        List<TdMonthlyStatementData> dataList = Lists.newArrayList(fundData);

        // Mock historical fund data
        TdMonthlyStatementBusinessData businessData = new TdMonthlyStatementBusinessData();
        FundMonthlyStatementDataDto historicalDataDto = new FundMonthlyStatementDataDto();
        FundMonthlyStatementDataDto.BasicInfoDto historicalBasicInfo = new FundMonthlyStatementDataDto.BasicInfoDto();
        historicalBasicInfo.setCustomerName("Historical Name");
        historicalBasicInfo.setOfficeLocation("Historical Office");
        historicalBasicInfo.setStreet("Historical Street");
        historicalBasicInfo.setArea("Historical Area");
        historicalBasicInfo.setCountry("Historical Country");
        historicalDataDto.setBasicInfo(historicalBasicInfo);

        Map<String,Object> data = new HashMap<>();
        data.put("fund", historicalDataDto);
        businessData.setData(data);

        // Mock dependencies
        when(monthlyStatementManager.getUserStatementBusinessData(anyString(), anyString())).thenReturn(businessData);
        when(monthlyStatementService.getStatementBusinessService(anyString())).thenReturn(statementBusinessService);
        when(statementBusinessService.getUserPeriodEndTotalMarketAndProfit(any(), any(), any(), anyString(), any()))
                .thenReturn(new cn.hutool.core.lang.Pair<>(BigDecimal.TEN, BigDecimal.ONE));
        when(monthlyStatementService.showBusinessTitle(anyString(), anyString(), anyString(), any())).thenReturn(true);

        // Mock JSON conversion
        when(JsonUtils.toJavaObject(anyString(), eq(FundMonthlyStatementDataDto.class))).thenReturn(historicalDataDto);

        // Execute test
        BaseMonthlyStatementDataDto result = statementBasicInfoService.generateDataDto(statementInfo, statementRecord, dataList);

        // Verify results
        assertNotNull(result);
        assertEquals("Historical Name", result.getCustomerName());
        assertEquals("Historical Office", result.getOfficeLocation());
        assertEquals("Historical Street", result.getStreet());
        assertEquals("Historical Area", result.getArea());
        assertEquals("Historical Country", result.getCountry());
    }

    @Test(expected = BusinessException.class)
    public void testGenerateDataDto_NullAddress() {
        // Prepare test data
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setBusinessType(AccountTypeEnum.FUND.getValue());

        TdFundMonthlyStatement statementRecord = new TdFundMonthlyStatement();
        statementRecord.setBankUserId(TEST_BANK_USER_ID);
        statementRecord.setClientId(TEST_CLIENT_ID);

        TdMonthlyStatementData fundData = new TdMonthlyStatementData();
        fundData.setBusinessType(AccountTypeEnum.FUND.getValue());
        fundData.setAccountId(TEST_ACCOUNT_ID);

        List<TdMonthlyStatementData> dataList = Lists.newArrayList(fundData);

        // Mock dependencies with null address
        when(monthlyStatementService.getStatementBusinessService(anyString())).thenReturn(statementBusinessService);
        when(statementBusinessService.getUserPeriodEndTotalMarketAndProfit(any(), any(), any(), anyString(), any()))
                .thenReturn(new cn.hutool.core.lang.Pair<>(BigDecimal.TEN, BigDecimal.ONE));

        AddressInfoResp addressInfoResp = new AddressInfoResp();
        addressInfoResp.setCurrentAddress(null);
        when(userRemoteService.queryContactAddressInfo(anyString())).thenReturn(addressInfoResp);

        // Execute test - should throw BusinessException
        statementBasicInfoService.generateDataDto(statementInfo, statementRecord, dataList);
    }

    @Test
    public void testGenerateDataMap() {
        // Prepare test data
        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setPeriod(TEST_PERIOD);
        statementInfo.setBusinessType(AccountTypeEnum.FUND.getValue());

        TdFundMonthlyStatement statementRecord = new TdFundMonthlyStatement();
        statementRecord.setBankUserId(TEST_BANK_USER_ID);
        statementRecord.setClientId(TEST_CLIENT_ID);
        statementRecord.setRecordTime(new Date());
        statementRecord.setBusinessId("testBusinessId");

        TdMonthlyStatementData fundData = new TdMonthlyStatementData();
        fundData.setBusinessType(AccountTypeEnum.FUND.getValue());
        fundData.setAccountId(TEST_ACCOUNT_ID);

        List<TdMonthlyStatementData> dataList = Lists.newArrayList(fundData);

        // Mock dependencies
        when(monthlyStatementService.getStatementBusinessService(anyString())).thenReturn(statementBusinessService);
        when(statementBusinessService.getUserPeriodEndTotalMarketAndProfit(any(), any(), any(), anyString(), any()))
                .thenReturn(new cn.hutool.core.lang.Pair<>(BigDecimal.TEN, BigDecimal.ONE));
        when(monthlyStatementService.showBusinessTitle(anyString(), anyString(), anyString(), any())).thenReturn(true);

        AddressInfoResp addressInfoResp = new AddressInfoResp();
        addressInfoResp.setEngName("John");
        addressInfoResp.setZhName("约翰");
        BankAddress bankAddress = new BankAddress();
        bankAddress.setEnglishAddressFlag(EnumYesOrNo.YES.getValue());
        addressInfoResp.setCurrentAddress(bankAddress);
        addressInfoResp.setOfficeLocation("Office Location");
        addressInfoResp.setStreet("Street");
        addressInfoResp.setArea("Area");
        addressInfoResp.setCountry("Country");

        when(userRemoteService.queryContactAddressInfo(anyString())).thenReturn(addressInfoResp);
        when(userRemoteService.isUserMcv(anyString())).thenReturn(false);

        // Execute test
        Map<String, Object> result = statementBasicInfoService.generateDataMap(statementInfo, statementRecord, dataList);

        // Verify results
//        assertNotNull(result);
//        assertEquals(TEST_CLIENT_ID, result.get("clientId"));
//        assertEquals("John 约翰", ((String)result.get("customerName")).trim());
//        assertEquals("Office Location", result.get("officeLocation"));
//        assertEquals("Street", result.get("street"));
//        assertEquals("Area", result.get("area"));
//        assertEquals("Country", result.get("country"));
    }
}

