package group.za.bank.statement.service;

import com.aliyun.oss.model.OSSObject;

import java.io.InputStream;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/20 11:04
 */
public interface FileManagerService {
    /**
     * ftp文件下载
     * @param filePath
     * @param fileName
     * @return
     */
    boolean downloadFile(String filePath, String fileName, String fileDir);

    String uploadAliOSSFile(String fileName, InputStream in);

    OSSObject downloadAliOSSFile(String objectKey);

    void downloadAliOSSFileAndSaveLocal(String objectKey, String localPath);

    boolean existOssFile(String objectKey);

    Date getOssFileLastModifyTime(String objectKey);

    long getOssFileSize(String objectKey);

}
