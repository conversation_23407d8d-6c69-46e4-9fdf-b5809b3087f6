package group.za.bank.statement.controller.inner;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.ResponseUtil;
import group.za.bank.statement.entity.dto.MonthlyStatementDto;
import group.za.bank.statement.entity.req.MonthlyStatementConfirmReq;
import group.za.bank.statement.entity.req.PubMonthlyStatementReq;
import group.za.bank.statement.entity.req.UserMonthlyStatementListReq;
import group.za.bank.statement.entity.resp.MonthlyStatementConfirmResp;
import group.za.bank.statement.entity.resp.UserInvestMonthlyStatementListResp;
import group.za.bank.statement.feign.StatementFeignService;
import group.za.bank.statement.service.MonthlyStatementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 15 10:47
 * @description
 */
@RestController
@Slf4j
public class StatementInnerController implements StatementFeignService {



    @Autowired
    MonthlyStatementService monthlyStatementService;


    /**
     * 后管确认推送
     * @param req
     * @return
     */
    @Override
    @RequestMapping(value = "/inner/statement/invest/pubConfirm", method = RequestMethod.POST)
    public ResponseData pubConfirm(@RequestBody @Valid MonthlyStatementConfirmReq req) {
        try{
            MonthlyStatementConfirmResp monthlyStatementConfirmResp = monthlyStatementService.pubConfirm(req);
            return ResponseUtil.success(monthlyStatementConfirmResp);
        }catch (BusinessException e){
            return ResponseUtil.error(e.getCode(),e.getMessage());
        }
    }



    /**
     * 后管结单列表
     * @param req
     * @return
     */
    @Override
    @RequestMapping(value = "/inner/statement/invest/userStatementList", method = RequestMethod.POST)
    public ResponseData<UserInvestMonthlyStatementListResp> userInvestMonthlyStatementList(@RequestBody @Valid UserMonthlyStatementListReq req) {
        UserInvestMonthlyStatementListResp userInvestMonthlyStatementListResp = monthlyStatementService.queryUserStatementDocList(req);
        return ResponseUtil.success(userInvestMonthlyStatementListResp);
    }

    @Override
    @RequestMapping(value = "/inner/statement/invest/queryPubMonthlyStatement", method = RequestMethod.POST)
    public ResponseData<List<MonthlyStatementDto>> queryPubMonthlyStatement(PubMonthlyStatementReq req) {
        List<MonthlyStatementDto> monthlyStatementDtos = monthlyStatementService.queryPubMonthlyStatement(req);
        return ResponseUtil.nullableSuccess(monthlyStatementDtos);
    }
}
