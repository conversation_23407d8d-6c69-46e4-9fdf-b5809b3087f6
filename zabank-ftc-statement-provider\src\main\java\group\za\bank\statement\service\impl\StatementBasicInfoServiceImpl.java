package group.za.bank.statement.service.impl;

import cn.hutool.core.lang.Pair;
import com.google.common.base.Strings;
import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.BankAddress;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.constants.enums.EnumYesOrNo;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.FundMonthlyStatementDataDto;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.StatementBasicInfoService;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.bank.statement.utils.StockStatementNumberFormatUtils;
import group.za.invest.web.json.JSON;
import group.za.invest.web.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static group.za.bank.invest.common.constants.enums.CurrencyEnum.HKD;
import static group.za.bank.statement.common.constants.StatementConstants.STATEMENT_NULL_VALUE_PLACEHOLDER;
import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.STATEMENT_CAN_NOT_GET_USER_HABITANCY_INFO;
import static group.za.bank.statement.constants.enums.StatementErrorMsgEnum.STATEMENT_SUB_BUSINESS_DATA_WRONG;
import static group.za.bank.statement.utils.StatementNumberFormatUtils.dateFormat2EngStr;

/**
 * <AUTHOR>
 * @createTime 01 17:01
 * @description
 */
@Slf4j
@Service
public class StatementBasicInfoServiceImpl implements StatementBasicInfoService {

    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private UserRemoteService userRemoteService;
    @Autowired
    private MonthlyStatementService monthlyStatementService;
    @Autowired
    private MonthlyStatementManager monthlyStatementManager;

    /**
     * 抽取业务结单数据
     *
     * @param tdFundMonthlyStatementInfo
     * @param statementRecord
     * @param monthlyStatementDataList
     * @return
     */
    @Override
    public BaseMonthlyStatementDataDto generateDataDto(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, TdFundMonthlyStatement statementRecord
            , List<TdMonthlyStatementData> monthlyStatementDataList) {

        String monthlyStatementPeriodFormat = systemConfig.getMonthlyStatementPeriodFormat();

        BaseMonthlyStatementDataDto basicInfoDto = new BaseMonthlyStatementDataDto();
        String bankUserId = statementRecord.getBankUserId();
        String period = tdFundMonthlyStatementInfo.getPeriod();
        String business = tdFundMonthlyStatementInfo.getBusinessType();

        Date periodDate = DateUtil.parse(period, monthlyStatementPeriodFormat);
        Date monthStartDate = DateUtil.getFirstDayOfMonth(periodDate);
        Date monthEndDate = DateUtil.getLastDayOfMonth(periodDate);

        basicInfoDto.setStatementStartDate(dateFormat2EngStr(monthStartDate));
        basicInfoDto.setStatementEndDate(dateFormat2EngStr(monthEndDate));

        //结单日期
        basicInfoDto.setStatementDate(dateFormat2EngStr(statementRecord.getRecordTime()));

        //投资账户
        basicInfoDto.setClientId(statementRecord.getClientId());

        //累计所有业务类型的市值
        CurrencyEnum targetCurrency = HKD;
        String[] businessSubtypeArray = business.split(systemConfig.getBusinessSubTypeSeparator());
        BigDecimal totalMarketValue = BigDecimal.ZERO;
        BigDecimal totalHoldingProfit = BigDecimal.ZERO;

        Map<String,TdMonthlyStatementData> businessTypeAndStatementDataMap = monthlyStatementDataList.stream()
                .collect(Collectors.toMap(TdMonthlyStatementData::getBusinessType, dto->dto));

        for (String businessSubtype : businessSubtypeArray) {
            TdMonthlyStatementData monthlyStatementData = businessTypeAndStatementDataMap.get(businessSubtype);
            String accountId = monthlyStatementData.getAccountId();
            if(!Strings.isNullOrEmpty(accountId)){
                StatementBusinessService statementBusinessService = monthlyStatementService.getStatementBusinessService(businessSubtype);
                String marketCode = null;
                if(AccountTypeEnum.STOCK.getValue().equals(businessSubtype)){
                    marketCode = MarketCodeEnum.US.getValue();
                }else if(AccountTypeEnum.HK_STOCK.getValue().equals(businessSubtype)){
                    marketCode = MarketCodeEnum.HK.getValue();
                }
                Pair<BigDecimal, BigDecimal> userPeriodEndData = statementBusinessService
                        .getUserPeriodEndTotalMarketAndProfit(tdFundMonthlyStatementInfo, statementRecord, targetCurrency, monthlyStatementData.getAccountId(), marketCode);
                if (userPeriodEndData == null) {
                    log.error("用户{}月结单数据错误! statementId:{}", businessSubtype, statementRecord.getBusinessId());
                    throw new BusinessException(STATEMENT_SUB_BUSINESS_DATA_WRONG);
                }
                totalMarketValue = totalMarketValue.add(userPeriodEndData.getKey());
                totalHoldingProfit = totalHoldingProfit.add(userPeriodEndData.getValue());
                log.info("bankUserId:{}, businessSubtype:{}, totalMarketValue:{},totalHoldingProfit:{}", bankUserId, businessSubtype, totalMarketValue, totalHoldingProfit);
            }
            //填充业务表头展示状态
            String clientId = statementRecord.getClientId();
            boolean showBusinessTitle = monthlyStatementService.showBusinessTitle(businessSubtype, period, clientId, monthlyStatementData);
            log.info("clientId:{},businessType:{},showBusinessTitle:{}", clientId, businessSubtype, showBusinessTitle);
            if (AccountTypeEnum.FUND.getValue().equals(businessSubtype)) {
                basicInfoDto.setFund(showBusinessTitle);
            }
            if (AccountTypeEnum.STOCK.getValue().equals(businessSubtype)) {
                basicInfoDto.setStock(showBusinessTitle);
            }
            if (AccountTypeEnum.HK_STOCK.getValue().equals(businessSubtype)) {
                basicInfoDto.setHkStock(showBusinessTitle);
            }
            if (AccountTypeEnum.CRYPTO.getValue().equals(businessSubtype)) {
                basicInfoDto.setCrypto(showBusinessTitle);
            }
            if (AccountTypeEnum.S_FUND.getValue().equals(businessSubtype)) {
                basicInfoDto.setSfund(showBusinessTitle);
            }
        }

        basicInfoDto.setTotalAmount(targetCurrency.getCurrency() + " " + StockStatementNumberFormatUtils.statementMoneyFormatOfRoundDown(totalMarketValue,STATEMENT_NULL_VALUE_PLACEHOLDER));

        //放入总持仓盈亏的原始值，在activity发送的时候做格式化
        basicInfoDto.setTotalAmountValue(totalMarketValue);
        basicInfoDto.setTotalHoldingProfit(totalHoldingProfit);

        // 若生成月份是基金历史月结单，以下字段customerName、.officeLocation、street、.area、country从基金的mongo中获取。
        if (StatementConstants.FUND_MIGRATION_FLAG.equals(statementRecord.getRemark())) {
            // 设置历史基金月结单地址等信息
            this.setHistoryFundStatementAddressInfo(statementRecord, basicInfoDto);
        } else {
            // 地址
            AddressInfoResp addressInfoResp = userRemoteService.queryContactAddressInfo(bankUserId);

            boolean isUserMcv = userRemoteService.isUserMcv(bankUserId);


            String name = (addressInfoResp.getEngName()==null?"":addressInfoResp.getEngName())
                    + " "
                    + (addressInfoResp.getZhName()==null?"":addressInfoResp.getZhName());

            basicInfoDto.setCustomerName(name);


            conversionAddress(basicInfoDto, addressInfoResp, isUserMcv);
        }



        return basicInfoDto;
    }

    /**
     * 设置历史基金月结单地址等信息
     */
    private void setHistoryFundStatementAddressInfo(TdFundMonthlyStatement statementRecord, BaseMonthlyStatementDataDto basicInfoDto) {
        TdMonthlyStatementBusinessData businessData = monthlyStatementManager.getUserStatementBusinessData(statementRecord.getBusinessId(), AccountTypeEnum.FUND.getValue());
        if (Objects.isNull(businessData)) {
            log.error("生成月份是基金历史月结单, 查询mongo结单数据为空,人工排查, TdFundMonthlyStatement:{}", JSON.toJSONString(statementRecord));
            throw new BusinessException(STATEMENT_SUB_BUSINESS_DATA_WRONG);
        }

        FundMonthlyStatementDataDto dataDto = JsonUtils.toJavaObject(JSON.toJSONString(businessData.getData()), FundMonthlyStatementDataDto.class);

        basicInfoDto.setCustomerName(dataDto.getBasicInfo().getCustomerName());
        basicInfoDto.setOfficeLocation(dataDto.getBasicInfo().getOfficeLocation());
        basicInfoDto.setStreet(dataDto.getBasicInfo().getStreet());
        basicInfoDto.setArea(dataDto.getBasicInfo().getArea());
        basicInfoDto.setCountry(dataDto.getBasicInfo().getCountry());
    }

    /**
     * 抽取业务结单数据
     *
     * @param tdFundMonthlyStatementInfo
     * @param statementRecord
     * @return
     */
    @Override
    public Map<String, Object> generateDataMap(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , TdFundMonthlyStatement statementRecord, List<TdMonthlyStatementData> monthlyStatementDataList) {


        return JSONObject.parseObject(JsonUtils.toFormatJsonString(generateDataDto(tdFundMonthlyStatementInfo,statementRecord,monthlyStatementDataList)));

    }




    /**
     * 参数：isMcv
     * 通讯地址转化为
     * 第一栏
     * 第二栏
     * 第三栏
     * 第四栏
     *
     * @param addressInfoResp
     * @return
     */
    void conversionAddress(BaseMonthlyStatementDataDto basicInfoDto, AddressInfoResp addressInfoResp, Boolean isMcv) {
        log.info("源habitancyInfoDto={}", JSON.toJSONString(addressInfoResp));
        BankAddress currentAddress = addressInfoResp.getCurrentAddress();
        if (currentAddress == null) {
            log.error("生成月结单失败，用户通讯地址为null! bankUserId:{}", addressInfoResp.getBankUserId());
            throw new BusinessException(STATEMENT_CAN_NOT_GET_USER_HABITANCY_INFO);
        }

        if (isMcv && (EnumYesOrNo.NO.getValue().equals(currentAddress.getEnglishAddressFlag()))) {
            basicInfoDto.setOfficeLocation(addressInfoResp.getCountry());
            basicInfoDto.setStreet(addressInfoResp.getArea());
            basicInfoDto.setArea(addressInfoResp.getOfficeLocation());
            basicInfoDto.setCountry("");
        } else {
            basicInfoDto.setOfficeLocation(addressInfoResp.getOfficeLocation());
            basicInfoDto.setStreet(addressInfoResp.getStreet());
            basicInfoDto.setArea(addressInfoResp.getArea());
            basicInfoDto.setCountry(addressInfoResp.getCountry());
        }
        log.info("转化basicInfoDto={}", JSON.toJSONString(addressInfoResp));
    }





}
