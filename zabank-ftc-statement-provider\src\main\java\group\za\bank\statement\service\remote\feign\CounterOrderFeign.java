package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.counterfront.feign.CounterOrderServiceFeign;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 柜台网关
 *
 * <AUTHOR>
 * @date 2024/04/25
 **/
@FeignClient(
        value = "zabank-sbs-counterfront-service",
        contextId = "counterOrderFeign",
        url = "${sbs.gateway.url}"
)
public interface CounterOrderFeign extends CounterOrderServiceFeign {
}
