package group.za.bank.statement.constants.enums;

import static group.za.bank.statement.constants.enums.StatementNativeRowContentLocationEnum.PREFIX;
import static group.za.bank.statement.constants.enums.StatementNativeRowContentLocationEnum.SUFFIX;

/**
 * <AUTHOR>
 * @createTime 15 11:42
 * @description
 */
public enum StatementNativeRowTypeEnum {

    /**
     * table中行的类型
     */
    NORMAL(1, SUFFIX.getType(), StatementNativeRowContentTypeEnum.ALL.getType(),false, false, "普通行"),
    NORMAL_LEFT_PREFIX(111, PREFIX.getType(),StatementNativeRowContentTypeEnum.TITLE.getType(), false, false, "普通行左边"),
    NORMAL_LEFT_SUFFIX(112, SUFFIX.getType(), StatementNativeRowContentTypeEnum.TITLE.getType(),false, false, "普通行右边"),
    NORMAL_RIGHT_PREFIX(121, PREFIX.getType(), StatementNativeRowContentTypeEnum.VALUE.getType(),false, false, "普通行左边"),
    NORMAL_RIGHT_SUFFIX(122, SUFFIX.getType(),StatementNativeRowContentTypeEnum.VALUE.getType(), false, false, "普通行右边"),


    HEADER(2, SUFFIX.getType(), StatementNativeRowContentTypeEnum.ALL.getType(),true, false, "标题行"),
    HEADER_LEFT_PREFIX(211, PREFIX.getType(),StatementNativeRowContentTypeEnum.TITLE.getType(), true, false, "标题行左边"),
    HEADER_LEFT_SUFFIX(212, SUFFIX.getType(), StatementNativeRowContentTypeEnum.TITLE.getType(),true, false, "标题行右边"),
    HEADER_RIGHT_PREFIX(221, PREFIX.getType(), StatementNativeRowContentTypeEnum.VALUE.getType(),true, false, "标题行左边"),
    HEADER_RIGHT_SUFFIX(222, SUFFIX.getType(), StatementNativeRowContentTypeEnum.VALUE.getType(),true, false, "标题行右边"),

    DATE_ROW(3, SUFFIX.getType(),StatementNativeRowContentTypeEnum.ALL.getType(), false, true, "日期行"),


    ;

    private Integer type;
    private String desc;
    private boolean isHeader;
    private boolean isDate;
    private Integer contentType;
    private Integer contentLocation;


    StatementNativeRowTypeEnum(Integer type, Integer contentLocation,Integer contentType
            , boolean isHeader, boolean isDate, String desc) {
        this.type = type;
        this.desc = desc;
        this.isDate = isDate;
        this.isHeader = isHeader;
        this.contentLocation = contentLocation;
        this.contentType = contentType;
    }

    public static StatementNativeRowTypeEnum judgeRowType(Integer rowType) {
        for (StatementNativeRowTypeEnum rowTypeEnum : StatementNativeRowTypeEnum.values()) {
            if(rowTypeEnum.getType().equals(rowType)){
                return rowTypeEnum;
            }
        }
        return NORMAL;
    }


    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isHeader() {
        return isHeader;
    }

    public boolean isDate() {
        return isDate;
    }

    public Integer getContentLocation() {
        return contentLocation;
    }

    public Integer getContentType() {
        return contentType;
    }
}
