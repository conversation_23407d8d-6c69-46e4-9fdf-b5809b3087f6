package group.za.bank.statement.entity.dto;

import lombok.Data;

/**
 * 证券资产摘要
 * <AUTHOR>
 * @date 2022/05/06
 **/
@Data
public class StatementAccountAssetDto extends StatementBaseDto{

    /**市场 */
    private String exchangeCode;

    /** 股票代码 */
    private String stockCode;

    /** 股票名称 */
    private String stockName;

    /** 期初结余 */
    private String openingBalance;

    /** 当期提存 */
    private String currentBalance;

    /** 期末结余 */
    private String closingBalance;

    /** 收市价 */
    private String closePrice;

    /** 币种 */
    private String currency;

    /** 市值 */
    private String marketValue;

    /**
     * 期初已交收持仓
     */
    private String openingSettleHolding;
    /**
     * 期末已交收持仓
     */
    private String closingSettleHolding;

}
