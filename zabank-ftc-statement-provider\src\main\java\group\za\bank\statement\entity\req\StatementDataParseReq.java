package group.za.bank.statement.entity.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 结单数据解析定时任务参数
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Data
public class StatementDataParseReq {

    @ApiModelProperty(value = "结单日期")
    private String statementDate;

    @ApiModelProperty(value = "文件路径日期")
    private String pathDate;

    @ApiModelProperty(value = "是否强制创新拉取")
    private Boolean refresh = false;
}
