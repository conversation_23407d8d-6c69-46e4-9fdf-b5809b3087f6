package group.za.bank.statement.service.impl;

import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.bankfront.mapper.CounterFileInfoExtendMapper;
import group.za.bank.sbs.bankfront.model.entity.CounterFileInfo;
import group.za.bank.sbs.counterfront.common.enums.EntrustTypeEnum;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.counterfront.common.enums.TtlLinkupTypeEnum;
import group.za.bank.sbs.counterfront.common.enums.TtlTxnTypeIdEnum;
import group.za.bank.sbs.trade.common.constant.enums.ActionTypeEnum;
import group.za.bank.sbs.trade.common.constant.enums.CompanyActionActivityTypeEnum;
import group.za.bank.sbs.trade.common.constant.enums.FeeNameEnum;
import group.za.bank.sbs.trade.model.dto.CompanyActionDetailInfoDTO;
import group.za.bank.sbs.trade.model.dto.CompanyActionExerciseDetailDTO;
import group.za.bank.sbs.trade.model.dto.OrderInfoStatementDTO;
import group.za.bank.sbs.trade.model.resp.feign.TradeDateDiffResp;
import group.za.bank.statement.constants.enums.*;
import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.StockStatementDataBusinessService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import group.za.invest.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 股票结单数据业务处理类
 *
 * <AUTHOR>
 * @date 2024/04/25
 **/
@Service
@Slf4j
public class StockStatementDataBusinessServiceImpl implements StockStatementDataBusinessService {

    public static final String STATEMENT_DATA_MARKET_CODE_DEFAULT = "USEX";

    public static final String STATEMENT_DATA_CURRENCY_DEFAULT = "USD";

    @Autowired
    private StockTradeManager stockTradeManager;

    @Autowired
    private TradeCalendarRemoteService tradeCalendarRemoteService;

    @Autowired
    private CounterFileInfoExtendMapper counterFileInfoExtendMapper;

    @Override
    public List<TdStockStatementData> localGenerationOrderData(String businessId, String statementDate, Date tradeDate) {
        //1.查询本地订单数据
        List<OrderInfoStatementDTO> orderInfoStatementList = stockTradeManager.queryOrderListByStatement(tradeDate);

        //2.查询订单的交收日期T+1
        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMATDAY);
        TradeDateDiffResp tradeDateDiffResp = tradeCalendarRemoteService.tradeDateDiff(MarketCodeEnum.US.getValue(), tradeDateStr, 1);

        Date settledDate = DateUtil.parse(tradeDateDiffResp.getTradeDate(), DateUtil.FORMATDAY);
        String settledDateStr = DateUtil.format(settledDate, DateUtil.FORMAT_SHORT);

        //3.组装数据
        List<TdStockStatementData> dataList = new ArrayList<>(orderInfoStatementList.size());
        for (OrderInfoStatementDTO orderInfoStatement : orderInfoStatementList) {
            //构建基础数据
            TdStockStatementData stockStatementData = buildCommonStatementData(businessId, statementDate, tradeDate, StatementFormatTypeEnum.ORDER);
            stockStatementData.setAccNo(orderInfoStatement.getAccountId());

            //交收日期
            stockStatementData.setParam2(settledDateStr);
            //币种默认USD
            stockStatementData.setParam3(STATEMENT_DATA_CURRENCY_DEFAULT);

            //BO订单号
            String orderBoNo = getStatementOrderBoNo(orderInfoStatement.getOrderNo());
            if (StringUtils.isEmpty(orderBoNo)) {
                log.info("autoBuildOrderData orderBoNo error, orderNo:{}", orderInfoStatement.getOrderNo());
                throw new BusinessException(StatementErrorMsgEnum.STOCK_STATEMENT_ORDER_NO_NULL);
            }
            stockStatementData.setParam4(orderBoNo);

            //买卖方向
            StatementDirectionEnum statementDirectionEnum = EntrustTypeEnum.BUY.getValue().equals(orderInfoStatement.getEntrustType())
                    ? StatementDirectionEnum.BUY : StatementDirectionEnum.SELL;
            stockStatementData.setParam5(statementDirectionEnum.getTradeType());

            //市场默认USEX
            stockStatementData.setParam6(STATEMENT_DATA_MARKET_CODE_DEFAULT);
            //股票代码和名称
            stockStatementData.setParam7(orderInfoStatement.getStockCode());
            stockStatementData.setParam8(orderInfoStatement.getStockName());

            //总数量，买入为正，卖出为负
            BigDecimal businessQty = orderInfoStatement.getBusinessQty();
            if (StatementDirectionEnum.SELL.equals(statementDirectionEnum)) {
                businessQty = businessQty.negate();
            }
            stockStatementData.setParam9(businessQty.toPlainString());

            //成交均价
            stockStatementData.setParam10(orderInfoStatement.getAvgPrice().toPlainString());

            //成交金额
            stockStatementData.setParam11(orderInfoStatement.getBusinessAmount().toPlainString());

            //净金额
            stockStatementData.setParam12(orderInfoStatement.getNetAmount().toPlainString());

            //佣金
            stockStatementData.setParam13(orderInfoStatement.getCommission().toPlainString());

            if (StatementDirectionEnum.SELL.equals(statementDirectionEnum)) {
                //finra费用
                stockStatementData.setParam14("US_USD_TRD_FINRA");
                stockStatementData.setParam15(FeeNameEnum.FINRA_FEE.getName());
                stockStatementData.setParam16(orderInfoStatement.getFinraFee().toPlainString());

                //sec费用
                stockStatementData.setParam17("US_USD_TRD_SEC");
                stockStatementData.setParam18(FeeNameEnum.SEC_FEE.getName());
                stockStatementData.setParam19(orderInfoStatement.getSecFee().toPlainString());
            }

            dataList.add(stockStatementData);
        }

        return dataList;
    }

    @Override
    public List<TdStockStatementData> localGenerationTradeChangeData(String businessId, String statementDate, Date tradeDate) {
        List<TdStockStatementData> dataList = new ArrayList<>();

        //1 查询本地bookCLose数据
        List<CompanyActionDetailInfoDTO> actionBookCloseData = stockTradeManager.queryActionBookCloseData(tradeDate);

        //1.1 处理派息
        List<CompanyActionDetailInfoDTO> dividendList = actionBookCloseData.stream()
                .filter(t -> t.getActivityType().equals(ActionTypeEnum.DIVIDEND.getValue())
                        && t.getOverrideAmount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        dataList.addAll(generationTradeChangeDividend(businessId, statementDate, tradeDate, dividendList));

        //1.2 拆合换红和派送股权
        List<CompanyActionDetailInfoDTO> fractionList = actionBookCloseData.stream()
                .filter(t -> ActionTypeEnum.FRACTION_TYPE_LIST.contains(t.getActivityType())
                        || ActionTypeEnum.RIGHTS_ISSUE.getValue().equals(t.getActivityType()))
                .collect(Collectors.toList());

        dataList.addAll(generationTradeChangeFraction(businessId, statementDate, tradeDate, fractionList));

        //2 查询本地exercise数据
        List<CompanyActionExerciseDetailDTO> actionExerciseData = stockTradeManager.queryActionExerciseData(tradeDate);

        dataList.addAll(generationTradeChangeExercise(businessId, statementDate, tradeDate, actionExerciseData));

        return dataList;
    }

    @Override
    public List<TdStockStatementData> localGenerationHoldingData(String businessId, String statementDate, Date tradeDate) {
        List<TdStockStatementData> dataList = new ArrayList<>();

        return dataList;
    }

    /**
     * 填充结单数据表得基础数据
     *
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param formatTypeEnum
     * @return
     */
    private TdStockStatementData buildCommonStatementData(String businessId, String statementDate, Date tradeDate, StatementFormatTypeEnum formatTypeEnum) {
        TdStockStatementData stockStatementData = new TdStockStatementData();
        stockStatementData.setBusinessId(businessId);
        stockStatementData.setStatementDate(statementDate);
        stockStatementData.setMarketCode(STATEMENT_DATA_MARKET_CODE_DEFAULT);
        stockStatementData.setTradeDate(tradeDate);
        stockStatementData.setStatementType(StatementTypeEnum.DAILY.getType());
        stockStatementData.setFormatType(formatTypeEnum.getFormatType());

        String tradeDateStr = DateUtil.format(tradeDate, DateUtil.FORMAT_SHORT);
        stockStatementData.setParam1(tradeDateStr);

        return stockStatementData;
    }

    /**
     * 根据本地订单号获取BO订单号
     *
     * @param orderNo
     * @return
     */
    private String getStatementOrderBoNo(String orderNo) {
        List<CounterFileInfo> counterFileInfos = counterFileInfoExtendMapper.queryByLocalOrderNoAndLinkupTypeAndTxnTypeId(orderNo,
                Arrays.asList(TtlLinkupTypeEnum.CONTRACT.getValue()),
                Arrays.asList(TtlTxnTypeIdEnum.BUY_CONTRACT.getValue(), TtlTxnTypeIdEnum.SELL_CONTRACT.getValue()));
        if (CollectionUtils.isEmpty(counterFileInfos)) {
            return null;
        }
        return counterFileInfos.get(0).getReleaseReference();
    }

    /**
     * 构建交易变动-派息数据
     *
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param dividendList
     * @return
     */
    @Override
    public List<TdStockStatementData> generationTradeChangeDividend(String businessId, String statementDate,
                                                                     Date tradeDate, List<CompanyActionDetailInfoDTO> dividendList) {
        List<TdStockStatementData> dataList = new ArrayList<>(dividendList.size());
        for (CompanyActionDetailInfoDTO detailInfoDTO : dividendList) {
            //构建基础数据
            TdStockStatementData tdStockStatementData = buildCommonStatementData(businessId, statementDate, tradeDate, StatementFormatTypeEnum.TRADE_CHANGE_DETAIL);
            tdStockStatementData.setAccNo(detailInfoDTO.getAccountId());

            //交收日等于交易日
            tdStockStatementData.setParam2(DateUtil.format(tradeDate, DateUtil.FORMAT_SHORT));

            //币种默认USD
            tdStockStatementData.setParam3(STATEMENT_DATA_CURRENCY_DEFAULT);

            //公司行动ID
            tdStockStatementData.setParam4(detailInfoDTO.getActionId());
            //业务类型
            tdStockStatementData.setParam5(StatementBusinessTypeEnum.CDV.getBusinessType());

            //市场默认USEX
            tdStockStatementData.setParam6(STATEMENT_DATA_MARKET_CODE_DEFAULT);
            //股票代码
            tdStockStatementData.setParam7(detailInfoDTO.getStockCode());

            //净金额
            tdStockStatementData.setParam10(detailInfoDTO.getOverrideAmount().subtract(detailInfoDTO.getFee()).subtract(detailInfoDTO.getUsTaxFee()).toPlainString());

            //市场默认USEX
            tdStockStatementData.setParam11(STATEMENT_DATA_MARKET_CODE_DEFAULT);
            //股票代码
            tdStockStatementData.setParam12(detailInfoDTO.getStockCode());

            //备注
            tdStockStatementData.setParam13(detailInfoDTO.getRemark());

            //总金额名称
            tdStockStatementData.setParam15("Gross Amount 總金額");
            //总金额
            tdStockStatementData.setParam16(detailInfoDTO.getOverrideAmount().toPlainString());

            //美股税费
            if (null != detailInfoDTO.getUsTaxFee() && detailInfoDTO.getUsTaxFee().compareTo(BigDecimal.ZERO) != 0) {
                //税费类型
                tdStockStatementData.setParam17("US_USD_DIV_TAX_30");
                //税费名称
                tdStockStatementData.setParam18("Collection Fee Of Cash 代收現金股息");
                //税费，负值
                tdStockStatementData.setParam19(detailInfoDTO.getUsTaxFee().negate().toPlainString());
            }

            dataList.add(tdStockStatementData);
        }
        return dataList;
    }

    /**
     * 构建交易变动-小数股相关数据
     *
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param fractionList
     * @return
     */
    @Override
    public List<TdStockStatementData> generationTradeChangeFraction(String businessId, String statementDate,
                                                                     Date tradeDate, List<CompanyActionDetailInfoDTO> fractionList) {
        List<TdStockStatementData> dataList = new ArrayList<>();
        for (CompanyActionDetailInfoDTO actionDetailInfoDTO : fractionList) {
            if (isBonusShareOrRightsIssue(actionDetailInfoDTO)) {
                //处理红股和派送股权
                if (actionDetailInfoDTO.getOverrideAmount().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                //判断businessType
                StatementBusinessTypeEnum businessTypeEnum = determineBusinessType(actionDetailInfoDTO);

                dataList.add(buildTradeChangeStockInOut(businessId, statementDate, tradeDate, businessTypeEnum.getBusinessType(), actionDetailInfoDTO));
            } else {
                //处理拆合换类型
                StatementBusinessTypeEnum businessTypeEnum;
                if (ActionTypeEnum.SHARE_SPLIT.getValue().equals(actionDetailInfoDTO.getActivityType())) {
                    businessTypeEnum = StatementBusinessTypeEnum.SSP;
                } else if (ActionTypeEnum.JOINT_STOCK.getValue().equals(actionDetailInfoDTO.getActivityType())) {
                    businessTypeEnum = StatementBusinessTypeEnum.SCS;
                } else {
                    //换股
                    BigDecimal issueRatioPer = actionDetailInfoDTO.getIssueRatioPer();
                    BigDecimal issueRatioDelivery = actionDetailInfoDTO.getIssueRatioDelivery();
                    if (issueRatioPer.compareTo(issueRatioDelivery) > 0) {
                        businessTypeEnum = StatementBusinessTypeEnum.SCS;
                    } else {
                        businessTypeEnum = StatementBusinessTypeEnum.SSP;
                    }
                }
                BigDecimal bookCloseQty = actionDetailInfoDTO.getBookCloseQty();
                BigDecimal overrideAmount = actionDetailInfoDTO.getOverrideAmount();
                //bookClose_qty是取出数量
                if (bookCloseQty.compareTo(BigDecimal.ZERO) > 0) {
                    //证券取，数量是负值
                    actionDetailInfoDTO.setOverrideAmount(bookCloseQty.negate());
                    dataList.add(buildTradeChangeStockInOut(businessId, statementDate, tradeDate, businessTypeEnum.getBusinessType(), actionDetailInfoDTO));
                }
                //override_amount是存入数量
                if (overrideAmount.compareTo(BigDecimal.ZERO) > 0) {
                    //证券存
                    actionDetailInfoDTO.setOverrideAmount(overrideAmount);
                    dataList.add(buildTradeChangeStockInOut(businessId, statementDate, tradeDate, businessTypeEnum.getBusinessType(), actionDetailInfoDTO));
                }
            }
        }
        return dataList;
    }

    /**
     * 构建公司行动exercise数据
     *
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param exerciseDetailList
     * @return
     */
    @Override
    public List<TdStockStatementData> generationTradeChangeExercise(String businessId, String statementDate,
                                                                     Date tradeDate, List<CompanyActionExerciseDetailDTO> exerciseDetailList) {
        List<TdStockStatementData> dataList = new ArrayList<>();
        for (CompanyActionExerciseDetailDTO exerciseDetailDTO : exerciseDetailList) {
            //供股
            if (ActionTypeEnum.RIGHTS_SUBSCRIPTION.getValue().equals(exerciseDetailDTO.getActionType())) {
                if (CompanyActionActivityTypeEnum.RIGHTS_SUBSCRIPTION_DEDUCT.getValue().equals(exerciseDetailDTO.getActivityType())) {
                    //补充deduct数据
                    dataList.add(buildTradeChangeExercise(businessId, statementDate, tradeDate, StatementBusinessTypeEnum.ESB.getBusinessType(), exerciseDetailDTO));
                } else if (CompanyActionActivityTypeEnum.RIGHTS_ISSUE_ALLOTMENT.getValue().equals(exerciseDetailDTO.getActivityType())) {
                    //如果deduct和confirm在同一天，则还需要补充deduct数据
                    if (tradeDate.compareTo(exerciseDetailDTO.getDeductDate()) == 0) {
                        //补充deduct数据
                        dataList.add(buildTradeChangeExercise(businessId, statementDate, tradeDate, StatementBusinessTypeEnum.ESB.getBusinessType(), exerciseDetailDTO));
                    }
                    //补充confirm数据
                    dataList.add(buildTradeChangeExercise(businessId, statementDate, tradeDate, StatementBusinessTypeEnum.EAL.getBusinessType(), exerciseDetailDTO));
                }
            }

            //回购
            if (ActionTypeEnum.OFFER_TO_BUY_BACK.getValue().equals(exerciseDetailDTO.getActionType())) {
                if (CompanyActionActivityTypeEnum.OFFER_TO_BUY_BACK_DEDUCT.getValue().equals(exerciseDetailDTO.getActivityType())) {
                    //补充deduct数据
                    dataList.add(buildTradeChangeExercise(businessId, statementDate, tradeDate, StatementBusinessTypeEnum.TPS.getBusinessType(), exerciseDetailDTO));
                } else if (CompanyActionActivityTypeEnum.OFFER_TO_BUY_BACK_ALLOTMENT.getValue().equals(exerciseDetailDTO.getActivityType())) {
                    //如果deduct和confirm在同一天，则还需要补充deduct数据
                    if (tradeDate.compareTo(exerciseDetailDTO.getDeductDate()) == 0) {
                        //补充deduct数据
                        dataList.add(buildTradeChangeExercise(businessId, statementDate, tradeDate, StatementBusinessTypeEnum.TPS.getBusinessType(), exerciseDetailDTO));
                    }
                    //补充confirm数据
                    dataList.add(buildTradeChangeExercise(businessId, statementDate, tradeDate, StatementBusinessTypeEnum.TPA.getBusinessType(), exerciseDetailDTO));
                }
            }
        }

        return dataList;
    }

    /**
     * 判断是红股或派送股权类型
     *
     * @param actionDetailInfoDTO
     * @return
     */
    private boolean isBonusShareOrRightsIssue(CompanyActionDetailInfoDTO actionDetailInfoDTO) {
        return ActionTypeEnum.BONUS_SHARE.getValue().equals(actionDetailInfoDTO.getActivityType())
                || ActionTypeEnum.RIGHTS_ISSUE.getValue().equals(actionDetailInfoDTO.getActivityType());
    }

    /**
     * 判断businessType
     *
     * @param actionDetailInfoDTO
     * @return
     */
    private StatementBusinessTypeEnum determineBusinessType(CompanyActionDetailInfoDTO actionDetailInfoDTO) {
        return ActionTypeEnum.RIGHTS_ISSUE.getValue().equals(actionDetailInfoDTO.getActivityType())
                ? StatementBusinessTypeEnum.BRT
                : (actionDetailInfoDTO.getStockCode().equals(actionDetailInfoDTO.getIssueStockCode())
                ? StatementBusinessTypeEnum.BSH
                : StatementBusinessTypeEnum.SCAS);
    }

    /**
     * 构建交易变动-股票存取数据
     *
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param businessType
     * @param actionDetailInfoDTO
     * @return
     */
    private TdStockStatementData buildTradeChangeStockInOut(String businessId, String statementDate,
                                                            Date tradeDate, String businessType, CompanyActionDetailInfoDTO actionDetailInfoDTO) {
        //构建基础数据
        TdStockStatementData stockStatementData = buildCommonStatementData(businessId, statementDate, tradeDate, StatementFormatTypeEnum.TRADE_CHANGE_DETAIL);
        stockStatementData.setAccNo(actionDetailInfoDTO.getAccountId());
        //交收日等于交易日
        stockStatementData.setParam2(DateUtil.format(tradeDate, DateUtil.FORMAT_SHORT));
        //公司行动ID
        stockStatementData.setParam4(actionDetailInfoDTO.getActionId());
        //业务类型
        stockStatementData.setParam5(businessType);
        stockStatementData.setParam6(STATEMENT_DATA_MARKET_CODE_DEFAULT);
        //目标股票代码
        stockStatementData.setParam7(actionDetailInfoDTO.getIssueStockCode());
        //存取数量
        stockStatementData.setParam9(actionDetailInfoDTO.getOverrideAmount().toPlainString());
        stockStatementData.setParam11(STATEMENT_DATA_MARKET_CODE_DEFAULT);
        //源股票代码
        stockStatementData.setParam12(actionDetailInfoDTO.getStockCode());
        //备注
        stockStatementData.setParam13(actionDetailInfoDTO.getRemark());

        return stockStatementData;
    }

    /**
     * 构建交易变动-exercise数据
     *
     * @param businessId
     * @param statementDate
     * @param tradeDate
     * @param businessType
     * @param exerciseDetailDTO
     * @return
     */
    private TdStockStatementData buildTradeChangeExercise(String businessId, String statementDate,
                                                          Date tradeDate, String businessType, CompanyActionExerciseDetailDTO exerciseDetailDTO) {
        //构建基础数据
        TdStockStatementData stockStatementData = buildCommonStatementData(businessId, statementDate, tradeDate, StatementFormatTypeEnum.TRADE_CHANGE_DETAIL);
        stockStatementData.setAccNo(exerciseDetailDTO.getAccountId());
        //交收日等于交易日
        stockStatementData.setParam2(DateUtil.format(tradeDate, DateUtil.FORMAT_SHORT));
        stockStatementData.setParam4(exerciseDetailDTO.getSubscriptionId());
        stockStatementData.setParam5(businessType);
        stockStatementData.setParam6(STATEMENT_DATA_MARKET_CODE_DEFAULT);

        if (StatementBusinessTypeEnum.ESB.getBusinessType().equals(businessType)) {
            //认购股权
            stockStatementData.setParam3(STATEMENT_DATA_CURRENCY_DEFAULT);
            //股票代码
            stockStatementData.setParam7(exerciseDetailDTO.getStockCode());
            //扣券数量，负值
            stockStatementData.setParam9(exerciseDetailDTO.getDeductQty().negate().toPlainString());
            //扣钱金额，负值
            stockStatementData.setParam10(exerciseDetailDTO.getDeductAmt().negate().toPlainString());
        } else if (StatementBusinessTypeEnum.EAL.getBusinessType().equals(businessType)) {
            //股权分配及退款
            //目标股票代码
            stockStatementData.setParam7(exerciseDetailDTO.getIssueStockCode());
            //股票分配数量
            stockStatementData.setParam9(exerciseDetailDTO.getOverrideAmount().toPlainString());
        } else if (StatementBusinessTypeEnum.TPS.getBusinessType().equals(businessType)) {
            //股票回购提取
            //股票代码
            stockStatementData.setParam7(exerciseDetailDTO.getStockCode());
            //扣券数量，负值
            stockStatementData.setParam9(exerciseDetailDTO.getDeductQty().negate().toPlainString());
        } else if (StatementBusinessTypeEnum.TPA.getBusinessType().equals(businessType)) {
            //股票回购分配
            stockStatementData.setParam3(STATEMENT_DATA_CURRENCY_DEFAULT);
            //股票代码
            stockStatementData.setParam7(exerciseDetailDTO.getStockCode());
            //股票分配数量
            stockStatementData.setParam10(exerciseDetailDTO.getConfirmAmt().toPlainString());
        }

        return stockStatementData;
    }
}
