package group.za.bank.statement.service.impl;

import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.market.constants.enums.ExchangeCodeEnum;
import group.za.bank.sbs.bankfront.mapper.CounterFileInfoExtendMapper;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.trade.common.constant.enums.TaskCodeEnum;
import group.za.bank.sbs.trade.common.constant.enums.TaskStatusEnum;
import group.za.bank.sbs.trade.model.entity.TaskResult;
import group.za.bank.sbs.trade.model.resp.feign.TradeDateDiffResp;
import group.za.bank.statement.common.config.AliOSSProperties;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.StatementBusinessTypeEnum;
import group.za.bank.statement.constants.enums.StatementFileStatusEnum;
import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.bank.statement.domain.mapper.TdStatementFileRecordMapper;
import group.za.bank.statement.domain.mapper.TdStockStatementDataMapper;
import group.za.bank.statement.entity.dto.StatementAccountAssetDto;
import group.za.bank.statement.entity.dto.StatementCashSummaryDto;
import group.za.bank.statement.entity.dto.StockStatementTradeDateInfo;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.FileManagerService;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import group.za.bank.statement.utils.AlarmUtil;
import group.za.invest.mybatis.table.TableInfo;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;

import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
public class TdStockStatementDataServiceImplTest {
    @Mock
    TdStockStatementDataMapper stockStatementDataMapper;
    @Mock
    TdStatementFileRecordMapper statementFileRecordMapper;
    @Mock
    FileManagerService fileManagerService;
    @Mock
    SystemConfig systemConfig;
    @Mock
    TdStatementFileRecordService statementFileRecordService;
    @Mock
    AlarmUtil alarmUtil;
    @Mock
    MonthlyStatementManager monthlyStatementManager;
    @Mock
    AliOSSProperties aliOSSProperties;

    @Mock
    private StockTradeManager stockTradeManager;
    @Mock
    private CounterFileInfoExtendMapper counterFileInfoExtendMapper;
    @Mock
    Logger log;
    //Field mapperClass of type Class - was not mocked since Mockito doesn't mock a Final class when 'mock-maker-inline' option is not set
//Field entityClass of type Class - was not mocked since Mockito doesn't mock a Final class when 'mock-maker-inline' option is not set
    @Mock
    TableInfo tableInfo;
    @Mock
    private TradeCalendarRemoteService tradeCalendarRemoteService;
    @Mock
    Map<String, String> fieldColumns;

    @Mock
    private ThreadPoolTaskExecutor statementCheckExecutor;
    @InjectMocks
    TdStockStatementDataServiceImpl tdStockStatementDataServiceImpl;


    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJson;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        mockedJson = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");


    }

    @After
    public void close(){
        mockedJsonUtils.close();
        mockedJson.close();
    }

    @Test
    public void testParseStatementMonthlyData() {
        when(fileManagerService.existOssFile(any())).thenReturn(true);
        when(fileManagerService.downloadFile(anyString(), anyString(), anyString())).thenReturn(true);
        when(systemConfig.getDir()).thenReturn("template");
        when(systemConfig.getLocalPath()).thenReturn(this.getClass().getClassLoader().getResource("template").getPath());

        TdStatementFileRecord tdStatementFileRecord = new TdStatementFileRecord();
        tdStatementFileRecord.setId(1111L);
        tdStatementFileRecord.setStatementType(StatementTypeEnum.MONTHLY.getType());
        tdStatementFileRecord.setStatementDate(new Date());
        tdStatementFileRecord.setDataStatus(StatementFileStatusEnum.ING.getFileStatus());
        when(statementFileRecordService.checkFileRecord(any(), any(), any(), any())).thenReturn(tdStatementFileRecord);

        try {
            tdStockStatementDataServiceImpl.parseStatementMonthlyData(StatementTypeEnum.MONTHLY,"********", "********", Boolean.TRUE);
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    @Test
    public void testQueryDataByFormatType() {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = new StockStatementTradeDateInfo();
        stockStatementTradeDateInfo.setPeriod("202206");
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo
                = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        marketStatementTradeDateInfo.setStartTradeDate("********");
        marketStatementTradeDateInfo.setEndTradeDate("********");
        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketStatementTradeDateInfoMap = new HashMap<>();
        marketStatementTradeDateInfoMap.put(ExchangeCodeEnum.US.getValue(),marketStatementTradeDateInfo);

        tdStockStatementDataServiceImpl.queryPeriodDataByFormatType(ExchangeCodeEnum.US.getValue(), "********", "********", 4,"50112001",4);
    }

    @Test
    public void testQueryUsOrderData() {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = new StockStatementTradeDateInfo();
        stockStatementTradeDateInfo.setPeriod("202206");
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo
                = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        marketStatementTradeDateInfo.setStartTradeDate("********");
        marketStatementTradeDateInfo.setEndTradeDate("********");
        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketStatementTradeDateInfoMap = new HashMap<>();
        marketStatementTradeDateInfoMap.put(ExchangeCodeEnum.US.getValue(),marketStatementTradeDateInfo);
        stockStatementTradeDateInfo.setMarketCodeAndTradeDateInfoMap(marketStatementTradeDateInfoMap);

        tdStockStatementDataServiceImpl.queryUsOrderData(stockStatementTradeDateInfo, StatementTypeEnum.MONTHLY.getType(), "*********", any());
    }

    @Test
    public void testQueryTradeChangeDetailData() {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = new StockStatementTradeDateInfo();
        stockStatementTradeDateInfo.setPeriod("202206");
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo
                = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        marketStatementTradeDateInfo.setStartTradeDate("********");
        marketStatementTradeDateInfo.setEndTradeDate("********");
        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketStatementTradeDateInfoMap = new HashMap<>();
        marketStatementTradeDateInfoMap.put(ExchangeCodeEnum.US.getValue(),marketStatementTradeDateInfo);
        tdStockStatementDataServiceImpl.queryTradeChangeDetailData("202401", stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(), "*********", StatementBusinessTypeEnum.getAllBusinessTypeList(), "USEX");
    }

    @Test
    public void testQueryAccountAssetData() {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = new StockStatementTradeDateInfo();
        stockStatementTradeDateInfo.setPeriod("202206");
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo
                = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        marketStatementTradeDateInfo.setStartTradeDate("********");
        marketStatementTradeDateInfo.setEndTradeDate("********");
        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketStatementTradeDateInfoMap = new HashMap<>();
        marketStatementTradeDateInfoMap.put(ExchangeCodeEnum.US.getValue(),marketStatementTradeDateInfo);

        List<StatementAccountAssetDto> result = tdStockStatementDataServiceImpl.queryAccountAssetData("202206", StatementTypeEnum.MONTHLY.getType(), "*********", any());
    }

    @Test
    public void testQueryCashSummaryData() {
        StockStatementTradeDateInfo stockStatementTradeDateInfo = new StockStatementTradeDateInfo();
        Map<String, StockStatementTradeDateInfo.MarketStatementTradeDateInfo> marketCodeAndTradeDateInfoMap = new HashMap<>();
        StockStatementTradeDateInfo.MarketStatementTradeDateInfo marketStatementTradeDateInfo = new StockStatementTradeDateInfo.MarketStatementTradeDateInfo();
        marketCodeAndTradeDateInfoMap.put(MarketCodeEnum.US.getValue(),marketStatementTradeDateInfo);
        stockStatementTradeDateInfo.setMarketCodeAndTradeDateInfoMap(marketCodeAndTradeDateInfoMap);
        TdStockStatementData tdStockStatementData = new TdStockStatementData();
        when(stockStatementDataMapper.queryPeriodDataByFormatType(any(), any(), any(), any(), any(), any())).thenReturn(Arrays.asList(tdStockStatementData));
        List<StatementCashSummaryDto> result = tdStockStatementDataServiceImpl.queryCashSummaryData(stockStatementTradeDateInfo, StatementTypeEnum.DAILY.getType(), "*********");
    }

    @Test
    public void testDailyStatementDataCheck(){
        TradeDateDiffResp tradeDateDiffResp = new TradeDateDiffResp();
        tradeDateDiffResp.setTradeDate("20240101");
        when(tradeCalendarRemoteService.tradeDateDiff(any(), any(), any())).thenReturn(tradeDateDiffResp);
        TaskResult taskResult = new TaskResult();
        taskResult.setTaskCode(TaskCodeEnum.COMPANY_ACTION_SETTLE_FINISH.getValue());
        taskResult.setStatus(TaskStatusEnum.SUCCESS.getValue());
        when(stockTradeManager.queryTaskResultList(any())).thenReturn(Arrays.asList(taskResult));
        tdStockStatementDataServiceImpl.dailyStatementDataCheck("20240101");
    }
}