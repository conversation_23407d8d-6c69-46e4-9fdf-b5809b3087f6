<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>group.za.bank</groupId>
        <artifactId>zabank-ftc-spring-boot-parent</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>zabank-ftc-statement-service</artifactId>
    <!-- 不需要更改此版本 -->
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>zabank-ftc-statement-provider</module>
        <module>zabank-ftc-statement-domain</module>
        <module>zabank-ftc-statement-share</module>
    </modules>

    <properties>
        <!-- 二方包 start -->
        <zabank.ftc.statement.domain.version>1.0.9</zabank.ftc.statement.domain.version>
        <zabank.ftc.statement.share.version>1.0.9</zabank.ftc.statement.share.version>
        <!-- 二方包 end -->
        <!-- 二方包插件 start -->
        <zabank.invest.common.version>1.44.7</zabank.invest.common.version>
        <zabank.invest.basecommon.version>1.0.3</zabank.invest.basecommon.version>

        <zabank.ftc.trade.domain.version>4.1.0</zabank.ftc.trade.domain.version>
        <zabank.ftc.trade.share.version>4.1.0</zabank.ftc.trade.share.version>


        <zabank.ftc.product.share.version>3.11.0</zabank.ftc.product.share.version>
        <zabank.ftc.product.domain.version>3.11.0</zabank.ftc.product.domain.version>


        <zabank.ftc.account.domain.version>4.0.0hksouthall-SNAPSHOT</zabank.ftc.account.domain.version>
        <zabank.ftc.account.share.version>4.0.0hksouthall-SNAPSHOT</zabank.ftc.account.share.version>

        <zabank.ftc.public.share.version>2.4.1</zabank.ftc.public.share.version>
        <zabank.ftc.public.domain.version>2.4.1</zabank.ftc.public.domain.version>

        <!--股票行情-->
        <zabank.sbs.quotasup.share.version>1.3.1</zabank.sbs.quotasup.share.version>
        <zabank-ccs-coresup-share.version>1.0.0</zabank-ccs-coresup-share.version>
        <zabank.ftc.outerfile.appgw.share.version>1.1.0</zabank.ftc.outerfile.appgw.share.version>

        <zainvest.tcs.tradefront.share.version>1.30.0</zainvest.tcs.tradefront.share.version>

        <!--股票交易-->
        <zabank-sbs-trade-domain.version>2.0.0-SNAPSHOT</zabank-sbs-trade-domain.version>
        <zabank-sbs-trade-share.version>2.0.0-SNAPSHOT</zabank-sbs-trade-share.version>

        <zabank-sbs-tradedata-share.version>1.0.2</zabank-sbs-tradedata-share.version>
        <!--股票柜台前置-->
        <zabank.sbs.countfront.share.version>1.0.3</zabank.sbs.countfront.share.version>
        <!--银行前置-->
        <zabank-sbs-bankfront-domain.version>1.2.3</zabank-sbs-bankfront-domain.version>
        <zabank-sbs-bankfront-share.version>1.2.3</zabank-sbs-bankfront-share.version>

        <zabank-ccs-tradecore-domain.version>1.1.9</zabank-ccs-tradecore-domain.version>
        <zabank-ccs-tradecore-share.version>1.1.9</zabank-ccs-tradecore-share.version>

        <zabank-sbs-business-share.version>1.0.11</zabank-sbs-business-share.version>
        <zabank.sbs.business.domain.version>1.0.12</zabank.sbs.business.domain.version>

        <zabank-sbs-ipo-domain.version>1.0.0-SNAPSHOT</zabank-sbs-ipo-domain.version>

        <zainvest.cache.version>1.40.0</zainvest.cache.version>
        <zainvest.accfront.share.vesrion>1.29.1</zainvest.accfront.share.vesrion>
        <zainvest.druid.version>1.10.0</zainvest.druid.version>
        <zainvest.web.version>1.10.0</zainvest.web.version>
        <zainvest.mybatis.version>1.10.0</zainvest.mybatis.version>
        <zainvest.file.spring.boot.version>1.28.1</zainvest.file.spring.boot.version>
        <za.bank.sso.version>0.7.4-SNAPSHOT</za.bank.sso.version>
        <zainvest.rabbitmq.springboot.starter.version>1.7.1-rc2</zainvest.rabbitmq.springboot.starter.version>

        <zainvest.file.parserversion>1.40.1-SNAPSHOT</zainvest.file.parserversion>

        <zainvest.rabbitmq.starter.version>1.40.0</zainvest.rabbitmq.starter.version>
        <zati.pcs.watchdog.version>1.0.3</zati.pcs.watchdog.version>


        <zabank.act.messaging.version>1.0.8-SNAPSHOT</zabank.act.messaging.version>
        <!-- 二方包插件 end -->


        <!-- 外部包 start -->
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <xxl.job.version>2.1.0</xxl.job.version>
        <shiro.version>1.11.0</shiro.version>
        <easyexcel.version>2.2.10</easyexcel.version>
        <mybatis.version>3.5.5</mybatis.version>
        <caffeine.version>2.5.5</caffeine.version>
        <jsoup.version>1.13.1</jsoup.version>
        <fel.version>0.8</fel.version>
        <json-smart.version>2.4.10</json-smart.version>
        <jettison.version>1.5.4</jettison.version>
        <aliyun.oss.version>3.16.0</aliyun.oss.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <mapstruct-processor.version>1.4.1.Final</mapstruct-processor.version>
        <spock-core.version>1.2-groovy-2.4</spock-core.version>
        <spock-spring.version>1.2-groovy-2.4</spock-spring.version>
        <!-- 外部包 end -->
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>com.zabank</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>./smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>zabank-ftc-statement-service</projectName>
                    <includes>
                        <include>group.za.bank:.*</include>
                        <include>group.za.invest:.*</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
