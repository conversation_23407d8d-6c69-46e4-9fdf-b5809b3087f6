package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.service.TdStatementFileRecordService;
import group.za.bank.statement.service.TdStockStatementDataService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

public class StatementDataDailyCheckJobTest {
    @Mock
    TdStatementFileRecordService statementFileRecordService;
    @Mock
    TdStockStatementDataService stockStatementDataService;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    StatementDataDailyCheckJob statementDataDailyCheckJob;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testExecute() throws Exception {
        when(statementFileRecordService.queryDailyFileRecord(any())).thenReturn(new TdStatementFileRecord());

        ReturnT<String> result = statementDataDailyCheckJob.execute("message");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme