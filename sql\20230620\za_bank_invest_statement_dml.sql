-- 清理上月生成的结单数据
delete from za_bank_invest_statement.td_fund_monthly_statement_info where period='202305';
delete from za_bank_invest_statement.td_fund_monthly_statement where period='202305';
delete from za_bank_invest_statement.td_monthly_statement_data;
delete from za_bank_invest_statement.td_statement_file_record;

-- 补充股票上月的结单文件解析记录
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-07-03', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (2, '2023-06-30', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-30', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-29', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-28', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-27', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-26', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-23', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-22', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-21', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-20', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-16', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-15', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-14', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-13', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-12', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-09', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-08', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-07', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-06', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-05', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-02', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-06-01', 2);