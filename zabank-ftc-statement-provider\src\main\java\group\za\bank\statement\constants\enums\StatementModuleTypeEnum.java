package group.za.bank.statement.constants.enums;

/**
 * 结单类型
 *
 * <AUTHOR>
 * @date 2022/04/29
 **/
public enum StatementModuleTypeEnum {
    /**
     * ttl提供月结单数据类型
     * "后期不会使用月结单数据了"
     */

    /**
     * Investment account position 投資帳戶持倉
     */
    HOLDING_MODULE("holding","持仓明细","持倉明細","Holdings Details"),
    /**
     * Confirmed Transaction 已確認交易
     */
    CONFIRMED_ORDER_MODULE("confirmedOrder","已确认交易","已確認交易","Confirmed Transaction"),

    /**
     * Fund Pending Transaction 處理中交易
     */
    PENDING_ORDER_MODULE("pendingOrder","处理中交易","處理中交易","Pending Transaction"),

    /**
     * Dividend 派息
     */
    DIVIDEND_ORDER_MODULE("dividendOrder","收益及费用摘要","收益及費用摘要","Income and Charges Summary"),

    /**
     * Deposit & Withdrawal 存入及提取
     * Holdings Movement 股票持倉變動
     */
    HOLDING_CHANGE_MODULE("holdingChange","持仓变动","持倉變動","Holdings Movement"),
    ;



    /**
     * 模块类型
     */
    private String moduleType;
    /**
     * 模块名称
     */
    private String moduleNameZh;
    private String moduleNameHk;
    private String moduleNameEn;


    StatementModuleTypeEnum(String moduleType, String moduleNameZh, String moduleNameHk, String moduleNameEn) {
        this.moduleType = moduleType;
        this.moduleNameZh = moduleNameZh;
        this.moduleNameHk = moduleNameHk;
        this.moduleNameEn = moduleNameEn;
    }



    public String getModuleType() {
        return moduleType;
    }

    public String getModuleNameZh() {
        return moduleNameZh;
    }

    public String getModuleNameHk() {
        return moduleNameHk;
    }

    public String getModuleNameEn() {
        return moduleNameEn;
    }
}
