package group.za.bank.statement.constants.enums;


import group.za.bank.invest.basecommon.constants.enums.BaseEnum;

/**
 * @Date  2021/7/17 14:18
 * @Description 月结单文档生成状态
 * @Version v1.0
 */
public enum MonthlyStatementInfoStatusEnum implements BaseEnum {
    PROCESSING("1", Byte.valueOf("1"), "处理中"),
    FINISHED("2", Byte.valueOf("2"), "生成成功")

    ;

    private String value;
    private Byte dbValue;
    private String msg;

    MonthlyStatementInfoStatusEnum(String value, Byte dbValue, String msg) {
        this.value = value;
        this.msg = msg;
        this.dbValue = dbValue;
    }

    /**
     * 字典值，实际存储在数据库的值
     *
     * @return String 字符串
     */
    @Override
    public String getValue() {
        return value;
    }

    /**
     * 字典描述信息，前端页面展示的值
     *
     * @return String 字符串
     */
    @Override
    public String getMsg() {
        return msg;
    }

    public Byte getDbValue() {
        return dbValue;
    }
}
