create table za_bank_invest_statement.stock_statement_data_parse_record (
  id int auto_increment primary key comment 'id',
  parse_id varchar(12) not null comment '记录Id,日期+类型',
  trade_date date not null comment '交易日期',
  data_type varchar(12) not null comment '数据类型',
  data_status int(4) default 0 null comment '解析状态:0-未开始,1-处理中,2-成功，3-失败',
  creator varchar(32) default 'system' not null comment '创建者',
  gmt_created datetime default CURRENT_TIMESTAMP not null comment '创建时间',
  modifier varchar(32) default 'system' not null comment '修改者',
  gmt_modified datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  is_deleted char default 'N' not null comment '是否删除',
  unique key uk_date_type (trade_date,data_type)
) ENGINE = InnoDB  comment '股票结单数据解析记录表';


create table za_bank_invest_statement.stock_statement_income_charges_info (
  id bigint auto_increment primary key comment 'id',
  biz_id varchar(32) not null comment '业务主键',
  parse_id varchar(32) not null comment '解析记录表关联ID',
  user_id varchar(32) not null comment '用户id',
  stock_code varchar(32) not null comment '股票代码',
  market_code varchar(10) not null comment '股票代码',
  business_type varchar(10) not null comment '收益费用类型',
  account_id varchar(32) not null comment '资金账号',
  statement_date date not null comment '结单日期',
  stock_name_en varchar(128) null comment '股票名称',
  stock_name_zh varchar(128) null comment '股票简体名称',
  stock_name_hk varchar(128) null comment '股票繁体名称',
  remark varchar(512) null comment 'remark',
  bonus_qty decimal(15, 4) null comment '红股数量',
  currency varchar(12) null comment '币种',
  amount decimal(15, 2) comment '金额',
  creator varchar(32) default 'system' not null comment '创建者',
  gmt_created datetime default CURRENT_TIMESTAMP not null comment '创建时间',
  modifier varchar(32) default 'system' not null comment '修改者',
  gmt_modified datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  is_deleted char default 'N' not null comment '是否删除',
  unique key uk_biz_id (biz_id,business_type),
  key idx_parse_id (parse_id),
  key idx_user_id (user_id)
) ENGINE = InnoDB  comment '股票收益及费用摘要记录表';

