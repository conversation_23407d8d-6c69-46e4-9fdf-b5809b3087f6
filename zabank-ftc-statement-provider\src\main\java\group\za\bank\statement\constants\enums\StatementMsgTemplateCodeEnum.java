package group.za.bank.statement.constants.enums;


import lombok.Getter;

/**
 * @description: 消息模板
 * @author: Zheng.Ke EmailTemplateCodeEnum
 * @time: 2021/8/27 15:22
 */
@Getter
public enum StatementMsgTemplateCodeEnum {
    /** */
    MONTHLY_INVEST_STATEMENT("monthly_invest_statement", "银行投资月结单"),
    SOUTH_BOUND_MONTHLY_INVEST_STATEMENT("south_bound_monthly_invest_statement", "南向通银行投资月结单"),
    //沿用线上现有的code--业务自定义修改模板固文案即可
    INVEST_CLOSE_MONTHLY_STATEMENT("invest_fund_close_monthly_statement", "银行投资销户月结单"),

    ;

    private String templateCode;
    private String desc;

    StatementMsgTemplateCodeEnum(String templateCode, String desc) {
        this.templateCode = templateCode;
        this.desc = desc;
    }

    public static StatementMsgTemplateCodeEnum judge(String templateCode) {
        for (StatementMsgTemplateCodeEnum item : values()) {
            if (item.getTemplateCode().equals(templateCode)) {
                return item;
            }
        }
        return null;
    }
}
