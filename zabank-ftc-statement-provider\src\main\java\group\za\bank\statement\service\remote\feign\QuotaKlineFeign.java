package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.quotasup.share.service.feign.QuotaKlineFeignService;
import group.za.bank.sbs.trade.feign.StkRateInfoHisFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 交易日历feign
 *
 * <AUTHOR>
 * @date 2022/05/27
 **/
@FeignClient(
        value = "zabank-sbs-quotasup-service",
        contextId = "quotaKlineFeign",
        url = "${sbs.gateway.url}"
)
public interface QuotaKlineFeign extends QuotaKlineFeignService {
}
