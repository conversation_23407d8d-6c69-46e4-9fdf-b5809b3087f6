package group.za.bank.statement.service.remote.feign;

import group.za.bank.ccs.core.support.share.feign.KlineQuerySupFeignService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 数字货币行情feign
 *
 * <AUTHOR>
 * @date 2022/05/27
 **/
@FeignClient(
        value = "zabank-ccs-coresup-service",
        contextId = "klineQuerySupFeignService1",
        url = "${ccs.quota.gateway.url}"
)
public interface CryptoQuotaKlineFeign extends KlineQuerySupFeignService {
}
