package group.za.bank.statement.entity.req;

import group.za.bank.sbs.tradedata.model.req.BaseAppReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName ContractNoteDetailReq
 * @Description 成交单合同详情请求对象
 * <AUTHOR>
 * @Date 2023/4/16 16:41
 */
@Data
@ApiModel(value = "成交单详情")
public class ContractNoteInfoReq extends BaseAppReq {

    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "orderNo can't empty")
    private String orderNo;

    private String marketCode;

    @ApiModelProperty(value = "订单类型 1-普通订单， 2-公司行动")
    @NotBlank(message = "orderType can't empty")
    private String orderType;

    @ApiModelProperty(value = "公司行动类型")
    private String activityType;

}


