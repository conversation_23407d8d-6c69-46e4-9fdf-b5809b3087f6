package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.trade.model.resp.feign.*;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.remote.feign.StkTradeCalendarFeign;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class TradeCalendarRemoteServiceImplTest extends BaseTestService {
    @Mock
    StkTradeCalendarFeign tradeCalendarFeign;
    @Mock
    Logger log;
    @InjectMocks
    TradeCalendarRemoteServiceImpl tradeCalendarRemoteServiceImpl;

    @Test
    public void testGetMonthLastTradeDate() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        when(tradeCalendarFeign.getMonthLastTradeDate(any())).thenReturn(responseData);
        MonthLastTradeDateResp result = tradeCalendarRemoteServiceImpl.getMonthLastTradeDate("marketCode", "yearMonth");
    }

    @Test
    public void testGetMonthFirstTradeDate() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        when(tradeCalendarFeign.getMonthFirstTradeDate(any())).thenReturn(responseData);
        MonthFirstTradeDateResp result = tradeCalendarRemoteServiceImpl.getMonthFirstTradeDate("marketCode", "yearMonth");
    }

    @Test
    public void testQueryTradeDateCalendarList() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        responseData.setValue(new QueryTradeDateCalendarListResp());
        when(tradeCalendarFeign.queryTradeDateCalendarList(any())).thenReturn(responseData);
        List<TradeCalendarResp> result = tradeCalendarRemoteServiceImpl.queryTradeDateCalendarList("marketCode", "startDate", "endDate");
    }

    @Test
    public void testTradeDateDiff() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        responseData.setValue(new TradeDateDiffResp());
        when(tradeCalendarFeign.tradeDateDiff(any())).thenReturn(responseData);
        TradeDateDiffResp result = tradeCalendarRemoteServiceImpl.tradeDateDiff("marketCode", "baseDate", Integer.valueOf(0));
    }

    @Test
    public void testIsTradeDay() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        IsTradeDateResp isTradeDateResp = new IsTradeDateResp();
        isTradeDateResp.setTradingDate(true);
        responseData.setValue(isTradeDateResp);
        when(tradeCalendarFeign.isTradeDate(any())).thenReturn(responseData);
        boolean result = tradeCalendarRemoteServiceImpl.isTradeDay("marketCode", new GregorianCalendar(2024, Calendar.DECEMBER, 11, 16, 54).getTime());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme