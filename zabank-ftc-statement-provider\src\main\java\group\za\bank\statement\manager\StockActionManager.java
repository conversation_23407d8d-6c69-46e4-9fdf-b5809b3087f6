package group.za.bank.statement.manager;

import com.google.common.collect.Maps;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.constants.enums.CurrencyEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.trade.common.constant.enums.FractionHandleTypeEnum;
import group.za.bank.sbs.trade.common.constant.enums.TaxDetailStatusEnum;
import group.za.bank.sbs.trade.mapper.StkActionExtendMapper;
import group.za.bank.sbs.trade.mapper.StkTaxDetailMapper;
import group.za.bank.sbs.trade.model.entity.StkActionDetail;
import group.za.bank.sbs.trade.model.entity.StkActionFractionInfo;
import group.za.bank.sbs.trade.model.entity.StkActionRecycleDetail;
import group.za.bank.sbs.trade.model.entity.StkTaxDetail;
import group.za.bank.sbs.tradedata.model.dto.ActionFractionOrderDTO;
import group.za.bank.sbs.tradedata.model.resp.ActionFractionOrderResp;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.entity.dto.TaxQueryDTO;
import group.za.bank.statement.service.remote.TradeRemoteService;
import group.za.bank.statement.utils.DataSortUtils;
import group.za.invest.cache.redis.lock.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 股票交易
 *
 * <AUTHOR>
 * @date 2023/09/25
 **/
@Service
@Slf4j
public class StockActionManager {

    @Autowired
    private StkActionExtendMapper stkActionExtendMapper;
    @Autowired
    private TradeRemoteService tradeRemoteService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private StkTaxDetailMapper stkTaxDetailMapper;

    public ActionFractionOrderResp actionFractionOrderQuery(String marketCode, String accountId, String bankUserId, Date startDate, Date endDate) {
        List<ActionFractionOrderDTO> orderList = new ArrayList<>();
        //查询时间区间内所有执行的公司行动ID，从缓存中获取
        Map<String, List<StkActionFractionInfo>> fractionInfoMap = this.queryActionInfoMap(marketCode, startDate, endDate);

        Set<String> actionIds = fractionInfoMap.keySet();
        if (CollectionUtils.isEmpty(actionIds)) {
            ActionFractionOrderResp actionFractionOrderResp = new ActionFractionOrderResp();
            actionFractionOrderResp.setOrderList(orderList);
            return actionFractionOrderResp;
        }

        // 分页查询处理 actionIds
        List<StkActionDetail> detailResult = queryUserActionDetail(actionIds, accountId);

        for (StkActionDetail stkActionDetail : detailResult) {
            List<StkActionFractionInfo> stkActionFractionInfoList = fractionInfoMap.get(stkActionDetail.getActionId());
            //处理小数股订单数据
            for (StkActionFractionInfo stkActionFractionInfo : stkActionFractionInfoList) {
                //查询IB卖出的订单数据
                if (FractionHandleTypeEnum.IB_SELL.getValue().equals(stkActionFractionInfo.getHandleType())) {
                    if (stkActionFractionInfo.getIbSellAmount().compareTo(BigDecimal.ZERO) > 0) {
                        //先校验业务日期是否在查询时间区间内
                        if (!checkDateInRange(startDate, endDate, stkActionFractionInfo.getIbSellDate())) {
                            log.error("月结单-公司行动IB卖出订单业务日期不在月结单时间区间内，不处理，actionId:{},userId:{}，startDate:{},endDate:{},ibSellDate:{}",
                                    stkActionFractionInfo.getActionId(), bankUserId, startDate, endDate, stkActionFractionInfo.getIbSellDate());
                            continue;
                        }

                        if (null == stkActionDetail.getContractnoteFraction()
                                || stkActionDetail.getContractnoteFraction().compareTo(BigDecimal.ZERO) <= 0) {
                            log.info("月结单-公司行动IB卖出订单拆单数量小于等于0，不处理，actionId:{},userId:{}, qty:{}",
                                    stkActionFractionInfo.getActionId(), bankUserId, stkActionDetail.getContractnoteFraction());
                            continue;
                        }

                        ActionFractionOrderDTO actionFractionOrderDTO = buildIbSellActionFractionOrderDTO(stkActionFractionInfo, stkActionDetail);

                        //计算交收日期
                        Date settleDate = this.calIbFractionOrderSettleDate(stkActionDetail, stkActionFractionInfoList);
                        actionFractionOrderDTO.setSettleDate(settleDate);

                        orderList.add(actionFractionOrderDTO);
                    }
                }
                //查询中台卖出的订单数据
                if (FractionHandleTypeEnum.SELL.getValue().equals(stkActionFractionInfo.getHandleType())) {
                    if (stkActionFractionInfo.getSellQty().compareTo(BigDecimal.ZERO) > 0) {
                        //先校验业务日期是否在查询时间区间内
                        if (!checkDateInRange(startDate, endDate, stkActionFractionInfo.getSellDate())) {
                            log.error("月结单-公司行动中台卖出订单业务日期不在月结单时间区间内，不处理，actionId:{},userId:{}，startDate:{},endDate:{},ibSellDate:{}",
                                    stkActionFractionInfo.getActionId(), bankUserId, startDate, endDate, stkActionFractionInfo.getSellDate());
                            continue;
                        }

                        if (stkActionDetail.getSellFractionQty().compareTo(BigDecimal.ZERO) <= 0) {
                            log.info("月结单-公司行动中台卖出订单拆单数量小于等于0，不处理，actionId:{},userId:{}, qty:{}",
                                    stkActionFractionInfo.getActionId(), bankUserId, stkActionDetail.getSellFractionQty());
                            continue;
                        }
                        //构建数据
                        ActionFractionOrderDTO actionFractionOrderDTO = buildSellActionFractionOrderDTO(stkActionFractionInfo, stkActionDetail);
                        //计算交收日期
                        Date settleDate = this.calFractionOrderSettleDate(stkActionDetail, stkActionFractionInfoList, stkActionFractionInfo);
                        actionFractionOrderDTO.setSettleDate(settleDate);

                        orderList.add(actionFractionOrderDTO);
                    }
                }
            }
        }

        //按交易日排序
        orderList = DataSortUtils.sortByDateAsc(ActionFractionOrderDTO::getTradeDate, orderList);

        ActionFractionOrderResp orderResp = new ActionFractionOrderResp();
        orderResp.setOrderList(orderList);

        return orderResp;
    }

    /**
     * 查询用户公司行动小数股订单数据
     * @param actionIds
     * @param accountId
     * @return
     */
    private List<StkActionDetail> queryUserActionDetail(Set<String> actionIds, String accountId){
        int batchSize = 1000;
        List<StkActionDetail> result = new ArrayList<>();

        // 分页处理 actionIds
        List<List<String>> partitions = new ArrayList<>();
        int size = actionIds.size();
        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            partitions.add(new ArrayList<>(actionIds).subList(i, end));
        }

        for (List<String> partition : partitions) {

            List<StkActionDetail> stkActionDetailsByActionId = stkActionExtendMapper.queryByAccountAndActionIdBatch(accountId, partition);

            result.addAll(stkActionDetailsByActionId);
        }
        return result;
    }
    /**
     * 计算公司行动小数股IB卖出订单交收日
     *
     * @param stkActionDetail
     * @param stkActionFractionInfoList
     * @return
     */
    private Date calIbFractionOrderSettleDate(StkActionDetail stkActionDetail, List<StkActionFractionInfo> stkActionFractionInfoList) {

        String marketCode = stkActionDetail.getMarketCode();
        //1.如果有资金派发记录，则取资金实际派发日期作为统一交收日
        StkActionFractionInfo capitalFractionInfo = stkActionFractionInfoList.stream()
                .filter(t -> FractionHandleTypeEnum.CAPITAL.getValue().equals(t.getHandleType()))
                .findFirst().orElse(null);
        if (null != capitalFractionInfo) {
            //从明细数据获取实际派发日期
            if (null != stkActionDetail && null != stkActionDetail.getFractionAmountTime()) {
                return stkActionDetail.getFractionAmountTime();
            }
        }
        //2.如果有中台卖出记录，则取中台卖出订单交收日作为统一交收日
        StkActionFractionInfo sellFractionInfo = stkActionFractionInfoList.stream()
                .filter(t -> FractionHandleTypeEnum.SELL.getValue().equals(t.getHandleType()))
                .findFirst().orElse(null);
        if (null != sellFractionInfo && sellFractionInfo.getSellQty().compareTo(BigDecimal.ZERO) > 0) {
            //交收日期，自己计算T+1 (交收日)
            Integer settleDiffDate = getSettleDiffDate(marketCode);
            return tradeRemoteService.getSettlementDate(MarketCodeEnum.US.getValue(), sellFractionInfo.getSellDate(), settleDiffDate);
        }

        if(systemConfig.isStatementCompanyFractionIbSellCheck()){
            log.error("结单公司行动小数股IB卖出订单结算日为空，actionId:{},userId:{}", stkActionDetail.getActionId(), stkActionDetail.getUserId());
            throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "结单公司行动小数股IB卖出订单结算日为空");
        }

        //3.如果资金派发记录和中台卖出记录都没有，则展示--
        return null;
    }

    /**
     * 计算公司行动小数股中台卖出订单交收日
     * @param stkActionDetail
     * @param stkActionFractionInfoList
     * @return
     */
    private Date calFractionOrderSettleDate(StkActionDetail stkActionDetail, List<StkActionFractionInfo> stkActionFractionInfoList, StkActionFractionInfo sellFractionInfo) {

        String marketCode = stkActionDetail.getMarketCode();
        //1.如果有资金派发记录，则取资金实际派发日期作为统一交收日
        StkActionFractionInfo capitalFractionInfo = stkActionFractionInfoList.stream()
                .filter(t -> FractionHandleTypeEnum.CAPITAL.getValue().equals(t.getHandleType()))
                .findFirst().orElse(null);
        if (null != capitalFractionInfo) {
            //从明细数据获取实际派发日期
            if (null != stkActionDetail && null != stkActionDetail.getFractionAmountTime()) {
                return stkActionDetail.getFractionAmountTime();
            }
        }

        //交收日期，自己计算T+1 (交收日)
        Integer settleDiffDate = getSettleDiffDate(marketCode);
        return tradeRemoteService.getSettlementDate(MarketCodeEnum.US.getValue(), sellFractionInfo.getSellDate(), settleDiffDate);
    }



    /**
     * 查询区间内发生的公司行动列表，并做相关数据检查及告警
     *
     * @return
     */
    private Map<String, List<StkActionFractionInfo>> queryActionInfoMap(String marketCode, Date startDate, Date endDate) {
        String startDateStr = DateUtil.format(startDate, DateUtil.FORMATDAY);
        String endDateStr = DateUtil.format(endDate, DateUtil.FORMATDAY);
        //先从缓存查，每月大概会有几个公司行动，数据量不大
        RBucket<Map<String, List<StkActionFractionInfo>>> bucket = redissonClient.getBucket(StatementRedisCacheKey.FRACTION_ACTION_INFO_LIST_CACHE.key(marketCode, startDateStr, endDateStr));
        if (null != bucket.get()) {
            return bucket.get();
        }
        try {
            //加锁防止首次查询大量请求进来
            distributedLock.tryLock(StatementRedisCacheKey.FRACTION_ACTION_INFO_LIST_QUERY.key(startDateStr, endDateStr), 5, TimeUnit.SECONDS, () -> {
                //做二次确认，避免并发请求全部往下走
                if (null != bucket.get()) {
                    return bucket.get();
                }
                Map<String, List<StkActionFractionInfo>> resultMap = Maps.newHashMap();
                //查询时间区间内所有执行的公司行动ID，取中台卖出和IB卖出数据的并集
                Set<String> actionIdSet = queryOrderActionIdByTradeDate(startDate, endDate);
                for (String actionId : actionIdSet) {
                    List<StkActionFractionInfo> fractionInfoList = new ArrayList<>();

                    //查询IB卖出记录
                    StkActionFractionInfo ibSellFractionInfo = queryFractionOrderList(actionId, FractionHandleTypeEnum.IB_SELL.getValue());
                    if (null != ibSellFractionInfo) {
                        fractionInfoList.add(ibSellFractionInfo);
                    }

                    //查询中台卖出记录
                    StkActionFractionInfo sellFractionInfo = queryFractionOrderList(actionId, FractionHandleTypeEnum.SELL.getValue());
                    if (null != sellFractionInfo) {
                        fractionInfoList.add(sellFractionInfo);
                    }

                    //查询资金派发记录
                    StkActionFractionInfo capitalFractionInfo = queryFractionOrderList(actionId, FractionHandleTypeEnum.CAPITAL.getValue());
                    if (null != capitalFractionInfo) {
                        fractionInfoList.add(capitalFractionInfo);
                    }

                    resultMap.put(actionId, fractionInfoList);
                }

                //缓存3H
                bucket.set(resultMap, StatementConstants.ACTION_FRACTION_ID_LIST_CACHE_TIMEOUT, TimeUnit.HOURS);
                log.info("actionFractionOrderQuery startDate:{},endDate:{}, id maps:{}", startDate, endDate, JsonUtils.toJsonString(resultMap));

                return resultMap;
            });
            return bucket.get();
        } catch (Exception e) {
            log.error("actionFractionOrderQuery queryActionIdSet error", e);
            throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR);
        }
    }

    /**
     * 校验日期是否在区间内
     */
    private boolean checkDateInRange(Date startDate, Date endDate, Date tradeDate) {
        if (null == tradeDate) {
            return true;
        }
        //校验日期是否在区间内
        if (tradeDate.before(startDate) || tradeDate.after(endDate)) {
            return false;
        }
        return true;
    }

    /**
     * 构建IB卖出订单组装
     *
     * @param stkActionFractionInfo
     * @param stkActionDetail
     * @return
     */
    private ActionFractionOrderDTO buildIbSellActionFractionOrderDTO(StkActionFractionInfo
                                                                             stkActionFractionInfo, StkActionDetail stkActionDetail) {
        ActionFractionOrderDTO actionFractionOrderDTO = new ActionFractionOrderDTO();
        actionFractionOrderDTO.setActionId(stkActionFractionInfo.getActionId());
        actionFractionOrderDTO.setTradeDate(stkActionFractionInfo.getIbSellDate());
        actionFractionOrderDTO.setStockCode(stkActionFractionInfo.getTargetStockCode());
        actionFractionOrderDTO.setCurrency(CurrencyEnum.USD.getCurrency());
        actionFractionOrderDTO.setQty(stkActionDetail.getContractnoteFraction());
        actionFractionOrderDTO.setPrice(stkActionFractionInfo.getIbSellPrice());

        // IB卖出订单的结算金额=IB卖出订单实际分派金额
        BigDecimal ibFractionAmount = stkActionDetail.getIbFractionAmount();
        actionFractionOrderDTO.setAmount(ibFractionAmount);
        actionFractionOrderDTO.setSettleAmount(ibFractionAmount);

        actionFractionOrderDTO.setSfcFee(BigDecimal.ZERO);
        actionFractionOrderDTO.setTradeFee(BigDecimal.ZERO);
        actionFractionOrderDTO.setCommission(BigDecimal.ZERO);

        return actionFractionOrderDTO;
    }

    /**
     * 构建IB卖出订单组装
     *
     * @param stkActionFractionInfo
     * @return
     */
    private ActionFractionOrderDTO buildSellActionFractionOrderDTO(StkActionFractionInfo stkActionFractionInfo, StkActionDetail stkActionDetail) {
        ActionFractionOrderDTO actionFractionOrderDTO = new ActionFractionOrderDTO();
        actionFractionOrderDTO.setActionId(stkActionFractionInfo.getActionId());
        actionFractionOrderDTO.setTradeDate(stkActionFractionInfo.getSellDate());

        actionFractionOrderDTO.setStockCode(stkActionFractionInfo.getTargetStockCode());
        actionFractionOrderDTO.setCurrency(CurrencyEnum.USD.getCurrency());
        actionFractionOrderDTO.setPrice(stkActionFractionInfo.getSellPrice());
        actionFractionOrderDTO.setSfcFee(BigDecimal.ZERO);
        actionFractionOrderDTO.setTradeFee(BigDecimal.ZERO);
        actionFractionOrderDTO.setCommission(BigDecimal.ZERO);

        //中台卖出订单结算金额=rounddown(中台卖出股数*中台成交价,2)；
        BigDecimal settleAmount = stkActionDetail.getSellFractionQty()
                .multiply(stkActionFractionInfo.getSellPrice())
                .setScale(2, RoundingMode.DOWN);
        actionFractionOrderDTO.setQty(stkActionDetail.getSellFractionQty());
        actionFractionOrderDTO.setAmount(settleAmount);
        actionFractionOrderDTO.setSettleAmount(settleAmount);

        return actionFractionOrderDTO;
    }

    /**
     * 获取交收的日期差数
     *
     * @param marketCode
     * @return
     */
    public Integer getSettleDiffDate(String marketCode) {
        if (MarketCodeEnum.US.getValue().equals(marketCode)) {
            return 1;
        } else {
            return 2;
        }
    }

    private Set<String> queryOrderActionIdByTradeDate(Date startDate, Date endDate) {
        List<StkActionFractionInfo> sellFractionInfos = stkActionExtendMapper.querySellFractionInfoList(startDate, endDate);
        List<StkActionFractionInfo> ibSellFractionInfos = stkActionExtendMapper.queryIbSellFractionInfoList(startDate, endDate);

        List<StkActionFractionInfo> resultList = new ArrayList<>(sellFractionInfos);
        resultList.addAll(ibSellFractionInfos);

        return resultList.stream().map(StkActionFractionInfo::getActionId).collect(Collectors.toSet());
    }

    private StkActionFractionInfo queryFractionOrderList(String actionId, String handlerType){
        return stkActionExtendMapper.queryFractionOrderList(actionId, handlerType);
    }

    public List<TaxQueryDTO> taxQuery(String accountId, String startDate, String endDate, String marketCode) {
        Date start = DateUtil.parse(startDate, DateUtil.FORMATDAY_SS);
        Date end = DateUtil.parse(endDate, DateUtil.FORMATDAY_SS);

        List<StkTaxDetail> stkTaxDetails = stkTaxDetailMapper.queryTaxDetailByAccountAndTime(accountId, start, end, TaxDetailStatusEnum.SUCCESS.getValue());
        return stkTaxDetails.stream().map(taxDetail -> {
            TaxQueryDTO taxQueryDTO = new TaxQueryDTO();
            taxQueryDTO.setDebitSuccessDate(taxDetail.getLatestApplyDebitTime());
            taxQueryDTO.setStockCode(taxDetail.getStockCode());
            taxQueryDTO.setType(taxDetail.getTaxType());
            taxQueryDTO.setCurrency(taxDetail.getCurrency());
            taxQueryDTO.setAmt(taxDetail.getAmt());
            return taxQueryDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询回收明细
     * @param accountId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<StkActionRecycleDetail> queryActionRecycleDetailList(String accountId, String startDate, String endDate) {
        return stkActionExtendMapper.queryActionRecycleDetailList(accountId, startDate, endDate);
    }
}
