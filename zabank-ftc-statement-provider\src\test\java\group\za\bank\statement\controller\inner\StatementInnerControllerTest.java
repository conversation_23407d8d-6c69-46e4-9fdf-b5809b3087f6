package group.za.bank.statement.controller.inner;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.statement.entity.req.MonthlyStatementConfirmReq;
import group.za.bank.statement.entity.req.UserMonthlyStatementListReq;
import group.za.bank.statement.entity.resp.MonthlyStatementConfirmResp;
import group.za.bank.statement.entity.resp.UserInvestMonthlyStatementListResp;
import group.za.bank.statement.service.MonthlyStatementService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

public class StatementInnerControllerTest {
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    Logger log;
    @InjectMocks
    StatementInnerController statementInnerController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPubConfirm() throws Exception {
        when(monthlyStatementService.pubConfirm(any())).thenReturn(new MonthlyStatementConfirmResp());

        ResponseData<MonthlyStatementConfirmResp> result = statementInnerController.pubConfirm(new MonthlyStatementConfirmReq());
    }

    @Test
    public void testUserInvestMonthlyStatementList() throws Exception {
        when(monthlyStatementService.queryUserStatementDocList(any())).thenReturn(new UserInvestMonthlyStatementListResp());

        ResponseData<UserInvestMonthlyStatementListResp> result = statementInnerController.userInvestMonthlyStatementList(new UserMonthlyStatementListReq());
    }
}

