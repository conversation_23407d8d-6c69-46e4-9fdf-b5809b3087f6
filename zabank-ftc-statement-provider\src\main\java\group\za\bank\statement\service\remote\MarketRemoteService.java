package group.za.bank.statement.service.remote;

import group.za.bank.market.entity.resp.FundInfoResp;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created on 2020/11/20 13:56.
 *
 * <AUTHOR> Description :
 */
public interface MarketRemoteService {

    /**
     * 批量查询多个基金基本信息
     * @param productIdList
     * @return
     */
    Map<String, FundInfoResp> queryFundInfo(List<String> productIdList);


    /**
     * 指定月份的最后一个交易日
     * @param monthDate
     * @return
     */
    Date getMonthLatestTradeDate(Date monthDate);
}
