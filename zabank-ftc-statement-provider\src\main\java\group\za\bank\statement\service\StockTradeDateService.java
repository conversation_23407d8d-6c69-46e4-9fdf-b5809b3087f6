package group.za.bank.statement.service;

import java.util.Date;
import java.util.List;

/**
 * 股票交易日
 */
public interface StockTradeDateService {


    /**
     * 查询core日历（主要是股票跟从ttl日历中的core日历）中的交易日历
     * 即香港交易日历和美股交易日历中交易日的并集
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @return
     */
    List<String> queryCoreCalendarTradeDateList(Date startDate, Date endDate);

    /**
     * 查询core日历（主要是股票跟从ttl日历中的core日历）中的交易日历
     * 即香港交易日历和美股交易日历中交易日的并集
     * @param period 结单月份，yyyyMMdd
     * @return
     */
    List<String> queryCoreCalendarTradeDateList(String period);

    /**
     * 查询指定交易市场交易日历
     * @param period 结单月份，yyyyMMdd
     * @param marketCode 指定市场编码
     * @return
     */
    List<String> queryCalendarTradeDateList(String marketCode,String period);
}
