package group.za.bank.statement.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.jcraft.jsch.ChannelSftp;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.transfer.impl.SFTPFileTransfer;
import group.za.bank.statement.common.config.AliOSSProperties;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.FileManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.InputStream;
import java.util.Date;
import java.util.UUID;
import java.util.Vector;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/20 11:04
 */
@Slf4j
@Service
public class FileManagerServiceImpl implements FileManagerService {

    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private AliOSSProperties aliOSSProperties;
    @Autowired
    private OSS ossClient;

    /**
     * sftp下载文件
     *
     * @param fileName
     * @return
     */
    @Override
    public boolean downloadFile(String filePath, String fileName, String fileDir) {
        File localDir = new File(filePath);
        if (!localDir.exists()) {
            // 创建文件夹
            localDir.mkdirs();
        }

        SFTPFileTransfer sftp = new SFTPFileTransfer(systemConfig.getSftpHost(),
                systemConfig.getSftpPort(), systemConfig.getUsername(), systemConfig.getPassword(), null);
        try {
            boolean connect = sftp.connect();
            if (!connect) {
                throw new BusinessException(CommonErrorMsgEnum.RPC_TIME_OUT);
            }
            Vector<ChannelSftp.LsEntry> fileList = sftp.getFileList(fileDir);
            if (CollectionUtils.isEmpty(fileList)) {
                log.info("ttl sftp 无任何文件！");
                return false;
            }
            if (fileList.stream().noneMatch(file -> file.getFilename().equals(fileName))) {
                log.info("ttl sftp 未找到文件fileName={}！", fileName);
                return false;
            }

            return sftp.download(fileDir, fileName, new File(filePath, fileName).getAbsolutePath());
        } catch (Exception e) {
            log.info("文件下载异常，fileName={}", fileName, e);
            return false;
        } finally {
            sftp.disConnect();
        }
    }

    @Override
    public String uploadAliOSSFile(String fileName, InputStream in) {
        String bucketName = aliOSSProperties.getBucketName();
        String objectKey = getBaseFilePath(fileName);
        try {
            ossClient.putObject(bucketName, objectKey, in);
            return objectKey;
        } catch (Exception e) {
            log.warn("FileManager uploadFile error", e);
            throw e;
        }
    }

    @Override
    public OSSObject downloadAliOSSFile(String objectKey) {
        String bucketName = aliOSSProperties.getBucketName();
        try {
            return ossClient.getObject(bucketName, objectKey);
        } catch (Exception e) {
            log.warn("FileManager downloadFile error", e);
            throw e;
        }

    }

    @Override
    public void downloadAliOSSFileAndSaveLocal(String objectKey, String localPath) {
        String bucketName = aliOSSProperties.getBucketName();
        try {
            File localFile = new File(localPath);
            File parentDir = localFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            ossClient.getObject(new GetObjectRequest(bucketName, objectKey), new File(localPath));
        } catch (Exception e) {
            log.warn("FileManager downloadFile error", e);
            throw e;
        }
    }

    @Override
    public boolean existOssFile(String objectKey) {
        String bucketName = aliOSSProperties.getBucketName();
        try {
            return ossClient.doesObjectExist(bucketName, objectKey);
        } catch (Exception e) {
            log.error("FileManager existOssFile error", e);
            return false;
        }
    }

    @Override
    public Date getOssFileLastModifyTime(String objectKey) {
        try {
            String bucketName = aliOSSProperties.getBucketName();
            // 获取 OSS 文件元数据
            ObjectMetadata objectMetadata = ossClient.getObjectMetadata(bucketName, objectKey);
            if (objectMetadata == null) {
                log.error("Statement file not found in OSS: {}", objectKey);
                throw new BusinessException(StatementErrorMsgEnum.FILE_DOWNLOAD_ERROR);
            }

            // 获取 OSS 文件最后修改时间
            return objectMetadata.getLastModified();

        }catch (Exception e){
            log.error("Statement file getOssFileLastModifyTime error, objectKey:{}", objectKey, e);
            throw new BusinessException(StatementErrorMsgEnum.FILE_DOWNLOAD_ERROR);
        }
    }

    @Override
    public long getOssFileSize(String objectKey) {
        String bucketName = aliOSSProperties.getBucketName();
        try {
            ObjectMetadata objectMetadata = ossClient.getObjectMetadata(bucketName, objectKey);
            return objectMetadata.getContentLength();
        } catch (Exception e) {
            log.error("FileManager getOssFileSize error", e);
            return 0;
        }
    }

    /**
     * 获取文件路径
     *
     * @param fileName
     * @return
     */
    public String getBaseFilePath(String fileName) {

        String datePath = DateUtil.currentDate();

        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        return aliOSSProperties.getPrefix() + "/" + datePath + "/" + uuid + ":" + fileName;
    }

}
