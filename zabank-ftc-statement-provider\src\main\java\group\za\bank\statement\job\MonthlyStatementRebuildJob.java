package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.entity.dto.MonthStatementRebuildDTO;
import group.za.bank.statement.service.MonthlyStatementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @date 2021/8/2 17:07
 * @Version v1.0
 * 月结单重新生成
 */
@Slf4j
@JobHandler(value = "monthlyStatementRebuildJob")
@Component
public class MonthlyStatementRebuildJob extends IJobHandler {

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    MonthlyStatementService monthlyStatementService;


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        StopWatch stopWatch = new StopWatch();
        log.info("monthlyStatementRebuildJob start, 入参:{}", param);
        MonthStatementRebuildDTO monthStatementRebuildDTO = JsonUtils.toJavaObject(param, MonthStatementRebuildDTO.class);
        if (null == monthStatementRebuildDTO
                || StringUtils.isEmpty(monthStatementRebuildDTO.getPeriod()) || StringUtils.isEmpty(monthStatementRebuildDTO.getClientIdList())) {
            log.warn("monthlyStatementRebuildJob入参不能为空");
            return ReturnT.SUCCESS;
        }

        String period = monthStatementRebuildDTO.getPeriod();
        String[] clientIdList = monthStatementRebuildDTO.getClientIdList().split(",");
        boolean notifyUser = monthStatementRebuildDTO.getNotifyUser();
        Date statementDate = parseStatementDate(monthStatementRebuildDTO.getStatementDate());

        try {
            Arrays.stream(clientIdList).forEach(clientId -> {
                monthlyStatementService.rebuildMonthlyStatement(period, clientId, notifyUser, statementDate);
            });
        } catch (Exception e) {
            log.error("monthlyStatementRebuildJob error 入参:{}, period:{}", param, period, e);
        }
        log.info("monthlyStatementInitJob end, 入参:{}, period:{}, cost:{}s", param, period, stopWatch.getTime(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    private Date parseStatementDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return new Date();
        }
        return DateUtil.stringToDate(dateStr, DateUtil.FORMATDAY);
    }

}
