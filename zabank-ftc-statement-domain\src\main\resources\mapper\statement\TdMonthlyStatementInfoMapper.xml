<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo">
        <id column="id" jdbcType="BIGINT"
            property="id"/>
        <result column="creator" jdbcType="VARCHAR"
                property="creator"/>
        <result column="gmt_created" jdbcType="TIMESTAMP"
                property="gmtCreated"/>
        <result column="modifier" jdbcType="VARCHAR"
                property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP"
                property="gmtModified"/>
        <result column="is_deleted" jdbcType="CHAR"
                property="isDeleted"/>
        <result column="business_id" jdbcType="VARCHAR"
                property="businessId"/>
        <result column="period" jdbcType="CHAR"
                property="period"/>
        <result column="business_type" jdbcType="VARCHAR"
                property="businessType"/>
        <result column="init_status" jdbcType="TINYINT"
                property="initStatus"/>
        <result column="pub_confirm_status" jdbcType="TINYINT"
                property="pubConfirmStatus"/>
        <result column="status" jdbcType="TINYINT"
                property="status"/>
        <result column="confirm_operator_id" jdbcType="VARCHAR"
                property="confirmOperatorId"/>
        <result column="confirm_operator_name" jdbcType="VARCHAR"
                property="confirmOperatorName"/>
        <result column="hk_temp_key" jdbcType="VARCHAR"
                property="hkTempKey"/>
        <result column="zh_temp_key" jdbcType="VARCHAR"
                property="zhTempKey"/>
        <result column="nxt_zh_temp_key" jdbcType="VARCHAR"
                property="nxtZhTempKey"/>
        <result column="nxt_hk_temp_key" jdbcType="VARCHAR"
                property="nxtHkTempKey"/>
        <result column="record_time" jdbcType="TIMESTAMP"
                property="recordTime"/>
        <result column="total_number" jdbcType="INTEGER"
                property="totalNumber"/>
        <result column="total_client_number" jdbcType="INTEGER"
                property="totalClientNumber"/>
        <result column="finished_number" jdbcType="INTEGER"
                property="finishedNumber"/>
        <result column="usd_hkd_rate" jdbcType="DECIMAL"
                property="usdHkdRate"/>
        <result column="cny_hkd_rate" jdbcType="DECIMAL"
                property="cnyHkdRate"/>
        <result column="rate_effect_time" jdbcType="TIMESTAMP"
                property="rateEffectTime"/>
        <result column="remark" jdbcType="VARCHAR"
                property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_id, period, business_type, init_status, pub_confirm_status, `status`, confirm_operator_id, confirm_operator_name, hk_temp_key, zh_temp_key, nxt_zh_temp_key, nxt_hk_temp_key, record_time, total_number, total_client_number, finished_number, usd_hkd_rate, cny_hkd_rate, rate_effect_time, remark, creator, gmt_created, modifier, gmt_modified, is_deleted
    </sql>

    <!-- 自定义通用SQL查询条件 -->
    <sql id="Where_Extra_Condition">
    </sql>

    <select id="queryOne" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        td_fund_monthly_statement_info
        WHERE is_deleted = 'N'
        AND period = #{period}
    </select>

</mapper>
