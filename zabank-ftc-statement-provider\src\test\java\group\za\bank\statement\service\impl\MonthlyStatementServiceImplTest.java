package group.za.bank.statement.service.impl;

import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.domain.account.mapper.UserInvestAccountMapper;
import group.za.bank.invest.pub.entity.resp.GeneratePdfResp;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementData;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.domain.repository.TdMonthlyStatementDataRepository;
import group.za.bank.statement.entity.UserMonthlyStatementDataPrepareDto;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.entity.dto.UserMonthlyStatementQueryDto;
import group.za.bank.statement.entity.dto.UserStatementRecordDto;
import group.za.bank.statement.entity.req.AppUserMonthlyStatementListReq;
import group.za.bank.statement.entity.req.MonthlyStatementConfirmReq;
import group.za.bank.statement.entity.req.UserMonthlyStatementListReq;
import group.za.bank.statement.entity.resp.MonthlyStatementConfirmResp;
import group.za.bank.statement.entity.resp.UserInvestMonthlyStatementListResp;
import group.za.bank.statement.entity.resp.UserMonthlyStatementListResp;
import group.za.bank.statement.entity.resp.UserMonthlyStatementResp;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.mq.UserStatementDataPrepareProducer;
import group.za.bank.statement.service.ActivityService;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.StatementBasicInfoService;
import group.za.bank.statement.service.StatementBusinessService;
import group.za.bank.statement.service.remote.PubRemoteService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.bank.statement.utils.AlarmUtil;
import group.za.invest.cache.redis.lock.DistributedLock;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;

import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
public class MonthlyStatementServiceImplTest {
    @Mock
    AlarmUtil alarmUtil;
    @Mock
    UserInvestAccountMapper userInvestAccountMapper;
    @Mock
    MonthlyStatementManager monthlyStatementManager;
    @Mock
    MonthlyStatementService monthlyStatementService;
    @Mock
    StatementBasicInfoService statementBasicInfoService;
    @Mock
    DistributedLock distributedLock;
    @Mock
    RedissonClient redissonClient;
    @Mock
    ThreadPoolTaskExecutor statementExecutor;
    @Mock
    SystemConfig systemConfig;
    @Mock
    PubRemoteService pubRemoteService;
    @Mock
    TdMonthlyStatementMapper tdMonthlyStatementMapper;
    @Mock
    TdMonthlyStatementDataRepository tdMonthlyStatementDataRepository;
    @Mock
    UserRemoteService userRemoteService;
    @Mock
    TdMonthlyStatementInfoMapper monthlyStatementInfoMapper;
    @Mock
    ActivityService activityService;
    @Mock
    List<StatementBusinessService> statementBusinessServiceList;
    @Mock
    UserStatementDataPrepareProducer userStatementDataPrepareProducer;
    @Mock
    Logger log;
    @InjectMocks
    MonthlyStatementServiceImpl monthlyStatementServiceImpl;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");
    }

    @After
    public void close(){
        mockedJsonUtils.close();
    }

    @Test
    public void testGetOrCreateStatementInfo() throws Exception {
        when(monthlyStatementManager.getStatement(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(monthlyStatementManager.createStatement(anyString(), anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(systemConfig.getZhTempKey()).thenReturn("getZhTempKeyResponse");
        when(systemConfig.getHkTempKey()).thenReturn("getHkTempKeyResponse");

        TdFundMonthlyStatementInfo result = monthlyStatementServiceImpl.createStatementInfo("business", "period");
    }

    @Test
    public void testCheckBeforeInit() throws Exception {
        boolean result = monthlyStatementServiceImpl.checkBeforeInit( "period", new TdFundMonthlyStatementInfo());
    }

    //@Test
    public void testInitMonthlyStatements() throws Exception {
        when(monthlyStatementService.createStatementInfo(anyString(), anyString())).thenReturn(new TdFundMonthlyStatementInfo());

        monthlyStatementServiceImpl.initMonthlyStatements("period");
    }

    @Test
    public void testUpdateStatementTotalInfo() throws Exception {
    }

    @Test
    public void testInitAllUserMonthlyStatement() throws Exception {
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");

    }

    //@Test
    public void testInitUserMonthlyStatement() throws Exception {
        when(monthlyStatementManager.listUserStatementRecords(anyString(), anyString())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));
        when(monthlyStatementManager.initUserStatementRecord(any(), any(), anyString())).thenReturn(Arrays.<UserStatementRecordDto>asList(new UserStatementRecordDto()));
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");

        monthlyStatementServiceImpl.initUserMonthlyStatement(new TdFundMonthlyStatementInfo(), null, "businessSubtype");
    }

    @Test
    public void testUserStatementDataPreparePublish() throws Exception {
        monthlyStatementServiceImpl.userStatementDataPreparePublish("period", "business", "bankUserId", "clientId", "businessSubtype");
    }

    //@Test
    public void testUserStatementDataPrepareConsume() throws Exception {
        when(monthlyStatementManager.getStatement(any())).thenReturn(new TdFundMonthlyStatementInfo());
        when(monthlyStatementManager.listUserStatementRecords(anyString(), anyString())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));
        when(monthlyStatementService.prepareUserMonthlyStatementData(any(), any())).thenReturn(true);

        monthlyStatementServiceImpl.userStatementDataPrepareConsume(new UserMonthlyStatementDataPrepareDto());
    }

    //@Test
    public void testPrepareUserMonthlyStatementData() throws Exception {
        when(monthlyStatementManager.listUserStatementDataRecord(anyString())).thenReturn(Arrays.<TdMonthlyStatementData>asList(new TdMonthlyStatementData()));
        when(monthlyStatementService.prepareStatementData(any(), any(), any())).thenReturn(true);

        boolean result = monthlyStatementServiceImpl.prepareUserMonthlyStatementData(new TdFundMonthlyStatementInfo(), null);
    }

    //@Test
    public void testPrepareStatementData() throws Exception {
        when(monthlyStatementManager.getUserStatementBusinessData(anyString(), anyString())).thenReturn(new TdMonthlyStatementBusinessData());
        TdMonthlyStatementData tdMonthlyStatementData = new TdMonthlyStatementData();

        boolean result = monthlyStatementServiceImpl.prepareStatementData(new TdFundMonthlyStatementInfo(), new TdFundMonthlyStatement(), tdMonthlyStatementData);
    }

    //@Test
    public void testInitUserStatementRecord() throws Exception {
        when(monthlyStatementManager.createUserMonthlyStatementRecords(anyString(), any(), any(), true, new Date())).thenReturn(new TdFundMonthlyStatement());
        when(monthlyStatementManager.createUserStatementBusinessData(any(), any(), any())).thenReturn(Arrays.<TdMonthlyStatementData>asList(null));

        List<UserStatementRecordDto> result = monthlyStatementManager.initUserStatementRecord(new TdFundMonthlyStatementInfo(), new HashMap<String, UserInvestClientInfoDto>() {{
            put("String", null);
        }}, "businessSubtype");
    }

    @Test
    public void testCheckBeforeNotifyStatement() throws Exception {
        monthlyStatementServiceImpl.checkBeforeNotifyStatement( "period", new TdFundMonthlyStatementInfo());
    }

    //@Test
    public void testNotifyGeneratePdf() throws Exception {

        when(monthlyStatementManager.getStatement(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(monthlyStatementManager.listUserStatementDataRecord(anyString())).thenReturn(Arrays.<TdMonthlyStatementData>asList(new TdMonthlyStatementData()));
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));
        when(tdMonthlyStatementMapper.queryDocUnNotifyStatementList(anyString(), anyLong(), anyInt())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));

        monthlyStatementServiceImpl.notifyGeneratePdf("period");
    }

    @Test
    public void testNotifyUserGeneratePdf() throws Exception {
        TdFundMonthlyStatement fundMonthlyStatement = new TdFundMonthlyStatement();
        fundMonthlyStatement.setBankUserId("bankUserId");
        fundMonthlyStatement.setBusinessId("businessId");
        when(monthlyStatementManager.getStatementRecord(anyString())).thenReturn(new TdFundMonthlyStatement());
        when(monthlyStatementManager.listUserStatementBusinessData(anyString())).thenReturn(Arrays.<TdMonthlyStatementBusinessData>asList(new TdMonthlyStatementBusinessData()));
        when(statementBasicInfoService.generateDataMap(any(), any(), any())).thenReturn(new HashMap<String, Object>() {{
            put("String", "generateDataMapResponse");
        }});
        when(pubRemoteService.generatePdf(anyString(), anyString(), any())).thenReturn(null);

        monthlyStatementServiceImpl.notifyUserGeneratePdf(new TdFundMonthlyStatementInfo(), fundMonthlyStatement, Arrays.asList(new TdMonthlyStatementData()));
    }

    @Test
    public void testRemoteNotifyGeneratePdf() throws Exception {
        GeneratePdfResp generatePdfResp = new GeneratePdfResp();
        generatePdfResp.setTaskStatus("0");
        when(pubRemoteService.generatePdf(any(), any(), any())).thenReturn(generatePdfResp);

        TdFundMonthlyStatement tdFundMonthlyStatement = new TdFundMonthlyStatement();
        tdFundMonthlyStatement.setDocLang("CN_zh");
        tdFundMonthlyStatement.setBusinessId("businessId");
        tdFundMonthlyStatement.setPeriod("period");
        tdFundMonthlyStatement.setClientId("clientId");

        GeneratePdfResp result = monthlyStatementServiceImpl.remoteNotifyGeneratePdf(new TdFundMonthlyStatementInfo(), tdFundMonthlyStatement, new HashMap<String, Object>() {{
            put("String", "monthlyStatementDataMap");
        }});
    }

    @Test
    public void testCheckBeforeSynStatementStatus() throws Exception {
        monthlyStatementServiceImpl.checkBeforeSynStatementStatus( "period", new TdFundMonthlyStatementInfo());
    }

    @Test
    public void testSynStatementPdfGenerateStatus() throws Exception {
        when(monthlyStatementManager.getStatement(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));
        when(tdMonthlyStatementMapper.queryDocUnGeneratedStatementList(anyString(), anyLong(), anyInt())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));

        monthlyStatementServiceImpl.synStatementPdfGenerateStatus("period");
    }

    @Test
    public void testSynUserStatementPdfGenerateStatus() throws Exception {
        when(pubRemoteService.queryPdf(anyString())).thenReturn(null);

    }

    @Test
    public void testCheckBeforePubStatement() throws Exception {
        when(systemConfig.isCheckConfirmStatusBeforePub()).thenReturn(true);

        monthlyStatementServiceImpl.checkBeforePubStatement( "period", new TdFundMonthlyStatementInfo());
    }

    @Test
    public void testPubMonthlyStatement() throws Exception {
        when(monthlyStatementManager.getStatement(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(systemConfig.isCheckConfirmStatusBeforePub()).thenReturn(true);
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));
        when(tdMonthlyStatementMapper.queryDocUnPublishedStatementList(anyString(), anyLong(), anyInt())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));

        monthlyStatementServiceImpl.pubMonthlyStatement("period");
    }

    @Test
    public void testPubUserMonthlyStatement() throws Exception {
        when(monthlyStatementManager.getStatementRecordByDocLang(anyString(), anyString(), anyString(), anyString())).thenReturn(new TdFundMonthlyStatement());
        when(userRemoteService.queryUserLanguage(anyString())).thenReturn("queryUserLanguageResponse");

    }

    @Test
    public void testSendActivity() throws Exception {
        activityService.monthlyStatementActivityPub(new TdFundMonthlyStatementInfo(), null, new BaseMonthlyStatementDataDto());
    }

    @Test
    public void testAllUsersPubFinish() throws Exception {
    }

    @Test
    public void testPubMonthlyStatement2() throws Exception {
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setInitStatus(Byte.valueOf("2"));
        when(monthlyStatementManager.getStatement(any())).thenReturn(new TdFundMonthlyStatementInfo());

        monthlyStatementServiceImpl.pubMonthlyStatement("202404");
    }

    //@Test
    public void testPubConfirm() throws Exception {
        TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo = new TdFundMonthlyStatementInfo();
        tdFundMonthlyStatementInfo.setInitStatus(Byte.valueOf("2"));
        when(monthlyStatementManager.getStatement(anyString())).thenReturn(tdFundMonthlyStatementInfo);
        when(monthlyStatementManager.isAllStatementDocGenerated(anyString(), anyString())).thenReturn(true);
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("fund,stock");
        when(systemConfig.isCheckAllStatementDocGenerateStatusBeforePubConfirm()).thenReturn(true);
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");

        MonthlyStatementConfirmResp result = monthlyStatementServiceImpl.pubConfirm(new MonthlyStatementConfirmReq());
    }

    //@Test
    public void testGetStatementBusinessService() throws Exception {
        when(systemConfig.getBusinessSubTypeSeparator()).thenReturn(",");
        StatementBusinessService result = monthlyStatementServiceImpl.getStatementBusinessService("fund");
    }

    @Test
    public void testQueryUserInvestAccountList() throws Exception {
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");

        List<UserInvestClientInfoDto> result = monthlyStatementServiceImpl.queryUserInvestAccountList("202404", "fund", 0);
    }

    @Test
    public void testQueryUserInvestAccountMap() throws Exception {
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        when(systemConfig.getBusinessSubTypeSeparator()).thenReturn(",");

        Map<String, UserInvestClientInfoDto> result = monthlyStatementServiceImpl.queryUserInvestAccountMap("202404", "bankUserId", "clientId", "fund,stock");
    }

    //@Test
    public void testChangeUserInvestAccount2Dto() throws Exception {
        monthlyStatementServiceImpl.changeUserInvestAccount2Dto("businessType", null);
    }

    //@Test
    public void testIsBusinessInitFinish() throws Exception {
        monthlyStatementServiceImpl.isBusinessInitFinish(new TdFundMonthlyStatementInfo(), "businessSubtype");
    }

    //@Test
    public void testBusinessInitFinish() throws Exception {
        monthlyStatementServiceImpl.businessInitFinish(new TdFundMonthlyStatementInfo(), "businessSubtype");
    }

    //@Test
    public void testClearStatementBusinessInitCache() throws Exception {
        monthlyStatementServiceImpl.clearStatementBusinessInitCache(new TdFundMonthlyStatementInfo());
    }

    @Test
    public void testMonthlyStatementsProcessStatusCheck() throws Exception {
        monthlyStatementServiceImpl.monthlyStatementsProcessStatusCheck("period");
    }

    @Test
    public void testQueryUserMonthlyStatement() throws Exception {
        when(monthlyStatementManager.getPubConfirmedFundStatementInfo(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("getDefaultMonthlyStatementBusinessTypeResponse");
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        when(systemConfig.getMonthlyStatementDateFormat()).thenReturn("dd MMM yyy");
        when(tdMonthlyStatementMapper.queryUserFundMonthlyStatementList(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));
        UserInvestAccountInfo userInvestAccountInfo = new UserInvestAccountInfo();
        userInvestAccountInfo.setClientId("clientId");
        userInvestAccountInfo.setOpenFinishedTime(new Date());
        when(userRemoteService.queryUserAllInvestAccountInfo(any())).thenReturn(Arrays.asList(userInvestAccountInfo));

        //mock静态方法

        UserMonthlyStatementListResp result = monthlyStatementServiceImpl.queryUserMonthlyStatement(I18nSupportEnum.CN_ZH, new AppUserMonthlyStatementListReq());
    }

    //@Test
    public void testDealWithUserStatementQuery() throws Exception {
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("getDefaultMonthlyStatementBusinessTypeResponse");
        when(systemConfig.getMonthlyStatementDateFormat()).thenReturn("yyyyMM");
        when(tdMonthlyStatementMapper.queryUserFundMonthlyStatementList(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));
        when(userRemoteService.queryUserAllInvestAccountInfo(anyString())).thenReturn(Arrays.<UserInvestAccountInfo>asList(null));

        UserMonthlyStatementQueryDto result = monthlyStatementServiceImpl.dealWithUserStatementQuery(I18nSupportEnum.CN_ZH, new AppUserMonthlyStatementListReq());
    }

    //@Test
    public void testFillStatement() throws Exception {
        when(monthlyStatementManager.getPubConfirmedFundStatementInfo(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        UserMonthlyStatementQueryDto userMonthlyStatementQueryDto = new UserMonthlyStatementQueryDto();
        userMonthlyStatementQueryDto.setStartPeriod("2024-01-01");
        userMonthlyStatementQueryDto.setEndPeriod("2024-02-01");
        UserMonthlyStatementListResp result = monthlyStatementServiceImpl.fillStatement(userMonthlyStatementQueryDto);
    }

    @Test
    public void testGenerateUserFundMonthlyStatementResp() throws Exception {
        when(monthlyStatementManager.getPubConfirmedFundStatementInfo(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");

        UserMonthlyStatementResp result = monthlyStatementServiceImpl.generateUserFundMonthlyStatementResp("userId", "bankAccountId", "202404", null);
    }

    @Test
    public void testIsLatestPeriodNeedShow() throws Exception {
        boolean result = monthlyStatementServiceImpl.isLatestPeriodNeedShow("bankUserId", "accountId", "latestPeriod");
    }

    //@Test
    public void testQueryUserStatementDocList() throws Exception {
        when(monthlyStatementManager.queryUserInvestMonthlyStatementList(anyString(), anyString(), anyString(), anyString())).thenReturn(Arrays.<TdFundMonthlyStatement>asList(new TdFundMonthlyStatement()));
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("getDefaultMonthlyStatementBusinessTypeResponse");
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        when(userRemoteService.queryUserAllInvestAccountInfo(anyString())).thenReturn(Arrays.<UserInvestAccountInfo>asList(null));

        UserInvestMonthlyStatementListResp result = monthlyStatementServiceImpl.queryUserStatementDocList(new UserMonthlyStatementListReq());
    }

    @Test
    public void testRemoveBankUserIdPrefix() throws Exception {
        String result = monthlyStatementServiceImpl.removeBankUserIdPrefix("bankUserId");
    }
}

