package group.za.bank.statement.manager;

import group.za.bank.ccs.core.support.share.entity.dto.DayKlineSupDTO;
import group.za.bank.ccs.tradecore.mapper.*;
import group.za.bank.ccs.tradecore.model.entity.*;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.common.cache.CacheLoad;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.entity.dto.CryptoHoldInfoDto;
import group.za.bank.statement.service.remote.CryptoQuotaRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.invest.web.json.JSON;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CryptoTradeCoreManagerTest {
    @Mock
    CcOrderExtendMapper orderExtendMapper;
    @Mock
    CcBusinessRecordExtendMapper businessRecordExtendMapper;
    @Mock
    CustCcAssetMonthlyBackupMapper custCcAssetMonthlyBackupMapper;
    @Mock
    CustCcAssetChangeDetailMapper custCcAssetChangeDetailMapper;
    @Mock
    CustCcCryptoInfoMapper custCcCryptoInfoMapper;
    @Mock
    SystemConfig systemConfig;
    @Mock
    CryptoQuotaRemoteService cryptoQuotaRemoteService;
    @Mock
    Logger log;
    @InjectMocks
    CryptoTradeCoreManager cryptoTradeCoreManager;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<String> bucket;

    @Mock
    private CacheLoad<List<CcCryptoInfo>> cacheLoad;

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJSON;
    private MockedStatic<DateUtil> mockedDateUtil;

    private MockedStatic<MonthlyUtils> mockedMonthlyUtils;

    private MockedStatic<AbstractCacheTemplate> mockedStatic;


    @Before
    public void setUp() {
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        // Mock JSON
        mockedJSON = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        // Mock DateUtil
        mockedDateUtil = mockStatic(DateUtil.class);
        when(DateUtil.parse(anyString(), anyString())).thenReturn(new Date());
        when(DateUtil.format(any(), any())).thenReturn("2024-04-01");
        when(DateUtil.getMonthsList(any(), any())).thenReturn(Arrays.asList("202404"));


        mockedMonthlyUtils = mockStatic(MonthlyUtils.class);

//        mockedStatic = mockStatic(AbstractCacheTemplate.class);
//        CcCryptoInfo ccCryptoInfo = new CcCryptoInfo();
//        ccCryptoInfo.setAssetType("BTC");
//        when(mockedStatic.queryFromCache(any(), any(), any(), any(), any())).thenReturn(Arrays.asList(ccCryptoInfo));

    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        if (mockedJSON != null) {
            mockedJSON.close();
        }
        if (mockedDateUtil != null) {
            mockedDateUtil.close();
        }
        if(mockedMonthlyUtils != null){
            mockedMonthlyUtils.close();
        }
    }


    @Test
    public void testCryptoOrderList() throws Exception {
        List<CcOrder> result = cryptoTradeCoreManager.cryptoOrderList("userId", "startDate", "endDate");
    }

    @Test
    public void testCryptoBusinessRecordList() throws Exception {
        List<CcBusinessRecord> result = cryptoTradeCoreManager.cryptoBusinessRecordList("orderNo");
    }

    //@Test
    public void testCryptoHoldList() throws Exception {

        CcCryptoInfo ccCryptoInfo = new CcCryptoInfo();
        ccCryptoInfo.setAssetType("BTC");




        //when(redissonClient.getBucket(key)).thenReturn(bucket);
        //when(cacheLoad.load()).thenReturn(Arrays.asList(ccCryptoInfo));

        //when(cacheTemplate.queryFromCache(any(), any(), any(), any(), any())).thenReturn(Arrays.asList(ccCryptoInfo));

        when(custCcCryptoInfoMapper.getAllAssetType()).thenReturn(Arrays.asList(ccCryptoInfo));
        CcAssetMonthlyBackup backup = new CcAssetMonthlyBackup();
        backup.setAssetType("BTC");
        when(custCcAssetMonthlyBackupMapper.selectList(any(), any())).thenReturn(Arrays.asList(backup));
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        when(cryptoQuotaRemoteService.queryDayKline(anyString(), anyString())).thenReturn(new DayKlineSupDTO());

        List<CryptoHoldInfoDto> result = cryptoTradeCoreManager.cryptoHoldList("202401", "accountId");
    }

    //@Test
    public void testGetCryptoName() throws Exception {
        String result = cryptoTradeCoreManager.getCryptoName("assetType", I18nSupportEnum.CN_ZH);
    }

    //@Test
    public void testGetCcCryptoInfo() throws Exception {
        CcCryptoInfo result = cryptoTradeCoreManager.getCcCryptoInfo("assetType");
    }

    //@Test
    public void testGetAllCryptoInfo() throws Exception {
        List<CcCryptoInfo> result = cryptoTradeCoreManager.getAllCryptoInfo();
    }

    @Test
    public void testQueryCryptoAssetChangeDetail() throws Exception {
        List<CcAssetChangeDetail> result = cryptoTradeCoreManager.queryCryptoAssetChangeDetail("202401", "accountId");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme