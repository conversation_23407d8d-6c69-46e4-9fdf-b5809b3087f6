package group.za.bank.statement.manager;

import com.google.common.collect.Maps;
import group.za.bank.invest.basecommon.constants.enums.CommonErrorMsgEnum;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.bankfront.mapper.CounterFileInfoExtendMapper;
import group.za.bank.sbs.bankfront.mapper.CrmBatchAssetDetailExtendMapper;
import group.za.bank.sbs.bankfront.model.dto.StatementBusinessCommonDTO;
import group.za.bank.sbs.bankfront.model.entity.CounterFileInfo;
import group.za.bank.sbs.counterfront.common.enums.MarketCodeEnum;
import group.za.bank.sbs.counterfront.common.enums.TtlLinkupTypeEnum;
import group.za.bank.sbs.counterfront.common.enums.TtlTxnTypeIdEnum;
import group.za.bank.sbs.counterfront.module.entity.order.resp.QueryOrderIdByContractIdListResp;
import group.za.bank.sbs.counterfront.module.entity.order.resp.QueryOrderIdByContractIdResp;
import group.za.bank.sbs.ipo.mapper.IpoApplyInfoMapper;
import group.za.bank.sbs.ipo.model.entity.ext.QueryApplyAllottedInfoResult;
import group.za.bank.sbs.ipo.model.entity.ext.QueryApplyDebitInfoResult;
import group.za.bank.sbs.trade.common.constant.enums.ActionTypeEnum;
import group.za.bank.sbs.trade.mapper.*;
import group.za.bank.sbs.trade.model.dto.*;
import group.za.bank.sbs.trade.model.entity.*;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.constants.enums.StatementBusinessTypeEnum;
import group.za.bank.statement.entity.convert.EntityConvert;
import group.za.bank.statement.entity.dto.ActionCapitalInfoDTO;
import group.za.bank.statement.entity.dto.StatementTradeChangeDetailDto;
import group.za.bank.statement.service.remote.CounterFrontRemoteService;
import group.za.bank.statement.service.remote.TradeRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 股票交易
 *
 * <AUTHOR>
 * @date 2023/09/25
 **/
@Service
@Slf4j
public class StockTradeManager {

    @Autowired
    private StkBusinessRecordExtendMapper stkBusinessRecordExtendMapper;
    @Autowired
    private CounterFileInfoExtendMapper counterFileInfoExtendMapper;
    @Autowired
    private TradeRemoteService tradeRemoteService;
    @Autowired
    private EntityConvert entityConvert;
    @Autowired
    private StkContractNoteExtendMapper stkContractNoteExtendMapper;
    @Autowired
    private StkHoldingHistoryExtendMapper stkHoldingHistoryExtendMapper;
    @Autowired
    private StkOrderExtendMapper stkOrderExtendMapper;
    @Autowired
    private StkActionActivityExtendMapper stkActionActivityExtendMapper;
    @Autowired
    private StkActionDetailExtendMapper stkActionDetailExtendMapper;
    @Autowired
    private TaskResultExtendMapper taskResultExtendMapper;

    @Autowired
    private CounterFrontRemoteService counterFrontRemoteService;

    @Autowired
    private CrmBatchAssetDetailExtendMapper batchAssetDetailExtendMapper;

    @Autowired
    private IpoApplyInfoMapper ipoApplyInfoMapper;

    @Autowired
    private StkActionExtendMapper stkActionExtendMapper;

    /**
     * 根据BO订单号查询成交明细
     *
     * @param referenceNo
     * @return
     */
    public List<StkBusinessRecordDTO> queryStkBusinessRecord(String referenceNo) {
        //根据BO订单号，在资金库查询订单号的关联关系
        List<CounterFileInfo> counterFileInfos = counterFileInfoExtendMapper.queryByReleaseReferenceAndLinkupTypeAndTxnTypeId(referenceNo,
                Arrays.asList(TtlLinkupTypeEnum.CONTRACT.getValue()),
                Arrays.asList(TtlTxnTypeIdEnum.BUY_CONTRACT.getValue(), TtlTxnTypeIdEnum.SELL_CONTRACT.getValue()));
        if (!CollectionUtils.isEmpty(counterFileInfos)) {
            //获取本地订单号
            CounterFileInfo counterFileInfo = counterFileInfos.get(0);
            String localOrderNo = counterFileInfo.getLocalOrderNo();
            log.info("从本地cash库查询成交明细完成，referenceNo：{},localOrderNo:{}", referenceNo, localOrderNo);

            if (!StringUtils.isEmpty(localOrderNo)) {
                //查询成交明细
                List<StkBusinessRecord> stkBusinessRecords = stkBusinessRecordExtendMapper.queryBusinessRecordByOrderNo(localOrderNo);

                //合并相同成交价格的成交明细数据并转换对象
                return this.mergeBusinessRecord(stkBusinessRecords);
            }
        }
        //如果在cash库没有查到本地订单号，则沿用原方案，远程调用接口查询
        log.info("cash库没有查到本地订单号,沿用原方案,referenceNo:{}", referenceNo);
        return tradeRemoteService.queryStatementBusinessRecordList(referenceNo);
    }

    /**
     * 合并相同成交价格的成交明细数据
     *
     * @param stkBusinessRecords
     * @return
     */
    private List<StkBusinessRecordDTO> mergeBusinessRecord(List<StkBusinessRecord> stkBusinessRecords) {
        //使用有序的map集合
        Map<String, StkBusinessRecordDTO> dataMap = Maps.newTreeMap();

        for (StkBusinessRecord stkBusinessRecord : stkBusinessRecords) {
            //把key格式化成统一格式字符串
            String priceKey = formatBusinessPrice(stkBusinessRecord.getBusinessPrice());

            StkBusinessRecordDTO stkBusinessRecordDTO = dataMap.getOrDefault(priceKey, new StkBusinessRecordDTO());

            stkBusinessRecordDTO.setOrderNo(stkBusinessRecord.getOrderNo());

            BigDecimal sourceQty = null == stkBusinessRecordDTO.getBusinessQty()
                    ? BigDecimal.ZERO : stkBusinessRecordDTO.getBusinessQty();
            stkBusinessRecordDTO.setBusinessQty(sourceQty.add(stkBusinessRecord.getBusinessQty()));

            stkBusinessRecordDTO.setBusinessPrice(stkBusinessRecord.getBusinessPrice());

            BigDecimal sourceAmt = null == stkBusinessRecordDTO.getBusinessAmount()
                    ? BigDecimal.ZERO : stkBusinessRecordDTO.getBusinessAmount();
            stkBusinessRecordDTO.setBusinessAmount(sourceAmt.add(stkBusinessRecord.getBusinessAmount()));

            stkBusinessRecordDTO.setBusinessTime(stkBusinessRecord.getBusinessTime());

            dataMap.put(priceKey, stkBusinessRecordDTO);
        }
        return new ArrayList<>(dataMap.values());
    }


    /**
     * Formats the given business price by converting it into a string representation with six decimal places.
     *
     * @param businessPrice the business price to be formatted
     * @return the formatted business price as a string with six decimal places
     */
    private String formatBusinessPrice(BigDecimal businessPrice) {
        DecimalFormat df = new DecimalFormat("#.000000");
        return df.format(businessPrice);
    }

    /**
     * 查询成交合同信息
     *
     * @param orderNo
     * @param userId
     * @param docLang
     * @param orderType
     * @param activityType
     * @return
     */
    public StkContractNote queryStkContractNoteInfo(String orderNo, String userId, String docLang, String orderType, String activityType) {
        return stkContractNoteExtendMapper.queryContractNoteInfo(orderNo, userId, docLang, orderType, activityType);
    }

    /**
     * 查询持仓历史数据
     *
     * @param accountId
     * @param tradeDate
     * @return
     */
    public StkHoldingHistory queryHoldingHistoryByTradeDate(String accountId, Date tradeDate, String stockCode) {
        return stkHoldingHistoryExtendMapper.queryHoldingHistoryByTradeDateAndStockCode(accountId, tradeDate, stockCode);
    }

    /**
     * 查询持仓历史数据
     *
     * @param accountId
     * @param tradeDate
     * @return
     */
    public StkHoldingHistory queryHoldingHistoryByStatementCheck(String accountId, Date tradeDate, String stockCode) {
        return stkHoldingHistoryExtendMapper.queryHoldingHistoryByStatementCheck(accountId, tradeDate, stockCode);
    }

    public List<StkOrder> queryOrderListByDate(Date tradeDate) {
        return stkOrderExtendMapper.queryOrderListByDate(tradeDate);
    }

    public List<CompanyActionCapitalInfoDTO> queryActionCapitalSuccessList(Date tradeDate) {
        return stkActionActivityExtendMapper.queryCapitalSuccessList(tradeDate);
    }

    public List<CompanyActionDetailInfoDTO> queryActionBookCloseData(Date tradeDate) {
        return stkActionDetailExtendMapper.queryActionBookCloseData(tradeDate);
    }

    public List<TaskResult> queryTaskResultList(Date tradeDate) {
        return taskResultExtendMapper.queryTaskResultList(tradeDate);
    }

    /**
     * 根据BO订单号查询订单信息
     *
     * @param boNo
     * @return
     */
    public StkOrder queryOrderInByBoNo(String boNo) {
        QueryOrderIdByContractIdListResp contractIdListResp = counterFrontRemoteService.queryOrderIdByContractId(boNo);
        List<QueryOrderIdByContractIdResp> orderIdList = contractIdListResp.getOrderIdList();
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        QueryOrderIdByContractIdResp queryOrderIdByContractIdResp = orderIdList.get(0);

        return stkOrderExtendMapper.queryOrderByExternalOrderNo(queryOrderIdByContractIdResp.getOrderId());
    }

    /**
     * 查询公司行动exercise数据
     *
     * @param tradeDate
     * @return
     */
    public List<CompanyActionExerciseDetailDTO> queryActionExerciseData(Date tradeDate) {
        return stkActionDetailExtendMapper.queryActionExerciseData(tradeDate);
    }

    /**
     * 查询订单数据
     *
     * @param tradeDate
     * @return
     */
    public List<OrderInfoStatementDTO> queryOrderListByStatement(Date tradeDate) {
        return stkOrderExtendMapper.queryOrderListByStatement(tradeDate);
    }

    /**
     * 查询资金转账数据
     *
     * @param accountId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<StatementBusinessCommonDTO> queryCapitalListByStatement(String accountId, String startDate, String endDate) {
        return batchAssetDetailExtendMapper.queryCapitalListByStatement(accountId, startDate, endDate);
    }

    /**
     * 查询股份变动数据
     *
     * @param accountId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<StatementBusinessCommonDTO> queryStockListByStatement(String accountId, String startDate, String endDate) {
        return batchAssetDetailExtendMapper.queryStockListByStatement(accountId, startDate, endDate);
    }

    /**
     * 查询IPO中签数据
     *
     * @param accountId
     * @param period
     * @return
     */
    public List<StatementTradeChangeDetailDto> queryIpoAllottedInfo(String accountId, String period) {
        List<StatementTradeChangeDetailDto> resultList = new ArrayList<>();

        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;
        List<QueryApplyAllottedInfoResult> allottedInfoResults = ipoApplyInfoMapper.queryApplyAllottedInfo(accountId, DateUtil.parse(startDate, DateUtil.FORMATDAY_SS),
                DateUtil.parse(endDate, DateUtil.FORMATDAY_SS));
        for (QueryApplyAllottedInfoResult allottedInfoResult : allottedInfoResults) {
            StatementTradeChangeDetailDto statementTradeChangeDetailDto = new StatementTradeChangeDetailDto();
            statementTradeChangeDetailDto.setBusinessType(StatementBusinessTypeEnum.STOCK_IPO_ALLOTMENT.getBusinessType());
            statementTradeChangeDetailDto.setTradeDate(DateUtil.format(allottedInfoResult.getAllottedTime(), DateUtil.FORMAT_SHORT));
            statementTradeChangeDetailDto.setStockCode(allottedInfoResult.getStockCode());
            statementTradeChangeDetailDto.setSourceStockCode(allottedInfoResult.getStockCode());
            statementTradeChangeDetailDto.setQty(allottedInfoResult.getAllottedQuantity().toPlainString());
            //默认港股
            statementTradeChangeDetailDto.setTtlMarketCode(MarketCodeEnum.HK.getTtlValue());
            statementTradeChangeDetailDto.setBusinessId(allottedInfoResult.getApplyNo());
            statementTradeChangeDetailDto.setStockNameCn(allottedInfoResult.getStockZhName());
            statementTradeChangeDetailDto.setStockNameHk(allottedInfoResult.getStockName());
            statementTradeChangeDetailDto.setStockNameEn(allottedInfoResult.getStockEnName());

            resultList.add(statementTradeChangeDetailDto);
        }

        return resultList;
    }

    public List<StatementTradeChangeDetailDto> queryIpoApplyInfo(String accountId, String period){
        List<StatementTradeChangeDetailDto> resultList = new ArrayList<>();

        String startDate = MonthlyUtils.getMonthFirstDay(period);
        String endDate = MonthlyUtils.getMonthLastDay(period) + StatementConstants.DAY_END_TIME;
        //申请信息
        List<QueryApplyDebitInfoResult> queryApplyDebitInfoResults = ipoApplyInfoMapper.queryApplyDebitInfo(accountId, DateUtil.parse(startDate, DateUtil.FORMATDAY_SS), DateUtil.parse(endDate, DateUtil.FORMATDAY_SS));
        for (QueryApplyDebitInfoResult queryApplyDebitInfoResult : queryApplyDebitInfoResults) {
            if(queryApplyDebitInfoResult.getApplyAmount().compareTo(BigDecimal.ZERO) > 0){
                StatementTradeChangeDetailDto statementTradeChangeDetailDto = new StatementTradeChangeDetailDto();
                statementTradeChangeDetailDto.setBusinessType(StatementBusinessTypeEnum.STOCK_IPO.getBusinessType());
                statementTradeChangeDetailDto.setTradeDate(DateUtil.format(queryApplyDebitInfoResult.getDebitTime(), DateUtil.FORMAT_SHORT));
                statementTradeChangeDetailDto.setStockCode(queryApplyDebitInfoResult.getStockCode());
                statementTradeChangeDetailDto.setSourceStockCode(queryApplyDebitInfoResult.getStockCode());
                statementTradeChangeDetailDto.setAmount(queryApplyDebitInfoResult.getApplyAmount().toPlainString());
                //默认港股
                statementTradeChangeDetailDto.setTtlMarketCode(MarketCodeEnum.HK.getTtlValue());
                statementTradeChangeDetailDto.setBusinessId(queryApplyDebitInfoResult.getApplyNo());
                statementTradeChangeDetailDto.setStockNameCn(queryApplyDebitInfoResult.getStockZhName());
                statementTradeChangeDetailDto.setStockNameHk(queryApplyDebitInfoResult.getStockName());
                statementTradeChangeDetailDto.setStockNameEn(queryApplyDebitInfoResult.getStockEnName());

                resultList.add(statementTradeChangeDetailDto);
            }
            if(queryApplyDebitInfoResult.getZaHandlingFee().compareTo(BigDecimal.ZERO) > 0){
                StatementTradeChangeDetailDto statementTradeChangeDetailDto = new StatementTradeChangeDetailDto();
                statementTradeChangeDetailDto.setBusinessType(StatementBusinessTypeEnum.STOCK_IPO_FEE.getBusinessType());
                statementTradeChangeDetailDto.setTradeDate(DateUtil.format(queryApplyDebitInfoResult.getDebitTime(), DateUtil.FORMAT_SHORT));
                statementTradeChangeDetailDto.setStockCode(queryApplyDebitInfoResult.getStockCode());
                statementTradeChangeDetailDto.setSourceStockCode(queryApplyDebitInfoResult.getStockCode());
                statementTradeChangeDetailDto.setAmount(queryApplyDebitInfoResult.getZaHandlingFee().toPlainString());
                //默认港股
                statementTradeChangeDetailDto.setTtlMarketCode(MarketCodeEnum.HK.getTtlValue());
                statementTradeChangeDetailDto.setBusinessId(queryApplyDebitInfoResult.getApplyNo());
                statementTradeChangeDetailDto.setStockNameCn(queryApplyDebitInfoResult.getStockZhName());
                statementTradeChangeDetailDto.setStockNameHk(queryApplyDebitInfoResult.getStockName());
                statementTradeChangeDetailDto.setStockNameEn(queryApplyDebitInfoResult.getStockEnName());

                resultList.add(statementTradeChangeDetailDto);
            }
        }

        //分配退款信息
        List<QueryApplyAllottedInfoResult> allottedInfoResults = ipoApplyInfoMapper.queryApplyAllottedInfo(accountId, DateUtil.parse(startDate, DateUtil.FORMATDAY_SS),
                DateUtil.parse(endDate, DateUtil.FORMATDAY_SS));
        for (QueryApplyAllottedInfoResult allottedInfoResult : allottedInfoResults) {
            if(allottedInfoResult.getRefundAmount().compareTo(BigDecimal.ZERO) > 0){
                StatementTradeChangeDetailDto statementTradeChangeDetailDto = new StatementTradeChangeDetailDto();
                statementTradeChangeDetailDto.setBusinessType(StatementBusinessTypeEnum.STOCK_IPO_REFUND.getBusinessType());
                statementTradeChangeDetailDto.setTradeDate(DateUtil.format(allottedInfoResult.getAllottedTime(), DateUtil.FORMAT_SHORT));
                statementTradeChangeDetailDto.setStockCode(allottedInfoResult.getStockCode());
                statementTradeChangeDetailDto.setSourceStockCode(allottedInfoResult.getStockCode());
                statementTradeChangeDetailDto.setAmount(allottedInfoResult.getRefundAmount().toPlainString());
                //默认港股
                statementTradeChangeDetailDto.setTtlMarketCode(MarketCodeEnum.HK.getTtlValue());
                statementTradeChangeDetailDto.setBusinessId(allottedInfoResult.getApplyNo());
                statementTradeChangeDetailDto.setStockNameCn(allottedInfoResult.getStockZhName());
                statementTradeChangeDetailDto.setStockNameHk(allottedInfoResult.getStockName());
                statementTradeChangeDetailDto.setStockNameEn(allottedInfoResult.getStockEnName());

                resultList.add(statementTradeChangeDetailDto);
            }
        }

        return resultList;
    }

    /**
     * 查询公司行动资金入账信息
     *
     * @param accountId
     * @param referenceNoSet
     * @return
     */
    public List<ActionCapitalInfoDTO> queryActionCapitalInfo(String accountId, Set<String> referenceNoSet) {
        List<ActionCapitalInfoDTO> detailList = new ArrayList<>();
        for (String referenceNo : referenceNoSet) {
            StkActionDetail stkActionDetail = queryActionDetailByReferenceNo(accountId, referenceNo);
            if (null == stkActionDetail) {
                log.error("月结单-查询公司行动执行详情数据异常，accountId;{},referenceNo:{}", accountId, referenceNo);
                continue;
            }
            ActionCapitalInfoDTO actionCapitalInfoDTO = new ActionCapitalInfoDTO();
            actionCapitalInfoDTO.setReferenceNo(referenceNo);
            StkActionActivity stkActionActivity = stkActionExtendMapper.queryByRightsId(stkActionDetail.getRightsId());
            if (null == stkActionActivity) {
                log.warn("月结单-未查询到资金入账信息，accountId;{},referenceNo:{}", accountId, referenceNo);
                actionCapitalInfoDTO.setCapitalTime(null);
            } else {
                actionCapitalInfoDTO.setCapitalTime(stkActionActivity.getCapitalTime());
            }
            detailList.add(actionCapitalInfoDTO);
        }
        return detailList;
    }

    /**
     * 根据referenceNo查询公司行动详情
     *
     * @param accountId
     * @param referenceNo
     * @return
     */
    private StkActionDetail queryActionDetailByReferenceNo(String accountId, String referenceNo) {
        List<StkActionDetail> stkActionDetails = stkActionExtendMapper.queryByAccountAndActionId(accountId, referenceNo);
        if (!CollectionUtils.isEmpty(stkActionDetails)) {
            if (stkActionDetails.size() == 1) {
                return stkActionDetails.get(0);
            } else {
                //如果查出多条数据的情况，则是选股选息的数据，过滤出选息的数据
                Optional<StkActionDetail> optionalStkActionDetail = stkActionDetails.stream()
                        .filter(stkActionDetail -> stkActionDetail.getActivityType().equals(ActionTypeEnum.SELECTION_INTEREST.getValue()))
                        .findFirst();
                if (optionalStkActionDetail.isPresent()) {
                    return optionalStkActionDetail.get();
                }
            }
        }
        return stkActionExtendMapper.queryBySubscriptionId(accountId, referenceNo);
    }

    /**
     * 根据referenceNo查询公司行动详情
     *
     * @param accountId
     * @return
     */
    public StkActionPlan queryActionPlanBySubscriptionId(String accountId, String referenceNo){
        StkActionDetail stkActionDetail = stkActionExtendMapper.queryBySubscriptionId(accountId, referenceNo);
        if (null == stkActionDetail){
            throw new BusinessException(CommonErrorMsgEnum.PARAM_ERROR.getCode(), "queryActionPlanBySubscriptionId沒有查詢到公司行動詳情數據");
        }
        return stkActionExtendMapper.queryActionPlan(stkActionDetail.getActionId());
    }
}
