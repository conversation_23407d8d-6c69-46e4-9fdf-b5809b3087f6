package group.za.bank.statement.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 09 15:10
 * @description
 */
@Data
public class NativeBusinessDto {


    /**
     * 业务类型标题
     * 持仓
     * 已确认交易
     * 处理中交易
     * 派息
     * 持仓变动
     */
    private String dataTypeTitle;


    /**
     * 数据类型
     */
    private String dataType;



    /**
     * 每个数据类型下有多个模块，按业务类型区分
     */
    @JsonSerialize
    private List<NativeModuleDto> nativeModuleList;

}
