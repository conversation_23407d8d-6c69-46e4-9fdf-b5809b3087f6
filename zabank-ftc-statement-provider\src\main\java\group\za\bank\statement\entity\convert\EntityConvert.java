package group.za.bank.statement.entity.convert;

import group.za.bank.sbs.trade.model.dto.StkBusinessRecordDTO;
import group.za.bank.sbs.trade.model.entity.StkBusinessRecord;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @date 2023/09/25
 **/
@Mapper(componentModel = "spring")
public interface EntityConvert {

    /**
     * 成交明细对象转换
     * @param stkBusinessRecords
     * @return
     */
    List<StkBusinessRecordDTO> StkBusinessRecordDTOConvert(List<StkBusinessRecord> stkBusinessRecords);
}
