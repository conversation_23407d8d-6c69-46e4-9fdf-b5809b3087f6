package group.za.bank.statement.service;

import group.za.bank.statement.constants.enums.StatementTypeEnum;
import group.za.bank.statement.domain.entity.TdStatementFileRecord;
import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.bank.statement.entity.dto.*;
import group.za.invest.mybatis.repository.service.CrudService;

import java.util.Date;
import java.util.List;

/**
* This service interface access the database table td_stock_statement_data
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 11:50:13
*/
public interface TdStockStatementDataService extends CrudService<TdStockStatementData> {

    /**
     * 解析结单数据
     * @param statementDate
     * @param filePathDate
     */
    void parseStatementMonthlyData(StatementTypeEnum statementTypeEnum,String statementDate, String filePathDate, Boolean refresh);


    /**
     * 根据formatType查询指定范围数据
     * @param startTradeDate yyyyMMdd
     * @param endTradeDate yyyyMMdd
     * @param statementType
     * @param accNo
     * @param formatType
     * @return
     */
    List<TdStockStatementData> queryPeriodDataByFormatType(String ttlMarketCode, String startTradeDate,String endTradeDate, Integer statementType, String accNo, Integer formatType);




    /**
     * 查询客户的美股成交数据
     * @param statementType
     * @param accNo
     * @return
     */
    List<StatementUsOrderDto> queryUsOrderData(StockStatementTradeDateInfo stockStatementTradeDateInfo, Integer statementType, String accNo, String ttlMarketCode);

    /**
     * 查询客户的证券变动数据
     *
     * @param period
     * @param statementType
     * @param accNo
     * @return
     */
    List<StatementTradeChangeDetailDto> queryTradeChangeDetailData(String period, StockStatementTradeDateInfo stockStatementTradeDateInfo, Integer statementType, String accNo, List<String> businessTypeList, String ttlMarketCode);

    /**
     * 构建tradeChangeDetailDto
     * @param tradeChangeDetailDto
     * @param stockStatementData
     */
    void buildTradeChangeDetailDto(StatementTradeChangeDetailDto tradeChangeDetailDto, TdStockStatementData stockStatementData);

    /**
     * 查询客户证券资产数据
     * @param statementType
     * @param accNo
     * @param ttlMarketCode
     * @return
     */
    List<StatementAccountAssetDto> queryAccountAssetData(String statementDate, Integer statementType, String accNo, String ttlMarketCode);

    /**
     * 查询客户现金摘要数据
     * @param statementType
     * @param accNo
     * @return
     */
    List<StatementCashSummaryDto> queryCashSummaryData(StockStatementTradeDateInfo stockStatementTradeDateInfo, Integer statementType, String accNo);

    /**
     * 日结单文件数据检查
     * @param statementDate
     */
    void dailyStatementDataCheck(String statementDate);

    /**
     * 自动构建结单文件数据
     * @param tradeDate
     * @param statementDate
     * @param statementFileRecord
     */
    void localGenerationStatementFileData(Date tradeDate, String statementDate, TdStatementFileRecord statementFileRecord);

    /**
     * 构建结单税费数据
     * @param tradeDate
     */
    void dailyLocalGenerationStatementIncomeCharges(Date tradeDate);
}
