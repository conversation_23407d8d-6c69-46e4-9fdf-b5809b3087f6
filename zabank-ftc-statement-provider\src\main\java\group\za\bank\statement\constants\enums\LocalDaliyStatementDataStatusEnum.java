package group.za.bank.statement.constants.enums;


import group.za.bank.invest.basecommon.constants.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Date  2021/7/17 14:18
 * @Description 结单数据落库状态
 * @Version v1.0
 */
@Getter
@AllArgsConstructor
public enum LocalDaliyStatementDataStatusEnum implements BaseEnum {
    INIT("0", "初始化"),
    PROCESSING("1", "生成中"),
    FINISHED("2",  "生成成功"),
    UN_NOTIFIED("3", "等待通知文件生成")
    ;

    private String value;

    private String msg;



}
