package group.za.bank.statement.utils;

import lombok.NonNull;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 29 10:41
 * @description 结单数据排序工具
 */
public class StatementDataSortUtils {


    /**
     * 对list列表中的元素抽取对应的字符串，并按字符串升序排列
     *
     * @param dtoStringExtractFunc 从dto中提取要参与排序的字符串
     * @param originalList
     * @param <T>
     * @return
     */
    public static <T> List<T> sortByStringAsc(@NonNull Function<T, String> dtoStringExtractFunc, @NonNull List<T> originalList) {
        return originalList.stream()
                .sorted((preDto, currentDto) -> {
                    String preString = dtoStringExtractFunc.apply(preDto);
                    String currentString = dtoStringExtractFunc.apply(currentDto);
                    return preString.compareTo(currentString);
                })
                .collect(Collectors.toList());
    }


    /**
     * 各种list按照日期升序排
     * @param originalList
     * @param dateExtractFunction
     * @param <T>
     * @return
     */
    public static <T> List<T> sortByDateAsc(@NonNull Function<T, Date> dateExtractFunction, @NonNull List<T>originalList) {
        List<T> finalList = originalList.stream().sorted((o1, o2) -> {
            Date date1 = dateExtractFunction.apply(o1);
            Date date2 = dateExtractFunction.apply(o2);

            // Handle nulls consistently: nulls first
            if (date1 == null && date2 == null) {
                return 0; // Both are null, consider them equal
            }
            if (date1 == null) {
                return -1; // date1 is null, date2 is not, so date1 comes first
            }
            if (date2 == null) {
                return 1;  // date2 is null, date1 is not, so date2 comes first (meaning date1 is greater)
            }

            // Both dates are non-null, use compareTo for natural ordering
            return date1.compareTo(date2);
        }).collect(Collectors.toList());
        return finalList;
    }

    /**
     * 对list列表中的元素按多个字段进行排序
     *
     * @param originalList      原始列表
     * @param sortFunctions     排序函数列表，按排序优先级排列
     * @param <T>               列表元素类型
     * @return                  排序后的列表
     */
    @SafeVarargs
    public static <T> List<T> sortByMultipleFields(
            @NonNull List<T> originalList,
            @NonNull Function<T, ? extends Comparable>... sortFunctions) {
        Comparator<T> comparator = Comparator.comparing(sortFunctions[0]);
        for (int i = 1; i < sortFunctions.length; i++) {
            comparator = comparator.thenComparing(sortFunctions[i]);
        }
        return originalList.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
    }
}
