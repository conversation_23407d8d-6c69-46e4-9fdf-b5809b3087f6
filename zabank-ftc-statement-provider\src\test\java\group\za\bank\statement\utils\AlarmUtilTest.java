package group.za.bank.statement.utils;

import group.za.bank.invest.pub.feign.AlarmMessageService;
import group.za.bank.statement.common.config.SystemConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Arrays;

import static org.mockito.Mockito.when;

public class AlarmUtilTest {
    @Mock
    SystemConfig systemConfig;
    @Mock
    AlarmMessageService alarmMessageService;
    @Mock
    ThreadPoolTaskExecutor commonExecutor;
    @Mock
    Logger log;
    @InjectMocks
    AlarmUtil alarmUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWechatAlarm() throws Exception {
        when(systemConfig.getWechatAlarmUserList()).thenReturn(Arrays.<String>asList("String"));
        when(systemConfig.getEmailAlarmUserList()).thenReturn(Arrays.<String>asList("String"));

        alarmUtil.wechatAlarm(true, "errorMsg", null, null);
    }

    @Test
    public void testEmailAlarm() throws Exception {
        when(systemConfig.getWechatAlarmUserList()).thenReturn(Arrays.<String>asList("String"));
        when(systemConfig.getEmailAlarmUserList()).thenReturn(Arrays.<String>asList("String"));

        alarmUtil.emailAlarm(true, "errorMsg", null, null);
    }

    @Test
    public void testBothWechatAndEmailAlarm() throws Exception {
        when(systemConfig.getWechatAlarmUserList()).thenReturn(Arrays.<String>asList("String"));
        when(systemConfig.getEmailAlarmUserList()).thenReturn(Arrays.<String>asList("String"));

        alarmUtil.bothWechatAndEmailAlarm(true, "errorMsg", null, null);
    }
}

