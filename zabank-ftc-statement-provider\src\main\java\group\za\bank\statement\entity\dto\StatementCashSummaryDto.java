package group.za.bank.statement.entity.dto;

import lombok.Data;

/**
 * 现金摘要结单数据对象
 * <AUTHOR>
 * @date 2022/05/06
 **/
@Data
public class StatementCashSummaryDto extends StatementBaseDto{

    private String currency;

    /** 已交收金额 */
    private String settledAmount;

    /** 当期购买力 */
    private String currentPower;

    /** 冻结金额 */
    private String frozenAmount;

    /** 可取金额 */
    private String withdrawAmount;

    /** 银行冻结金额  **/
    private String bankFrozenAmount;
}
