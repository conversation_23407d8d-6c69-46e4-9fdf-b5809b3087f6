package group.za.bank.statement.domain.mapper;

import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.invest.mybatis.mapper.template.CrudMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* This mapper interface access the database table td_stock_statement_data
* <p>
* Database Table Remarks: 
* </p>
*
* <AUTHOR>
* @since 2022年05月05日 11:50:13
*/
@Repository
public interface TdStockStatementDataMapper extends CrudMapper<TdStockStatementData> {

    /**
     * 根据businessId删除数据
     * @param businessId
     * @return
     */
    Integer deleteByBusinessId(@Param("businessId") String businessId);




    List<TdStockStatementData> queryPeriodDataByFormatType(@Param("ttlMarketCode") String ttlMarketCode,@Param("startTradeDate")  String startTradeDate
            ,@Param("endTradeDate")  String endTradeDate, @Param("statementType") Integer statementType,@Param("accNo")  String accNo,@Param("formatType") Integer formatType);



}
