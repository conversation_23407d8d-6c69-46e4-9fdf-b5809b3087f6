package group.za.bank.statement.domain.entity;

import group.za.invest.mybatis.repository.entity.AuditIdEntity;
import group.za.invest.mybatis.table.annotation.Column;
import group.za.invest.mybatis.table.annotation.GenerationType;
import group.za.invest.mybatis.table.annotation.SequenceGenerator;
import group.za.invest.mybatis.table.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 月结单公司行动待处理数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-08
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@SequenceGenerator(strategy = GenerationType.USE_GENERATED_KEYS)
@Table(name = "td_statement_action_pending_data")
public class TdStatementActionPendingData extends AuditIdEntity<Long> {

    private static final long serialVersionUID = 1L;
    /**
     * 公司行动ID
     */
    @Column(name = "action_id")
    private String actionId;

    /**
     * 用户ID
     */
    @Column(name = "bank_user_id")
    private String bankUserId;

    /**
     * 资金账号
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 结单数据ID(用于关联原始结单数据)
     */
    @Column(name = "statement_data_id")
    private Long statementDataId;

    /**
     * 市场代码
     */
    @Column(name = "market_code")
    private String marketCode;

    /**
     * 公司行动类型
     */
    @Column(name = "action_type")
    private String actionType;

    /**
     * 资金处理成功时间
     */
    @Column(name = "capital_time")
    private Date capitalTime;

    /**
     * 原始月结单期数:yyyyMM
     */
    @Column(name = "original_period")
    private String originalPeriod;

    /**
     * 所属月结单期数:yyyyMM
     */
    @Column(name = "period")
    private String period;

    /**
     * 数据状态 0-未处理 1-已处理
     */
    @Column(name = "data_status")
    private String dataStatus;

    /**
     * 处理时间
     */
    @Column(name = "execute_time")
    private Date executeTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 版本控制
     */
    @Column(name = "version")
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @Column(name = "gmt_created")
    private Date gmtCreated;

    /**
     * 修改者
     */
    @Column(name = "modifier")
    private String modifier;

    /**
     * 更新时间
     */
    @Column(name = "gmt_modified")
    private Date gmtModified;

}
