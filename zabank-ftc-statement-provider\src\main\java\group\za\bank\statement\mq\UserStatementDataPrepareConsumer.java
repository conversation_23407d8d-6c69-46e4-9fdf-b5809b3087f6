package group.za.bank.statement.mq;

import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.statement.constants.MqConstants;
import group.za.bank.statement.entity.UserMonthlyStatementDataPrepareDto;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.invest.rabbitmq.base.BaseRabbitMqConsumer;
import group.za.invest.rabbitmq.entity.BaseMqMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 订单延时消息补偿消费者
 * <AUTHOR>
 * @Date 2022/3/14 11:02
 * @Version 1.0
 **/
@Component
@Slf4j
public class UserStatementDataPrepareConsumer extends BaseRabbitMqConsumer {


    @Autowired
    private MonthlyStatementService monthlyStatementService;


    @Override
    protected String configPrefix() {
        return MqConstants.USER_MONTHLY_STATEMENT_DATA_PREPARE_PREFIX;
    }

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("收到用户月结单数据准备消息{}", body);
        UserMonthlyStatementDataPrepareDto userMonthlyStatementDataPrepareDto = null;
        try {
            BaseMqMsg<UserMonthlyStatementDataPrepareDto> baseMqMsg = null;
            try {
                baseMqMsg = JsonUtils.toJavaObject(body, new TypeReference<BaseMqMsg<UserMonthlyStatementDataPrepareDto>>() {});
            } catch (Exception e) {
                log.error("解析消息报文失败", e);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            if (baseMqMsg == null || baseMqMsg.getData() == null) {
                log.info("解析消息数据为空");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            userMonthlyStatementDataPrepareDto = baseMqMsg.getData();
            monthlyStatementService.userStatementDataPrepareConsume(userMonthlyStatementDataPrepareDto);

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("消费用户月结单数据准备消息异常! userMonthlyStatementDataPrepareDto:{}",userMonthlyStatementDataPrepareDto,e);
            // 这里报错不重新消费，避免程序问题陷入死循环
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }

    }


}
