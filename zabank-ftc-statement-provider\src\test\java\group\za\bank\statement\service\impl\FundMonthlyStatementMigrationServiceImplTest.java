package group.za.bank.statement.service.impl;

import group.za.bank.fund.domain.trade.entity.TdExchangeRateInfoHistory;
import group.za.bank.fund.domain.trade.entity.TdFundMonthlyStatement;
import group.za.bank.fund.domain.trade.entity.TdMonthlyStatementOrderSnapshot;
import group.za.bank.fund.domain.trade.entity.TdMonthlyStatementOverview;
import group.za.bank.fund.domain.trade.mapper.*;
import group.za.bank.fund.trade.constants.enums.FundBusinessTypeDescEnum;
import group.za.bank.fund.trade.entity.FundMonthlyStatementDataDto;
import group.za.bank.fund.trade.entity.remote.resp.InvestMonthlyStatementSwitchResp;
import group.za.bank.invest.pub.entity.dto.GrayDetail;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.mapper.TdFundMonthlyOrderTmpMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementDataMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementInfoMapper;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.manager.ActivityStatementManager;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.bank.statement.service.remote.FundTradeRemoteService;
import group.za.bank.statement.service.remote.PubRemoteService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;

import static org.mockito.Mockito.*;

public class FundMonthlyStatementMigrationServiceImplTest extends BaseTestService {
    @Mock
    MonthlyStatementManager monthlyStatementManager;
    @Mock
    SystemConfig systemConfig;
    @Mock
    TdFundMonthlyStatementInfoMapper tdFundMonthlyStatementInfoMapper;
    @Mock
    TdExchangeRateInfoHistoryMapper tdExchangeRateInfoHistoryMapper;
    @Mock
    TdMonthlyStatementInfoMapper tdMonthlyStatementInfoMapper;
    @Mock
    ExchangeRateInfoService exchangeRateInfoService;
    @Mock
    TdFundMonthlyStatementMapper tdFundMonthlyStatementMapper;
    @Mock
    TdMonthlyStatementMapper tdMonthlyStatementMapper;
    @Mock
    TdMonthlyStatementDataMapper tdMonthlyStatementDataMapper;
    @Mock
    TdMonthlyStatementHoldingSnapshotMapper tdMonthlyStatementHoldingSnapshotMapper;
    @Mock
    TdMonthlyStatementOrderSnapshotMapper tdMonthlyStatementOrderSnapshotMapper;
    @Mock
    PubRemoteService pubRemoteService;
    @Mock
    ThreadPoolTaskExecutor statementExecutor;
    @Mock
    TdMonthlyStatementOverviewMapper tdMonthlyStatementOverviewMapper;
    @Mock
    FundTradeRemoteService fundTradeRemoteService;
    @Mock
    TdFundMonthlyOrderTmpMapper tdFundMonthlyOrderTmpMapper;
    @Mock
    ActivityStatementManager activityStatementManager;
    @Mock
    Logger log;
    @InjectMocks
    FundMonthlyStatementMigrationServiceImpl fundMonthlyStatementMigrationServiceImpl;


    @Test
    public void testFundMonthlyStatementMigration() throws Exception {
        when(monthlyStatementManager.getStatement(anyString())).thenReturn(new TdFundMonthlyStatementInfo());
        when(monthlyStatementManager.getUserStatementBusinessData(anyString(), anyString())).thenReturn(new TdMonthlyStatementBusinessData());
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn("fund,stock");
        when(systemConfig.getTempPath()).thenReturn("getTempPathResponse");
        when(systemConfig.getZhTempKey()).thenReturn("getZhTempKeyResponse");
        when(systemConfig.getHkTempKey()).thenReturn("getHkTempKeyResponse");
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));
        when(exchangeRateInfoService.getExchangeHkdRate(anyString(), anyString(), anyString())).thenReturn(new BigDecimal(0));
        when(pubRemoteService.queryBusinessData(anyString())).thenReturn("queryBusinessDataResponse");
        when(pubRemoteService.queryGray(any())).thenReturn(null);
        when(fundTradeRemoteService.queryInvestMonthlyStatementSwitch()).thenReturn(null);
        when(tdFundMonthlyOrderTmpMapper.deleteTdFundMonthlyOrderTmp()).thenReturn(0);
        when(activityStatementManager.getEqualHkdMonthHoldingIncomeValue(anyString(), anyString(), anyString(), anyString())).thenReturn(new BigDecimal(0));

        InvestMonthlyStatementSwitchResp resp = new InvestMonthlyStatementSwitchResp();
        resp.setDownloadInvestMonthlyStatementSwitch(false);
        resp.setDownloadInvestMonthlyStatementGraySwitch(true);
        when(fundTradeRemoteService.queryInvestMonthlyStatementSwitch()).thenReturn(resp);

        when(tdExchangeRateInfoHistoryMapper.queryMonthStatementRate(any(), any(), any(),any(),any())).thenReturn(new TdExchangeRateInfoHistory());
        when(tdMonthlyStatementInfoMapper.updateByCondition(any(),any())).thenReturn(1);

        GrayDetail grayDetail = new GrayDetail();
        grayDetail.setSwitchOpen(true);
        when(pubRemoteService.queryGray(any())).thenReturn(grayDetail);

        TdFundMonthlyStatement docUnGeneratedStatement = new TdFundMonthlyStatement();
        when(tdFundMonthlyStatementMapper.queryMonthlyStatementList(any(), any(), any(), any())).thenReturn(Arrays.asList(docUnGeneratedStatement));
        when(tdFundMonthlyStatementMapper.queryMonthlyStatementList(any(), any(), any(), any())).thenReturn(null);
        fundMonthlyStatementMigrationServiceImpl.fundMonthlyStatementMigration("202401");
    }

    @Test
    public void testSetUsdToHkdRate() throws Exception {
        when(tdExchangeRateInfoHistoryMapper.queryMonthStatementRate(any(), any(), any(),any(),any())).thenReturn(new TdExchangeRateInfoHistory());
        when(tdMonthlyStatementInfoMapper.updateByCondition(any(),any())).thenReturn(1);
        fundMonthlyStatementMigrationServiceImpl.setUsdToHkdRate(new TdFundMonthlyStatementInfo());
    }

    @Test
    public void testGenerateDataMap() throws Exception {
        when(exchangeRateInfoService.getExchangeHkdRate(anyString(), anyString(), anyString())).thenReturn(new BigDecimal(0));
        when(activityStatementManager.getEqualHkdMonthHoldingIncomeValue(anyString(), anyString(), anyString(), anyString())).thenReturn(new BigDecimal(0));
        InvestMonthlyStatementSwitchResp resp = new InvestMonthlyStatementSwitchResp();
        resp.setDownloadInvestMonthlyStatementSwitch(false);
        when(fundTradeRemoteService.queryInvestMonthlyStatementSwitch()).thenReturn(resp);
        TdMonthlyStatementOverview tdMonthlyStatementOverview = new TdMonthlyStatementOverview();
        tdMonthlyStatementOverview.setTotalMarket(new BigDecimal(0));
        FundMonthlyStatementDataDto fundMonthlyStatementDataDto = new FundMonthlyStatementDataDto();
        fundMonthlyStatementDataDto.setBasicInfo(new FundMonthlyStatementDataDto.BasicInfoDto());
        Map<String, Object> result = fundMonthlyStatementMigrationServiceImpl.generateDataMap(new group.za.bank.statement.domain.entity.TdFundMonthlyStatement(), fundMonthlyStatementDataDto, tdMonthlyStatementOverview);
    }

    @Test
    public void testJudgeDividendType() throws Exception {
        FundBusinessTypeDescEnum result = fundMonthlyStatementMigrationServiceImpl.judgeDividendType(new TdMonthlyStatementOrderSnapshot());
    }

    @Test
    public void testFundMonthlyStatementMigrationObsKeyFlush() throws Exception {
        when(systemConfig.getPageSize()).thenReturn(Integer.valueOf(0));

        fundMonthlyStatementMigrationServiceImpl.fundMonthlyStatementMigrationObsKeyFlush("202401");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme