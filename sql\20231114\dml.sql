--初始化月结单解析记录
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-01', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-02', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-03', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-06', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-07', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-08', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-09', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-10', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-13', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-14', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-15', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-16', 2);
INSERT INTO za_bank_invest_statement.td_statement_file_record (statement_type, statement_date, data_status) VALUES (1, '2023-11-17', 2);