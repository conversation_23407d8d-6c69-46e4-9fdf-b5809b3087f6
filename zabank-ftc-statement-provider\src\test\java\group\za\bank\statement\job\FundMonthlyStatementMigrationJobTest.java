package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.service.FundMonthlyStatementMigrationService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

public class FundMonthlyStatementMigrationJobTest {
    @Mock
    FundMonthlyStatementMigrationService fundMonthlyStatementMigrationService;
    @Mock
    Logger log;
    @Mock
    ReturnT<String> SUCCESS;
    @Mock
    ReturnT<String> FAIL;
    @Mock
    ReturnT<String> FAIL_TIMEOUT;
    @InjectMocks
    FundMonthlyStatementMigrationJob fundMonthlyStatementMigrationJob;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testExecute() throws Exception {
        ReturnT<String> result = fundMonthlyStatementMigrationJob.execute("202401");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme