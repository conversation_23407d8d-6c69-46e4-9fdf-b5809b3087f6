package group.za.bank.statement.utils;

import group.za.bank.invest.common.constants.enums.AlarmModule;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.pub.entity.req.AlarmMessage;
import group.za.bank.invest.pub.feign.AlarmMessageService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.invest.web.config.ApplicationEventTracer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/4/12 15:08
 * @Version v1.0
 **/
@Component
@Slf4j
public class AlarmUtil {

    @Autowired
    private SystemConfig systemConfig;
    @Autowired
    private AlarmMessageService alarmMessageService;
    @Autowired
    @Qualifier("statementExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    /**
     * 企业微信告警
     * 
     * @param errorLog
     * @param errorMsg
     * @param module
     * @param e
     */
    public void wechatAlarm(boolean errorLog, String errorMsg, AlarmModule module, Throwable e) {
        alarm(errorLog, true, false, errorMsg, module, e);
    }

    /**
     * 邮件告警
     * 
     * @param errorLog
     * @param errorMsg
     * @param module
     * @param e
     */
    public void emailAlarm(boolean errorLog, String errorMsg, AlarmModule module, Throwable e) {
        alarm(errorLog, false, true, errorMsg, module, e);
    }

    /**
     * 企业微信邮件都告警
     * 
     * @param errorLog
     * @param errorMsg
     * @param module
     * @param e
     */
    public void bothWechatAndEmailAlarm(boolean errorLog, String errorMsg, AlarmModule module, Throwable e) {
        alarm(errorLog, true, true, errorMsg, module, e);
    }

    /**
     * <AUTHOR>
     * @Description
     * @Date 2021/4/12 15:23
     * @Param [errorMsg 告警信息, e 异常, errorLog 是否打印error日志]
     * @return void
     **/
    private void alarm(boolean errorLog, boolean wechat, boolean email, String errorMsg, AlarmModule module,
        Throwable e) {
        commonExecutor.submit(() -> {
            try {
                doAlarm(errorLog, wechat, email, errorMsg, module, e);
            } catch (Exception exception) {
                log.warn("告警异常 errorMsg={}", errorMsg, e);
            }
        });

    }

    private void doAlarm(boolean errorLog, boolean wechat, boolean email, String errorMsg, AlarmModule module, Throwable e) {
        if (errorLog) {
            if (Objects.isNull(e)) {
                log.error(errorMsg);
            } else {
                log.error(errorMsg, e);
            }
        }

        if (StringUtils.isBlank(errorMsg) || Objects.isNull(module)) {
            log.warn("Can't send alarm,errorMsg={},module={}", errorMsg, module);
            return;
        }
        AlarmMessage alarmMessage = new AlarmMessage();
        alarmMessage.setServiceName(StatementConstants.SERVER_NAME);
        alarmMessage.setModule(module.getValue());
        alarmMessage.setErrorDescribe(errorMsg);

        alarmMessage.setErrorPosition("traceId=" + ApplicationEventTracer.getTraceId());
        alarmMessage.setAlarmTime(new Date());
        if (wechat) {
            AlarmMessage.WechatParam wechatParam = new AlarmMessage.WechatParam();
            wechatParam.setUserList(systemConfig.getWechatAlarmUserList());
            alarmMessage.addWechatAlarm(wechatParam);
        }

        if (email) {
            AlarmMessage.EmailParam emailParam = new AlarmMessage.EmailParam();
            emailParam.setTo(systemConfig.getEmailAlarmUserList());
            alarmMessage.addEmailAlarm(emailParam);
        }

        log.info("开始发送告警消息 wechat={} email={} alarmMessage={}", wechat, email, JsonUtils.toJsonString(alarmMessage));
        alarmMessageService.alarm2Administrators(alarmMessage);
        log.info("发送告警消息结束");
    }
}
