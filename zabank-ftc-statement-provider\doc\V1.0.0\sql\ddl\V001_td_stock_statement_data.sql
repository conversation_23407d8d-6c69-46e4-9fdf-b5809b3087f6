create TABLE `td_stock_statement_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `business_id` varchar(64) DEFAULT NULL comment '业务逻辑id',
  `statement_date` VARCHAR(12) DEFAULT NULL comment '结单日期:月结单-yyyyMM，日结单-yyyyMMdd',
  `statement_type` int(4) NOT NULL DEFAULT '1' comment '结单类型:1-日结单，2-月结单',
  `acc_no` varchar(20) DEFAULT NULL comment 'ttl柜台账户',
  `format_type` varchar(20) DEFAULT NULL comment '1-港股成交单,2-美股成交单，3-交易变动明细，4证券资产摘要，5-现金摘要',
  `param1` varchar(128) DEFAULT NULL,
  `param2` varchar(128) DEFAULT NULL,
  `param3` varchar(100) DEFAULT NULL,
  `param4` varchar(128) DEFAULT NULL,
  `param5` varchar(128) DEFAULT NULL,
  `param6` varchar(128) DEFAULT NULL,
  `param7` varchar(256) DEFAULT NULL,
  `param8` varchar(256) DEFAULT NULL,
  `param9` varchar(128) DEFAULT NULL,
  `param10` varchar(256) DEFAULT NULL,
  `param11` varchar(128) DEFAULT NULL,
  `param12` varchar(256) DEFAULT NULL,
  `param13` varchar(128) DEFAULT NULL,
  `param14` varchar(256) DEFAULT NULL,
  `param15` varchar(128) DEFAULT NULL,
  `param16` varchar(256) DEFAULT NULL,
  `param17` varchar(128) DEFAULT NULL,
  `param18` varchar(256) DEFAULT NULL,
  `param19` varchar(128) DEFAULT NULL,
  `param20` varchar(256) DEFAULT NULL,
  `param21` varchar(128) DEFAULT NULL,
  `param22` varchar(128) DEFAULT NULL,
  `param23` varchar(128) DEFAULT NULL,
  `param24` varchar(128) DEFAULT NULL,
  `param25` varchar(128) DEFAULT NULL,
  `param26` varchar(128) DEFAULT NULL,
  `param27` varchar(128) DEFAULT NULL,
  `param28` varchar(128) DEFAULT NULL,
  `param29` varchar(128) DEFAULT NULL,
  `param30` varchar(128) DEFAULT NULL,
  `param31` varchar(128) DEFAULT NULL,
  `param32` varchar(128) DEFAULT NULL,
  `param33` varchar(128) DEFAULT NULL,
  `param34` varchar(128) DEFAULT NULL,
  `record_time` datetime DEFAULT NULL comment '结单记录生成时间',
  `remark` varchar(100) DEFAULT '' comment '备注',
  `creator` varchar(32) NOT NULL DEFAULT 'system' comment '创建者',
  `gmt_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
  `modifier` varchar(32) NOT NULL DEFAULT 'system' comment '修改者',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' comment '是否删除',
  PRIMARY KEY (`id`),
  index idx_stdata_bussinessid(business_id),
  index idx_stdata_date_type(statement_date,statement_type,format_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 comment='ttl股票结单数据';
