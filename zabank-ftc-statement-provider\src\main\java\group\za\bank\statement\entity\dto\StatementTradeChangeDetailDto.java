package group.za.bank.statement.entity.dto;

import group.za.bank.statement.constants.enums.StatementActionEventTypeEnum;
import lombok.Data;

import java.util.Map;

/**
 * 交易变动明细数据对象
 * <AUTHOR>
 * @date 2022/05/06
 **/
@Data
public class StatementTradeChangeDetailDto extends StatementBaseDto{

    /**交易日：格式是 yyyy-MM-dd */
    private String tradeDate;

    /** 结算日：格式是 yyyy-MM-dd */
    private String clearDate;

    /** 币种 */
    private String currency;


    /**
     * ttl的市场代码
     */
    private String ttlMarketCode;

    /** 参考编号 */
    private String referenceNo;

    /** 交易类型 */
    private String businessType;

    /** 股票代码 */
    private String stockCode;

    private String stockNameCn;

    private String stockNameHk;

    private String stockNameEn;

    /** 交易描述 */
    private String description;

    /** 股票变动 */
    private String qty;

    /** 净金额 */
    private String amount;


    /**
     * 源股票代码
     */
    private String sourceStockCode;
    /**
     * 源ttl市场代码
     */
    private String sourceTtlMarketCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当前订单的所有费用
     */
    private Map<String,StatementFeeDto> feeMap;

    /**
     * 事件类型枚举
     */
    private StatementActionEventTypeEnum eventTypeEnum;

}
