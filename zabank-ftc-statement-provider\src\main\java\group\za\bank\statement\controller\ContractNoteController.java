package group.za.bank.statement.controller;


import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.common.utils.ResponseUtil;
import group.za.bank.statement.entity.req.ContractNoteInfoReq;
import group.za.bank.statement.entity.resp.ContractNoteInfoResp;
import group.za.bank.statement.service.ContractNoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 结单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@RestController
@Slf4j
public class ContractNoteController {

    @Autowired
    private ContractNoteService contractNoteService;

    /**
     * Activity contractnote跳转状态查询
     * @param req
     * @return
     */
    @RequestMapping(value = "/statement/normal/contractnote/info", method = RequestMethod.POST)
    public ResponseData<ContractNoteInfoResp> contractInfo(@RequestBody @Valid ContractNoteInfoReq req) {
        return ResponseUtil.success(contractNoteService.contractInfoQuery(req));
    }

}

