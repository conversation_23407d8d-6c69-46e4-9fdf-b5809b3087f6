package group.za.bank.statement.service.remote;

import group.za.bank.market.entity.resp.FundInfoResp;
import group.za.bank.sbs.quotasup.share.service.entity.resp.DayKlineRespDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created on 2020/11/20 13:56.
 *
 * <AUTHOR> Description :
 */
public interface    StockMarketRemoteService {

    /**
     * 批量查询多个股票收盘价
     * @param assetIdList
     */
    Map<String, DayKlineRespDTO> queryStockKline(Date tradeDte,List<String> assetIdList);


}
