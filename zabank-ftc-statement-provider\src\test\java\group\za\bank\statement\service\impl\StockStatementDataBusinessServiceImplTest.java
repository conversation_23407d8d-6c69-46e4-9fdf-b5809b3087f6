package group.za.bank.statement.service.impl;

import group.za.bank.sbs.bankfront.mapper.CounterFileInfoExtendMapper;
import group.za.bank.sbs.bankfront.model.entity.CounterFileInfo;
import group.za.bank.sbs.trade.model.dto.CompanyActionDetailInfoDTO;
import group.za.bank.sbs.trade.model.dto.CompanyActionExerciseDetailDTO;
import group.za.bank.sbs.trade.model.dto.OrderInfoStatementDTO;
import group.za.bank.sbs.trade.model.resp.feign.TradeDateDiffResp;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.domain.entity.TdStockStatementData;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.remote.TradeCalendarRemoteService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.*;

public class StockStatementDataBusinessServiceImplTest extends BaseTestService {
    @Mock
    StockTradeManager stockTradeManager;
    @Mock
    TradeCalendarRemoteService tradeCalendarRemoteService;
    @Mock
    CounterFileInfoExtendMapper counterFileInfoExtendMapper;
    @Mock
    Logger log;
    @InjectMocks
    StockStatementDataBusinessServiceImpl stockStatementDataBusinessServiceImpl;


    @Test
    public void testLocalGenerationOrderData() throws Exception {
        OrderInfoStatementDTO orderInfoStatementDTO = new OrderInfoStatementDTO();
        orderInfoStatementDTO.setBusinessQty(new BigDecimal(1));
        orderInfoStatementDTO.setAvgPrice(new BigDecimal(1));
        orderInfoStatementDTO.setBusinessAmount(new BigDecimal(1));
        orderInfoStatementDTO.setNetAmount(new BigDecimal(1));
        orderInfoStatementDTO.setCommission(new BigDecimal(1));
        orderInfoStatementDTO.setFinraFee(new BigDecimal(1));
        orderInfoStatementDTO.setSecFee(new BigDecimal(1));
        when(stockTradeManager.queryOrderListByStatement(any())).thenReturn(Arrays.<OrderInfoStatementDTO>asList(orderInfoStatementDTO));
        TradeDateDiffResp tradeDateDiffResp = new TradeDateDiffResp();
        tradeDateDiffResp.setTradeDate("20241231");
        when(tradeCalendarRemoteService.tradeDateDiff(anyString(), anyString(), anyInt())).thenReturn(tradeDateDiffResp);
        CounterFileInfo counterFileInfo = new CounterFileInfo();
        counterFileInfo.setReleaseReference("1234567890");
        when(counterFileInfoExtendMapper.queryByLocalOrderNoAndLinkupTypeAndTxnTypeId(any(), any(), any())).thenReturn(Arrays.asList(counterFileInfo));

        List<TdStockStatementData> result = stockStatementDataBusinessServiceImpl.localGenerationOrderData("businessId", "20240101", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 29).getTime());
    }

    @Test
    public void testLocalGenerationTradeChangeData() throws Exception {
        CompanyActionDetailInfoDTO companyActionDetailInfoDTO = new CompanyActionDetailInfoDTO();
        companyActionDetailInfoDTO.setActivityType("2");
        companyActionDetailInfoDTO.setOverrideAmount(new BigDecimal("1"));
        companyActionDetailInfoDTO.setStockCode("1234567890");
        companyActionDetailInfoDTO.setIssueStockCode("1234567890");
        when(stockTradeManager.queryActionBookCloseData(any())).thenReturn(Arrays.<CompanyActionDetailInfoDTO>asList(companyActionDetailInfoDTO));
        CompanyActionExerciseDetailDTO companyActionExerciseDetailDTO = new CompanyActionExerciseDetailDTO();
        companyActionExerciseDetailDTO.setActionType("9");
        companyActionExerciseDetailDTO.setActivityType("10");
        companyActionExerciseDetailDTO.setDeductQty(new BigDecimal(1));
        companyActionExerciseDetailDTO.setDeductAmt(new BigDecimal(1));

        CompanyActionExerciseDetailDTO companyActionExerciseDetailDTO2 = new CompanyActionExerciseDetailDTO();
        companyActionExerciseDetailDTO2.setActionType("10");
        companyActionExerciseDetailDTO2.setActivityType("14");
        companyActionExerciseDetailDTO2.setDeductQty(new BigDecimal(1));
        companyActionExerciseDetailDTO2.setDeductAmt(new BigDecimal(1));
        companyActionExerciseDetailDTO2.setConfirmAmt(new BigDecimal(1));
        companyActionExerciseDetailDTO2.setDeductDate(new Date());

        when(stockTradeManager.queryActionExerciseData(any())).thenReturn(Arrays.<CompanyActionExerciseDetailDTO>asList(companyActionExerciseDetailDTO, companyActionExerciseDetailDTO2));

        List<TdStockStatementData> result = stockStatementDataBusinessServiceImpl.localGenerationTradeChangeData("businessId", "20240101", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 29).getTime());
    }

    @Test
    public void testLocalGenerationHoldingData() throws Exception {
        List<TdStockStatementData> result = stockStatementDataBusinessServiceImpl.localGenerationHoldingData("businessId", "20240101", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 29).getTime());
    }

    @Test
    public void testGenerationTradeChangeDividend() throws Exception {
        CompanyActionDetailInfoDTO companyActionDetailInfoDTO = new CompanyActionDetailInfoDTO();
        companyActionDetailInfoDTO.setFee(new BigDecimal(1));
        companyActionDetailInfoDTO.setUsTaxFee(new BigDecimal(1));
        companyActionDetailInfoDTO.setOverrideAmount(new BigDecimal(1));

        List<TdStockStatementData> result = stockStatementDataBusinessServiceImpl.generationTradeChangeDividend("businessId", "20240101", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 29).getTime(),
                Arrays.<CompanyActionDetailInfoDTO>asList(companyActionDetailInfoDTO));
    }

    @Test
    public void testGenerationTradeChangeFraction() throws Exception {
        CompanyActionDetailInfoDTO companyActionDetailInfoDTO = new CompanyActionDetailInfoDTO();
        companyActionDetailInfoDTO.setIssueRatioPer(new BigDecimal(1));
        companyActionDetailInfoDTO.setIssueRatioDelivery(new BigDecimal(1));
        companyActionDetailInfoDTO.setBookCloseQty(new BigDecimal(1));
        companyActionDetailInfoDTO.setOverrideAmount(new BigDecimal(1));
        List<TdStockStatementData> result = stockStatementDataBusinessServiceImpl.generationTradeChangeFraction("businessId", "20240101", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 29).getTime(), Arrays.<CompanyActionDetailInfoDTO>asList(companyActionDetailInfoDTO));
    }

    @Test
    public void testGenerationTradeChangeExercise() throws Exception {
        CompanyActionExerciseDetailDTO tdStockStatementData = new CompanyActionExerciseDetailDTO();
        List<TdStockStatementData> result = stockStatementDataBusinessServiceImpl.generationTradeChangeExercise("businessId", "20240101", new GregorianCalendar(2024, Calendar.DECEMBER, 9, 18, 29).getTime(),
                Arrays.<CompanyActionExerciseDetailDTO>asList(tdStockStatementData));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme