package group.za.bank.statement.job;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import group.za.bank.statement.base.BaseIntegrationTest;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.repository.TdMonthlyStatementDataRepository;
import group.za.bank.statement.entity.UserMonthlyStatementDataPrepareDto;
import group.za.bank.statement.entity.dto.MonthlyStatementDto;
import group.za.bank.statement.entity.req.PubMonthlyStatementReq;
import group.za.bank.statement.service.ExchangeRateInfoService;
import group.za.bank.statement.service.MonthlyStatementService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/14
 * @Description
 * @Version v1.0
 */
@Slf4j
public class JobIntegrationTest extends BaseIntegrationTest {

    @Resource
    private MonthlyStatementInitJob monthlyStatementInitJob;
    @Resource
    private MonthlyStatementService monthlyStatementService;
    @Resource
    private MonthlyStatementDocGenerateNotifyJob monthlyStatementDocGenerateNotifyJob;
    @Resource
    private MonthlyStatementDocGenerateJob monthlyStatementDocGenerateJob;
    @Resource
    private ExchangeRateInfoService exchangeRateInfoService;
    @Resource
    private FundMonthlyStatementMigrationJob fundMonthlyStatementMigrationJob;
    @Resource
    private MonthlyStatementDocPublishJob monthlyStatementDocPublishJob;
    @Resource
    private FundMonthlyStatementMigrationObsKeyFlushJob fundMonthlyStatementMigrationObsKeyFlushJob;
    @Resource
    private TdMonthlyStatementDataRepository tdMonthlyStatementDataRepository;

    @Test
    public void monthlyStatementInitJobTest() throws Exception {
        String param = "{\"period\":\"202302\",businessType:\"fund,stock\"}";
                monthlyStatementInitJob.execute(param);

//        try{
//            for (int i = 0; i < 5; i++) {
//                String param = "{\"period\":\"202302\",businessType:\"fund\"}";
//                new Thread(()->{
//                    try {
//                        monthlyStatementInitJob.execute(param);
//                    } catch (Exception e) {
//                        log.error("monthlyStatementInitJobTest", e);
//                    }
//                }).start();
//                TimeUnit.SECONDS.sleep(5);
//            }
//        }catch (Exception e){
//            log.error("monthlyStatementInitJobTest error", e);
//        }
    }

    @Test
    public void userStatementDataPrepareConsumeTest(){
        String body = "{\"bankUserId\":\"1579832650271621632\",\"business\":\"fund\",\"businessSubtype\":\"fund\",\"clientId\":\"**********\",\"period\":\"202302\"}";
        try {
            UserMonthlyStatementDataPrepareDto dto = JSON.parseObject(body, UserMonthlyStatementDataPrepareDto.class);
            monthlyStatementService.userStatementDataPrepareConsume(dto);

        } catch (Exception e) {
            log.error("消费用户月结单数据准备消息异常!",e);
        }
    }

    @Test
    public void monthlyStatementDocGenerateNotifyJobTest() throws Exception {
        String param = "{\"period\":\"202305\",businessType:\"fund,stock\"}";
        monthlyStatementDocGenerateNotifyJob.execute(param);
    }

    @Test
    public void monthlyStatementDocGenerateTest() throws Exception {
        String param = "{\"period\":\"202201\",businessType:\"fund,stock\"}";
        monthlyStatementDocGenerateJob.execute(param);
    }

    @Test
    public void exchangeRateInfoServiceTest(){
        exchangeRateInfoService.initExchangeRate("2022tt");

    }

    @Test
    public void getExchangeHkdRateTest(){
        BigDecimal usd = exchangeRateInfoService.getExchangeHkdRate("USD", "202201", "fund");
    }

    @Test
    public void fundMonthlyStatementMigrationJobTest() throws Exception {
//        ReturnT<String> execute = fundMonthlyStatementMigrationJob.execute("202112,202201,202202,202203,202204,202205,202206,202207,202208,202209,202210,202211,202212,202301,202302,202303,202304,202305,202306,202307");
//        ReturnT<String> execute = fundMonthlyStatementMigrationJob.execute("202301,202302,202303,202304,202305,202306");
        ReturnT<String> execute = fundMonthlyStatementMigrationJob.execute("202305");
//        ReturnT<String> execute = fundMonthlyStatementMigrationJob.execute("202210,202211,202212,202301,202302,202303,202304,202305,202306,202307");
    }

    @Test
    public void monthlyStatementDocPublishJobTest() throws Exception {
        String param = "{\"period\":\"202308\",businessType:\"fund,stock\"}";
        monthlyStatementDocPublishJob.execute(param);
    }


    @Test
    public void fundMonthlyStatementMigrationObsKeyFlushJobTest() throws Exception {
        ReturnT<String> execute = fundMonthlyStatementMigrationObsKeyFlushJob.execute("202305");
    }

    @Test
    public void monthlyStatementServiceTest() throws IOException {
        List<String> periods = new ArrayList<>();
        periods.add("202211");
        periods.add("202301");
        periods.add("202302");
        periods.add("202303");
        periods.add("202304");
        periods.add("202305");
        periods.add("202306");
        periods.add("202307");
        PubMonthlyStatementReq req = new PubMonthlyStatementReq();
        req.setClientId("6221725599");
        req.setPeriods(periods);
//        req.setDocLang(LanguageTemEnum.CN_ZH.getValue());

        List<MonthlyStatementDto> monthlyStatementDtos = monthlyStatementService.queryPubMonthlyStatement(req);
//        System.in.read();
    }

    @Test
    public void tdMonthlyStatementDataRepositoryTest(){
        List<String> ss = new ArrayList<>();
        ss.add("1682284645727203328");
        ss.add("1682284645907558400");
//        ss.add("3");
        List<TdMonthlyStatementBusinessData> allByStatementIdAndBusinessType = tdMonthlyStatementDataRepository.findAllByStatementIdInAndBusinessType(ss, "statementInfo");
        System.out.println();
    }
}