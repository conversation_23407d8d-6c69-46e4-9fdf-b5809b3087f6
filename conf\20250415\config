
# 新增activity配置
spring.cloud.stream.bindings.activityOutputRabbitmqNonCore.destination = act-nonCore
spring.cloud.stream.bindings.activityOutputRabbitmqNonCore.content-type = application/json
spring.cloud.stream.bindings.activityOutputRabbitmqNonCore.binder = activity
# 删除配置
---need.after.console.finish
# 新增月结单模板
monthly.statement.doc.nxtHkTempKey = zabank_invest_monthly_hk_southBound_v2.html
monthly.statement.doc.nxtZhTempKey = zabank_invest_monthly_zh_southBound_v2.html