package group.za.bank.statement.service.impl;

import cn.hutool.json.JSONUtil;
import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.invest.common.utils.JsonUtils;
import group.za.bank.invest.pub.feign.FileService;
import group.za.bank.statement.common.config.SystemConfig;
import group.za.bank.statement.constants.enums.MonthlyStatementDocStatusEnum;
import group.za.bank.statement.constants.enums.MonthlyStatementPubStatusEnum;
import group.za.bank.statement.constants.enums.StatementModuleTypeEnum;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatement;
import group.za.bank.statement.domain.entity.TdFundMonthlyStatementInfo;
import group.za.bank.statement.domain.entity.TdMonthlyStatementBusinessData;
import group.za.bank.statement.domain.entity.TdMonthlyStatementHtmlParsedData;
import group.za.bank.statement.domain.mapper.TdMonthlyStatementMapper;
import group.za.bank.statement.domain.repository.TdMonthlyStatementParsedDataRepository;
import group.za.bank.statement.entity.dto.BaseMonthlyStatementDataDto;
import group.za.bank.statement.entity.req.NativeDetailReq;
import group.za.bank.statement.entity.req.NativeSummaryReq;
import group.za.bank.statement.entity.resp.NativeSummaryListResp;
import group.za.bank.statement.manager.MonthlyStatementManager;
import group.za.bank.statement.service.MonthlyStatementService;
import group.za.bank.statement.service.StatementBasicInfoService;
import group.za.bank.statement.service.remote.UserRemoteService;
import group.za.bank.statement.utils.MonthlyUtils;
import group.za.invest.web.json.JSON;
import group.za.invest.web.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;

import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * UserStatementNativeServiceImpl的单元测试类
 */
@RunWith(org.mockito.junit.MockitoJUnitRunner.class)
public class UserStatementNativeServiceImplTest {

    @InjectMocks
    private UserStatementNativeServiceImpl userStatementNativeService;

    @Mock
    private SystemConfig systemConfig;

    @Mock
    private UserRemoteService userRemoteService;

    @Mock
    private FileService fileService;
    @Mock
    private MonthlyStatementManager monthlyStatementManager;

    @Mock
    private MonthlyStatementService monthlyStatementService;

    @Mock
    private StatementBasicInfoService statementBasicInfoService;

    @Mock
    private TdMonthlyStatementParsedDataRepository tdMonthlyStatementParsedDataRepository;

    @Mock
    private TdMonthlyStatementMapper tdMonthlyStatementMapper;

    private MockedStatic<JsonUtils> mockedJsonUtils;
    private MockedStatic<JSON> mockedJSON;
    private MockedStatic<DateUtil> mockedDateUtil;
    private MockedStatic<MonthlyUtils> mockedMonthlyUtils;

    private MockedStatic<JSONObject> mockedJSONObject;

    private MockedStatic<JSONUtil> mockedJSONUtil;

    @Before
    public void setUp() {
        // Mock JsonUtils
        mockedJsonUtils = mockStatic(JsonUtils.class);
        when(JsonUtils.toJsonString(any())).thenReturn("{}");
        when(JsonUtils.toFormatJsonString(any())).thenReturn("{}");

        // Mock JSON
        mockedJSON = mockStatic(JSON.class);
        when(JSON.toJSONString(any())).thenReturn("{}");

        // Mock DateUtil
        mockedDateUtil = mockStatic(DateUtil.class);
        when(DateUtil.parse(anyString(), anyString())).thenReturn(new Date());
        when(DateUtil.format(any(), any())).thenReturn("2024-04-01");
        when(DateUtil.getMonthsList(any(), any())).thenReturn(Arrays.asList("202404"));


        mockedMonthlyUtils = mockStatic(MonthlyUtils.class);

        mockedJSONObject = mockStatic(JSONObject.class);
        BaseMonthlyStatementDataDto baseMonthlyStatementDataDto = new BaseMonthlyStatementDataDto();
        //baseMonthlyStatementDataDto.setFund(true);
        baseMonthlyStatementDataDto.setTotalAmount("10000.00 HKD");
        when(JSONObject.parse(any(), eq(BaseMonthlyStatementDataDto.class))).thenReturn(baseMonthlyStatementDataDto);

        mockedJSONUtil = mockStatic(JSONUtil.class);
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        when(JSONUtil.parseObj(any())).thenReturn(jsonObject);
    }

    @After
    public void tearDown() {
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
        if (mockedJSON != null) {
            mockedJSON.close();
        }
        if (mockedDateUtil != null) {
            mockedDateUtil.close();
        }
        if(mockedMonthlyUtils != null){
            mockedMonthlyUtils.close();
        }
        if (mockedJSONObject != null) {
            mockedJSONObject.close();
        }
        if (mockedJSONUtil != null) {
            mockedJSONUtil.close();
        }
    }

    @Test
    public void testQueryUserMonthlyStatementNativeSummaryData_NormalCase() {
        // 准备测试数据
        String bankUserId = "testUser";
        String clientId = "testClient";
        String defaultBusiness = "DEFAULT";
        
        // 设置请求参数
        NativeSummaryReq req = new NativeSummaryReq();
        req.setBankUserId(bankUserId);
        
        // Mock SystemConfig返回值
        when(systemConfig.getDefaultMonthlyStatementBusinessType()).thenReturn(defaultBusiness);
        when(systemConfig.getNativeSummaryMonthSize()).thenReturn(36); // 默认3年
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        
        // Mock UserInvestAccountInfo
        UserInvestAccountInfo accountInfo = new UserInvestAccountInfo();
        accountInfo.setClientId(clientId);
        accountInfo.setAccountType("fund");
        when(userRemoteService.queryUserAllInvestAccountInfo(any())).thenReturn(Arrays.asList(accountInfo));

        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        when(monthlyStatementManager.getStatement(any())).thenReturn(statementInfo);
        
        // Mock 月结单列表
        List<TdFundMonthlyStatement> monthlyStatements = new ArrayList<>();
        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setPeriod("202404");
        statement.setPubStatus(MonthlyStatementPubStatusEnum.FINISHED.getDbValue());
        statement.setDocStatus(MonthlyStatementDocStatusEnum.FINISHED.getDbValue());
        statement.setDocLang(I18nSupportEnum.CN_ZH.getName());
        monthlyStatements.add(statement);
        when(tdMonthlyStatementMapper.queryUserFundMonthlyStatementList(
            anyString(), anyString(), any(), any(), any()
        )).thenReturn(monthlyStatements);

        TdMonthlyStatementBusinessData businessData = new TdMonthlyStatementBusinessData();
        Map<String, Object> map = new HashMap<>();
        businessData.setData(map);
        when(monthlyStatementManager.getUserStatementBusinessData(any(), any())).thenReturn(businessData);
        
        // 执行测试
        NativeSummaryListResp result = userStatementNativeService.queryUserMonthlyStatementNativeSummaryData(
            I18nSupportEnum.CN_ZH, req);



    }

    @Test
    public void testQueryUserMonthlyStatementNativeSummaryData_EmptyClientId() {
        // 准备测试数据
        String bankUserId = "testUser";
        
        // 设置请求参数
        NativeSummaryReq req = new NativeSummaryReq();
        req.setBankUserId(bankUserId);
        
        // Mock UserInvestAccountInfo with empty clientId
        UserInvestAccountInfo accountInfo = new UserInvestAccountInfo();
        accountInfo.setAccountType("fund");
        when(userRemoteService.queryUserAllInvestAccountInfo(any())).thenReturn(Arrays.asList(accountInfo));

        // 执行测试
        NativeSummaryListResp result = userStatementNativeService.queryUserMonthlyStatementNativeSummaryData(
            I18nSupportEnum.US_EN, req);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getSummaryList());
        assertTrue(result.getSummaryList().isEmpty());
    }

    @Test
    public void testQueryUserMonthlyStatementNativeSummaryData_WithCustomBusiness() {
        // 准备测试数据
        String bankUserId = "testUser";
        String clientId = "testClient";
        String customBusiness = "CUSTOM";
        
        // 设置请求参数
        NativeSummaryReq req = new NativeSummaryReq();
        req.setBankUserId(bankUserId);
        req.setBusiness(customBusiness);
        
        // Mock SystemConfig返回值
        when(systemConfig.getNativeSummaryMonthSize()).thenReturn(36);
        when(systemConfig.getMonthlyStatementPeriodFormat()).thenReturn("yyyyMM");
        
        // Mock UserInvestAccountInfo
        UserInvestAccountInfo accountInfo = new UserInvestAccountInfo();
        accountInfo.setClientId(clientId);
        accountInfo.setAccountType("fund");
        when(userRemoteService.queryUserAllInvestAccountInfo(any())).thenReturn(Arrays.asList(accountInfo));
        
        // Mock 月结单列表
        List<TdFundMonthlyStatement> monthlyStatements = new ArrayList<>();
        when(tdMonthlyStatementMapper.queryUserFundMonthlyStatementList(
            anyString(), anyString(), any(), any(), any()
        )).thenReturn(monthlyStatements);
        
        // 执行测试
        NativeSummaryListResp result = userStatementNativeService.queryUserMonthlyStatementNativeSummaryData(
            I18nSupportEnum.US_EN, req);

    }

    @Test
    public void testQueryUserMonthlyStatementNativeDetail() {
        NativeDetailReq req = new NativeDetailReq();
        req.setBankUserId("testUser");
        req.setPeriod("202303");
        req.setSubBusinessType("fund");
        req.setModuleType(StatementModuleTypeEnum.HOLDING_MODULE.getModuleType());

        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
        statementInfo.setBusinessId("testBusinessId");

        when(monthlyStatementManager.getStatement(any())).thenReturn(statementInfo);

        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
        statement.setPeriod("202303");
        statement.setDocLang(I18nSupportEnum.CN_ZH.getName());
        // Mock user account info
        when(monthlyStatementManager.listUserStatementRecords(any(), any())).thenReturn(Arrays.asList(statement));

        UserInvestAccountInfo userInvestAccountInfo = new UserInvestAccountInfo();
        userInvestAccountInfo.setClientId("clientId");
        userInvestAccountInfo.setAccountType("fund");
        userInvestAccountInfo.setOpenFinishedTime(new Date());
        when(userRemoteService.queryUserAllInvestAccountInfo(any())).thenReturn(Arrays.asList(userInvestAccountInfo));

        BaseMonthlyStatementDataDto baseMonthlyStatementDataDto = new BaseMonthlyStatementDataDto();
        when(monthlyStatementManager.getUserStatementBusinessData(any(), any()))
                .thenReturn(new TdMonthlyStatementBusinessData());

        TdMonthlyStatementHtmlParsedData tdMonthlyStatementHtmlParsedData = new TdMonthlyStatementHtmlParsedData();
        tdMonthlyStatementHtmlParsedData.setRecordDate(new Date());
        when(tdMonthlyStatementParsedDataRepository
                .getByStatementInfoIdAndUserIdAndClientIdAndAppLang(any(), any(), any(), any())).thenReturn(Arrays.asList(tdMonthlyStatementHtmlParsedData));

        userStatementNativeService.queryUserMonthlyStatementNativeDetail(I18nSupportEnum.CN_ZH, req);

    }

//    @Test
//    public void testHtmlDocParseProcess(){
//        // Mocking the response from fileService.fileDownload
//        Response.Builder responseBuilder = Response.builder();
//        Response response = new Response(responseBuilder);
//        when(fileService.fileDownload(any())).thenReturn(response);
//
//        TdFundMonthlyStatementInfo statementInfo = new TdFundMonthlyStatementInfo();
//
//        TdFundMonthlyStatement statement = new TdFundMonthlyStatement();
//        statement.setDocLang(I18nSupportEnum.CN_ZH.getName());
//        userStatementNativeService.htmlDocParseProcess(statementInfo, statement);
//    }
}
