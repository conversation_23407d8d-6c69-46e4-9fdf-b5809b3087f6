package group.za.bank.statement.service.remote.impl;

import group.za.bank.sbs.tradedata.model.resp.ActionCapitalInfoResp;
import group.za.bank.sbs.tradedata.model.resp.ActionFractionOrderResp;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.sbs.tradedata.model.resp.ActionInfoResp;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.service.remote.feign.CompanyActionFeign;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class TradedataRemoteServiceImplTest extends BaseTestService {
    @Mock
    CompanyActionFeign companyActionFeign;
    @Mock
    Logger log;
    @InjectMocks
    TradedataRemoteServiceImpl tradedataRemoteServiceImpl;


    @Test
    public void testActionFractionOrderQuery() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        when(companyActionFeign.actionFractionOrderQuery(any())).thenReturn(responseData);
        ActionFractionOrderResp result = tradedataRemoteServiceImpl.actionFractionOrderQuery(null);
    }

    @Test
    public void testActionCapitalInfoQuery() throws Exception {
        ResponseData responseData = new ResponseData();
        responseData.setCode("0000");
        when(companyActionFeign.actionCapitalInfoQuery(any())).thenReturn(responseData);
        ActionCapitalInfoResp result = tradedataRemoteServiceImpl.actionCapitalInfoQuery(null);
    }

    @Test
    public void testActionInfoQuery() throws Exception {
        ResponseData<ActionInfoResp> responseData = new ResponseData();
        responseData.setCode("0000");
        responseData.setValue(new ActionInfoResp());
        when(companyActionFeign.actionInfoQuery(any())).thenReturn(responseData);
        ActionInfoResp result = tradedataRemoteServiceImpl.actionInfoQuery(null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme