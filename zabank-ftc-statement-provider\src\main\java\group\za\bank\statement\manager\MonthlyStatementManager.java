package group.za.bank.statement.manager;


import group.za.bank.statement.domain.entity.*;
import group.za.bank.statement.entity.dto.UserInvestClientInfoDto;
import group.za.bank.statement.entity.dto.UserStatementRecordDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MonthlyStatementManager {


    /**
     * 查询指定月份已确认发送的月结单信息
     *
     * @param period
     * @return
     */
    TdFundMonthlyStatementInfo getPubConfirmedFundStatementInfo(String period);


    /**
     * 未处理完成的结单
     *
     * @param period
     * @return
     */
    TdFundMonthlyStatementInfo getStatement(String period);


    /**
     * 生成结单
     *
     * @param business
     * @param period
     * @return
     */
    TdFundMonthlyStatementInfo createStatement(String business, String period, String hkTempKey, String zhTempKey, String nxtHkTempKey, String nxtZhTempKey);


    /**
     * 结单初始化完成
     */
    void statementInitFinish(String business, String period);


    /**
     * 结单确认可以推送
     *
     * @param period
     * @param operatorId
     */
    void statementPubConfirm(String period, String operatorId);


    /**
     * 查询结单记录
     *
     * @param statementId
     * @return
     */
    TdFundMonthlyStatement getStatementRecord(String statementId);


    /**
     * 查询结单记录
     *
     * @return
     */
    TdFundMonthlyStatement getStatementRecordByDocLang(String period, String business, String clientId, String docLang);

    /**
     * 获取指定账户下的结单记录
     *
     * @param period
     * @param clientId
     * @return
     */
    List<TdFundMonthlyStatement> listUserStatementRecords(String period, String clientId);


    /**
     * 获取指定账户下的结单记录
     *
     * @param business
     * @param period
     * @param userId
     * @return
     */
    List<TdFundMonthlyStatement> listUserStatementRecordsByUserId(String business, String period, String userId);


    /**
     * 创建用户的月结单记录
     *
     * @param docLang
     * @param tdFundMonthlyStatementInfo
     * @param userInvestClientInfoDto
     * @param statementDate
     * @return
     */
    TdFundMonthlyStatement createUserMonthlyStatementRecords(String docLang, TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo
            , UserInvestClientInfoDto userInvestClientInfoDto, boolean notifyUser, Date statementDate);


    /**
     * 用户业务数据处理完毕
     *
     * @param tdMonthlyStatementData
     */
    void finishUserDataPrepare(TdMonthlyStatementData tdMonthlyStatementData, boolean hasData);

    /**
     * 结单记录初始化完毕
     *
     * @param statementId
     */
    void userStatementInitFinish(String statementId);

    /**
     * 完成生成通知
     *
     * @param statementId
     */
    void statementDocNotified(String statementId);


    /**
     * 结单文件已生成
     *
     * @param statementId
     */
    void userStatementDocGenerated(String statementId, String pdfPath,String htmlPath);


    /**
     * 结单已推送
     *
     * @param statementId
     */
    void userStatementPubFinished(String statementId, String remark);


    /**
     * 结单数据
     *
     * @param statementId
     * @return
     */
    List<TdMonthlyStatementData> listUserStatementDataRecord(String statementId);


    /**
     * 创建用户的结单数据
     *
     * @param statementInfo
     * @param monthlyStatement
     * @return
     */
    List<TdMonthlyStatementData> createUserStatementBusinessData(TdFundMonthlyStatementInfo statementInfo
            , TdFundMonthlyStatement monthlyStatement
            , Map<String, UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap);


    /**
     * 是否本期所有结单都生成完毕
     *
     * @param business
     * @param period
     * @return
     */
    boolean isAllStatementDocGenerated(String business, String period);


    /**
     * 保存结单数据
     *
     * @param statementId
     * @param businessType
     * @param dataMap
     */
    void saveUserStatementBusinessData(String statementId, String businessType, Map<String, Object> dataMap);


    /**
     * 获取指定的结单数据
     *
     * @param statementId
     * @param businessType
     * @return
     */
    TdMonthlyStatementBusinessData getUserStatementBusinessData(String statementId, String businessType);

    /**
     * 获取指定的结单数据列表
     */
    List<TdMonthlyStatementBusinessData> getStatementIdsBusinessData(List<String> statementIds, String businessType);

    /**
     * 删除指定的结单数据
     *
     * @param statementId
     * @param businessType
     * @return
     */
    void deleteUserStatementBusinessData(String statementId, String businessType);


    /**
     * 结单数据
     *
     * @param statementId
     * @return
     */
    List<TdMonthlyStatementBusinessData> listUserStatementBusinessData(String statementId);

    /**
     * 批量插入结单数据
     *
     * @param batchList
     */
    void insertBatchStatementData(List<TdStockStatementData> batchList);

    List<TdFundMonthlyStatement> queryUserInvestMonthlyStatementList(String bankUserId, String clientId, String year, String period);

    /**
     * 初始化用户的结单记录
     * @param monthlyStatementInfo
     * @param businessTypeAndUserInvestClientInfoDtoMap
     * @param businessSubtype
     * @return
     */
    List<UserStatementRecordDto> initUserStatementRecord(TdFundMonthlyStatementInfo monthlyStatementInfo
            , Map<String, UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap, String businessSubtype);


    List<UserStatementRecordDto> reInitUserStatementRecord(TdFundMonthlyStatementInfo tdFundMonthlyStatementInfo, Map<String, UserInvestClientInfoDto> businessTypeAndUserInvestClientInfoDtoMap,
                                                           String businessSubtype, List<TdFundMonthlyStatement> monthlyStatements, boolean notifyUser, Date statementDate);
}
