package group.za.bank.statement.entity.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 港股成交结单数据对象
 * <AUTHOR>
 * @date 2022/05/06
 **/
@Data
public class StatementUsOrderDto extends StatementBaseDto{

    /**交易日：格式是 yyyy-MM-dd */
    private String tradeDate;

    /** 结算日：格式是 yyyy-MM-dd */
    private String clearDate;

    /** 币种 */
    private String currency;

    /** 参考编号 */
    private String referenceNo;

    /** 买入/卖出 */
    private String tradeType;

    /** 股票代码 */
    private String stockCode;

    /** 股票名称 */
    private String stockName;

    /** 交易总数量 */
    private String qty;

    /** 成交均价 */
    private String avgPrice;

    /** 交易总金额 */
    private String amount;

    /** 交易佣金 */
    private String commission;

    /** sec征费 */
    private String secFee;

    /** 净金额 */
    private String netAmount;

    private String clearFee;

    private String activityFee;


    /**
     * 当前订单的所有费用
     */
    private Map<String,StatementFeeDto> feeMap = new HashMap<>();
}
