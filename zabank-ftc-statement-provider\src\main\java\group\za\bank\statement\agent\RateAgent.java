package group.za.bank.statement.agent;

import group.za.bank.invest.common.utils.DateUtil;
import group.za.bank.sbs.bankfront.common.enums.RateSourceEnum;
import group.za.bank.sbs.trade.model.req.feign.StkRateInfoHisReq;
import group.za.bank.statement.common.cache.AbstractCacheTemplate;
import group.za.bank.statement.constants.StatementRedisCacheKey;
import group.za.bank.statement.service.remote.RateRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 汇率缓存
 * <AUTHOR>
 * @date 2022/05/12
 **/
@Slf4j
@Component
public class RateAgent {

    /** 实时汇率本地缓存1小时 */
    private final static Long RATE_TIMEOUT = 60 * 60L;

    /** 历史汇率本地缓存8小时 */
    private final static Long RATE_HIS_TIMEOUT = 60 * 60 * 8L;

    @Autowired
    private RateRemoteService rateRemoteService;


    /**
     * 查询实时汇率信息
     * @param sourceCcy
     * @param targetCcy
     * @return
     */
    public BigDecimal getRate(String sourceCcy, String targetCcy){
        String redisKey = StatementRedisCacheKey.RATE_INFO_CACHE_KEY.key(sourceCcy, targetCcy);
        return AbstractCacheTemplate.queryFromCache(redisKey, RATE_TIMEOUT, TimeUnit.SECONDS,
                ()-> rateRemoteService.queryRate(sourceCcy, targetCcy).getRate(), false);
    }

    /**
     * 查询历史汇率信息
     * @param sourceCcy
     * @param targetCcy
     * @param tradeDate
     * @return
     */
    public BigDecimal getRateHis(String sourceCcy, String targetCcy, Date tradeDate, String marketCode) {
        String redisKey = StatementRedisCacheKey.RATE_INFO_HIS_CACHE_KEY.key(sourceCcy, targetCcy, DateUtil.format(tradeDate, DateUtil.FORMATDAY));
        return AbstractCacheTemplate.queryFromCache(redisKey, RATE_HIS_TIMEOUT, TimeUnit.SECONDS,
                () -> rateRemoteService.queryRateHis(buildStkRateInfoHisReq(sourceCcy, targetCcy, tradeDate, marketCode)).getRate(), false);
    }

    /**
     * 查询最新一条历史汇率信息
     * @param sourceCcy
     * @param targetCcy
     * @return
     */
    public BigDecimal getLastRateHis(String sourceCcy, String targetCcy, String marketCode) {
        String redisKey = StatementRedisCacheKey.RATE_INFO_HIS_LAST_CACHE_KEY.key(sourceCcy, targetCcy);
        return AbstractCacheTemplate.queryFromCache(redisKey, RATE_HIS_TIMEOUT, TimeUnit.SECONDS,
                () -> rateRemoteService.getLatestHisRateInfo(buildLastStkRateInfoHisReq(sourceCcy, targetCcy, marketCode)).getRate(), false);
    }

    private StkRateInfoHisReq buildStkRateInfoHisReq(String sourceCcy, String targetCcy, Date tradeDate, String marketCode){
        StkRateInfoHisReq stkRateInfoHisReq = new StkRateInfoHisReq();
        stkRateInfoHisReq.setTradeDate(tradeDate);
        stkRateInfoHisReq.setMarketCode(marketCode);
        stkRateInfoHisReq.setRateSource(RateSourceEnum.ZA_BANK_CORE.getValue());
        stkRateInfoHisReq.setSourceCcy(sourceCcy);
        stkRateInfoHisReq.setTargetCcy(targetCcy);
        return stkRateInfoHisReq;
    }

    private StkRateInfoHisReq buildLastStkRateInfoHisReq(String sourceCcy, String targetCcy, String marketCode){
        StkRateInfoHisReq stkRateInfoHisReq = new StkRateInfoHisReq();
        stkRateInfoHisReq.setMarketCode(marketCode);
        stkRateInfoHisReq.setRateSource(RateSourceEnum.ZA_BANK_CORE.getValue());
        stkRateInfoHisReq.setSourceCcy(sourceCcy);
        stkRateInfoHisReq.setTargetCcy(targetCcy);
        return stkRateInfoHisReq;
    }
}
