package group.za.bank.statement.service.impl;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;
import group.za.bank.invest.basecommon.utils.UserUtil;
import group.za.bank.invest.pub.constants.enums.ContractNoteTypeEnum;
import group.za.bank.sbs.trade.common.constant.enums.ContractNoteFileStatusEnum;
import group.za.bank.sbs.trade.model.entity.StkContractNote;
import group.za.bank.statement.common.constants.StatementConstants;
import group.za.bank.statement.entity.req.ContractNoteInfoReq;
import group.za.bank.statement.entity.resp.ContractNoteInfoResp;
import group.za.bank.statement.manager.StockTradeManager;
import group.za.bank.statement.service.ContractNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 成交合同
 *
 * <AUTHOR>
 * @date 2023/09/25
 **/
@Service
public class ContractNoteServiceImpl implements ContractNoteService {

    @Autowired
    private StockTradeManager stockTradeManager;

    @Override
    public ContractNoteInfoResp contractInfoQuery(ContractNoteInfoReq req) {
        String userId = req.getBankUserId();
        String orderType = req.getOrderType();
        String orderNo = req.getOrderNo();
        String activityType = req.getActivityType();

        //获取用户语言  简体-简英，繁体-繁英，英文-繁英
        I18nSupportEnum language = UserUtil.getLanguage();
        if (I18nSupportEnum.US_EN.equals(language)) {
            language = I18nSupportEnum.CN_HK;
        }

        //兼容公司行动orderNo一对多的场景(orderNo_activityType)
        if(orderNo.contains(StatementConstants.UNDERSCORE_SEPARATOR)
                && ContractNoteTypeEnum.TYPE_CORPORATE_ACTION.getValue().equals(orderType)){
            String[] split = orderNo.split(StatementConstants.UNDERSCORE_SEPARATOR);
            orderNo = split[0];
            activityType = split[1];
        }

        StkContractNote stkContractNote = stockTradeManager.queryStkContractNoteInfo(orderNo, userId, language.name(),
                orderType, activityType);

        //组装对象返回
        ContractNoteInfoResp resp = new ContractNoteInfoResp();
        if (Objects.isNull(stkContractNote)) {
            resp.setDocStatus(ContractNoteFileStatusEnum.FILE_STATUS_EXECUTING.getValue());
        } else {
            resp.setDocStatus(stkContractNote.getDocStatus());
        }
        return resp;
    }
}
