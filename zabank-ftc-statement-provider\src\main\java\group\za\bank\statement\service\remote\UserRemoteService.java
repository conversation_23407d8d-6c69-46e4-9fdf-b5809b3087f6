package group.za.bank.statement.service.remote;

import group.za.bank.invest.account.constants.enums.AccountTypeEnum;
import group.za.bank.invest.account.entity.dto.HabitancyInfoDto;
import group.za.bank.invest.account.entity.dto.QueryAccountInfoDto;
import group.za.bank.invest.account.entity.dto.UserInvestAccountInfo;
import group.za.bank.invest.account.entity.req.GetUserInvestAccountInfoReq;
import group.za.bank.invest.account.entity.req.QueryBankUserInfoReq;
import group.za.bank.invest.account.entity.resp.AddressInfoResp;
import group.za.bank.invest.account.entity.resp.QueryBankUserInfoResp;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description 用户远程服务
 * @Date 2020/10/13 20:55
 * @Version v1.0
 **/
public interface UserRemoteService {

    /**
     * 用户当前所有投资账户的id
     * @param bankUserId
     * @return
     */
    List<UserInvestAccountInfo> queryUserAllInvestAccountInfo(String bankUserId);
    /**
     * 查询用户投资账号信息
     *
     * @param bankUserId
     * @param accountType
     * @return
     */
    UserInvestAccountInfo queryUserInvestAccountInfo(String bankUserId, AccountTypeEnum accountType);

    /**
     * 查询用户投资账号信息
     *
     * @param getUserInvestAccountInfoReq
     * @return
     */
    UserInvestAccountInfo queryUserInvestAccountInfo(GetUserInvestAccountInfoReq getUserInvestAccountInfoReq);
    /**
     * 查询用户app设置的语言
     *
     * @param bankUserId
     * @return
     */
    String queryUserLanguage(String bankUserId);


    /**
     * 查询用户基本信息
     * @param bankUserId
     * @return
     */
    HabitancyInfoDto queryHabitancyInfo(String bankUserId) ;




    /**
     * 查询用户基本信息
     * @param bankUserId
     * @return
     */
    AddressInfoResp queryContactAddressInfo(String bankUserId) ;

    /**
     * 批量查询用户信息
     */
    Map<String, QueryAccountInfoDto> queryByBankUserIdList(List<String> userIdList);




    /**
     * 判断用户是否是mcv
     * @param bankUserId
     * @return
     */
    boolean isUserMcv(String bankUserId);

    /**
     * 查询用户投资账号列表信息
     *
     * @param bankUserId
     * @return
     */
    List<UserInvestAccountInfo> queryUserInvestCloseAccountInfo(String bankUserId);

    /**
     * 查询用户是否已销银行户
     * @param bankUserId
     * @return
     */
    Boolean judgeBankAccountClosed(String bankUserId);

    /**
     * 查询银行用户信息
     */
    Map<String, QueryBankUserInfoResp> queryBankUserInfo(String bankUserId);

    /**
     * 批量查询银行用户信息
     */
    Map<String, QueryBankUserInfoResp> queryBankUserInfoList(QueryBankUserInfoReq req);

}
