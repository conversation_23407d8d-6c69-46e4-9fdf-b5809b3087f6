# 新增apollo配置
spring.main.banner-mode = log
spring.jackson.date-format = yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone = GMT+8

# mongoDB
spring.data.mongodb.uri = 
spring.data.mongodb.database = za_bank_invest_statement

# redis config
spring.redis.host = 
spring.redis.database = 0
spring.redis.port = 
spring.redis.password = 

# eureka config
eureka.client.healthcheck.enabled = true
eureka.client.serviceUrl.defaultZone = 
eureka.instance.prefer-ip-address = true
eureka.instance.lease-expiration-duration-in-seconds = 30
eureka.instance.lease-renewal-interval-in-seconds = 10

#sbs gateway
sbs.gateway.url = 

# ribbon 超时设置
ribbon.ReadTimeout = 6000
ribbon.ConnectTimeout = 1500
ribbon.MaxAutoRetries = 0


# MySQL Config
zainvest.sharding.enabled = true

# za_bank_invest_statement db
zainvest.sharding.group.statement-group.beanNamePrefix = statement
zainvest.sharding.group.statement-group.mybatis.mapper-locations[0] = classpath*:mapper/statement/*Mapper.xml
zainvest.sharding.group.statement-group.mybatis.mapper-scanner.base-package = group.za.bank.statement.domain.mapper
zainvest.sharding.group.statement-group.databases[0].ip = 
zainvest.sharding.group.statement-group.databases[0].port = 
zainvest.sharding.group.statement-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.statement-group.databases[0].userName = 
zainvest.sharding.group.statement-group.databases[0].password = 
zainvest.sharding.group.statement-group.databases[0].dbName = za_bank_invest_statement

# za_bank_fund_account db
zainvest.sharding.group.account-group.beanNamePrefix = account
zainvest.sharding.group.account-group.mybatis.mapper-locations[0] = classpath*:mapper/account/*Mapper.xml
zainvest.sharding.group.account-group.mybatis.mapper-scanner.base-package = group.za.bank.invest.domain.account.mapper
zainvest.sharding.group.account-group.databases[0].ip = 
zainvest.sharding.group.account-group.databases[0].port = 
zainvest.sharding.group.account-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.account-group.databases[0].userName = 
zainvest.sharding.group.account-group.databases[0].password = 
zainvest.sharding.group.account-group.databases[0].dbName = za_bank_fund_account

# za_bank_fund_market db
zainvest.sharding.group.market-group.beanNamePrefix = market
zainvest.sharding.group.market-group.mybatis.mapper-locations[0] = classpath*:mapper/market/*Mapper.xml
zainvest.sharding.group.market-group.mybatis.mapper-scanner.base-package = group.za.bank.market.domain.mapper
zainvest.sharding.group.market-group.databases[0].ip = 
zainvest.sharding.group.market-group.databases[0].port = 
zainvest.sharding.group.market-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.market-group.databases[0].userName = 
zainvest.sharding.group.market-group.databases[0].password = 
zainvest.sharding.group.market-group.databases[0].dbName = za_bank_fund_market

# za_bank_fund_trade db
zainvest.sharding.group.trade-group.beanNamePrefix = trade
zainvest.sharding.group.trade-group.mybatis.mapper-locations[0] = classpath*:mapper/trade/*Mapper.xml
zainvest.sharding.group.trade-group.mybatis.mapper-locations[1] = classpath*:mapper/clear/*Mapper.xml
zainvest.sharding.group.trade-group.mybatis.mapper-scanner.base-package = group.za.bank.fund.domain.trade.mapper,group.za.bank.fund.domain.clear.mapper
zainvest.sharding.group.trade-group.databases[0].ip = 
zainvest.sharding.group.trade-group.databases[0].port = 
zainvest.sharding.group.trade-group.databases[0].driverClass = com.mysql.jdbc.Driver
zainvest.sharding.group.trade-group.databases[0].userName = 
zainvest.sharding.group.trade-group.databases[0].password = 
zainvest.sharding.group.trade-group.databases[0].dbName = za_bank_fund_trade

# 配置银行基金xxjob任务
xxl.job.admin.addresses = 
xxl.job.executor.appname = zabank-ftc-statement-service
xxl.job.executor.ip = ${spring.cloud.client.ip-address}
xxl.job.executor.port = -1
xxl.job.coupon.expire.sendBeforeDay = 2
xxl.job.executor.logpath = /home/<USER>/logs/xxl-job/jobhandler/zabank-ftc-statement-service
xxl.job.executor.logretentiondays = -1

# 告警配置
system.alarm.wechat.userList = ua2204261438414715016,ua2204261439104945010,ua2204261439105015004,ua2204261439114775032,ua2204261438443405017,ua2204271136003730003,ua2204271135565560012,ua2204261437055275031,ua2204261437055195011,lishiwen,za-xiaoke,ua2204261438375775006,ua2204261441286715019
system.alarm.email.userList = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#结单业务类型
monthly.statement.defaultMonthlyStatementBusinessType = fund,stock
monthly.statement.pageSize = 500

# 月结单模板文件存放路径
monthly.statement.doc.tempPath = invest/template/invest/
monthly.statement.doc.hkTempKey = zabank_invest_monthly_hk_20230530_v1.html
monthly.statement.doc.zhTempKey = zabank_invest_monthly_zh_20230530_v1.html

# 确认可以推送前检查所有结单状态必须是已生成才可以推送
monthly.statement.checkAllStatementDocGenerateStatusBeforePubConfirm = true
# 推送前是否需要确认
monthly.statement.checkConfirmStatusBeforePub = true
monthly.statement.monthlyStatementPeriodFormat = yyyyMM
monthly.statement.monthlyStatementDateFormat = dd MMM yyy
monthly.statement.monthlyStatementShortDateFormat = dd MMM
monthly.statement.businessSubTypeSeparator = ,



# 结单文件本地存放路径
ttl.file.localPath = /opt/ttl_sftp/statement/
ttl.file.over.minute = 30
# sftp文件服务器地址
ttl.file.sftp.host = 
ttl.file.sftp.port = 
ttl.file.sftp.username = 
ttl.file.sftp.password = 
ttl.file.sftp.dir = 

#配置mq中间件
rabbitmq.common.addresses = 
rabbitmq.common.port = 
rabbitmq.common.userName = 
rabbitmq.common.password = 
rabbitmq.common.virtualHost = 

#rabbitmq基础配置
spring.rabbitmq.userName = ${rabbitmq.common.userName}
spring.rabbitmq.password = ${rabbitmq.common.password}
spring.rabbitmq.host = ${rabbitmq.common.addresses}
spring.rabbitmq.port = ${rabbitmq.common.port}
spring.rabbitmq.virtualHost = ${rabbitmq.common.virtualHost}
spring.rabbitmq.maxAttempts = 5
spring.rabbitmq.retryEnable = true
spring.rabbitmq.maxInterval = 5000

#activity相关配置 start
#spring stream binder配置
spring.cloud.stream.binders.activity.type = rabbit
spring.cloud.stream.binders.activity.environment.spring.rabbitmq.host = 
spring.cloud.stream.binders.activity.environment.spring.rabbitmq.port = 
spring.cloud.stream.binders.activity.environment.spring.rabbitmq.username = 
spring.cloud.stream.binders.activity.environment.spring.rabbitmq.password = 
spring.cloud.stream.binders.activity.environment.spring.rabbitmq.virtual-host = 
#spring stream 生产者配置
spring.cloud.stream.bindings.activityOutputRabbitmq.destination = act
spring.cloud.stream.bindings.activityOutputRabbitmq.content-type = application/json
spring.cloud.stream.bindings.activityOutputRabbitmq.binder = activity
spring.cloud.stream.bindings.activityOutputRabbitmq.group = activity-queue

#activity 线程池配置
activity.poolConfig.corePoolSize = 2
activity.poolConfig.maxPoolSize = 10
activity.poolConfig.queueCapacity = 1000
activity.poolConfig.namePrefix = activitySendMessaging-

#activity 参数校验错误是否继续发送
activity.param.error.deal.flag = true

#activity 是否在sdk中做数据校验，true则校验
activity.data.check.flag = true

#activity相关配置 end

# rabbitmq生产者配置
rabbitmq.producer.userName = ${rabbitmq.common.userName}
rabbitmq.producer.password = ${rabbitmq.common.password}
rabbitmq.producer.addresses = ${rabbitmq.common.addresses}
rabbitmq.producer.virtualHost = ${rabbitmq.common.virtualHost}
rabbitmq.producer.maxAttempts = 5
rabbitmq.producer.retryEnable = true
rabbitmq.producer.maxInterval = 5000


# rabbitmq消费者配置
rabbitmq.consumer.userMonthlyStatementDataPrepare.userName = ${rabbitmq.common.userName}
rabbitmq.consumer.userMonthlyStatementDataPrepare.password = ${rabbitmq.common.password}
rabbitmq.consumer.userMonthlyStatementDataPrepare.addresses = ${rabbitmq.common.addresses}
rabbitmq.consumer.userMonthlyStatementDataPrepare.virtualHost = ${rabbitmq.common.virtualHost}
#消费端的监听个数(即RabbitListener开启几个线程去处理数据。
rabbitmq.consumer.userMonthlyStatementDataPrepare.concurrency = 10
# 消费端的监听最大个数
rabbitmq.consumer.userMonthlyStatementDataPrepare.maxConcurrency = 10
# 消费者每次从队列获取的消息数量，如果有事务的话，必须大于等于transaction数量.
rabbitmq.consumer.userMonthlyStatementDataPrepare.prefetch = 10
#指定一个事务处理的消息数量，最好是小于等于prefetch的数量.
rabbitmq.consumer.userMonthlyStatementDataPrepare.transactionSize = 1
rabbitmq.consumer.userMonthlyStatementDataPrepare.exchange = user.monthly.statement.data.prepare.exchange
rabbitmq.consumer.userMonthlyStatementDataPrepare.queueName = user.monthly.statement.data.prepare.queue
rabbitmq.consumer.userMonthlyStatementDataPrepare.routeKeys = user.monthly.statement.data.prepare

# 日结单文件解析的类型，多个类型用英文逗号分隔
monthly.statement.dailyFile.parse.formatTypes = 1,3,4,5
# 月结单文件解析的类型，多个类型用英文逗号分隔
monthly.statement.monthlyFile.parse.formatTypes = 4,5

# 监管费用
monthly.statement.stock.secFeeKey = US SFC FEE
# 交易费用
monthly.statement.stock.finraFeeKey = US FINRA FEE

# 费用为null的时候是否生成结单：true-生成  false-不生成
monthly.statement.stock.generateStatementWhenFeeNull = true

# 银行核心请求参数配置
core.sys-req-system = FTC
core.sys-req-channel = MOBIE
core.app-teller-id = CHN0002
core.product-code = DD000001
core.currency-type = M
bank.corefront.url = 

#月结单native页面分页大小
monthly.statement.nativePageSize = 2

