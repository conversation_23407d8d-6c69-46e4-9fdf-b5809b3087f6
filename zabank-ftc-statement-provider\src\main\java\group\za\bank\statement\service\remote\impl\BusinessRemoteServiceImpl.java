package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.sbs.business.model.req.TransferDetailReq;
import group.za.bank.sbs.business.model.resp.TransferDetailResp;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.BusinessRemoteService;
import group.za.bank.statement.service.remote.feign.BusinessTransferFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 股票综合服务
 *
 * <AUTHOR>
 * @date 2023/08/22
 **/
@Service
@Slf4j
public class BusinessRemoteServiceImpl implements BusinessRemoteService {

    @Autowired
    private BusinessTransferFeign businessTransferFeign;

    @Override
    public List<TransferDetailResp> queryTransferDetailList(TransferDetailReq transferDetailReq) {
        ResponseData<List<TransferDetailResp>> listResponseData = businessTransferFeign.queryTransferDetailList(transferDetailReq);
        if (!listResponseData.judgeSuccess()) {
            log.info("queryTransferDetailList fail,transferDetailReq:{},msg:{}", transferDetailReq, listResponseData.getMsg());
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return listResponseData.getValue();
    }
}
