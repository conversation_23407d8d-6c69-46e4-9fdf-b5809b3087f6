package group.za.bank.statement.service.impl;

import group.za.bank.sbs.trade.model.entity.StkContractNote;
import group.za.bank.statement.entity.req.ContractNoteInfoReq;
import group.za.bank.statement.entity.resp.ContractNoteInfoResp;
import group.za.bank.statement.manager.StockTradeManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@RunWith(org.mockito.runners.MockitoJUnitRunner.class)
public class ContractNoteServiceImplTest {
    @Mock
    StockTradeManager stockTradeManager;
    @InjectMocks
    ContractNoteServiceImpl contractNoteServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testContractInfoQuery() throws Exception {
        StkContractNote stkContractNote = new StkContractNote();
        stkContractNote.setOrderNo("3112_223");
        when(stockTradeManager.queryStkContractNoteInfo(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(stkContractNote);

        ContractNoteInfoReq contractNoteInfoReq = new ContractNoteInfoReq();
        contractNoteInfoReq.setOrderNo("3112_223");
        ContractNoteInfoResp result = contractNoteServiceImpl.contractInfoQuery(contractNoteInfoReq);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme