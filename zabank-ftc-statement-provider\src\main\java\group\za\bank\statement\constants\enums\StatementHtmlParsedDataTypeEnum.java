package group.za.bank.statement.constants.enums;

import group.za.bank.invest.basecommon.constants.enums.I18nSupportEnum;

import static group.za.bank.statement.constants.enums.StatementSubBusinessTypeEnum.STOCK;

/**
 * <AUTHOR>
 * @createTime 22 11:18
 * @description
 */
public enum  StatementHtmlParsedDataTypeEnum {

    /**
     * 结单
     */
    HOLDING_CHANGE_STOCK_IN(StatementModuleTypeEnum.HOLDING_CHANGE_MODULE.getModuleType(),STOCK.getType()
            ,StatementHtmlContentCompareTypeEnum.EQUAL.getType(),StatementInOutEnum.IN.getDesc(),StatementInOutEnum.IN.getDescHk()),
    HOLDING_CHANGE_STOCK_OUT(StatementModuleTypeEnum.HOLDING_CHANGE_MODULE.getModuleType(),STOCK.getType()
            ,StatementHtmlContentCompareTypeEnum.EQUAL.getType(),StatementInOutEnum.OUT.getDesc(),StatementInOutEnum.OUT.getDescHk()),



    ;

    private String moduleType;
    private String subBusinessType;
    private String zhKey;
    private String hkKey;
    private Integer contentCompareType;

    StatementHtmlParsedDataTypeEnum(String moduleType, String subBusinessType, Integer contentCompareType,String zhKey, String hkKey) {
        this.moduleType = moduleType;
        this.subBusinessType = subBusinessType;
        this.zhKey = zhKey;
        this.hkKey = hkKey;
        this.contentCompareType = contentCompareType;
    }


    public  String getKey(String lang){
        if(I18nSupportEnum.CN_ZH.getNameOfBank().equals(lang)){
            return getZhKey();
        }
        return getHkKey();
    }

    public static StatementHtmlParsedDataTypeEnum contentMatch(String lang,String content){
        if(content==null){
            return null;
        }
        for(StatementHtmlParsedDataTypeEnum statementHtmlParsedDataTypeEnum:StatementHtmlParsedDataTypeEnum.values()){
            String businessId = null;
            if(I18nSupportEnum.CN_ZH.getNameOfBank().equals(lang)){
               businessId = statementHtmlParsedDataTypeEnum.getZhKey();
            }else {
                businessId = statementHtmlParsedDataTypeEnum.getHkKey();
            }

            if(StatementHtmlContentCompareTypeEnum.EQUAL.getType().equals(statementHtmlParsedDataTypeEnum.getContentCompareType())){
                if(content.equals(businessId)){
                    return statementHtmlParsedDataTypeEnum;
                }
            }else if(StatementHtmlContentCompareTypeEnum.START_WITH.getType().equals(statementHtmlParsedDataTypeEnum.getContentCompareType())){
                if(content.startsWith(businessId)){
                    return statementHtmlParsedDataTypeEnum;
                }
            }else if(StatementHtmlContentCompareTypeEnum.END_WITH.getType().equals(statementHtmlParsedDataTypeEnum.getContentCompareType())){
                if(content.endsWith(businessId)){
                    return statementHtmlParsedDataTypeEnum;
                }
            }else if(StatementHtmlContentCompareTypeEnum.CONTAINS.getType().equals(statementHtmlParsedDataTypeEnum.getContentCompareType())){
                if(content.contains(businessId)){
                    return statementHtmlParsedDataTypeEnum;
                }
            }
        }
        return null;
    }



    public String getModuleType() {
        return moduleType;
    }

    public String getSubBusinessType() {
        return subBusinessType;
    }

    public String getZhKey() {
        return zhKey;
    }

    public String getHkKey() {
        return hkKey;
    }

    public Integer getContentCompareType() {
        return contentCompareType;
    }
}
