<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="group.za.bank.statement.domain.mapper.TdStatementActionPendingDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="group.za.bank.statement.domain.entity.TdStatementActionPendingData">
        <id column="id" property="id" />
        <result column="action_id" property="actionId" />
        <result column="bank_user_id" property="bankUserId" />
        <result column="account_id" property="accountId" />
        <result column="statement_data_id" property="statementDataId" />
        <result column="market_code" property="marketCode" />
        <result column="action_type" property="actionType" />
        <result column="capital_time" property="capitalTime" />
        <result column="original_period" property="originalPeriod" />
        <result column="period" property="period" />
        <result column="data_status" property="dataStatus" />
        <result column="execute_time" property="executeTime" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
        <result column="creator" property="creator" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="modifier" property="modifier" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, action_id, bank_user_id, account_id, statement_data_id, action_type, capital_time, original_period, period, data_status, execute_time, remark, version, creator, gmt_created, modifier, gmt_modified, is_deleted
    </sql>
    <select id="queryAllByDataStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        td_statement_action_pending_data
        where data_status = #{dataStatus}
    </select>

</mapper>
