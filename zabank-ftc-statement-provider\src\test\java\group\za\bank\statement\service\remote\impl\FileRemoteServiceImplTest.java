package group.za.bank.statement.service.remote.impl;

import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.pub.entity.resp.SingleFileUploadResp;
import group.za.bank.invest.pub.feign.FileFeignService;
import group.za.bank.statement.base.BaseTestService;
import group.za.bank.statement.entity.dto.ByteArrayMultipartFile;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class FileRemoteServiceImplTest extends BaseTestService {
    @Mock
    FileFeignService fileFeignService;
    @Mock
    Logger log;
    @InjectMocks
    FileRemoteServiceImpl fileRemoteServiceImpl;

    @Test
    public void testSingleFile() throws Exception {
        when(fileFeignService.singleFileUpload(any(), any())).thenReturn(new ResponseData<>("0000", "", new SingleFileUploadResp()));
        String result = fileRemoteServiceImpl.singleFile(new ByteArrayMultipartFile("name", "originalFilename", "contentType", new byte[]{(byte) 0}), "storyType");
    }

    @Test
    public void testReadLongTextFromObs() throws Exception {
        when(fileFeignService.readLongTextFromObs(any())).thenReturn(new ResponseData<>("0000", "", ""));
        String result = fileRemoteServiceImpl.readLongTextFromObs("obsKey");
    }

    @Test
    public void testSingleFileUploadToObs() throws Exception {
        when(fileFeignService.singleFileUploadToObs(any(), any())).thenReturn(new ResponseData<>("0000", "", new SingleFileUploadResp()));
        String result = fileRemoteServiceImpl.singleFileUploadToObs(new ByteArrayMultipartFile("name", "originalFilename", "contentType", new byte[]{(byte) 0}), "storyType");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme