package group.za.bank.statement.service.remote.feign;

import group.za.bank.sbs.trade.feign.StkTradeCalendarService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 交易日历feign
 *
 * <AUTHOR>
 * @date 2022/05/27
 **/
@FeignClient(
        value = "zabank-sbs-trade-service",
        contextId = "stkTradeCalendarService1",
        url = "${sbs.gateway.url}"
)
public interface StkTradeCalendarFeign extends StkTradeCalendarService {
}
