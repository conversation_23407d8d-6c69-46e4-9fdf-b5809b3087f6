package group.za.bank.statement.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import group.za.bank.statement.service.CloseMonthlyStatementService;
import group.za.invest.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName closeMonthlyStatementSendToOPJob
 * @Description 销户月结单发送OP任务-- 参数例子：传202310（period期数）
 * @date 2023/10/11 17:40
 * @Version 1.0
 */

@Slf4j
@JobHandler(value = "closeMonthlyStatementSendToOPJob")
@Component
public class CloseMonthlyStatementSendToOPJob extends I<PERSON>ob<PERSON>andler {

    @Autowired
    private CloseMonthlyStatementService closeMonthlyStatementService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("销户月结单发送OP任务开始， 入参 = {}", s);
        String period = null;
        if(Strings.isEmpty(s)){
            Date currentDate = new Date();
            Date monthlyStatementDate = DateUtil.getLastDayOfLastMonth(currentDate);
            period = DateUtil.format(monthlyStatementDate,"yyyyMM");
        }else {
            period = s;
        }
        closeMonthlyStatementService.generateCloseMonthlyStatement(period);
        log.info("销户月结单发送OP结束， 入参 = {}", s);
        return ReturnT.SUCCESS;
    }
}
