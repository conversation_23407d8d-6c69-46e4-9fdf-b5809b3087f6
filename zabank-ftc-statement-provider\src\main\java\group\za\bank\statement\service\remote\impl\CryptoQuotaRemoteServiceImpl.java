package group.za.bank.statement.service.remote.impl;

import group.za.bank.ccs.core.support.share.entity.dto.DayKlineSupDTO;
import group.za.bank.ccs.core.support.share.entity.req.DayKlineSupReq;
import group.za.bank.invest.basecommon.entity.resp.ResponseData;
import group.za.bank.invest.basecommon.exception.BusinessException;
import group.za.bank.statement.constants.enums.StatementErrorMsgEnum;
import group.za.bank.statement.service.remote.CryptoQuotaRemoteService;
import group.za.bank.statement.service.remote.feign.CryptoQuotaKlineFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 数字货币行情
 *
 * <AUTHOR>
 * @date 2023/08/31
 **/
@Service
@Slf4j
public class CryptoQuotaRemoteServiceImpl implements CryptoQuotaRemoteService {

    @Autowired
    private CryptoQuotaKlineFeign cryptoQuotaKlineFeign;

    /**
     * 查询行情数据
     * @param assetId
     * @param quotaDate
     * @return
     */
    @Override
    public DayKlineSupDTO queryDayKline(String assetId, String quotaDate){
        DayKlineSupReq req = new DayKlineSupReq();
        req.setAssetIdList(Arrays.asList(assetId));
        req.setQuoteDate(quotaDate);
        ResponseData<List<DayKlineSupDTO>> listResponseData = cryptoQuotaKlineFeign.queryDayKline(req);
        if (!listResponseData.judgeSuccess()) {
            throw new BusinessException(listResponseData.getCode(), listResponseData.getMsg());
        }
        List<DayKlineSupDTO> klineSupDTOS = listResponseData.getValue();
        if(CollectionUtils.isEmpty(klineSupDTOS)){
            log.warn("queryDayKline 查询行情数据为空，assetId：{}，quotaDate：{} ", assetId, quotaDate);
            throw new BusinessException(StatementErrorMsgEnum.REMOTE_ERROR);
        }
        return klineSupDTOS.get(0);
    }
}
